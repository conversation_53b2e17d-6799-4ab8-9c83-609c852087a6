package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 知识库专业配置对象 s_knowledge_speciality
 * 
 * <AUTHOR>
 * @date 2024-07-30
 */
public class KnowledgeSpeciality extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 知识库appId */
    @Excel(name = "知识库appId")
    private String appId;

    /** 知识库secretKey */
    @Excel(name = "知识库secretKey")
    private String secretKey;

    /** 学校id */
    @Excel(name = "学校id")
    private Long universityId;

    /** 学院id */
    @Excel(name = "学院id")
    private Long collegeId;

    /** 专业id */
    @Excel(name = "专业id")
    private Long majorId;

    private String universityName;
    private String collegeName;
    private String majorName;

    @Excel(name = "所属单位")
    @TableField(exist = false)
    private Long[] affiliatedUnit;

    @Excel(name = "所属单位名称")
    @TableField(exist = false)
    private String[] affiliatedUnitName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setAppId(String appId) 
    {
        this.appId = appId;
    }

    public String getAppId() 
    {
        return appId;
    }
    public void setSecretKey(String secretKey) 
    {
        this.secretKey = secretKey;
    }

    public String getSecretKey() 
    {
        return secretKey;
    }
    public void setUniversityId(Long universityId) 
    {
        this.universityId = universityId;
    }

    public Long getUniversityId() 
    {
        return universityId;
    }
    public void setCollegeId(Long collegeId) 
    {
        this.collegeId = collegeId;
    }

    public Long getCollegeId() 
    {
        return collegeId;
    }
    public void setMajorId(Long majorId) 
    {
        this.majorId = majorId;
    }

    public Long getMajorId() 
    {
        return majorId;
    }

    public Long[] getAffiliatedUnit() {
        return affiliatedUnit;
    }

    public void setAffiliatedUnit(Long[] affiliatedUnit) {
        this.affiliatedUnit = affiliatedUnit;
    }

    public String[] getAffiliatedUnitName() {
        return affiliatedUnitName;
    }

    public void setAffiliatedUnitName(String[] affiliatedUnitName) {
        this.affiliatedUnitName = affiliatedUnitName;
    }

    public String getUniversityName() {
        return universityName;
    }

    public void setUniversityName(String universityName) {
        this.universityName = universityName;
    }

    public String getCollegeName() {
        return collegeName;
    }

    public void setCollegeName(String collegeName) {
        this.collegeName = collegeName;
    }

    public String getMajorName() {
        return majorName;
    }

    public void setMajorName(String majorName) {
        this.majorName = majorName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("appId", getAppId())
            .append("secretKey", getSecretKey())
            .append("universityId", getUniversityId())
            .append("collegeId", getCollegeId())
            .append("majorId", getMajorId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
