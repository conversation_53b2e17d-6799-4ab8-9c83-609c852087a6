package com.ruoyi.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.annotation.Excel;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 学校信息表(University)表实体类
 *
 * <AUTHOR>
 * @since 2024-06-11 11:26:57
 */
@Data
@TableName("s_university")
public class University {
    private static final long serialVersionUID = 1L;

    /**
     * 学校ID
     **/
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 学校名称
     **/
    @TableField("univer_name")
    @Excel(name = "学校名称")
    private String univerName;

    /**
     * 学校地址
     **/
    @TableField("univer_addr")
    @Excel(name = "学校地址")
    private String univerAddr;

    /**
     * 联系人
     **/
    @TableField("contact")
    @Excel(name = "联系人")
    private String contact;

    /**
     * 联系方式
     **/
    @TableField("phone")
    @Excel(name = "联系方式")
    private String phone;

    /**
     * 创建人
     **/
    @TableField("create_by")
    @Excel(name = "创建人")
    private String createBy;

    /**
     * 创建时间
     **/
    @TableField("create_time")
    @Excel(name = "创建时间")
    private Date createTime;

    /**
     * 修改人
     **/
    @TableField("update_by")
    @Excel(name = "修改人")
    private String updateBy;

    /**
     * 修改时间
     **/
    @TableField("update_time")
    @Excel(name = "修改时间")
    private Date updateTime;

    /**
     * 学校名称（用于返回树状结构）
     */
    private String name;
    /**
     * 学院Id
     */
    private Long collegeId;
    /**
     * 专业Id
     */
    private Long majorId;
    /**
     * 班级Id
     */
    private Long classId;
    private String colleName;
    private String majorName;
    private String className;
}

