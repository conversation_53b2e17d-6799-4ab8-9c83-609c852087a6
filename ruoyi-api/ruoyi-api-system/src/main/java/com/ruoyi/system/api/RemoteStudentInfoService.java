package com.ruoyi.system.api;


import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.system.api.domain.StudentInfo;
import com.ruoyi.system.api.factory.RemoteStudentInfoFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

@FeignClient(contextId = "remoteStudentInfoService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteStudentInfoFallbackFactory.class)
public interface RemoteStudentInfoService {
    @PostMapping( "/studentinfo/getList")
    List<StudentInfo> getList(@RequestBody StudentInfo studentInfo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
