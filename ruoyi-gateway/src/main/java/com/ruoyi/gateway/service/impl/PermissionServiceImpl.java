package com.ruoyi.gateway.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.TokenConstants;
import com.ruoyi.common.core.utils.JwtUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.gateway.service.PermissionService;
import io.jsonwebtoken.Claims;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.PatternMatchUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * 权限验证服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class PermissionServiceImpl implements PermissionService {

    private static final Logger log = LoggerFactory.getLogger(PermissionServiceImpl.class);

    @Resource
    private RedisService redisService;

    @Override
    public boolean checkApiKeyPermission(String apiKey, String url) {
        try {
            String cacheKey = TokenConstants.SYSTEM_ACCESS_API_KEY_SECRET + apiKey;
            Object cacheObject = redisService.getCacheObject(cacheKey);
            if (cacheObject == null) {
                log.debug("[权限验证] API Key权限缓存不存在: {}", cacheKey);
                return false;
            }

            String jsonStr = JSON.toJSONString(cacheObject);
            if (StringUtils.isBlank(jsonStr)) {
                log.debug("[权限验证] API Key权限数据为空: {}", cacheKey);
                return false;
            }

            JSONObject jsonObj = JSON.parseObject(jsonStr);
            List<String> permissions = Optional.ofNullable(jsonObj.getList("permissions", String.class))
                    .orElse(Collections.emptyList());

            // 检查是否有匹配的权限路径
            boolean hasPermission = matchesAnyPattern(url, permissions);
            log.debug("[权限验证] API Key权限检查 - 路径: {}, 权限列表: {}, 结果: {}", url, permissions, hasPermission);
            return hasPermission;

        } catch (Exception e) {
            log.error("[权限验证] API Key权限检查异常: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean checkApiTokenPermission(String apiToken, String url) {
        try {
            String cacheKey = TokenConstants.SYSTEM_ACCESS_KEY_SECRET_TOKEN + apiToken;
            Object cacheObject = redisService.getCacheObject(cacheKey);
            if (cacheObject == null) {
                log.debug("[权限验证] API Token权限缓存不存在: {}", cacheKey);
                return false;
            }

            String jsonStr = JSON.toJSONString(cacheObject);
            if (StringUtils.isBlank(jsonStr)) {
                log.debug("[权限验证] API Token权限数据为空: {}", cacheKey);
                return false;
            }

            JSONObject jsonObj = JSON.parseObject(jsonStr);
            List<String> tokenPermissions = Optional.ofNullable(jsonObj.getList("tokenPermissions", String.class))
                    .orElse(Collections.emptyList());

            // 检查是否有匹配的权限路径
            boolean hasPermission = matchesAnyPattern(url, tokenPermissions);
            log.debug("[权限验证] API Token权限检查 - 路径: {}, 权限列表: {}, 结果: {}", url, tokenPermissions, hasPermission);
            return hasPermission;

        } catch (Exception e) {
            log.error("[权限验证] API Token权限检查异常: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean checkTokenPermission(String token, String url) {
        try {
            // 解析 Token 获取用户信息
            Claims claims = JwtUtils.parseToken(token);
            if (claims == null) {
                log.debug("[权限验证] JWT Token解析失败");
                return false;
            }

            String userKey = JwtUtils.getUserKey(claims);
            String tokenCacheKey = CacheConstants.LOGIN_TOKEN_KEY + userKey;
            
            // 从缓存中获取用户登录信息
            Object loginUserObj = redisService.getCacheObject(tokenCacheKey);
            if (loginUserObj == null) {
                log.debug("[权限验证] 用户登录信息缓存不存在: {}", tokenCacheKey);
                return false;
            }

            // 这里可以根据实际需求扩展权限验证逻辑
            // 例如：检查用户角色权限、菜单权限等
            // 当前实现：JWT Token通过基础验证后，进行更细粒度的权限控制
            
            String userId = JwtUtils.getUserId(claims);
            if (StringUtils.isNotEmpty(userId)) {
                return checkUserUrlPermission(Long.valueOf(userId), url);
            }
            
            log.debug("[权限验证] JWT Token权限检查通过 - 路径: {}, 用户: {}", url, userKey);
            return true;

        } catch (Exception e) {
            log.error("[权限验证] JWT Token权限检查异常: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean checkUserPermission(Long userId, String permission) {
        try {
            // 这里可以实现基于用户ID和权限码的验证逻辑
            // 例如：从数据库或缓存中获取用户权限列表，然后进行匹配
            log.debug("[权限验证] 用户权限检查 - 用户ID: {}, 权限码: {}", userId, permission);
            
            // 当前简化实现，实际项目中需要根据业务需求完善
            return true;
        } catch (Exception e) {
            log.error("[权限验证] 用户权限检查异常: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean checkRolePermission(String roleKey, String url) {
        try {
            // 这里可以实现基于角色的权限验证逻辑
            log.debug("[权限验证] 角色权限检查 - 角色: {}, 路径: {}", roleKey, url);
            
            // 当前简化实现，实际项目中需要根据业务需求完善
            return true;
        } catch (Exception e) {
            log.error("[权限验证] 角色权限检查异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查用户URL权限（内部方法）
     *
     * @param userId 用户ID
     * @param url    请求路径
     * @return 是否有权限
     */
    private boolean checkUserUrlPermission(Long userId, String url) {
        try {
            // 这里可以实现更复杂的用户URL权限验证逻辑
            // 例如：
            // 1. 从缓存或数据库获取用户的菜单权限
            // 2. 检查URL是否在用户的权限范围内
            // 3. 支持通配符匹配等
            
            log.debug("[权限验证] 用户URL权限检查 - 用户ID: {}, 路径: {}", userId, url);
            
            // 当前简化实现，默认允许访问
            // 实际项目中需要根据具体的权限模型来实现
            return true;
        } catch (Exception e) {
            log.error("[权限验证] 用户URL权限检查异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查URL是否匹配任何一个权限模式
     *
     * @param url      请求URL
     * @param patterns 权限模式列表
     * @return 是否匹配
     */
    private boolean matchesAnyPattern(String url, List<String> patterns) {
        if (patterns == null || patterns.isEmpty()) {
            return false;
        }
        
        return patterns.stream()
                .filter(StringUtils::hasText)
                .anyMatch(pattern -> PatternMatchUtils.simpleMatch(pattern, url));
    }
}
