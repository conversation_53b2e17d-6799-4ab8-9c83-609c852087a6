package com.ruoyi.gateway.service;

/**
 * 权限验证服务接口
 * 
 * <AUTHOR>
 */
public interface PermissionService {

    /**
     * 检查 API Key 权限
     *
     * @param apiKey API密钥
     * @param url    请求路径
     * @return 是否有权限
     */
    boolean checkApiKeyPermission(String apiKey, String url);

    /**
     * 检查 API Token 权限
     *
     * @param apiToken API令牌
     * @param url      请求路径
     * @return 是否有权限
     */
    boolean checkApiTokenPermission(String apiToken, String url);

    /**
     * 检查 JWT Token 权限
     *
     * @param token JWT令牌
     * @param url   请求路径
     * @return 是否有权限
     */
    boolean checkTokenPermission(String token, String url);

    /**
     * 检查用户权限（基于用户ID和权限码）
     *
     * @param userId     用户ID
     * @param permission 权限码
     * @return 是否有权限
     */
    boolean checkUserPermission(Long userId, String permission);

    /**
     * 检查角色权限
     *
     * @param roleKey 角色标识
     * @param url     请求路径
     * @return 是否有权限
     */
    boolean checkRolePermission(String roleKey, String url);
}
