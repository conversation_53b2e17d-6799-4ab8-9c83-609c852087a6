package com.ruoyi.gateway.filter;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.HttpStatus;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.TokenConstants;
import com.ruoyi.common.core.utils.JwtUtils;
import com.ruoyi.common.core.utils.ServletUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.gateway.config.properties.IgnoreWhiteProperties;
import com.ruoyi.gateway.service.PermissionService;
import io.jsonwebtoken.Claims;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.*;

/**
 * 网关鉴权
 *
 * <AUTHOR>
 */
@Component
public class AuthFilter implements GlobalFilter, Ordered {
    private static final Logger log = LoggerFactory.getLogger(AuthFilter.class);

    // 排除过滤的 uri 地址，nacos自行添加
    @Resource
    private IgnoreWhiteProperties ignoreWhite;

    @Resource
    private RedisService redisService;

    @Resource
    private PermissionService permissionService;

    @Qualifier("reactiveRedisRouteDefinitionTemplate")
    @Autowired
    private ReactiveRedisTemplate reactiveRedisRouteDefinitionTemplate;


    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpRequest.Builder mutate = request.mutate();
        String url = request.getURI().getPath();

        // 跳过不需要验证的路径
        if (StringUtils.matches(url, ignoreWhite.getWhites())) {
            return chain.filter(exchange);
        }

        // 获取三种认证方式的值
        String apiKey = request.getHeaders().getFirst(TokenConstants.API_KEY);
        String apiToken = request.getHeaders().getFirst(TokenConstants.API_TOKEN);
        String token = getToken(request);

        // 检查是否至少有一种认证方式
        if (StringUtils.isBlank(apiKey) && StringUtils.isBlank(apiToken) && StringUtils.isBlank(token)) {
            log.warn("[认证过滤器] 访问拒绝: 路径 {} 未找到任何认证信息", url);
            return unauthorizedResponse(exchange, "缺少认证信息");
        }

        // 按优先级依次验证：API Key > API Token > JWT Token

        // 1. 优先验证 API Key
        if (StringUtils.isNotBlank(apiKey)) {
            return handleApiKeyValidation(exchange, chain, mutate, apiKey, url);
        }

        // 2. 验证 API Token
        if (StringUtils.isNotBlank(apiToken)) {
            return handleApiTokenValidation(exchange, chain, mutate, apiToken, url);
        }

        // 3. 最后验证 JWT Token
        if (StringUtils.isNotBlank(token)) {
            return handleTokenValidation(exchange, chain, mutate, token, url);
        }

        // 所有认证方式都失败
        log.warn("[认证过滤器] 路径 {} 所有认证方式均验证失败", url);
        return unauthorizedResponse(exchange, "认证失败");
    }

    /**
     * 处理 API Key 验证
     *
     * @param exchange ServerWebExchange
     * @param chain    GatewayFilterChain
     * @param mutate   ServerHttpRequest.Builder
     * @param apiKey   API密钥
     * @param url      请求路径
     * @return Mono<Void>
     */
    private Mono<Void> handleApiKeyValidation(ServerWebExchange exchange, GatewayFilterChain chain,
                                              ServerHttpRequest.Builder mutate, String apiKey, String url) {
        // 验证 API Key 有效性
        if (!checkApiKey(apiKey)) {
            log.warn("[认证过滤器] 路径 {} API Key验证失败", url);
            return apiKeyUnauthorizedResponse(exchange, "API Key无效");
        }

        // API Key 验证成功，检查权限
        if (!permissionService.checkApiKeyPermission(apiKey, url)) {
            log.warn("[认证过滤器] 路径 {} API Key权限验证失败", url);
            return forbiddenResponse(exchange, "API Key权限不足，无法访问该接口");
        }

        log.info("[认证过滤器] 路径 {} API Key验证成功", url);
        removeHeader(mutate, SecurityConstants.FROM_SOURCE);
        addHeader(mutate, TokenConstants.API_KEY, apiKey);
        return chain.filter(exchange.mutate().request(mutate.build()).build());
    }

    /**
     * 处理 API Token 验证
     *
     * @param exchange ServerWebExchange
     * @param chain    GatewayFilterChain
     * @param mutate   ServerHttpRequest.Builder
     * @param apiToken API令牌
     * @param url      请求路径
     * @return Mono<Void>
     */
    private Mono<Void> handleApiTokenValidation(ServerWebExchange exchange, GatewayFilterChain chain,
                                                ServerHttpRequest.Builder mutate, String apiToken, String url) {
        // 验证 API Token 有效性
        if (!checkApiToken(apiToken)) {
            log.warn("[认证过滤器] 路径 {} API Token验证失败", url);
            return unauthorizedResponse(exchange, "API Token无效");
        }

        // API Token 验证成功，检查权限
        if (!checkApiTokenPermission(apiToken, url)) {
            log.warn("[认证过滤器] 路径 {} API Token权限验证失败", url);
            return forbiddenResponse(exchange, "API Token权限不足，无法访问该接口");
        }

        log.info("[认证过滤器] 路径 {} API Token验证成功", url);
        removeHeader(mutate, SecurityConstants.FROM_SOURCE);
        addHeader(mutate, TokenConstants.API_TOKEN, apiToken);
        return chain.filter(exchange.mutate().request(mutate.build()).build());
    }

    /**
     * 处理 JWT Token 验证
     *
     * @param exchange ServerWebExchange
     * @param chain    GatewayFilterChain
     * @param mutate   ServerHttpRequest.Builder
     * @param token    JWT令牌
     * @param url      请求路径
     * @return Mono<Void>
     */
    private Mono<Void> handleTokenValidation(ServerWebExchange exchange, GatewayFilterChain chain,
                                             ServerHttpRequest.Builder mutate, String token, String url) {
        // 解析 Token
        Claims claims = JwtUtils.parseToken(token);
        if (claims == null) {
            log.warn("[认证过滤器] 路径 {} JWT Token解析失败", url);
            return unauthorizedResponse(exchange, "令牌已过期或无效");
        }

        String userKey = JwtUtils.getUserKey(claims);
        // 检查登录状态
        if (!redisService.hasKey(getTokenKey(userKey))) {
            log.warn("[认证过滤器] 路径 {} JWT Token登录状态已过期, userKey: {}", url, userKey);
            return unauthorizedResponse(exchange, "登录状态已过期");
        }

        String userid = JwtUtils.getUserId(claims);
        String username = JwtUtils.getUserName(claims);
        // 检查用户信息完整性
        if (StringUtils.isEmpty(userid) || StringUtils.isEmpty(username)) {
            log.warn("[认证过滤器] 路径 {} JWT Token用户信息不完整", url);
            return unauthorizedResponse(exchange, "令牌验证失败");
        }

        // JWT Token 验证成功，检查用户权限
        if (!checkTokenPermission(token, url)) {
            log.warn("[认证过滤器] 路径 {} JWT Token权限验证失败, 用户: {}", url, username);
            return forbiddenResponse(exchange, "权限不足，无法访问该接口");
        }

        // Token 验证成功，添加用户信息到请求头
        log.info("[认证过滤器] 路径 {} JWT Token验证成功, 用户: {}", url, username);
        addHeader(mutate, SecurityConstants.USER_KEY, userKey);
        addHeader(mutate, SecurityConstants.DETAILS_USER_ID, userid);
        addHeader(mutate, SecurityConstants.DETAILS_USERNAME, username);
        removeHeader(mutate, SecurityConstants.FROM_SOURCE);

        return chain.filter(exchange.mutate().request(mutate.build()).build());
    }


    private void addHeader(ServerHttpRequest.Builder mutate, String name, Object value) {
        if (value == null) {
            return;
        }
        String valueStr = value.toString();
        String valueEncode = ServletUtils.urlEncode(valueStr);
        mutate.header(name, valueEncode);
    }

    private void removeHeader(ServerHttpRequest.Builder mutate, String name) {
        mutate.headers(httpHeaders -> httpHeaders.remove(name)).build();
    }

    private Mono<Void> unauthorizedResponse(ServerWebExchange exchange, String msg) {
        log.error("[鉴权异常处理]请求路径:{}", exchange.getRequest().getPath());
        return ServletUtils.webFluxResponseWriter(exchange.getResponse(), msg, HttpStatus.UNAUTHORIZED);
    }

    private Mono<Void> apiKeyUnauthorizedResponse(ServerWebExchange exchange, String msg) {
        // 在日志中记录失败原因
        log.error("[API Key认证失败] 路径: {}, 原因: {}", exchange.getRequest().getPath(), msg);
        return ServletUtils.webFluxResponseWriter(exchange.getResponse(), msg, HttpStatus.API_KEY_UNAUTHORIZED);
    }

    private Mono<Void> forbiddenResponse(ServerWebExchange exchange, String msg) {
        log.error("[权限验证失败] 路径: {}, 原因: {}", exchange.getRequest().getPath(), msg);
        return ServletUtils.webFluxResponseWriter(exchange.getResponse(), msg, HttpStatus.FORBIDDEN);
    }

    /**
     * 获取缓存key
     */
    private String getTokenKey(String token) {
        return CacheConstants.LOGIN_TOKEN_KEY + token;
    }

    /**
     * 获取请求token
     */
    private String getToken(ServerHttpRequest request) {
        String token = request.getHeaders().getFirst(TokenConstants.AUTHENTICATION);
        // 如果前端设置了令牌前缀，则裁剪掉前缀
        if (StringUtils.isNotEmpty(token) && token.startsWith(TokenConstants.PREFIX)) {
            token = token.replaceFirst(TokenConstants.PREFIX, StringUtils.EMPTY);
        }
        return token;
    }

    @Override
    public int getOrder() {
        return -200;
    }

    /**
     * 检查 API Key 的有效性
     *
     * @param apiKey API密钥
     * @return 如果有效返回 true，否则 false
     */
    private boolean checkApiKey(String apiKey) {
        if (StringUtils.isBlank(apiKey)) {
            return false;
        }
        String cacheKey = TokenConstants.SYSTEM_ACCESS_API_KEY_SECRET + apiKey;
        boolean isValid = redisService.hasKey(cacheKey);
        log.debug("[认证过滤器] API Key验证 - 缓存键: {}, 结果: {}", cacheKey, isValid);
        return isValid;
    }

    /**
     * 检查 API Token 的有效性
     *
     * @param apiToken API令牌
     * @return 如果有效返回 true，否则 false
     */
    private boolean checkApiToken(String apiToken) {
        if (StringUtils.isBlank(apiToken)) {
            return false;
        }
        String cacheKey = TokenConstants.SYSTEM_ACCESS_KEY_SECRET_TOKEN + apiToken;
        boolean isValid = redisService.hasKey(cacheKey);
        log.debug("[认证过滤器] API Token验证 - 缓存键: {}, 结果: {}", cacheKey, isValid);
        return isValid;
    }

    /**
     * 检查 API Key 的权限
     *
     * @param apiKey API密钥
     * @param url    请求路径
     * @return 如果有权限返回 true，否则 false
     */
    private boolean checkApiKeyPermission(String apiKey, String url) {
        try {
            String cacheKey = TokenConstants.SYSTEM_ACCESS_API_KEY_SECRET + apiKey;
            Object cacheObject = redisService.getCacheObject(cacheKey);
            if (cacheObject == null) {
                log.debug("[权限验证] API Key权限缓存不存在: {}", cacheKey);
                return false;
            }

            String jsonStr = JSON.toJSONString(cacheObject);
            if (StringUtils.isBlank(jsonStr)) {
                log.debug("[权限验证] API Key权限数据为空: {}", cacheKey);
                return false;
            }

            JSONObject jsonObj = JSON.parseObject(jsonStr);
            List<String> permissions = Optional.ofNullable(jsonObj.getList("permissions", String.class))
                    .orElse(Collections.emptyList());

            // 检查是否有匹配的权限路径
            boolean hasPermission = StringUtils.matches(url, permissions);
            log.debug("[权限验证] API Key权限检查 - 路径: {}, 权限列表: {}, 结果: {}", url, permissions, hasPermission);
            return hasPermission;

        } catch (Exception e) {
            log.error("[权限验证] API Key权限检查异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查 API Token 的权限
     *
     * @param apiToken API令牌
     * @param url      请求路径
     * @return 如果有权限返回 true，否则 false
     */
    private boolean checkApiTokenPermission(String apiToken, String url) {
        try {
            String cacheKey = TokenConstants.SYSTEM_ACCESS_KEY_SECRET_TOKEN + apiToken;
            Object cacheObject = redisService.getCacheObject(cacheKey);
            if (cacheObject == null) {
                log.debug("[权限验证] API Token权限缓存不存在: {}", cacheKey);
                return false;
            }

            String jsonStr = JSON.toJSONString(cacheObject);
            if (StringUtils.isBlank(jsonStr)) {
                log.debug("[权限验证] API Token权限数据为空: {}", cacheKey);
                return false;
            }

            JSONObject jsonObj = JSON.parseObject(jsonStr);
            List<String> tokenPermissions = Optional.ofNullable(jsonObj.getList("tokenPermissions", String.class))
                    .orElse(Collections.emptyList());

            // 检查是否有匹配的权限路径
            boolean hasPermission = StringUtils.matches(url, tokenPermissions);
            log.debug("[权限验证] API Token权限检查 - 路径: {}, 权限列表: {}, 结果: {}", url, tokenPermissions, hasPermission);
            return hasPermission;

        } catch (Exception e) {
            log.error("[权限验证] API Token权限检查异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查 JWT Token 的权限
     *
     * @param token JWT令牌
     * @param url   请求路径
     * @return 如果有权限返回 true，否则 false
     */
    private boolean checkTokenPermission(String token, String url) {
        try {
            // 解析 Token 获取用户信息
            Claims claims = JwtUtils.parseToken(token);
            if (claims == null) {
                log.debug("[权限验证] JWT Token解析失败");
                return false;
            }

            String userKey = JwtUtils.getUserKey(claims);
            String tokenCacheKey = getTokenKey(userKey);

            // 从缓存中获取用户登录信息
            Object loginUserObj = redisService.getCacheObject(tokenCacheKey);
            if (loginUserObj == null) {
                log.debug("[权限验证] 用户登录信息缓存不存在: {}", tokenCacheKey);
                return false;
            }

            // 这里可以根据实际需求扩展权限验证逻辑
            // 例如：检查用户角色权限、菜单权限等
            // 当前实现：JWT Token通过基础验证后，默认允许访问
            // 如果需要更细粒度的权限控制，可以在这里添加具体的权限验证逻辑

            log.debug("[权限验证] JWT Token权限检查通过 - 路径: {}, 用户: {}", url, userKey);
            return true;

        } catch (Exception e) {
            log.error("[权限验证] JWT Token权限检查异常: {}", e.getMessage(), e);
            return false;
        }
    }
}
