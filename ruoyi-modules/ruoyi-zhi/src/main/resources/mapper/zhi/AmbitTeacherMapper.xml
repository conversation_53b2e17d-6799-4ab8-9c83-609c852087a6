<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhi.mapper.AmbitTeacherMapper">
    
    <resultMap type="com.ruoyi.zhi.domain.AmbitTeacher" id="AmbitTeacherResult">
        <result property="id"    column="id"    />
        <result property="majorId"    column="major_id"    />
        <result property="disciplineId"    column="discipline_id"    />
        <result property="name"    column="name"    />
        <result property="sex"    column="sex"    />
        <result property="title"    column="title"    />
        <result property="education"    column="education"    />
        <result property="researchDirection"    column="research_direction"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="courseName"    column="course_name"    />
        <result property="researchType"    column="research_type"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="colleId"    column="colle_id"    />
        <result property="colleName"    column="colle_name"    />
        <result property="univerId"    column="univer_id"    />
        <result property="univerName"    column="univer_name"    />
        <result property="isPersonCharge"    column="is_person_charge"    />
        <result property="majorName"    column="major_name"    />
        <result property="collegeId"    column="college_id"    />
        <result property="collegeName"    column="college_name"    />
        <result property="isAllianceCourse"    column="is_alliance_course"    />
        <result property="coursesNumber"      column="courses_number"    />
        <result property="textbooksNumber"    column="textbooks_number"    />
        <result property="knowledgeNumber"    column="knowledge_number"    />
        <collection property="teacherFindingsList" resultMap="TeacherFindingsResult">
        </collection>
    </resultMap>
    <resultMap type="com.ruoyi.zhi.domain.TeacherFindings" id="TeacherFindingsResult">
        <result property="researchFindings"    column="research_findings"    />
    </resultMap>
    <sql id="selectAmbitTeacherVo">
        select id, major_id, name, sex,discipline_id, title, education, research_direction, create_by, create_time, update_by, update_time,course_name,research_type,teacher_id,colle_id,colle_name,univer_id,univer_name,is_person_charge from s_ambit_teacher
    </sql>

<!--    <select id="selectAmbitTeacherList" parameterType="com.ruoyi.zhi.domain.AmbitTeacher" resultMap="AmbitTeacherResult">-->
<!--        select t1.id, t1.major_id, t1.name, t1.sex,t1.discipline_id, t1.title, t1.education, t1.research_direction,-->
<!--        t1.create_by, t1.create_time, t1.update_by, t1.update_time,-->
<!--        t2.research_findings-->
<!--        from s_ambit_teacher t1 left join s_teacher_findings t2 on t1.id = t2.teacher_id-->
<!--        <where>  -->
<!--            <if test="majorId != null "> and t1.major_id = #{majorId}</if>-->
<!--            <if test="disciplineId != null "> and t1.discipline_id = #{disciplineId}</if>-->
<!--            <if test="name != null  and name != ''"> and t1.name like concat('%', #{name}, '%')</if>-->
<!--            <if test="sex != null  and sex != ''"> and t1.sex = #{sex}</if>-->
<!--            <if test="title != null  and title != ''"> and t1.title = #{title}</if>-->
<!--            <if test="education != null  and education != ''"> and t1.education = #{education}</if>-->
<!--            <if test="researchDirection != null  and researchDirection != ''"> and t1.research_direction = #{researchDirection}</if>-->
<!--        </where>-->
<!--    </select>-->

        <select id="selectAmbitTeacherList" parameterType="com.ruoyi.zhi.domain.AmbitTeacher" resultMap="AmbitTeacherResult">
            <include refid="selectAmbitTeacherVo"/>
            <where>
                <if test="majorId != null "> and major_id = #{majorId}</if>
                <if test="disciplineId != null "> and discipline_id = #{disciplineId}</if>
                <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
                <if test="sex != null  and sex != ''"> and sex = #{sex}</if>
                <if test="title != null  and title != ''"> and title = #{title}</if>
                <if test="education != null  and education != ''"> and education = #{education}</if>
                <if test="researchDirection != null  and researchDirection != ''"> and t1.research_direction = #{researchDirection}</if>
                <if test="courseName != null  and courseName != ''"> and course_name = #{courseName}</if>
                <if test="researchType != null  and researchType != ''"> and research_type = #{researchType}</if>
                <if test="teacherId != null  "> and teacher_id = #{teacherId}</if>
                <if test="colleId != null  "> and colle_id = #{colleId}</if>
                <if test="colleName != null  "> and colle_name = #{colleName}</if>
                <if test="univerId != null  "> and univer_id = #{univerId}</if>
                <if test="univerName != null  "> and univer_name = #{univerName}</if>
                <if test="isPersonCharge != null  "> and is_person_charge = #{isPersonCharge}</if>
            </where>
        </select>

    <select id="selectAmbitTeacherById" parameterType="Long" resultMap="AmbitTeacherResult">
        <include refid="selectAmbitTeacherVo"/>
        where id = #{id}
    </select>

    <insert id="insertAmbitTeacher" parameterType="com.ruoyi.zhi.domain.AmbitTeacher" useGeneratedKeys="true" keyProperty="id">
        insert into s_ambit_teacher
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="majorId != null">major_id,</if>
            <if test="disciplineId != null">discipline_id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="sex != null">sex,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="education != null and education != ''">education,</if>
            <if test="researchDirection != null and researchDirection != ''">research_direction,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="courseName != null">course_name,</if>
            <if test="researchType != null">research_type,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="colleId != null">colle_id,</if>
            <if test="colleName != null">colle_name,</if>
            <if test="univerId != null">univer_id,</if>
            <if test="univerName != null">univer_name,</if>
            <if test="isPersonCharge != null">is_person_charge,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="majorId != null">#{majorId},</if>
            <if test="disciplineId != null">#{disciplineId},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="sex != null">#{sex},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="education != null and education != ''">#{education},</if>
            <if test="researchDirection != null and researchDirection != ''">#{researchDirection},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="courseName != null">#{courseName},</if>
            <if test="researchType != null">#{researchType},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="colleId != null">#{colleId},</if>
            <if test="colleName != null">#{colleName},</if>
            <if test="univerId != null">#{univerId},</if>
            <if test="univerName != null">#{univerName},</if>
            <if test="isPersonCharge != null">#{isPersonCharge},</if>
         </trim>
    </insert>

    <update id="updateAmbitTeacher" parameterType="com.ruoyi.zhi.domain.AmbitTeacher">
        update s_ambit_teacher
        <trim prefix="SET" suffixOverrides=",">
            <if test="majorId != null">major_id = #{majorId},</if>
            <if test="disciplineId != null">discipline_id = #{disciplineId},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="education != null and education != ''">education = #{education},</if>
            <if test="researchDirection != null and researchDirection != ''">research_direction = #{researchDirection},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="courseName != null">course_name = #{courseName},</if>
            <if test="researchType != null">research_type = #{researchType},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="colleId != null">colle_id = #{colleId},</if>
            <if test="colleName != null">colle_name = #{colleName},</if>
            <if test="univerId != null">univer_id = #{univerId},</if>
            <if test="univerName != null">univer_name = #{univerName},</if>
            <if test="isPersonCharge != null">is_person_charge = #{isPersonCharge},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAmbitTeacherById" parameterType="Long">
        delete from s_ambit_teacher where id = #{id}
    </delete>

    <delete id="deleteAmbitTeacherByIds" parameterType="String">
        delete from s_ambit_teacher where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectAmbitTeacherByMajorId" parameterType="Long" resultMap="AmbitTeacherResult">
        select t1.id, t1.major_id, t1.name, t1.sex,t1.discipline_id, t1.title, t1.education, t1.research_direction,
                t1.create_by, t1.create_time, t1.update_by, t1.update_time,t1.teacher_id,
                t2.research_findings
                from s_ambit_teacher t1 left join s_teacher_findings t2 on t1.id = t2.teacher_id
        where t1.major_id = #{majorId}
    </select>

    <select id="selectAmbitTeacherByCourseName" parameterType="String" resultMap="AmbitTeacherResult">
        select t1.id, t1.major_id, t1.name, t1.sex,t1.discipline_id, t1.title, t1.education, t1.research_direction,
                t1.create_by, t1.create_time, t1.update_by, t1.update_time,t1.teacher_id,
                t2.research_findings
                from s_ambit_teacher t1 left join s_teacher_findings t2 on t1.id = t2.teacher_id
        where t1.course_name = #{courseName}
    </select>

    <select id="selectAmbitTeacherByTeacherId" parameterType="String" resultMap="AmbitTeacherResult">
        <include refid="selectAmbitTeacherVo"/>
        where teacher_id = #{jobId}
    </select>

    <select id="selectKnowledgeList" parameterType="com.ruoyi.zhi.domain.AmbitTeacher" resultMap="AmbitTeacherResult">
        select distinct t1.id, t1.major_id,  t1.course_name,t1.colle_id as college_id,t4.colle_name as college_name,t1.univer_id,t1.univer_name,t2.major_name,t3.is_alliance_course,t1.is_person_charge,t1.teacher_id,t1.discipline_id from s_ambit_teacher t1
        left join s_major_info t2 on t1.major_id = t2.id
        left join s_knowledge_base_file t3 on t1.major_id = t3.major_id and t1.course_name = t3.course_name
        left join s_college_info t4 on t1.colle_id = t4.id
        <where>
            <if test="majorId != null "> and t1.major_id = #{majorId}</if>
            <if test="courseName != null  and courseName != ''"> and t1.course_name like concat('%', #{courseName}, '%')</if>
            <if test="teacherId != null  "> and t1.teacher_id = #{teacherId}</if>
            <if test="colleId != null  "> and t1.colle_id = #{colleId}</if>
            <if test="colleName != null  "> and t1.colle_name = #{colleName}</if>
            <if test="univerId != null  "> and t1.univer_id = #{univerId}</if>
            <if test="univerName != null  "> and t1.univer_name = #{univerName}</if>
            <if test="majorName != null  "> and t2.major_name like concat('%', #{majorName}, '%')</if>
            <if test="disciplineId != null">and t1.discipline_id = #{disciplineId}</if>
        </where>
        order by t1.colle_id, t1.major_id
    </select>

    <select id="selectKnowledgeNoList" parameterType="com.ruoyi.zhi.domain.AmbitTeacher" resultMap="AmbitTeacherResult">
        select distinct t1.id, t1.major_id, t1.major_name,t1.college_id ,t1.college_name,t2.course_name,t2.is_alliance_course,t1.discipline_id,t1.univer_id,t3.is_person_charge from s_ambit_major t1
        left join s_knowledge_base_file t2 on t1.major_id = t2.major_id
        left join s_ambit_teacher t3 on t1.major_id = t3.major_id and t2.course_name = t3.course_name and t3.teacher_id = #{teacherId}
        <where>
            <if test="majorId != null "> and t1.major_id = #{majorId}</if>
            <if test="courseName != null  and courseName != ''"> and t1.course_name = #{courseName}</if>
            <if test="colleId != null  "> and t1.colle_id = #{colleId}</if>
            <if test="colleName != null  "> and t1.colle_name = #{colleName}</if>
            <if test="univerId != null  "> and t1.univer_id = #{univerId}</if>
            <if test="univerName != null  "> and t1.univer_name = #{univerName}</if>
            <if test="majorName != null  "> and t1.major_name like concat('%', #{majorName}, '%')</if>
            <if test="disciplineId != null">and t1.discipline_id = #{disciplineId}</if>
            and t2.course_name != ''
        </where>
        order by t1.college_id, t1.major_id
    </select>

    <select id="countNumber" parameterType="String"  resultMap="AmbitTeacherResult">
        SELECT
            COUNT(DISTINCT t1.course_name) AS courses_number,
            COUNT(DISTINCT t2.id) AS textbooks_number,
            COUNT(DISTINCT t3.keyword) AS knowledge_number
        FROM
            s_ambit_teacher t1
        JOIN
            s_knowledge_base_file t2 ON t1.major_id = t2.major_id AND t1.course_name = t2.course_name
        JOIN
            s_textbook_keyword_data t3 ON t2.id = t3.textbook_id
        WHERE
            t1.teacher_id = #{jobId} AND t1.is_person_charge = 1
		    and t2.file_name != '';
    </select>
</mapper>