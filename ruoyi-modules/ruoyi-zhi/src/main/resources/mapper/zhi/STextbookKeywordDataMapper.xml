<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhi.mapper.STextbookKeywordDataMapper">
    
    <resultMap type="com.ruoyi.zhi.domain.TextbookKeywordData" id="STextbookKeywordDataResult">
        <result property="id"    column="id"    />
        <result property="textbookId"    column="textbook_id"    />
        <result property="knowledgeCategory"    column="knowledge_category"    />
        <result property="category"    column="category"    />
        <result property="keyword"    column="keyword"    />
        <result property="count"    column="count"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="com.ruoyi.zhi.domain.TextbookData" id="STextbookDataResult">
        <result property="id"    column="id"    />
        <result property="textbookId"    column="textbook_id"    />
        <result property="textbook"    column="textbook"    />
        <result property="academicDiscipline"    column="academic_discipline"    />
        <result property="colleName"    column="colle_name"    />
        <result property="educationalLevel"    column="educational_level"    />
        <result property="majorName"    column="major_name"    />
        <result property="publishingHouse"    column="publishing_house"    />
        <result property="author"    column="author"    />
        <result property="knowledgeCategory"    column="knowledge_category"    />
        <result property="category"    column="category"    />
        <result property="keyword"    column="keyword"    />
        <result property="count"    column="count"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSTextbookKeywordDataVo">
        select id, textbook_id, knowledge_category, category, keyword, create_by, create_time, update_by, update_time from s_textbook_keyword_data
    </sql>

    <select id="selectSTextbookKeywordDataList" parameterType="com.ruoyi.zhi.domain.TextbookKeywordData" resultMap="STextbookKeywordDataResult">
        <include refid="selectSTextbookKeywordDataVo"/>
        <where>  
            <if test="textbookId != null "> and textbook_id = #{textbookId}</if>
            <if test="knowledgeCategory != null  and knowledgeCategory != ''"> and knowledge_category = #{knowledgeCategory}</if>
            <if test="category != null  and category != ''"> and category = #{category}</if>
            <if test="keyword != null  and keyword != ''"> and keyword = #{keyword}</if>
        </where>
    </select>
    
    <select id="selectSTextbookKeywordDataById" parameterType="Long" resultMap="STextbookKeywordDataResult">
        <include refid="selectSTextbookKeywordDataVo"/>
        where id = #{id}
    </select>
    <select id="getCategory" parameterType="com.ruoyi.zhi.domain.TextbookKeywordData" resultType="java.lang.String">
        SELECT category FROM s_textbook_keyword_data
         WHERE knowledge_category = #{knowledgeCategory} GROUP BY category
    </select>

    <insert id="insertSTextbookKeywordData" parameterType="com.ruoyi.zhi.domain.TextbookKeywordData">
        insert into s_textbook_keyword_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="textbookId != null">textbook_id,</if>
            <if test="knowledgeCategory != null">knowledge_category,</if>
            <if test="category != null">category,</if>
            <if test="keyword != null">keyword,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="textbookId != null">#{textbookId},</if>
            <if test="knowledgeCategory != null">#{knowledgeCategory},</if>
            <if test="category != null">#{category},</if>
            <if test="keyword != null">#{keyword},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>
    <insert id="insertTextbookKeywordDataList">
        insert into s_textbook_keyword_data (textbook_id,knowledge_category,category,keyword,create_by ,create_time) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.textbookId},#{item.knowledgeCategory},#{item.category},#{item.keyword},#{item.createBy},#{item.createTime})
        </foreach>
    </insert>

    <insert id="insertTextbookKeywordDatas">
        insert into s_textbook_keyword_data (textbook_id,knowledge_category,category,keyword) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.textbookId},#{item.knowledgeCategory},#{item.category},#{item.keyword})
        </foreach>
    </insert>

    <update id="updateSTextbookKeywordData" parameterType="com.ruoyi.zhi.domain.TextbookKeywordData">
        update s_textbook_keyword_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="textbookId != null">textbook_id = #{textbookId},</if>
            <if test="knowledgeCategory != null">knowledge_category = #{knowledgeCategory},</if>
            <if test="category != null">category = #{category},</if>
            <if test="keyword != null">keyword = #{keyword},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSTextbookKeywordDataById" parameterType="Long">
        delete from s_textbook_keyword_data where id = #{id}
    </delete>

    <delete id="deleteSTextbookKeywordDataByIds" parameterType="String">
        delete from s_textbook_keyword_data where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteSTextbookKeywordDataByTextbookId" parameterType="Long">
        delete from s_textbook_keyword_data where textbook_id = #{id}
    </delete>
</mapper>