<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhi.mapper.CleaningTaskMapper">
    <resultMap type="com.ruoyi.zhi.domain.CleaningTask" id="CleaningTaskResult">
        <result property="id" column="id"/>
        <result property="etlTaskId" column="etl_task_id"/>
        <result property="etlTaskName" column="etl_task_name"/>
        <result property="userId" column="user_id"/>
        <result property="sourceDatasetStrId" column="source_dataset_str_id"/>
        <result property="sourceDatasetStrName" column="source_dataset_str_name"/>
        <result property="destDatasetStrId" column="dest_dataset_str_id"/>
        <result property="destDatasetStrName" column="dest_dataset_str_name"/>
        <result property="taskId" column="task_id"/>
        <result property="entityType" column="entity_type"/>
        <result property="removeInvisibleCharacter" column="remove_invisible_character"/>
        <result property="replaceUniformWhitespace" column="replace_uniform_whitespace"/>
        <result property="removeNonMeaningCharacters" column="remove_non_meaning_characters"/>
        <result property="replaceTraditionalChineseToSimplified" column="replace_traditional_chinese_to_simplified"/>
        <result property="removeWebIdentifiers" column="remove_web_identifiers"/>
        <result property="removeEmoji" column="remove_emoji"/>
        <result property="numberWords" column="number_words"/>
        <result property="numberMinRange" column="number_min_range"/>
        <result property="numberMaxRange" column="number_max_range"/>
        <result property="characterRepetitionRemoval" column="character_repetition_removal"/>
        <result property="characterRepetitionNumber" column="character_repetition_number"/>
        <result property="wordRepetitionRemoval" column="word_repetition_removal"/>
        <result property="wordRepetitionNumber" column="word_repetition_number"/>
        <result property="specialCharacters" column="special_characters"/>
        <result property="specialNumber" column="special_number"/>
        <result property="flaggedWords" column="flagged_words"/>
        <result property="flaggedNumber" column="flagged_number"/>
        <result property="langId" column="lang_id"/>
        <result property="langNumber" column="lang_number"/>
        <result property="perplexity" column="perplexity"/>
        <result property="perplexityNumber" column="perplexity_number"/>
        <result property="simhashOperator" column="simhash_operator"/>
        <result property="replaceEmails" column="replace_emails"/>
        <result property="replaceIp" column="replace_ip"/>
        <result property="replaceIdentifier" column="replace_identifier"/>
        <result property="simhashOperatorNumber" column="simhash_operator_number"/>
        <result property="processStatus" column="process_status"/>
        <result property="status" column="status"/>
        <result property="finishTime" column="finish_time"/>
        <result property="creatorName" column="creator_name"/>
        <result property="sourceDatasetName" column="source_dataset_name"/>
        <result property="destDatasetName" column="dest_dataset_name"/>
        <result property="etlResult" column="etl_result"/>
        <result property="remainingEntity" column="remaining_entity"/>
        <result property="exceptionResult" column="exception_result"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="logPath" column="log_path"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="menuRouting"    column="menu_routing"    />
    </resultMap>
    <sql id="selectCleaningTaskVo"> select id, etl_task_id, etl_task_name, user_id, source_dataset_str_id, source_dataset_str_name, dest_dataset_str_id, dest_dataset_str_name, task_id, entity_type, remove_invisible_character, replace_uniform_whitespace, remove_non_meaning_characters, replace_traditional_chinese_to_simplified, remove_web_identifiers, remove_emoji, number_words, number_min_range, number_max_range, character_repetition_removal, character_repetition_number, word_repetition_removal, word_repetition_number, special_characters, special_number, flagged_words, flagged_number, lang_id, lang_number, perplexity, perplexity_number, simhash_operator, replace_emails, replace_ip, replace_identifier, simhash_operator_number, process_status, status, finish_time, creator_name, source_dataset_name, dest_dataset_name, etl_result, remaining_entity, exception_result, start_time, end_time, modify_time, log_path, create_by, create_time, update_by, update_time,menu_routing from s_cleaning_task </sql>
    <select id="selectCleaningTaskList" parameterType="com.ruoyi.zhi.domain.CleaningTask" resultMap="CleaningTaskResult">
        <include refid="selectCleaningTaskVo"/>
        <where>
            <if test="etlTaskId != null and etlTaskId != ''"> and etl_task_id = #{etlTaskId}</if>
            <if test="etlTaskName != null and etlTaskName != ''"> and etl_task_name like concat('%', #{etlTaskName}, '%')</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="sourceDatasetStrId != null "> and source_dataset_str_id = #{sourceDatasetStrId}</if>
            <if test="sourceDatasetStrName != null and sourceDatasetStrName != ''"> and source_dataset_str_name like concat('%', #{sourceDatasetStrName}, '%')</if>
            <if test="destDatasetStrId != null "> and dest_dataset_str_id = #{destDatasetStrId}</if>
            <if test="destDatasetStrName != null and destDatasetStrName != ''"> and dest_dataset_str_name like concat('%', #{destDatasetStrName}, '%')</if>
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="entityType != null "> and entity_type = #{entityType}</if>
            <if test="removeInvisibleCharacter != null "> and remove_invisible_character = #{removeInvisibleCharacter}</if>
            <if test="replaceUniformWhitespace != null "> and replace_uniform_whitespace = #{replaceUniformWhitespace}</if>
            <if test="removeNonMeaningCharacters != null "> and remove_non_meaning_characters = #{removeNonMeaningCharacters}</if>
            <if test="replaceTraditionalChineseToSimplified != null "> and replace_traditional_chinese_to_simplified = #{replaceTraditionalChineseToSimplified}</if>
            <if test="removeWebIdentifiers != null "> and remove_web_identifiers = #{removeWebIdentifiers}</if>
            <if test="removeEmoji != null "> and remove_emoji = #{removeEmoji}</if>
            <if test="numberWords != null "> and number_words = #{numberWords}</if>
            <if test="numberMinRange != null "> and number_min_range = #{numberMinRange}</if>
            <if test="numberMaxRange != null "> and number_max_range = #{numberMaxRange}</if>
            <if test="characterRepetitionRemoval != null "> and character_repetition_removal = #{characterRepetitionRemoval}</if>
            <if test="characterRepetitionNumber != null and characterRepetitionNumber != ''"> and character_repetition_number = #{characterRepetitionNumber}</if>
            <if test="wordRepetitionRemoval != null and wordRepetitionRemoval != ''"> and word_repetition_removal = #{wordRepetitionRemoval}</if>
            <if test="wordRepetitionNumber != null and wordRepetitionNumber != ''"> and word_repetition_number = #{wordRepetitionNumber}</if>
            <if test="specialCharacters != null "> and special_characters = #{specialCharacters}</if>
            <if test="specialNumber != null and specialNumber != ''"> and special_number = #{specialNumber}</if>
            <if test="flaggedWords != null "> and flagged_words = #{flaggedWords}</if>
            <if test="flaggedNumber != null and flaggedNumber != ''"> and flagged_number = #{flaggedNumber}</if>
            <if test="langId != null "> and lang_id = #{langId}</if>
            <if test="langNumber != null and langNumber != ''"> and lang_number = #{langNumber}</if>
            <if test="perplexity != null "> and perplexity = #{perplexity}</if>
            <if test="perplexityNumber != null and perplexityNumber != ''"> and perplexity_number = #{perplexityNumber}</if>
            <if test="simhashOperator != null "> and simhash_operator = #{simhashOperator}</if>
            <if test="replaceEmails != null "> and replace_emails = #{replaceEmails}</if>
            <if test="replaceIp != null "> and replace_ip = #{replaceIp}</if>
            <if test="replaceIdentifier != null "> and replace_identifier = #{replaceIdentifier}</if>
            <if test="simhashOperatorNumber != null and simhashOperatorNumber != ''"> and simhash_operator_number = #{simhashOperatorNumber}</if>
            <if test="processStatus != null "> and process_status = #{processStatus}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="finishTime != null and finishTime != ''"> and finish_time = #{finishTime}</if>
            <if test="creatorName != null and creatorName != ''"> and creator_name like concat('%', #{creatorName}, '%')</if>
            <if test="sourceDatasetName != null and sourceDatasetName != ''"> and source_dataset_name like concat('%', #{sourceDatasetName}, '%')</if>
            <if test="destDatasetName != null and destDatasetName != ''"> and dest_dataset_name like concat('%', #{destDatasetName}, '%')</if>
            <if test="etlResult != null and etlResult != ''"> and etl_result = #{etlResult}</if>
            <if test="remainingEntity != null "> and remaining_entity = #{remainingEntity}</if>
            <if test="exceptionResult != null and exceptionResult != ''"> and exception_result = #{exceptionResult}</if>
            <if test="startTime != null and startTime != ''"> and start_time = #{startTime}</if>
            <if test="endTime != null and endTime != ''"> and end_time = #{endTime}</if>
            <if test="modifyTime != null and modifyTime != ''"> and modify_time = #{modifyTime}</if>
            <if test="logPath != null and logPath != ''"> and log_path = #{logPath}</if>
            <if test="menuRouting != null and menuRouting != ''"> and menu_routing = #{menuRouting}</if>
            <if test="createBy != null "> and create_by = #{createBy}</if>
        </where>
        order by create_time Desc
    </select>
    <select id="selectCleaningTaskById" parameterType="Long" resultMap="CleaningTaskResult">
        <include refid="selectCleaningTaskVo"/>
        where id = #{id}
    </select>
    <insert id="insertCleaningTask" parameterType="com.ruoyi.zhi.domain.CleaningTask" useGeneratedKeys="true" keyProperty="id">
        insert into s_cleaning_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="etlTaskId != null">etl_task_id,</if>
            <if test="etlTaskName != null">etl_task_name,</if>
            <if test="userId != null">user_id,</if>
            <if test="sourceDatasetStrId != null">source_dataset_str_id,</if>
            <if test="sourceDatasetStrName != null">source_dataset_str_name,</if>
            <if test="destDatasetStrId != null">dest_dataset_str_id,</if>
            <if test="destDatasetStrName != null">dest_dataset_str_name,</if>
            <if test="taskId != null">task_id,</if>
            <if test="entityType != null">entity_type,</if>
            <if test="removeInvisibleCharacter != null">remove_invisible_character,</if>
            <if test="replaceUniformWhitespace != null">replace_uniform_whitespace,</if>
            <if test="removeNonMeaningCharacters != null">remove_non_meaning_characters,</if>
            <if test="replaceTraditionalChineseToSimplified != null">replace_traditional_chinese_to_simplified,</if>
            <if test="removeWebIdentifiers != null">remove_web_identifiers,</if>
            <if test="removeEmoji != null">remove_emoji,</if>
            <if test="numberWords != null">number_words,</if>
            <if test="numberMinRange != null">number_min_range,</if>
            <if test="numberMaxRange != null">number_max_range,</if>
            <if test="characterRepetitionRemoval != null">character_repetition_removal,</if>
            <if test="characterRepetitionNumber != null">character_repetition_number,</if>
            <if test="wordRepetitionRemoval != null">word_repetition_removal,</if>
            <if test="wordRepetitionNumber != null">word_repetition_number,</if>
            <if test="specialCharacters != null">special_characters,</if>
            <if test="specialNumber != null">special_number,</if>
            <if test="flaggedWords != null">flagged_words,</if>
            <if test="flaggedNumber != null">flagged_number,</if>
            <if test="langId != null">lang_id,</if>
            <if test="langNumber != null">lang_number,</if>
            <if test="perplexity != null">perplexity,</if>
            <if test="perplexityNumber != null">perplexity_number,</if>
            <if test="simhashOperator != null">simhash_operator,</if>
            <if test="replaceEmails != null">replace_emails,</if>
            <if test="replaceIp != null">replace_ip,</if>
            <if test="replaceIdentifier != null">replace_identifier,</if>
            <if test="simhashOperatorNumber != null">simhash_operator_number,</if>
            <if test="processStatus != null">process_status,</if>
            <if test="status != null">status,</if>
            <if test="finishTime != null">finish_time,</if>
            <if test="creatorName != null">creator_name,</if>
            <if test="sourceDatasetName != null">source_dataset_name,</if>
            <if test="destDatasetName != null">dest_dataset_name,</if>
            <if test="etlResult != null">etl_result,</if>
            <if test="remainingEntity != null">remaining_entity,</if>
            <if test="exceptionResult != null">exception_result,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="logPath != null">log_path,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="menuRouting != null">menu_routing,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="etlTaskId != null">#{etlTaskId},</if>
            <if test="etlTaskName != null">#{etlTaskName},</if>
            <if test="userId != null">#{userId},</if>
            <if test="sourceDatasetStrId != null">#{sourceDatasetStrId},</if>
            <if test="sourceDatasetStrName != null">#{sourceDatasetStrName},</if>
            <if test="destDatasetStrId != null">#{destDatasetStrId},</if>
            <if test="destDatasetStrName != null">#{destDatasetStrName},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="entityType != null">#{entityType},</if>
            <if test="removeInvisibleCharacter != null">#{removeInvisibleCharacter},</if>
            <if test="replaceUniformWhitespace != null">#{replaceUniformWhitespace},</if>
            <if test="removeNonMeaningCharacters != null">#{removeNonMeaningCharacters},</if>
            <if test="replaceTraditionalChineseToSimplified != null">#{replaceTraditionalChineseToSimplified},</if>
            <if test="removeWebIdentifiers != null">#{removeWebIdentifiers},</if>
            <if test="removeEmoji != null">#{removeEmoji},</if>
            <if test="numberWords != null">#{numberWords},</if>
            <if test="numberMinRange != null">#{numberMinRange},</if>
            <if test="numberMaxRange != null">#{numberMaxRange},</if>
            <if test="characterRepetitionRemoval != null">#{characterRepetitionRemoval},</if>
            <if test="characterRepetitionNumber != null">#{characterRepetitionNumber},</if>
            <if test="wordRepetitionRemoval != null">#{wordRepetitionRemoval},</if>
            <if test="wordRepetitionNumber != null">#{wordRepetitionNumber},</if>
            <if test="specialCharacters != null">#{specialCharacters},</if>
            <if test="specialNumber != null">#{specialNumber},</if>
            <if test="flaggedWords != null">#{flaggedWords},</if>
            <if test="flaggedNumber != null">#{flaggedNumber},</if>
            <if test="langId != null">#{langId},</if>
            <if test="langNumber != null">#{langNumber},</if>
            <if test="perplexity != null">#{perplexity},</if>
            <if test="perplexityNumber != null">#{perplexityNumber},</if>
            <if test="simhashOperator != null">#{simhashOperator},</if>
            <if test="replaceEmails != null">#{replaceEmails},</if>
            <if test="replaceIp != null">#{replaceIp},</if>
            <if test="replaceIdentifier != null">#{replaceIdentifier},</if>
            <if test="simhashOperatorNumber != null">#{simhashOperatorNumber},</if>
            <if test="processStatus != null">#{processStatus},</if>
            <if test="status != null">#{status},</if>
            <if test="finishTime != null">#{finishTime},</if>
            <if test="creatorName != null">#{creatorName},</if>
            <if test="sourceDatasetName != null">#{sourceDatasetName},</if>
            <if test="destDatasetName != null">#{destDatasetName},</if>
            <if test="etlResult != null">#{etlResult},</if>
            <if test="remainingEntity != null">#{remainingEntity},</if>
            <if test="exceptionResult != null">#{exceptionResult},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="logPath != null">#{logPath},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="menuRouting != null">#{menuRouting},</if>
        </trim>
    </insert>
    <update id="updateCleaningTask" parameterType="com.ruoyi.zhi.domain.CleaningTask">
        update s_cleaning_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="etlTaskId != null">etl_task_id = #{etlTaskId},</if>
            <if test="etlTaskName != null">etl_task_name = #{etlTaskName},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="sourceDatasetStrId != null">source_dataset_str_id = #{sourceDatasetStrId},</if>
            <if test="sourceDatasetStrName != null">source_dataset_str_name = #{sourceDatasetStrName},</if>
            <if test="destDatasetStrId != null">dest_dataset_str_id = #{destDatasetStrId},</if>
            <if test="destDatasetStrName != null">dest_dataset_str_name = #{destDatasetStrName},</if>
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="entityType != null">entity_type = #{entityType},</if>
            <if test="removeInvisibleCharacter != null">remove_invisible_character = #{removeInvisibleCharacter},</if>
            <if test="replaceUniformWhitespace != null">replace_uniform_whitespace = #{replaceUniformWhitespace},</if>
            <if test="removeNonMeaningCharacters != null">remove_non_meaning_characters = #{removeNonMeaningCharacters},</if>
            <if test="replaceTraditionalChineseToSimplified != null">replace_traditional_chinese_to_simplified = #{replaceTraditionalChineseToSimplified},</if>
            <if test="removeWebIdentifiers != null">remove_web_identifiers = #{removeWebIdentifiers},</if>
            <if test="removeEmoji != null">remove_emoji = #{removeEmoji},</if>
            <if test="numberWords != null">number_words = #{numberWords},</if>
            <if test="numberMinRange != null">number_min_range = #{numberMinRange},</if>
            <if test="numberMaxRange != null">number_max_range = #{numberMaxRange},</if>
            <if test="characterRepetitionRemoval != null">character_repetition_removal = #{characterRepetitionRemoval},</if>
            <if test="characterRepetitionNumber != null">character_repetition_number = #{characterRepetitionNumber},</if>
            <if test="wordRepetitionRemoval != null">word_repetition_removal = #{wordRepetitionRemoval},</if>
            <if test="wordRepetitionNumber != null">word_repetition_number = #{wordRepetitionNumber},</if>
            <if test="specialCharacters != null">special_characters = #{specialCharacters},</if>
            <if test="specialNumber != null">special_number = #{specialNumber},</if>
            <if test="flaggedWords != null">flagged_words = #{flaggedWords},</if>
            <if test="flaggedNumber != null">flagged_number = #{flaggedNumber},</if>
            <if test="langId != null">lang_id = #{langId},</if>
            <if test="langNumber != null">lang_number = #{langNumber},</if>
            <if test="perplexity != null">perplexity = #{perplexity},</if>
            <if test="perplexityNumber != null">perplexity_number = #{perplexityNumber},</if>
            <if test="simhashOperator != null">simhash_operator = #{simhashOperator},</if>
            <if test="replaceEmails != null">replace_emails = #{replaceEmails},</if>
            <if test="replaceIp != null">replace_ip = #{replaceIp},</if>
            <if test="replaceIdentifier != null">replace_identifier = #{replaceIdentifier},</if>
            <if test="simhashOperatorNumber != null">simhash_operator_number = #{simhashOperatorNumber},</if>
            <if test="processStatus != null">process_status = #{processStatus},</if>
            <if test="status != null">status = #{status},</if>
            <if test="finishTime != null">finish_time = #{finishTime},</if>
            <if test="creatorName != null">creator_name = #{creatorName},</if>
            <if test="sourceDatasetName != null">source_dataset_name = #{sourceDatasetName},</if>
            <if test="destDatasetName != null">dest_dataset_name = #{destDatasetName},</if>
            <if test="etlResult != null">etl_result = #{etlResult},</if>
            <if test="remainingEntity != null">remaining_entity = #{remainingEntity},</if>
            <if test="exceptionResult != null">exception_result = #{exceptionResult},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="logPath != null">log_path = #{logPath},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="menuRouting != null">menu_routing = #{menuRouting},</if>
        </trim>
        where id = #{id}
    </update>
    <delete id="deleteCleaningTaskById" parameterType="Long"> delete from s_cleaning_task where id = #{id} </delete>
    <delete id="deleteCleaningTaskByIds" parameterType="String">
        delete from s_cleaning_task where id in
        <foreach item="id" collection="array" open="(" separator="," close=")"> #{id} </foreach>
    </delete>
</mapper>