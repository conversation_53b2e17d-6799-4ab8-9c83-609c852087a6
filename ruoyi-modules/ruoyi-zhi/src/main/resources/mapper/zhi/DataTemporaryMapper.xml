<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhi.mapper.DataTemporaryMapper">
    
    <resultMap type="com.ruoyi.zhi.domain.DataTemporary" id="DataTemporaryResult">
        <result property="id"    column="id"    />
        <result property="groupName"    column="group_name"    />
        <result property="importProgress"    column="import_progress"    />
        <result property="dataType"    column="data_type"    />
        <result property="projectType"    column="project_type"    />
        <result property="templateType"    column="template_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="entityCount"    column="entity_count"    />
        <result property="menuRouting"    column="menu_routing"    />
    </resultMap>

    <sql id="selectDataTemporaryVo">
        select id, group_name, data_type, project_type, template_type, create_by, create_time, update_by, update_time,import_progress,menu_routing from s_data_temporary
    </sql>

    <select id="selectDataTemporaryList" parameterType="com.ruoyi.zhi.domain.DataTemporary" resultMap="DataTemporaryResult">
        select t1.*,
        (SELECT COUNT(1) FROM 	s_data_information_temporary
        WHERE data_id = t1.id) as entity_count
        from s_data_temporary t1
        <where>  
            <if test="groupName != null  and groupName != ''"> and t1.group_name like concat('%', #{groupName}, '%')</if>
            <if test="dataType != null "> and t1.data_type = #{dataType}</if>
            <if test="projectType != null "> and t1.project_type = #{projectType}</if>
            <if test="templateType != null "> and t1.template_type = #{templateType}</if>
            <if test="menuRouting != null "> and t1.menu_routing = #{menuRouting}</if>
            <if test="createBy != null "> and t1.create_by = #{createBy}</if>
        </where>
    </select>
    
    <select id="selectDataTemporaryById" parameterType="Long" resultMap="DataTemporaryResult">
        <include refid="selectDataTemporaryVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertDataTemporary" parameterType="com.ruoyi.zhi.domain.DataTemporary" useGeneratedKeys="true" keyProperty="id">
        insert into s_data_temporary
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="groupName != null">group_name,</if>
            <if test="dataType != null">data_type,</if>
            <if test="projectType != null">project_type,</if>
            <if test="templateType != null">template_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="importProgress != null">import_progress,</if>
            <if test="menuRouting != null">menu_routing,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="groupName != null">#{groupName},</if>
            <if test="dataType != null">#{dataType},</if>
            <if test="projectType != null">#{projectType},</if>
            <if test="templateType != null">#{templateType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="importProgress != null">#{importProgress},</if>
            <if test="menuRouting != null">#{menuRouting},</if>
         </trim>
    </insert>

    <update id="updateDataTemporary" parameterType="com.ruoyi.zhi.domain.DataTemporary">
        update s_data_temporary
        <trim prefix="SET" suffixOverrides=",">
            <if test="groupName != null">group_name = #{groupName},</if>
            <if test="dataType != null">data_type = #{dataType},</if>
            <if test="projectType != null">project_type = #{projectType},</if>
            <if test="templateType != null">template_type = #{templateType},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="importProgress != null">import_progress = #{importProgress},</if>
            <if test="menuRouting != null">menu_routing = #{menuRouting},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataTemporaryById" parameterType="Long">
        delete from s_data_temporary where id = #{id}
    </delete>

    <delete id="deleteDataTemporaryByIds" parameterType="String">
        delete from s_data_temporary where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>