<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhi.mapper.SKnowledgeSchoolFileMapper">

    <resultMap type="com.ruoyi.zhi.domain.SKnowledgeSchoolFile" id="SKnowledgeSchoolFileResult">
        <result property="id"    column="id"    />
        <result property="fileId"    column="file_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="kbId"    column="kb_id"    />
        <result property="filePath"    column="file_path"    />
        <result property="indexingStatus"    column="indexing_status"    />
        <result property="wordCount"    column="word_count"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"/>
    </resultMap>

    <sql id="selectSKnowledgeSchoolFileVo">
        select id, file_id, file_name, kb_id, file_path, indexing_status, word_count, create_by, create_time from s_knowledge_school_file
    </sql>

    <select id="selectSKnowledgeSchoolFileList" parameterType="com.ruoyi.zhi.domain.SKnowledgeSchoolFile" resultMap="SKnowledgeSchoolFileResult">
        <include refid="selectSKnowledgeSchoolFileVo"/>
        <where>
            <if test="fileId != null  and fileId != ''"> and file_id = #{fileId}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="kbId != null  and kbId != ''"> and kb_id = #{kbId}</if>
            <if test="filePath != null  and filePath != ''"> and file_path = #{filePath}</if>
            <if test="indexingStatus != null  and indexingStatus != ''"> and indexing_status = #{indexingStatus}</if>
            <if test="wordCount != null "> and word_count = #{wordCount}</if>
        </where>
    </select>

    <select id="selectSKnowledgeSchoolFileById" parameterType="Long" resultMap="SKnowledgeSchoolFileResult">
        <include refid="selectSKnowledgeSchoolFileVo"/>
        where id = #{id}
    </select>

    <insert id="insertSKnowledgeSchoolFile" parameterType="com.ruoyi.zhi.domain.SKnowledgeSchoolFile">
        insert into s_knowledge_school_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileId != null">file_id,</if>
            <if test="fileName != null">file_name,</if>
            <if test="kbId != null">kb_id,</if>
            <if test="filePath != null">file_path,</if>
            <if test="indexingStatus != null">indexing_status,</if>
            <if test="wordCount != null">word_count,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileId != null">#{fileId},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="kbId != null">#{kbId},</if>
            <if test="filePath != null">#{filePath},</if>
            <if test="indexingStatus != null">#{indexingStatus},</if>
            <if test="wordCount != null">#{wordCount},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
        ON DUPLICATE KEY UPDATE
        file_name = VALUES(file_name),
        kb_id = VALUES(kb_id),
        file_path = VALUES(file_path),
        indexing_status = VALUES(indexing_status),
        word_count = VALUES(word_count),
        create_by = VALUES(create_by),
        create_time = VALUES(create_time)
    </insert>

    <update id="updateSKnowledgeSchoolFile" parameterType="com.ruoyi.zhi.domain.SKnowledgeSchoolFile">
        update s_knowledge_school_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileId != null">file_id = #{fileId},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="kbId != null">kb_id = #{kbId},</if>
            <if test="filePath != null">file_path = #{filePath},</if>
            <if test="indexingStatus != null">indexing_status = #{indexingStatus},</if>
            <if test="wordCount != null">word_count = #{wordCount},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSKnowledgeSchoolFileById" parameterType="Long">
        delete from s_knowledge_school_file where id = #{id}
    </delete>

    <delete id="deleteSKnowledgeSchoolFileByIds" parameterType="String">
        delete from s_knowledge_school_file where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
