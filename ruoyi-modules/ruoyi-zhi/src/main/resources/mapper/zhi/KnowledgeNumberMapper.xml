<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhi.mapper.KnowledgeNumberMapper">
    
    <resultMap type="com.ruoyi.zhi.domain.KnowledgeNumber" id="KnowledgeNumberResult">
        <result property="yearMonth"    column="year_month"    />
        <result property="knowleNumber"    column="knowle_number"    />
    </resultMap>

    <sql id="selectKnowledgeNumberVo">
        select `year_month`, `knowle_number` from s_knowledge_number
    </sql>

    <select id="selectKnowledgeNumberList" parameterType="com.ruoyi.zhi.domain.KnowledgeNumber" resultMap="KnowledgeNumberResult">
        <include refid="selectKnowledgeNumberVo"/>
        <where>  
            <if test="yearMonth != null  and yearMonth != ''"> and `year_month` = #{yearMonth}</if>
            <if test="knowleNumber != null "> and knowle_number = #{knowleNumber}</if>
        </where>
    </select>
    
    <select id="selectKnowledgeNumberByYearMonth" parameterType="String" resultMap="KnowledgeNumberResult">
        <include refid="selectKnowledgeNumberVo"/>
        where `year_month` = #{yearMonth}
    </select>
        
    <insert id="insertKnowledgeNumber" parameterType="com.ruoyi.zhi.domain.KnowledgeNumber">
        insert into s_knowledge_number
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="yearMonth != null">`year_month`,</if>
            <if test="knowleNumber != null">knowle_number,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="yearMonth != null">#{yearMonth},</if>
            <if test="knowleNumber != null">#{knowleNumber},</if>
         </trim>
    </insert>

    <update id="updateKnowledgeNumber" parameterType="com.ruoyi.zhi.domain.KnowledgeNumber">
        update s_knowledge_number
        <trim prefix="SET" suffixOverrides=",">
            <if test="knowleNumber != null">knowle_number = #{knowleNumber},</if>
        </trim>
        where `year_month` = #{yearMonth}
    </update>

    <delete id="deleteKnowledgeNumberByYearMonth" parameterType="String">
        delete from s_knowledge_number where `year_month` = #{yearMonth}
    </delete>

    <delete id="deleteKnowledgeNumberByYearMonths" parameterType="String">
        delete from s_knowledge_number where `year_month` in
        <foreach item="yearMonth" collection="array" open="(" separator="," close=")">
            #{yearMonth}
        </foreach>
    </delete>

    <select id="selectKnowledgeNumberAll"  resultMap="KnowledgeNumberResult">
        <include refid="selectKnowledgeNumberVo"/>
    </select>
</mapper>