<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhi.mapper.ApplicationScenarioPromptMapper">

    <resultMap type="com.ruoyi.zhi.domain.ApplicationScenarioPrompt" id="ApplicationScenarioPromptResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="majorId"    column="major_id"    />
        <result property="prompt"    column="prompt"    />
        <result property="applicationScenarioId"    column="application_scenario_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectApplicationScenarioPromptVo">
        select id, name, major_id, prompt, application_scenario_id, create_by, create_time, update_by, update_time from application_scenario_prompt
    </sql>

    <select id="selectApplicationScenarioPromptList" parameterType="com.ruoyi.zhi.domain.ApplicationScenarioPrompt" resultMap="ApplicationScenarioPromptResult">
        <include refid="selectApplicationScenarioPromptVo"/>
        <where>
            <if test="applicationScenarioId != null  and applicationScenarioId != ''"> and application_scenario_id = #{applicationScenarioId}</if>
        </where>
    </select>

    <select id="selectApplicationScenarioPromptById" parameterType="Long" resultMap="ApplicationScenarioPromptResult">
        <include refid="selectApplicationScenarioPromptVo"/>
        where id = #{id}
    </select>

    <insert id="insertApplicationScenarioPrompt" parameterType="com.ruoyi.zhi.domain.ApplicationScenarioPrompt" useGeneratedKeys="true" keyProperty="id">
        insert into application_scenario_prompt
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="majorId != null">major_id,</if>
            <if test="prompt != null">prompt,</if>
            <if test="applicationScenarioId != null and applicationScenarioId != ''">application_scenario_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="majorId != null">#{majorId},</if>
            <if test="prompt != null">#{prompt},</if>
            <if test="applicationScenarioId != null and applicationScenarioId != ''">#{applicationScenarioId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateApplicationScenarioPrompt" parameterType="com.ruoyi.zhi.domain.ApplicationScenarioPrompt">
        update application_scenario_prompt
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="majorId != null">major_id = #{majorId},</if>
            <if test="prompt != null">prompt = #{prompt},</if>
            <if test="applicationScenarioId != null and applicationScenarioId != ''">application_scenario_id = #{applicationScenarioId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteApplicationScenarioPromptById" parameterType="Long">
        delete from application_scenario_prompt where id = #{id}
    </delete>

    <delete id="deleteApplicationScenarioPromptByIds" parameterType="String">
        delete from application_scenario_prompt where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
