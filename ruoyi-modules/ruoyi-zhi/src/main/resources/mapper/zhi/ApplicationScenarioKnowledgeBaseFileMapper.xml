<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhi.mapper.ApplicationScenarioKnowledgeBaseFileMapper">

    <resultMap type="com.ruoyi.zhi.domain.ApplicationScenarioKnowledgeBaseFile" id="ApplicationScenarioKnowledgeBaseFileResult">
        <result property="id"    column="id"    />
        <result property="fileId"    column="file_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="kbId"    column="kb_id"    />
        <result property="filePath"    column="file_path"    />
        <result property="createdAt"    column="created_at"    />
        <result property="indexingStatus"    column="indexing_status"    />
        <result property="error"    column="error"    />
        <result property="enabled"    column="enabled"    />
        <result property="disabledAt"    column="disabled_at"    />
        <result property="disabledBy"    column="disabled_by"    />
        <result property="displayStatus"    column="display_status"    />
        <result property="wordCount"    column="word_count"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="majorId"    column="major_id"    />
        <result property="courseName"    column="course_name"    />
        <result property="disciplineId"    column="discipline_id"    />
        <result property="parseStatus"    column="parse_status"    />
        <result property="submitStatus"    column="submit_status"    />
        <result property="parseFlag"    column="parse_flag"    />
        <result property="isAllianceCourse"    column="is_alliance_course"    />
        <result property="examineFlag"    column="examineFlag"    />
        <result property="examineMessage"    column="examine_message"    />
    </resultMap>

    <sql id="selectApplicationScenarioKnowledgeBaseFileVo">
        select id, file_id, file_name, kb_id, file_path, created_at, indexing_status, error, enabled, disabled_at, disabled_by, display_status, word_count, create_by, create_time, major_id, course_name, discipline_id, parse_status, submit_status, parse_flag, is_alliance_course, examineFlag, examine_message from application_scenario_knowledge_base_file
    </sql>

    <select id="selectApplicationScenarioKnowledgeBaseFileList" parameterType="com.ruoyi.zhi.domain.ApplicationScenarioKnowledgeBaseFile" resultMap="ApplicationScenarioKnowledgeBaseFileResult">
        <include refid="selectApplicationScenarioKnowledgeBaseFileVo"/>
        <where>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
            <if test="parseFlag != null  and parseFlag != ''"> and parse_flag = #{parseFlag}</if>
            <if test="kbId != null  and kbId != ''"> and kb_id = #{kbId}</if>
        </where>
    </select>

    <select id="selectApplicationScenarioKnowledgeBaseFileById" parameterType="Long" resultMap="ApplicationScenarioKnowledgeBaseFileResult">
        <include refid="selectApplicationScenarioKnowledgeBaseFileVo"/>
        where id = #{id}
    </select>
    <select id="selectApplicationScenarioByNameAndMajor" resultType="java.lang.Integer">
        select id from application_scenario where application_name=#{applicationName} and major=#{Major}
    </select>

    <insert id="insertApplicationScenarioKnowledgeBaseFile" parameterType="com.ruoyi.zhi.domain.ApplicationScenarioKnowledgeBaseFile">
        insert into application_scenario_knowledge_base_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="fileId != null">file_id,</if>
            <if test="fileName != null">file_name,</if>
            <if test="kbId != null">kb_id,</if>
            <if test="filePath != null">file_path,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="indexingStatus != null">indexing_status,</if>
            <if test="error != null">error,</if>
            <if test="enabled != null">enabled,</if>
            <if test="disabledAt != null">disabled_at,</if>
            <if test="disabledBy != null">disabled_by,</if>
            <if test="displayStatus != null">display_status,</if>
            <if test="wordCount != null">word_count,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="majorId != null">major_id,</if>
            <if test="courseName != null">course_name,</if>
            <if test="disciplineId != null">discipline_id,</if>
            <if test="parseStatus != null">parse_status,</if>
            <if test="submitStatus != null">submit_status,</if>
            <if test="parseFlag != null">parse_flag,</if>
            <if test="isAllianceCourse != null">is_alliance_course,</if>
            <if test="examineFlag != null">examineFlag,</if>
            <if test="examineMessage != null">examine_message,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="fileId != null">#{fileId},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="kbId != null">#{kbId},</if>
            <if test="filePath != null">#{filePath},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="indexingStatus != null">#{indexingStatus},</if>
            <if test="error != null">#{error},</if>
            <if test="enabled != null">#{enabled},</if>
            <if test="disabledAt != null">#{disabledAt},</if>
            <if test="disabledBy != null">#{disabledBy},</if>
            <if test="displayStatus != null">#{displayStatus},</if>
            <if test="wordCount != null">#{wordCount},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="majorId != null">#{majorId},</if>
            <if test="courseName != null">#{courseName},</if>
            <if test="disciplineId != null">#{disciplineId},</if>
            <if test="parseStatus != null">#{parseStatus},</if>
            <if test="submitStatus != null">#{submitStatus},</if>
            <if test="parseFlag != null">#{parseFlag},</if>
            <if test="isAllianceCourse != null">#{isAllianceCourse},</if>
            <if test="examineFlag != null">#{examineFlag},</if>
            <if test="examineMessage != null">#{examineMessage},</if>
         </trim>
    </insert>

    <update id="updateApplicationScenarioKnowledgeBaseFile" parameterType="com.ruoyi.zhi.domain.ApplicationScenarioKnowledgeBaseFile">
        update application_scenario_knowledge_base_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileId != null">file_id = #{fileId},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="kbId != null">kb_id = #{kbId},</if>
            <if test="filePath != null">file_path = #{filePath},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="indexingStatus != null">indexing_status = #{indexingStatus},</if>
            <if test="error != null">error = #{error},</if>
            <if test="enabled != null">enabled = #{enabled},</if>
            <if test="disabledAt != null">disabled_at = #{disabledAt},</if>
            <if test="disabledBy != null">disabled_by = #{disabledBy},</if>
            <if test="displayStatus != null">display_status = #{displayStatus},</if>
            <if test="wordCount != null">word_count = #{wordCount},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="majorId != null">major_id = #{majorId},</if>
            <if test="courseName != null">course_name = #{courseName},</if>
            <if test="disciplineId != null">discipline_id = #{disciplineId},</if>
            <if test="parseStatus != null">parse_status = #{parseStatus},</if>
            <if test="submitStatus != null">submit_status = #{submitStatus},</if>
            <if test="parseFlag != null">parse_flag = #{parseFlag},</if>
            <if test="isAllianceCourse != null">is_alliance_course = #{isAllianceCourse},</if>
            <if test="examineFlag != null">examineFlag = #{examineFlag},</if>
            <if test="examineMessage != null">examine_message = #{examineMessage},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteApplicationScenarioKnowledgeBaseFileById" parameterType="Long">
        delete from application_scenario_knowledge_base_file where id = #{id}
    </delete>

    <delete id="deleteApplicationScenarioKnowledgeBaseFileByIds" parameterType="String">
        delete from application_scenario_knowledge_base_file where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
