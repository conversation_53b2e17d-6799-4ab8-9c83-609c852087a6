<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhi.mapper.ModelTrainingMapper">

    <resultMap type="com.ruoyi.zhi.domain.ModelTraining" id="ModelTrainingResult">
        <result property="id" column="id"/>
        <result property="taskId" column="task_id"/>
        <result property="jobId" column="job_id"/>
        <result property="taskName" column="task_name"/>
        <result property="description" column="description"/>
        <result property="incrementTaskId" column="increment_task_id"/>
        <result property="epoch" column="epoch"/>
        <result property="batchSize" column="batch_size"/>
        <result property="learningRate" column="learning_rate"/>
        <result property="maxSeqlen" column="max_seqLen"/>
        <result property="loggingSteps" column="logging_steps"/>
        <result property="warmupRatio" column="warmup_ratio"/>
        <result property="weightDecay" column="weight_decay"/>
        <result property="loraRank" column="lora_rank"/>
        <result property="loraAllLinear" column="lora_all_linear"/>
        <result property="sourceType" column="source_type"/>
        <result property="datasetId" column="dataset_id"/>
        <result property="samplingRate" column="sampling_rate"/>
        <result property="versionBosUri" column="version_bos_uri"/>
        <result property="splitRatio" column="split_ratio"/>
        <result property="corpusProportion" column="corpus_proportion"/>
        <result property="taskStatus" column="task_status"/>
        <result property="groupName" column="group_name"/>
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="modelId"    column="model_id"    />
        <result property="modelSetId"    column="model_set_id"    />
        <result property="menuRouting"    column="menu_routing"    />
        <result property="ak"    column="ak"    />
        <result property="sk"    column="sk"    />
    </resultMap>

    <sql id="selectModelTrainingVo">
        select id, task_id, job_id, task_name,task_status, description, increment_task_id, epoch, batch_size, learning_rate, max_seqLen, logging_steps, warmup_ratio, weight_decay, lora_rank, lora_all_linear, source_type, dataset_id, sampling_rate, version_bos_uri, split_ratio, corpus_proportion ,group_name,create_by, create_time, update_by, update_time,model_id,model_set_id,menu_routing,ak,sk from s_model_training
    </sql>
    <sql id="selectModelTrainingVo2">
        select id, task_id, job_id, task_name,task_status, description, increment_task_id, epoch, batch_size, learning_rate, max_seqLen, logging_steps, warmup_ratio, weight_decay, lora_rank, lora_all_linear, source_type, dataset_id, sampling_rate, version_bos_uri, split_ratio, corpus_proportion ,group_name,create_by, create_time, update_by, update_time,model_id,model_set_id,menu_routing from s_model_training
    </sql>

    <select id="selectModelTrainingList" parameterType="com.ruoyi.zhi.domain.ModelTraining"
            resultMap="ModelTrainingResult">
        <include refid="selectModelTrainingVo2"/>
        <where>
            <if test="taskId != null  and taskId != ''">and task_id = #{taskId}</if>
            <if test="jobId != null  and jobId != ''">and job_id = #{jobId}</if>
            <if test="taskName != null  and taskName != ''">and task_name like concat('%', #{taskName}, '%')</if>
            <if test="description != null  and description != ''">and description = #{description}</if>
            <if test="incrementTaskId != null  and incrementTaskId != ''">and increment_task_id = #{incrementTaskId}
            </if>
            <if test="epoch != null ">and epoch = #{epoch}</if>
            <if test="batchSize != null ">and batch_size = #{batchSize}</if>
            <if test="learningRate != null ">and learning_rate = #{learningRate}</if>
            <if test="maxSeqlen != null ">and max_seqLen = #{maxSeqlen}</if>
            <if test="loggingSteps != null ">and logging_steps = #{loggingSteps}</if>
            <if test="warmupRatio != null ">and warmup_ratio = #{warmupRatio}</if>
            <if test="weightDecay != null ">and weight_decay = #{weightDecay}</if>
            <if test="loraRank != null ">and lora_rank = #{loraRank}</if>
            <if test="loraAllLinear != null  and loraAllLinear != ''">and lora_all_linear = #{loraAllLinear}</if>
            <if test="sourceType != null  and sourceType != ''">and source_type = #{sourceType}</if>
            <if test="datasetId != null  and datasetId != ''">and dataset_id = #{datasetId}</if>
            <if test="samplingRate != null ">and sampling_rate = #{samplingRate}</if>
            <if test="versionBosUri != null  and versionBosUri != ''">and version_bos_uri = #{versionBosUri}</if>
            <if test="splitRatio != null ">and split_ratio = #{splitRatio}</if>
            <if test="corpusProportion != null  and corpusProportion != ''">and corpus_proportion =
                #{corpusProportion}
            </if>
            <if test="taskStatus != null  and taskStatus != ''">and task_status = #{taskStatus}</if>
            <if test="groupName != null  and groupName != ''">and group_name = #{groupName}</if>
            <if test="modelId != null  and modelId != ''">and model_id = #{modelId}</if>
            <if test="modelSetId != null  and modelSetId != ''">and model_set_id = #{modelSetId}</if>
            <if test="menuRouting != null  and menuRouting != ''">and menu_routing = #{menuRouting}</if>
            <if test="createBy != null "> and create_by = #{createBy}</if>
        </where>
    </select>

    <select id="selectModelTrainingById" parameterType="Long" resultMap="ModelTrainingResult">
        <include refid="selectModelTrainingVo2"/>
        where id = #{id}
    </select>

    <select id="selectModelTrainingByIdDone" parameterType="com.ruoyi.zhi.domain.ModelTraining"   resultMap="ModelTrainingResult">
        <include refid="selectModelTrainingVo"/>
        where  id = (SELECT MAX(id) from s_model_training where task_status = 'Done' and ak = #{ak} and sk = #{sk} ) and ak = #{ak} and sk = #{sk}
    </select>


    <insert id="insertModelTraining" parameterType="com.ruoyi.zhi.domain.ModelTraining" useGeneratedKeys="true"
            keyProperty="id">
        insert into s_model_training
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="jobId != null">job_id,</if>
            <if test="taskName != null">task_name,</if>
            <if test="description != null">description,</if>
            <if test="incrementTaskId != null">increment_task_id,</if>
            <if test="epoch != null">epoch,</if>
            <if test="batchSize != null">batch_size,</if>
            <if test="learningRate != null">learning_rate,</if>
            <if test="maxSeqlen != null">max_seqLen,</if>
            <if test="loggingSteps != null">logging_steps,</if>
            <if test="warmupRatio != null">warmup_ratio,</if>
            <if test="weightDecay != null">weight_decay,</if>
            <if test="loraRank != null">lora_rank,</if>
            <if test="loraAllLinear != null">lora_all_linear,</if>
            <if test="sourceType != null">source_type,</if>
            <if test="datasetId != null">dataset_id,</if>
            <if test="samplingRate != null">sampling_rate,</if>
            <if test="versionBosUri != null">version_bos_uri,</if>
            <if test="splitRatio != null">split_ratio,</if>
            <if test="corpusProportion != null">corpus_proportion,</if>
            <if test="taskStatus != null">task_status,</if>
            <if test="groupName != null">group_name,</if>
            <if test="modelId != null">model_id,</if>
            <if test="modelSetId != null">model_set_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="menuRouting != null">menu_routing,</if>
            <if test="ak != null">ak,</if>
            <if test="sk != null">sk,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="jobId != null">#{jobId},</if>
            <if test="taskName != null">#{taskName},</if>
            <if test="description != null">#{description},</if>
            <if test="incrementTaskId != null">#{incrementTaskId},</if>
            <if test="epoch != null">#{epoch},</if>
            <if test="batchSize != null">#{batchSize},</if>
            <if test="learningRate != null">#{learningRate},</if>
            <if test="maxSeqlen != null">#{maxSeqlen},</if>
            <if test="loggingSteps != null">#{loggingSteps},</if>
            <if test="warmupRatio != null">#{warmupRatio},</if>
            <if test="weightDecay != null">#{weightDecay},</if>
            <if test="loraRank != null">#{loraRank},</if>
            <if test="loraAllLinear != null">#{loraAllLinear},</if>
            <if test="sourceType != null">#{sourceType},</if>
            <if test="datasetId != null">#{datasetId},</if>
            <if test="samplingRate != null">#{samplingRate},</if>
            <if test="versionBosUri != null">#{versionBosUri},</if>
            <if test="splitRatio != null">#{splitRatio},</if>
            <if test="corpusProportion != null">#{corpusProportion},</if>
            <if test="taskStatus != null">#{taskStatus},</if>
            <if test="groupName != null">#{groupName},</if>
            <if test="modelId != null">#{modelId},</if>
            <if test="modelSetId != null">#{modelSetId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="menuRouting != null">#{menuRouting},</if>
            <if test="ak != null">#{ak},</if>
            <if test="sk != null">#{sk},</if>
        </trim>
    </insert>

    <update id="updateModelTraining" parameterType="com.ruoyi.zhi.domain.ModelTraining">
        update s_model_training
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="jobId != null">job_id = #{jobId},</if>
            <if test="taskName != null">task_name = #{taskName},</if>
            <if test="description != null">description = #{description},</if>
            <if test="incrementTaskId != null">increment_task_id = #{incrementTaskId},</if>
            <if test="epoch != null">epoch = #{epoch},</if>
            <if test="batchSize != null">batch_size = #{batchSize},</if>
            <if test="learningRate != null">learning_rate = #{learningRate},</if>
            <if test="maxSeqlen != null">max_seqLen = #{maxSeqlen},</if>
            <if test="loggingSteps != null">logging_steps = #{loggingSteps},</if>
            <if test="warmupRatio != null">warmup_ratio = #{warmupRatio},</if>
            <if test="weightDecay != null">weight_decay = #{weightDecay},</if>
            <if test="loraRank != null">lora_rank = #{loraRank},</if>
            <if test="loraAllLinear != null">lora_all_linear = #{loraAllLinear},</if>
            <if test="sourceType != null">source_type = #{sourceType},</if>
            <if test="datasetId != null">dataset_id = #{datasetId},</if>
            <if test="samplingRate != null">sampling_rate = #{samplingRate},</if>
            <if test="versionBosUri != null">version_bos_uri = #{versionBosUri},</if>
            <if test="splitRatio != null">split_ratio = #{splitRatio},</if>
            <if test="corpusProportion != null">corpus_proportion = #{corpusProportion},</if>
            <if test="taskStatus != null">task_status = #{taskStatus},</if>
            <if test="groupName != null">group_name = #{groupName},</if>
            <if test="modelId != null">model_id = #{modelId},</if>
            <if test="modelSetId != null">model_set_id = #{modelSetId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="menuRouting != null">menu_routing = #{menuRouting},</if>
            <if test="ak != null">ak = #{ak},</if>
            <if test="sk != null">sk = #{sk},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteModelTrainingById" parameterType="Long">
        delete from s_model_training where id = #{id}
    </delete>

    <delete id="deleteModelTrainingByIds" parameterType="String">
        delete from s_model_training where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectModelTraining" parameterType="com.ruoyi.zhi.domain.ModelTraining" resultMap="ModelTrainingResult">
        <include refid="selectModelTrainingVo"/>
        where id = (SELECT MIN(id) from s_model_training where task_status = 'line'and ak = #{ak} and sk = #{sk}) and ak = #{ak} and sk = #{sk}
    </select>


    <select id="selectModelTrainingCarry" parameterType="com.ruoyi.zhi.domain.ModelTraining" resultMap="ModelTrainingResult">
        <include refid="selectModelTrainingVo"/>
        where task_status = 'Running' and ak = #{ak} and sk = #{sk}
    </select>


    <update id="updateModelTrainingById" parameterType="com.ruoyi.zhi.domain.ModelTraining">
        update s_model_training
        set task_status = #{taskStatus}
        where id = #{id}
    </update>

    <select id="selectDisMenuRouting" resultMap="ModelTrainingResult">
        select distinct ak,sk from s_model_training
    </select>
</mapper>
