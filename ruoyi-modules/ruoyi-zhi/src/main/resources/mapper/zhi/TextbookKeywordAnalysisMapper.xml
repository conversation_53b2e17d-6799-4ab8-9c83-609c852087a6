<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhi.mapper.TextbookKeywordAnalysisMapper">
    
    <resultMap type="com.ruoyi.zhi.domain.TextbookKeywordAnalysis" id="TextbookKeywordAnalysisResult">
        <result property="id"    column="id"    />
        <result property="textbookId"    column="textbook_id"    />
        <result property="knowledgeCategory"    column="knowledge_category"    />
        <result property="category"    column="category"    />
        <result property="keyword"    column="keyword"    />
        <result property="chapter"    column="chapter"    />
    </resultMap>

    <sql id="selectTextbookKeywordAnalysisVo">
        select id, textbook_id, knowledge_category, category, keyword ,chapter from s_textbook_keyword_analysis
    </sql>

    <select id="selectTextbookKeywordAnalysisList" parameterType="com.ruoyi.zhi.domain.TextbookKeywordAnalysis" resultMap="TextbookKeywordAnalysisResult">
        <include refid="selectTextbookKeywordAnalysisVo"/>
        <where>  
            <if test="textbookId != null "> and textbook_id = #{textbookId}</if>
            <if test="knowledgeCategory != null  and knowledgeCategory != ''"> and knowledge_category = #{knowledgeCategory}</if>
            <if test="category != null  and category != ''"> and category = #{category}</if>
            <if test="keyword != null  and keyword != ''"> and keyword = #{keyword}</if>
            <if test="chapter != null  and chapter != ''"> and chapter = #{chapter}</if>
        </where>
    </select>
    
    <select id="selectTextbookKeywordAnalysisByIds" parameterType="Long" resultMap="TextbookKeywordAnalysisResult">
        <include refid="selectTextbookKeywordAnalysisVo"/>
        where textbook_id = #{id}
    </select>

    <select id="selectTextbookKeywordAnalysisById" parameterType="Long" resultMap="TextbookKeywordAnalysisResult">
        <include refid="selectTextbookKeywordAnalysisVo"/>
        where id = #{id}
    </select>

    <insert id="insertTextbookKeywordAnalysis" parameterType="com.ruoyi.zhi.domain.TextbookKeywordAnalysis" useGeneratedKeys="true" keyProperty="id">
        insert into s_textbook_keyword_analysis
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="textbookId != null">textbook_id,</if>
            <if test="knowledgeCategory != null">knowledge_category,</if>
            <if test="category != null">category,</if>
            <if test="keyword != null">keyword,</if>
            <if test="chapter != null">chapter,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="textbookId != null">#{textbookId},</if>
            <if test="knowledgeCategory != null">#{knowledgeCategory},</if>
            <if test="category != null">#{category},</if>
            <if test="keyword != null">#{keyword},</if>
            <if test="chapter != null">#{chapter},</if>
         </trim>
    </insert>

    <update id="updateTextbookKeywordAnalysis" parameterType="com.ruoyi.zhi.domain.TextbookKeywordAnalysis">
        update s_textbook_keyword_analysis
        <trim prefix="SET" suffixOverrides=",">
            <if test="textbookId != null">textbook_id = #{textbookId},</if>
            <if test="knowledgeCategory != null">knowledge_category = #{knowledgeCategory},</if>
            <if test="category != null">category = #{category},</if>
            <if test="keyword != null">keyword = #{keyword},</if>
            <if test="chapter != null">chapter = #{chapter},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTextbookKeywordAnalysisById" parameterType="Long">
        delete from s_textbook_keyword_analysis where id = #{id}
    </delete>

    <delete id="deleteTextbookKeywordAnalysisByIds" parameterType="String">
        delete from s_textbook_keyword_analysis where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertTextbookKeywordAnalysisList">
        insert into s_textbook_keyword_analysis(textbook_id, knowledge_category, category,keyword,chapter) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.textbookId},#{item.knowledgeCategory},#{item.category},#{item.keyword},#{item.chapter})
        </foreach>
    </insert>

    <delete id="deleteTextbookKeywordAnalysisByTextbookId" parameterType="Long">
        delete from s_textbook_keyword_analysis where textbook_id = #{textbookId}
    </delete>
</mapper>