<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhi.mapper.KnowledgeBaseFileMapper">
    
    <resultMap type="com.ruoyi.zhi.domain.KnowledgeBaseFile" id="KnowledgeBaseFileResult">
        <result property="id"    column="id"    />
        <result property="fileId"    column="file_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="kbId"    column="kb_id"    />
        <result property="filePath"    column="file_path"    />
        <result property="createdAt"    column="created_at"    />
        <result property="indexingStatus"    column="indexing_status"    />
        <result property="error"    column="error"    />
        <result property="enabled"    column="enabled"    />
        <result property="disabledAt"    column="disabled_at"    />
        <result property="disabledBy"    column="disabled_by"    />
        <result property="displayStatus"    column="display_status"    />
        <result property="wordCount"    column="word_count"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="majorId"    column="major_id"    />
        <result property="courseName"    column="course_name"    />
        <result property="disciplineId"    column="discipline_id"    />
        <result property="parseStatus"    column="parse_status"    />
        <result property="submitStatus"    column="submit_status"    />
        <result property="parseFlag"    column="parse_flag"    />
        <result property="isAllianceCourse"    column="is_alliance_course"    />
        <result property="examineFlag"    column="examineFlag"    />
        <result property="examineMessage"    column="examine_message"    />
    </resultMap>

    <sql id="selectKnowledgeBaseFileVo">
        select id, file_id, file_name, kb_id, file_path, created_at, indexing_status, error, enabled, disabled_at, disabled_by, display_status, word_count, create_by, create_time,major_id,course_name,discipline_id,parse_status,submit_status,parse_flag from s_knowledge_base_file
    </sql>

    <select id="selectKnowledgeBaseFileList" parameterType="com.ruoyi.zhi.domain.KnowledgeBaseFile" resultMap="KnowledgeBaseFileResult">
        <include refid="selectKnowledgeBaseFileVo"/>
        <where>  
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="kbId != null "> and kb_id = #{kbId}</if>
            <if test="filePath != null  and filePath != ''"> and file_path = #{filePath}</if>
            <if test="createdAt != null  and createdAt != ''"> and created_at = #{createdAt}</if>
            <if test="indexingStatus != null  and indexingStatus != ''"> and indexing_status = #{indexingStatus}</if>
            <if test="error != null  and error != ''"> and error = #{error}</if>
            <if test="enabled != null "> and enabled = #{enabled}</if>
            <if test="disabledAt != null  and disabledAt != ''"> and disabled_at = #{disabledAt}</if>
            <if test="disabledBy != null  and disabledBy != ''"> and disabled_by = #{disabledBy}</if>
            <if test="displayStatus != null  and displayStatus != ''"> and display_status = #{displayStatus}</if>
            <if test="wordCount != null "> and word_count = #{wordCount}</if>
            <if test="majorId != null "> and major_id = #{majorId}</if>
            <if test="courseName != null "> and course_name = #{courseName}</if>
            <if test="disciplineId != null "> and discipline_id = #{disciplineId}</if>
            <if test="parseStatus != null "> and parse_status = #{parseStatus}</if>
            <if test="submitStatus != null "> and submit_status = #{submitStatus}</if>
            <if test="isAllianceCourse != null "> and is_alliance_course = #{isAllianceCourse}</if>
             and file_name !=''
        </where>
        ORDER BY course_name
    </select>
    
    <select id="selectKnowledgeBaseFileById" parameterType="Long" resultMap="KnowledgeBaseFileResult">
        <include refid="selectKnowledgeBaseFileVo"/>
        where id = #{id}
    </select>
    <select id="selectKnowledgeBaseFileByIds" parameterType="Long" resultMap="KnowledgeBaseFileResult">
        <include refid="selectKnowledgeBaseFileVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectKnowledgeBaseFileListBykbId" resultMap="KnowledgeBaseFileResult">
        <include refid="selectKnowledgeBaseFileVo"/>
        where kb_id = #{kbId}
    </select>

    <select id="selectKnowledgeBaseFileByMajorId" resultMap="KnowledgeBaseFileResult">
        <include refid="selectKnowledgeBaseFileVo"/>
        where major_id = #{majorId}
    </select>

    <insert id="insertKnowledgeBaseFile" parameterType="com.ruoyi.zhi.domain.KnowledgeBaseFile" useGeneratedKeys="true" keyProperty="id">
        insert into s_knowledge_base_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="fileId != null">file_id,</if>
            <if test="fileName != null">file_name,</if>
            <if test="kbId != null">kb_id,</if>
            <if test="filePath != null">file_path,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="indexingStatus != null">indexing_status,</if>
            <if test="error != null">error,</if>
            <if test="enabled != null">enabled,</if>
            <if test="disabledAt != null">disabled_at,</if>
            <if test="disabledBy != null">disabled_by,</if>
            <if test="displayStatus != null">display_status,</if>
            <if test="wordCount != null">word_count,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="majorId != null">major_id,</if>
            <if test="courseName != null">course_name,</if>
            <if test="disciplineId != null">discipline_id,</if>
            <if test="parseStatus != null">parse_status,</if>
            <if test="submitStatus != null">submit_status,</if>
            <if test="isAllianceCourse != null">is_alliance_course,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="fileId != null">#{fileId},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="kbId != null">#{kbId},</if>
            <if test="filePath != null">#{filePath},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="indexingStatus != null">#{indexingStatus},</if>
            <if test="error != null">#{error},</if>
            <if test="enabled != null">#{enabled},</if>
            <if test="disabledAt != null">#{disabledAt},</if>
            <if test="disabledBy != null">#{disabledBy},</if>
            <if test="displayStatus != null">#{displayStatus},</if>
            <if test="wordCount != null">#{wordCount},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="majorId != null">#{majorId},</if>
            <if test="courseName != null">#{courseName},</if>
            <if test="disciplineId != null">#{disciplineId},</if>
            <if test="parseStatus != null">#{parseStatus},</if>
            <if test="submitStatus != null">#{submitStatus},</if>
            <if test="isAllianceCourse != null">#{isAllianceCourse},</if>
         </trim>
    </insert>

    <update id="updateKnowledgeBaseFile" parameterType="com.ruoyi.zhi.domain.KnowledgeBaseFile">
        update s_knowledge_base_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileId != null">file_id = #{fileId},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="kbId != null">kb_id = #{kbId},</if>
            <if test="filePath != null">file_path = #{filePath},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="indexingStatus != null">indexing_status = #{indexingStatus},</if>
            <if test="error != null">error = #{error},</if>
            <if test="enabled != null">enabled = #{enabled},</if>
            <if test="disabledAt != null">disabled_at = #{disabledAt},</if>
            <if test="disabledBy != null">disabled_by = #{disabledBy},</if>
            <if test="displayStatus != null">display_status = #{displayStatus},</if>
            <if test="wordCount != null">word_count = #{wordCount},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="majorId != null">major_id = #{majorId},</if>
            <if test="courseName != null">course_name = #{courseName},</if>
            <if test="disciplineId != null">discipline_id = #{disciplineId},</if>
            <if test="parseStatus != null">parse_status = #{parseStatus},</if>
            <if test="submitStatus != null">submit_status = #{submitStatus},</if>
            <if test="isAllianceCourse != null">is_alliance_course = #{isAllianceCourse},</if>
            <if test="parseFlag != null">parse_flag = #{parseFlag},</if>
            <if test="examineFlag != null">examineFlag = #{examineFlag},</if>
            <if test="examineMessage != null">examine_message = #{examineMessage},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKnowledgeBaseFileById" parameterType="Long">
        delete from s_knowledge_base_file where id = #{id}
    </delete>

    <delete id="deleteKnowledgeBaseFileByIds" parameterType="String">
        delete from s_knowledge_base_file where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectCourseNameById" parameterType="com.ruoyi.zhi.domain.KnowledgeBaseFile" resultMap="KnowledgeBaseFileResult">
        select distinct major_id,course_name,is_alliance_course from s_knowledge_base_file
        <where>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="kbId != null "> and kb_id = #{kbId}</if>
            <if test="filePath != null  and filePath != ''"> and file_path = #{filePath}</if>
            <if test="createdAt != null  and createdAt != ''"> and created_at = #{createdAt}</if>
            <if test="indexingStatus != null  and indexingStatus != ''"> and indexing_status = #{indexingStatus}</if>
            <if test="error != null  and error != ''"> and error = #{error}</if>
            <if test="enabled != null "> and enabled = #{enabled}</if>
            <if test="disabledAt != null  and disabledAt != ''"> and disabled_at = #{disabledAt}</if>
            <if test="disabledBy != null  and disabledBy != ''"> and disabled_by = #{disabledBy}</if>
            <if test="displayStatus != null  and displayStatus != ''"> and display_status = #{displayStatus}</if>
            <if test="wordCount != null "> and word_count = #{wordCount}</if>
            <if test="majorId != null "> and major_id = #{majorId}</if>
            <if test="courseName != null "> and course_name = #{courseName}</if>
            <if test="disciplineId != null "> and discipline_id = #{disciplineId}</if>
            <if test="parseStatus != null "> and parse_status = #{parseStatus}</if>
            <if test="submitStatus != null "> and submit_status = #{submitStatus}</if>
            <if test="isAllianceCourse != null "> and is_alliance_course = #{isAllianceCourse}</if>
            <if test="courseList != null and !courseList.isEmpty()">
                and course_name in
                <foreach item="item" index="index" collection="courseList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="majorList != null and !majorList.isEmpty()">
                and major_id in
                <foreach item="item" index="index" collection="majorList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectKnowledgeBaseFileListAndTextBook" parameterType="com.ruoyi.zhi.domain.KnowledgeBaseFile" resultMap="KnowledgeBaseFileResult">
        SELECT
            a.*,
            b.author,
            b.publishing_house
        FROM
            s_knowledge_base_file a
        LEFT JOIN s_textbook_data_temporary b ON a.id =b.id
        <where>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="kbId != null "> and kb_id = #{kbId}</if>
            <if test="filePath != null  and filePath != ''"> and file_path = #{filePath}</if>
            <if test="createdAt != null  and createdAt != ''"> and created_at = #{createdAt}</if>
            <if test="indexingStatus != null  and indexingStatus != ''"> and indexing_status = #{indexingStatus}</if>
            <if test="error != null  and error != ''"> and error = #{error}</if>
            <if test="enabled != null "> and enabled = #{enabled}</if>
            <if test="disabledAt != null  and disabledAt != ''"> and disabled_at = #{disabledAt}</if>
            <if test="disabledBy != null  and disabledBy != ''"> and disabled_by = #{disabledBy}</if>
            <if test="displayStatus != null  and displayStatus != ''"> and display_status = #{displayStatus}</if>
            <if test="wordCount != null "> and word_count = #{wordCount}</if>
            <if test="majorId != null "> and major_id = #{majorId}</if>
            <if test="courseName != null "> and course_name = #{courseName}</if>
            <if test="disciplineId != null "> and discipline_id = #{disciplineId}</if>
            <if test="parseStatus != null "> and parse_status = #{parseStatus}</if>
            <if test="submitStatus != null "> and submit_status = #{submitStatus}</if>
        </where>
        ORDER BY course_name
    </select>

    <select id="selectKnowledgeBaseFileByCourseName" resultMap="KnowledgeBaseFileResult">
        SELECT
            a.*,
            b.author,
            b.publishing_house
        FROM
            s_knowledge_base_file a
        LEFT JOIN s_textbook_data_temporary b ON a.id =b.id
        where a.course_name = #{courseName}  and a.is_alliance_course = 'N' and a.file_name !=''
    </select>

    <select id="selectIsAllianceCourseList" resultType="String">
        select distinct is_alliance_course from s_knowledge_base_file
        where course_name = #{courseName}  and major_id = #{majorId}
    </select>

</mapper>