<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhi.mapper.GenerateDetailsMapper">
    
    <resultMap type="com.ruoyi.zhi.domain.GenerateDetails" id="GenerateDetailsResult">
        <result property="id"    column="id"    />
        <result property="taskId"    column="task_id"    />
        <result property="problem"    column="problem"    />
        <result property="answer"    column="answer"    />
    </resultMap>

    <sql id="selectGenerateDetailsVo">
        select id, task_id, problem, answer from s_generate_details
    </sql>

    <select id="selectGenerateDetailsList" parameterType="com.ruoyi.zhi.domain.GenerateDetails" resultMap="GenerateDetailsResult">
        <include refid="selectGenerateDetailsVo"/>
        <where>  
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="problem != null  and problem != ''"> and problem = #{problem}</if>
            <if test="answer != null  and answer != ''"> and answer = #{answer}</if>
        </where>
    </select>
    
    <select id="selectGenerateDetailsById" parameterType="Long" resultMap="GenerateDetailsResult">
        <include refid="selectGenerateDetailsVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertGenerateDetails" parameterType="com.ruoyi.zhi.domain.GenerateDetails" useGeneratedKeys="true" keyProperty="id">
        insert into s_generate_details
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="problem != null">problem,</if>
            <if test="answer != null">answer,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="problem != null">#{problem},</if>
            <if test="answer != null">#{answer},</if>
         </trim>
    </insert>

    <update id="updateGenerateDetails" parameterType="com.ruoyi.zhi.domain.GenerateDetails">
        update s_generate_details
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="problem != null">problem = #{problem},</if>
            <if test="answer != null">answer = #{answer},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGenerateDetailsById" parameterType="Long">
        delete from s_generate_details where id = #{id}
    </delete>

    <delete id="deleteGenerateDetailsByIds" parameterType="String">
        delete from s_generate_details where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteGenerateDetailsByTaskIds" parameterType="String">
        delete from s_generate_details where task_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="countPoductionTask">
       select count(1) from s_generate_details
        where task_id = #{taskId}
    </select>
</mapper>