<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhi.mapper.PoductionTaskMapper">

    <resultMap type="com.ruoyi.zhi.domain.PoductionTask" id="PoductionTaskResult">
        <result property="id"    column="id"    />
        <result property="fileName"    column="file_name"    />
        <result property="totalNumber"    column="total_number"    />
        <result property="taskStatus"    column="task_status"    />
        <result property="menuRouting"    column="menu_routing"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPoductionTaskVo">
        select id, file_name, total_number, task_status, menu_routing, create_by, create_time, update_by, update_time from s_poduction_task
    </sql>

    <select id="selectPoductionTaskList" parameterType="com.ruoyi.zhi.domain.PoductionTask" resultMap="PoductionTaskResult">
        <include refid="selectPoductionTaskVo"/>
        <where>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="totalNumber != null "> and total_number = #{totalNumber}</if>
            <if test="taskStatus != null  and taskStatus != ''"> and task_status = #{taskStatus}</if>
            <if test="menuRouting != null  and menuRouting != ''"> and menu_routing = #{menuRouting}</if>
            <if test="createBy != null "> and create_by = #{createBy}</if>
        </where>
    </select>

    <select id="selectPoductionTaskById" parameterType="Long" resultMap="PoductionTaskResult">
        <include refid="selectPoductionTaskVo"/>
        where id = #{id}
    </select>
    <select id="selectPoductionTaskfileObjectNameById" resultType="java.lang.Long">
        select file_object_name
        from s_poduction_task
        where id =#{id}

    </select>


    <insert id="insertPoductionTask" parameterType="com.ruoyi.zhi.domain.PoductionTask">
        insert into s_poduction_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="fileName != null">file_name,</if>
            <if test="totalNumber != null">total_number,</if>
            <if test="taskStatus != null">task_status,</if>
            <if test="menuRouting != null">menu_routing,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="fileIds != null and fileIds.length > 0">file_object_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="totalNumber != null">#{totalNumber},</if>
            <if test="taskStatus != null">#{taskStatus},</if>
            <if test="menuRouting != null">#{menuRouting},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="fileIds != null">
                <foreach collection="fileIds" item="fileId" open="(" close=")" separator=",">
                    #{fileId}
                </foreach>
            </if>
        </trim>
    </insert>

    <update id="updatePoductionTask" parameterType="com.ruoyi.zhi.domain.PoductionTask">
        update s_poduction_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="totalNumber != null">total_number = #{totalNumber},</if>
            <if test="taskStatus != null">task_status = #{taskStatus},</if>
            <if test="menuRouting != null">menu_routing = #{menuRouting},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePoductionTaskById" parameterType="Long">
        delete from s_poduction_task where id = #{id}
    </delete>

    <delete id="deletePoductionTaskByIds" parameterType="String">
        delete from s_poduction_task where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
