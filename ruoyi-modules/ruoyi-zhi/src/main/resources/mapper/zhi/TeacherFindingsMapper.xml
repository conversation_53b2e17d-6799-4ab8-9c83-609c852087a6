<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhi.mapper.TeacherFindingsMapper">
    
    <resultMap type="com.ruoyi.zhi.domain.TeacherFindings" id="TeacherFindingsResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="researchFindings"    column="research_findings"    />
    </resultMap>

    <sql id="selectTeacherFindingsVo">
        select id, teacher_id, research_findings from s_teacher_findings
    </sql>

    <select id="selectTeacherFindingsList" parameterType="com.ruoyi.zhi.domain.TeacherFindings" resultMap="TeacherFindingsResult">
        <include refid="selectTeacherFindingsVo"/>
        <where>  
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="researchFindings != null  and researchFindings != ''"> and research_findings = #{researchFindings}</if>
        </where>
    </select>
    
    <select id="selectTeacherFindingsById" parameterType="Long" resultMap="TeacherFindingsResult">
        <include refid="selectTeacherFindingsVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTeacherFindings" parameterType="com.ruoyi.zhi.domain.TeacherFindings" useGeneratedKeys="true" keyProperty="id">
        insert into s_teacher_findings
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="researchFindings != null and researchFindings != ''">research_findings,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="researchFindings != null and researchFindings != ''">#{researchFindings},</if>
         </trim>
    </insert>

    <update id="updateTeacherFindings" parameterType="com.ruoyi.zhi.domain.TeacherFindings">
        update s_teacher_findings
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="researchFindings != null and researchFindings != ''">research_findings = #{researchFindings},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTeacherFindingsById" parameterType="Long">
        delete from s_teacher_findings where teacher_id = #{id}
    </delete>

    <delete id="deleteTeacherFindingsByIds" parameterType="String">
        delete from s_teacher_findings where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertTeacherFindingsList">
        insert into s_teacher_findings(teacher_id, research_findings) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.teacherId},#{item.researchFindings})
        </foreach>
    </insert>

    <select id="selectTeacherFindingsByIds" parameterType="Long" resultMap="TeacherFindingsResult">
        <include refid="selectTeacherFindingsVo"/>
        where teacher_id = #{id}
    </select>

    <select id="selectTeacherFindingsByIdList" parameterType="java.util.List" resultMap="TeacherFindingsResult">
        <include refid="selectTeacherFindingsVo"/>
        WHERE teacher_id IN
        <foreach item="item" index="index" collection="list" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>