<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhi.mapper.RulesContentMapper">
    
    <resultMap type="com.ruoyi.zhi.domain.RulesContent" id="RulesContentResult">
        <result property="id"    column="id"    />
        <result property="rulesId"    column="rules_id"    />
        <result property="tagContent"    column="tag_content"    />
        <result property="tagType"    column="tag_type"    />
        <result property="contentPosition"    column="content_position"    />
    </resultMap>

    <sql id="selectRulesContentVo">
        select id, rules_id, tag_content, tag_type, content_position from s_rules_content
    </sql>

    <select id="selectRulesContentList" parameterType="com.ruoyi.zhi.domain.RulesContent" resultMap="RulesContentResult">
        <include refid="selectRulesContentVo"/>
        <where>  
            <if test="rulesId != null "> and rules_id = #{rulesId}</if>
            <if test="tagContent != null  and tagContent != ''"> and tag_content = #{tagContent}</if>
            <if test="tagType != null  and tagType != ''"> and tag_type = #{tagType}</if>
            <if test="contentPosition != null  and contentPosition != ''"> and content_position = #{contentPosition}</if>
        </where>
    </select>
    
    <select id="selectRulesContentById" parameterType="Long" resultMap="RulesContentResult">
        <include refid="selectRulesContentVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertRulesContent" parameterType="com.ruoyi.zhi.domain.RulesContent" useGeneratedKeys="true" keyProperty="id">
        insert into s_rules_content
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rulesId != null">rules_id,</if>
            <if test="tagContent != null">tag_content,</if>
            <if test="tagType != null">tag_type,</if>
            <if test="contentPosition != null">content_position,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rulesId != null">#{rulesId},</if>
            <if test="tagContent != null">#{tagContent},</if>
            <if test="tagType != null">#{tagType},</if>
            <if test="contentPosition != null">#{contentPosition},</if>
         </trim>
    </insert>

    <update id="updateRulesContent" parameterType="com.ruoyi.zhi.domain.RulesContent">
        update s_rules_content
        <trim prefix="SET" suffixOverrides=",">
            <if test="rulesId != null">rules_id = #{rulesId},</if>
            <if test="tagContent != null">tag_content = #{tagContent},</if>
            <if test="tagType != null">tag_type = #{tagType},</if>
            <if test="contentPosition != null">content_position = #{contentPosition},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRulesContentById" parameterType="Long">
        delete from s_rules_content where id = #{id}
    </delete>

    <delete id="deleteRulesContentByIds" parameterType="String">
        delete from s_rules_content where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectRulesContentsById" parameterType="Long" resultMap="RulesContentResult">
        <include refid="selectRulesContentVo"/>
        where rules_id = #{id}
    </select>
</mapper>