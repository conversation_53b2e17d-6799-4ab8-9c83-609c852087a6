<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhi.mapper.DataInformationMapper">
    
    <resultMap type="com.ruoyi.zhi.domain.DataInformation" id="DataInformationResult">
        <result property="id"    column="id"    />
        <result property="dataId"    column="data_id"    />
        <result property="prompt"    column="prompt"    />
        <result property="response"    column="response"    />
    </resultMap>

    <sql id="selectDataInformationVo">
        select id, data_id, prompt, response from s_data_information
    </sql>

    <select id="selectDataInformationList" parameterType="com.ruoyi.zhi.domain.DataInformation" resultMap="DataInformationResult">
        <include refid="selectDataInformationVo"/>
        <where>  
            <if test="dataId != null "> and data_id = #{dataId}</if>
            <if test="prompt != null  and prompt != ''"> and prompt = #{prompt}</if>
            <if test="response != null  and response != ''"> and response = #{response}</if>
        </where>
    </select>
    
    <select id="selectDataInformationById" parameterType="Long" resultMap="DataInformationResult">
        <include refid="selectDataInformationVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertDataInformation" parameterType="com.ruoyi.zhi.domain.DataInformation" useGeneratedKeys="true" keyProperty="id">
        insert into s_data_information
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataId != null">data_id,</if>
            <if test="prompt != null">prompt,</if>
            <if test="response != null">response,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataId != null">#{dataId},</if>
            <if test="prompt != null">#{prompt},</if>
            <if test="response != null">#{response},</if>
         </trim>
    </insert>
    <insert id="insertDataInformationList">
        insert into s_data_information(data_id, prompt, response) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.dataId},#{item.prompt},#{item.response})
        </foreach>
    </insert>

    <update id="updateDataInformation" parameterType="com.ruoyi.zhi.domain.DataInformation">
        update s_data_information
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataId != null">data_id = #{dataId},</if>
            <if test="prompt != null">prompt = #{prompt},</if>
            <if test="response != null">response = #{response},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataInformationById" parameterType="Long">
        delete from s_data_information where id = #{id}
    </delete>

    <delete id="deleteDataInformationByIds" parameterType="String">
        delete from s_data_information where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectDataInformationByDataId" parameterType="Long" resultMap="DataInformationResult">
        <include refid="selectDataInformationVo"/>
        where data_id = #{id}
    </select>

    <delete id="deleteDataInformationBydata_id" parameterType="Long">
        delete from s_data_information where data_id in
        <foreach item="data_id" collection="array" open="(" separator="," close=")">
            #{data_id}
        </foreach>
    </delete>
</mapper>