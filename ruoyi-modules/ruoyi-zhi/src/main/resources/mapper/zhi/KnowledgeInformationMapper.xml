<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhi.mapper.KnowledgeInformationMapper">
    
    <resultMap type="com.ruoyi.zhi.domain.KnowledgeInformation" id="KnowledgeInformationResult">
        <result property="id"    column="id"    />
        <result property="fileId"    column="file_id"    />
        <result property="prompt"    column="prompt"    />
        <result property="response"    column="response"    />
    </resultMap>

    <sql id="selectKnowledgeInformationVo">
        select id, file_id, prompt, response from s_knowledge_information
    </sql>

    <select id="selectKnowledgeInformationList" parameterType="com.ruoyi.zhi.domain.KnowledgeInformation" resultMap="KnowledgeInformationResult">
        <include refid="selectKnowledgeInformationVo"/>
        <where>  
            <if test="fileId != null "> and file_id = #{fileId}</if>
            <if test="prompt != null  and prompt != ''"> and prompt = #{prompt}</if>
            <if test="response != null  and response != ''"> and response = #{response}</if>
        </where>
    </select>
    
    <select id="selectKnowledgeInformationById" parameterType="Long" resultMap="KnowledgeInformationResult">
        <include refid="selectKnowledgeInformationVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertKnowledgeInformation" parameterType="com.ruoyi.zhi.domain.KnowledgeInformation" useGeneratedKeys="true" keyProperty="id">
        insert into s_knowledge_information
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileId != null">file_id,</if>
            <if test="prompt != null">prompt,</if>
            <if test="response != null">response,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileId != null">#{fileId},</if>
            <if test="prompt != null">#{prompt},</if>
            <if test="response != null">#{response},</if>
         </trim>
    </insert>
    <insert id="insertKnowledgeInformationList" useGeneratedKeys="true" keyProperty="id">
        insert into s_knowledge_information (file_id,prompt,response) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.fileId},#{item.prompt},#{item.response})
        </foreach>
    </insert>

    <update id="updateKnowledgeInformation" parameterType="com.ruoyi.zhi.domain.KnowledgeInformation">
        update s_knowledge_information
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileId != null">file_id = #{fileId},</if>
            <if test="prompt != null">prompt = #{prompt},</if>
            <if test="response != null">response = #{response},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKnowledgeInformationById" parameterType="Long">
        delete from s_knowledge_information where id = #{id}
    </delete>

    <delete id="deleteKnowledgeInformationByIds" parameterType="String">
        delete from s_knowledge_information where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>