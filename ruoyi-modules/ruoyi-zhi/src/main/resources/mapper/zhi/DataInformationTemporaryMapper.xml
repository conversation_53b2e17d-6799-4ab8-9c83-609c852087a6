<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhi.mapper.DataInformationTemporaryMapper">
    
    <resultMap type="com.ruoyi.zhi.domain.DataInformationTemporary" id="DataInformationTemporaryResult">
        <result property="id"    column="id"    />
        <result property="dataId"    column="data_id"    />
        <result property="prompt"    column="prompt"    />
        <result property="response"    column="response"    />
    </resultMap>

    <sql id="selectDataInformationTemporaryVo">
        select id, data_id, prompt, response from s_data_information_temporary
    </sql>

    <select id="selectDataInformationTemporaryList" parameterType="com.ruoyi.zhi.domain.DataInformationTemporary" resultMap="DataInformationTemporaryResult">
        <include refid="selectDataInformationTemporaryVo"/>
        <where>  
            <if test="dataId != null "> and data_id = #{dataId}</if>
            <if test="prompt != null  and prompt != ''"> and prompt = #{prompt}</if>
            <if test="response != null  and response != ''"> and response = #{response}</if>
        </where>
    </select>
    
    <select id="selectDataInformationTemporaryById" parameterType="Long" resultMap="DataInformationTemporaryResult">
        <include refid="selectDataInformationTemporaryVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertDataInformationTemporary" parameterType="com.ruoyi.zhi.domain.DataInformationTemporary" useGeneratedKeys="true" keyProperty="id">
        insert into s_data_information_temporary
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataId != null">data_id,</if>
            <if test="prompt != null">prompt,</if>
            <if test="response != null">response,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataId != null">#{dataId},</if>
            <if test="prompt != null">#{prompt},</if>
            <if test="response != null">#{response},</if>
         </trim>
    </insert>

    <update id="updateDataInformationTemporary" parameterType="com.ruoyi.zhi.domain.DataInformationTemporary">
        update s_data_information_temporary
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataId != null">data_id = #{dataId},</if>
            <if test="prompt != null">prompt = #{prompt},</if>
            <if test="response != null">response = #{response},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataInformationTemporaryById" parameterType="Long">
        delete from s_data_information_temporary where id = #{id}
    </delete>

    <delete id="deleteDataInformationTemporaryByIds" parameterType="String">
        delete from s_data_information_temporary where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectDataInformationTemporaryListByDataId" parameterType="Long" resultMap="DataInformationTemporaryResult">
        <include refid="selectDataInformationTemporaryVo"/>
        where data_id = #{id}
    </select>
</mapper>