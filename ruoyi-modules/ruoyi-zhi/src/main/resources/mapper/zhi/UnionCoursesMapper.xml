<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhi.mapper.UnionCoursesMapper">

    <resultMap type="com.ruoyi.zhi.domain.UnionCourses" id="UnionCoursesResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="schoolIds" column="school_ids"/>
        <result property="courseName" column="course_name"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <resultMap type="com.ruoyi.zhi.domain.KnowledgeBaseFile" id="KnowledgeBaseFileResult">
        <result property="id" column="id"/>
        <result property="fileId" column="file_id"/>
        <result property="fileName" column="file_name"/>
        <result property="kbId" column="kb_id"/>
        <result property="filePath" column="file_path"/>
        <result property="createdAt" column="created_at"/>
        <result property="indexingStatus" column="indexing_status"/>
        <result property="error" column="error"/>
        <result property="enabled" column="enabled"/>
        <result property="disabledAt" column="disabled_at"/>
        <result property="disabledBy" column="disabled_by"/>
        <result property="displayStatus" column="display_status"/>
        <result property="wordCount" column="word_count"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="majorId" column="major_id"/>
        <result property="courseName" column="course_name"/>
        <result property="disciplineId" column="discipline_id"/>
        <result property="parseStatus" column="parse_status"/>
        <result property="submitStatus" column="submit_status"/>
        <result property="parseFlag" column="parse_flag"/>
        <result property="isAllianceCourse" column="is_alliance_course"/>
        <result property="examineFlag" column="examineFlag"/>
        <result property="examineMessage" column="examine_message"/>
        <result property="createByNickname" column="nick_name"/>
        <result property="createByUniverName" column="univer_name"/>
    </resultMap>

    <resultMap type="com.ruoyi.zhi.vo.SystemUserVo" id="SysUserResult">
        <id property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="userName" column="user_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="email" column="email"/>
        <result property="phonenumber" column="phonenumber"/>
        <result property="sex" column="sex"/>
        <result property="avatar" column="avatar"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="loginIp" column="login_ip"/>
        <result property="loginDate" column="login_date"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="identityCard" column="identity_card"/>
        <result property="authStatus" column="auth_status"/>
        <result property="universityId" column="university_id"/>
        <result property="collegeId" column="college_id"/>
        <result property="majorId" column="major_id"/>
        <result property="classId" column="class_id"/>
        <result property="jobId" column="job_id"/>
        <result property="studentId" column="student_id"/>
        <result property="expTimeStart" column="exp_time_start"/>
        <result property="expTimeEnd" column="exp_time_end"/>
    </resultMap>

    <resultMap type="com.ruoyi.zhi.vo.AmbitTeacherVo" id="AmbitTeacherResult">
        <result property="id" column="id"/>
        <result property="majorId" column="major_id"/>
        <result property="disciplineId" column="discipline_id"/>
        <result property="name" column="name"/>
        <result property="sex" column="sex"/>
        <result property="title" column="title"/>
        <result property="education" column="education"/>
        <result property="researchDirection" column="research_direction"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="courseName" column="course_name"/>
        <result property="researchType" column="research_type"/>
        <result property="teacherId" column="teacher_id"/>
        <result property="colleId" column="colle_id"/>
        <result property="colleName" column="colle_name"/>
        <result property="univerId" column="univer_id"/>
        <result property="univerName" column="univer_name"/>
        <result property="isPersonCharge" column="is_person_charge"/>
        <result property="majorName" column="major_name"/>
        <result property="collegeId" column="college_id"/>
        <result property="collegeName" column="college_name"/>
    </resultMap>

    <sql id="selectUnionCoursesVo">
        select id,
               name,
               school_ids,
               course_name,
               create_by,
               create_time,
               update_by,
               update_time
        from s_union_courses
    </sql>

    <select id="selectUnionCoursesList" parameterType="com.ruoyi.zhi.domain.UnionCourses"
            resultMap="UnionCoursesResult">
        <include refid="selectUnionCoursesVo"/>
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="schoolIds != null ">and school_ids like concat('%', #{schoolIds}, '%')</if>
            <if test="courseName != null  and courseName != ''">and course_name like concat('%', #{courseName}, '%')
            </if>
        </where>
    </select>

    <select id="selectUnionCoursesById" parameterType="Long" resultMap="UnionCoursesResult">
        <include refid="selectUnionCoursesVo"/>
        where id = #{id}
    </select>
    <select id="selectNameList" resultType="java.lang.String">
        select name
        from s_union_courses
    </select>
    <select id="selectUnionCoursesListVO" resultType="com.ruoyi.zhi.vo.UnionCoursesVo">
        select id,
        name,
        school_ids,
        course_name,
        create_by,
        create_time,
        update_by,
        update_time
        from s_union_courses
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="schoolIds != null ">and school_ids like concat('%', #{schoolIds}, '%')</if>
            <if test="courseName != null  and courseName != ''">and course_name like concat('%', #{courseName}, '%')
            </if>
        </where>
    </select>
    <select id="selectUnionCoursesVoListAndCourseUser" resultType="com.ruoyi.zhi.vo.UnionCoursesVo">
        SELECT
        unionCourse.*,
        GROUP_CONCAT(DISTINCT university.univer_name) AS schoolNames,
        unionCourse.course_name AS courseName,
        GROUP_CONCAT(DISTINCT user.user_id) AS userIds,
        GROUP_CONCAT(DISTINCT user.nick_name) AS nickNames,
        GROUP_CONCAT(DISTINCT user.user_name) AS userNames
        FROM
        s_union_courses unionCourse
        LEFT JOIN s_knowledge_base_file skbf ON unionCourse.course_name = skbf.course_name
        LEFT JOIN sys_user user ON user.user_name = skbf.create_by AND user.del_flag = '0'
        LEFT JOIN s_university university ON FIND_IN_SET(university.id, unionCourse.school_ids) > 0
        <where>
            <if test="name != null and name != ''">AND unionCourse.name LIKE CONCAT('%', #{name}, '%')</if>
            <if test="schoolIds != null">AND unionCourse.school_ids LIKE CONCAT('%', #{schoolIds}, '%')</if>
            <if test="courseName != null and courseName != ''">AND unionCourse.course_name LIKE CONCAT('%',
                #{courseName}, '%')
            </if>
        </where>
        GROUP BY
        unionCourse.id,
        unionCourse.create_time
        ORDER BY
        unionCourse.create_time
    </select>


    <select id="selectUnionCoursesVoById" resultType="com.ruoyi.zhi.vo.UnionCoursesVo">
        SELECT unionCourse.*,
               GROUP_CONCAT(DISTINCT university.univer_name)               AS schoolNames,
               unionCourse.course_name                                     AS courseName,
               GROUP_CONCAT(DISTINCT user.user_id)                         AS userIds,
               GROUP_CONCAT(DISTINCT user.nick_name)                       AS nickNames,
               GROUP_CONCAT(DISTINCT user.user_name)                       AS userNames,
               GROUP_CONCAT(DISTINCT skbf.file_name)                       AS bookNames,
               GROUP_CONCAT(DISTINCT CONCAT(skbf.file_name, ':', skbf.id)) AS bookNameIdPairs,
               user.nick_name                                              AS createdByNickName
        FROM s_union_courses unionCourse
                 LEFT JOIN s_knowledge_base_file skbf
                           ON unionCourse.course_name = skbf.course_name AND skbf.file_name != ''
                 LEFT JOIN sys_user user ON user.user_name = skbf.create_by AND user.del_flag = '0'
                 LEFT JOIN s_university university ON FIND_IN_SET(university.id, unionCourse.school_ids) > 0

        WHERE unionCourse.id = #{id}
        GROUP BY unionCourse.id,
                 unionCourse.create_time
        ORDER BY unionCourse.create_time
    </select>

    <select id="selectUnionCourseTeacherList" resultType="com.ruoyi.zhi.vo.SystemUserVo">
        SELECT DISTINCT su.*
        FROM s_union_courses suc
        LEFT JOIN s_knowledge_base_file skbf ON skbf.course_name = suc.course_name
        LEFT JOIN sys_user su ON su.user_name = skbf.create_by AND su.del_flag = 0
        <where>
            suc.id = #{id}
            <if test="userName != null and userName !=''">AND su.user_name = #{userName}</if>
            <if test="nickName != null and nickName !=''">AND su.nick_name LIKE CONCAT('%',#{nickName},'%')</if>
        </where>
    </select>

    <select id="selectUnionCourseBook" resultType="com.ruoyi.zhi.domain.KnowledgeBaseFile">
        select skbf.*, su.nick_name AS createByNickname, uni.univer_name AS createByUniverName
        FROM s_knowledge_base_file skbf
                 LEFT JOIN sys_user su ON su.user_name = skbf.create_by AND su.del_flag = '0'
                 LEFT JOIN s_university uni ON uni.id = su.university_id
        WHERE skbf.id = #{keywordValue}
          AND skbf.file_name = #{keywordLabel}
    </select>
    <select id="selectUnionCoursesVoListAndCourseUserByConditionLoginUser"
            resultType="com.ruoyi.zhi.vo.UnionCoursesVo">
        SELECT
        suc.*,
        GROUP_CONCAT(DISTINCT suni.univer_name SEPARATOR ',') AS schoolNames
        FROM
        s_union_courses suc
        LEFT JOIN s_university suni ON FIND_IN_SET(suni.id, suc.school_ids)
        <where>
            (suc.create_by = #{loginUserId} OR FIND_IN_SET( #{loginUserUniversityId}, suc.school_ids ) > 0 )
            <if test="name != null and name != ''">
                AND suc.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="courseName != null and courseName != ''">
                AND suc.course_name LIKE CONCAT('%', #{courseName}, '%')
            </if>
        </where>
        GROUP BY
        suc.id
        <!--        SELECT-->
        <!--        suc.*,-->
        <!--        GROUP_CONCAT(DISTINCT suni.univer_name SEPARATOR ', ') AS schoolNames-->
        <!--        FROM-->
        <!--        s_union_courses suc-->
        <!--        JOIN-->
        <!--        s_university suni ON FIND_IN_SET(suni.id, suc.school_ids) > 0-->
        <!--        LEFT JOIN-->
        <!--        s_ambit_teacher sat ON sat.course_name = suc.course_name-->

        <!--        <where>-->
        <!--            (suc.create_by = #{loginUserId} OR FIND_IN_SET(sat.univer_id, suc.school_ids) > 0)-->
        <!--            <if test="name != null and name != ''">-->
        <!--                AND suc.name LIKE CONCAT('%', #{name}, '%')-->
        <!--            </if>-->
        <!--            <if test="courseName != null and courseName != ''">-->
        <!--                AND suc.course_name LIKE CONCAT('%', #{courseName}, '%')-->
        <!--            </if>-->
        <!--        </where>-->
        <!--        GROUP BY-->
        <!--        suc.id-->
    </select>
    <!--    <select id="selectUnionCoursesVoListAndCourseUserByConditionLoginUser"-->
    <!--            resultType="com.ruoyi.zhi.vo.UnionCoursesVo">-->
    <!--                SELECT-->
    <!--                uc.*,-->
    <!--                sUser.nick_name AS createdByNickName-->
    <!--                GROUP_CONCAT(DISTINCT university.univer_name) AS schoolNames,-->
    <!--                uc.course_name AS courseName,-->
    <!--                GROUP_CONCAT(DISTINCT user.user_id) AS userIds,-->
    <!--                GROUP_CONCAT(DISTINCT user.nick_name) AS nickNames,-->
    <!--                GROUP_CONCAT(DISTINCT user.user_name) AS userNames,-->
    <!--                FROM-->
    <!--                s_union_courses uc-->
    <!--                LEFT JOIN sys_user sUser ON sUser.user_id = uc.create_by AND sUser.del_flag = '0'-->
    <!--                LEFT JOIN s_knowledge_base_file skbf ON  skbf.course_name = uc.course_name AND skbf.is_alliance_course = 'N'-->
    <!--                LEFT JOIN sys_user user ON user.user_name = skbf.create_by AND user.del_flag = '0'-->
    <!--                LEFT JOIN s_university university ON FIND_IN_SET(university.id, uc.school_ids) > 0-->
    <!--                <where>-->
    <!--                    (uc.create_by = #{loginUserId} OR user.user_id = #{loginUserId})-->
    <!--                    <if test="name != null and name != ''">-->
    <!--                        AND uc.name LIKE CONCAT('%', #{name}, '%')-->
    <!--                    </if>-->
    <!--                    <if test="schoolIds != null">-->
    <!--                        AND uc.school_ids LIKE CONCAT('%', #{schoolIds}, '%')-->
    <!--                    </if>-->
    <!--                    <if test="courseName != null and courseName != ''">-->
    <!--                        AND uc.course_name LIKE CONCAT('%', #{courseName}, '%')-->
    <!--                    </if>-->
    <!--                </where>-->
    <!--                GROUP BY-->
    <!--                uc.id,-->
    <!--                uc.create_time-->
    <!--                ORDER BY-->
    <!--                uc.create_time-->
    <!--    </select>-->
    <select id="selectBooksByName" resultMap="KnowledgeBaseFileResult">
        SELECT *
        FROM s_knowledge_base_file
        WHERE file_name like concat('%', #{fileName}, '%')
    </select>
    <select id="selectUserList" resultType="com.ruoyi.zhi.vo.SystemUserVo">
        select *
        from sys_user
        where del_flag = '0'
          AND nick_name like concat('%', #{nickName}, '%')
    </select>
    <select id="selectAmbitTeacherByCourseName" resultMap="AmbitTeacherResult">
        SELECT sat.*
        FROM s_ambit_teacher sat
                 JOIN s_union_courses suc ON sat.course_name = suc.course_name
        WHERE sat.course_name = #{courseName}
          AND FIND_IN_SET(sat.univer_id, suc.school_ids) > 0

    </select>
    <select id="selectAmbitTeacherByCondition" resultType="com.ruoyi.zhi.vo.AmbitTeacherVo">
        SELECT sat.*,
        GROUP_CONCAT(DISTINCT stf.research_findings) AS researchFindings
        FROM s_ambit_teacher sat
        JOIN s_union_courses suc ON suc.course_name = sat.course_name
        LEFT JOIN s_teacher_findings stf ON stf.teacher_id = sat.id
        <where>
            suc.id = #{id}
            AND FIND_IN_SET(sat.univer_id, suc.school_ids) > 0
            <if test="keywordLabel != null and keywordLabel != ''">
                AND sat.id = #{keywordValue}
            </if>
            <if test="keywordLabel == 'teacherName'">
                AND sat.name = #{keywordLabel}
            </if>
        </where>
    </select>
    <select id="selectAmbitTeacherByUnionCourseId" resultType="com.ruoyi.zhi.vo.AmbitTeacherVo">
        SELECT sat.*,
               GROUP_CONCAT(stf.research_findings SEPARATOR ',') AS researchFindings
        FROM s_ambit_teacher sat
                 LEFT JOIN s_union_courses suc ON sat.course_name = suc.course_name
                 LEFT JOIN s_teacher_findings stf ON stf.teacher_id = sat.id
        WHERE suc.id = #{id}
          AND FIND_IN_SET(sat.univer_id, suc.school_ids) > 0
        GROUP BY sat.id
    </select>
    <select id="selectUnionCourseBookList" resultMap="KnowledgeBaseFileResult">
        SELECT skbf.*,
               su.nick_name,
               uni.univer_name
        FROM s_knowledge_base_file skbf
                 LEFT JOIN s_union_courses suc ON suc.course_name = skbf.course_name
                 LEFT JOIN sys_user su ON su.user_name = skbf.create_by AND su.del_flag = '0'
                 LEFT JOIN s_university uni ON uni.id = su.university_id
        WHERE suc.id = #{id}
          AND FIND_IN_SET(su.university_id, suc.school_ids) > 0
          AND skbf.file_name != ''
          AND skbf.file_name IS NOT NULL
        GROUP BY skbf.id
    </select>
    <select id="selectTeacherFindings" resultType="com.ruoyi.zhi.vo.TeacherFindingsVo">
        SELECT stf.id, stf.teacher_id, stf.research_findings
        FROM s_teacher_findings stf
        WHERE teacher_id = #{id}
    </select>
    <select id="selectUnionCoursesAndSchoolNames" resultType="com.ruoyi.zhi.vo.UnionCoursesVo">
        SELECT suc.*,
               GROUP_CONCAT(DISTINCT suni.univer_name SEPARATOR ', ') AS schoolNames
        FROM s_union_courses suc
                 JOIN
             s_university suni ON FIND_IN_SET(suni.id, suc.school_ids) > 0
        WHERE suc.id = #{id}
    </select>


    <insert id="insertUnionCourses" parameterType="com.ruoyi.zhi.domain.UnionCourses" useGeneratedKeys="true"
            keyProperty="id">
        insert into s_union_courses
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="schoolIds != null">school_ids,</if>
            <if test="courseName != null">course_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="schoolIds != null">#{schoolIds},</if>
            <if test="courseName != null">#{courseName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateUnionCourses" parameterType="com.ruoyi.zhi.domain.UnionCourses">
        update s_union_courses
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="schoolIds != null">school_ids = #{schoolIds},</if>
            <if test="courseName != null">course_name = #{courseName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUnionCoursesById" parameterType="Long">
        delete
        from s_union_courses
        where id = #{id}
    </delete>

    <delete id="deleteUnionCoursesByIds" parameterType="String">
        delete from s_union_courses where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
