<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhi.mapper.KnowledgeBaseMapper">
    
    <resultMap type="com.ruoyi.zhi.domain.KnowledgeBase" id="KnowledgeBaseResult">
        <result property="id"    column="id"    />
        <result property="kbId"    column="kb_id"    />
        <result property="kbName"    column="kb_name"    />
        <result property="isCustomProcessRule"    column="is_custom_process_rule"    />
        <result property="customProcessRule"    column="custom_process_rule"    />
        <result property="isEnhanced"    column="is_enhanced"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="menuRouting"    column="menu_routing"    />
    </resultMap>

    <sql id="selectKnowledgeBaseVo">
        select id, kb_id, kb_name, is_custom_process_rule, custom_process_rule, is_enhanced, create_by, create_time, update_by, update_time, menu_routing from s_knowledge_base
    </sql>

    <select id="selectKnowledgeBaseList" parameterType="com.ruoyi.zhi.domain.KnowledgeBase" resultMap="KnowledgeBaseResult">
        <include refid="selectKnowledgeBaseVo"/>
        <where>  
            <if test="kbId != null "> and kb_id = #{kbId}</if>
            <if test="kbName != null  and kbName != ''"> and kb_name like concat('%', #{kbName}, '%')</if>
            <if test="isCustomProcessRule != null "> and is_custom_process_rule = #{isCustomProcessRule}</if>
            <if test="customProcessRule != null  and customProcessRule != ''"> and custom_process_rule = #{customProcessRule}</if>
            <if test="isEnhanced != null "> and is_enhanced = #{isEnhanced}</if>
            <if test="menuRouting != null  and menuRouting != ''"> and menu_routing = #{menuRouting}</if>
        </where>
    </select>
    
    <select id="selectKnowledgeBaseById" parameterType="Long" resultMap="KnowledgeBaseResult">
        <include refid="selectKnowledgeBaseVo"/>
        where id = #{id}
    </select>
    <select id="selectKnowledgeBaseByIds" resultMap="KnowledgeBaseResult">
        <include refid="selectKnowledgeBaseVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertKnowledgeBase" parameterType="com.ruoyi.zhi.domain.KnowledgeBase" useGeneratedKeys="true" keyProperty="id">
        insert into s_knowledge_base
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="kbId != null">kb_id,</if>
            <if test="kbName != null">kb_name,</if>
            <if test="isCustomProcessRule != null">is_custom_process_rule,</if>
            <if test="customProcessRule != null">custom_process_rule,</if>
            <if test="isEnhanced != null">is_enhanced,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="menuRouting != null">menu_routing,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="kbId != null">#{kbId},</if>
            <if test="kbName != null">#{kbName},</if>
            <if test="isCustomProcessRule != null">#{isCustomProcessRule},</if>
            <if test="customProcessRule != null">#{customProcessRule},</if>
            <if test="isEnhanced != null">#{isEnhanced},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="menuRouting != null">#{menuRouting},</if>
         </trim>
    </insert>

    <update id="updateKnowledgeBase" parameterType="com.ruoyi.zhi.domain.KnowledgeBase">
        update s_knowledge_base
        <trim prefix="SET" suffixOverrides=",">
            <if test="kbId != null">kb_id = #{kbId},</if>
            <if test="kbName != null">kb_name = #{kbName},</if>
            <if test="isCustomProcessRule != null">is_custom_process_rule = #{isCustomProcessRule},</if>
            <if test="customProcessRule != null">custom_process_rule = #{customProcessRule},</if>
            <if test="isEnhanced != null">is_enhanced = #{isEnhanced},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="menuRouting != null">menu_routing = #{menuRouting},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKnowledgeBaseById" parameterType="Long">
        delete from s_knowledge_base where id = #{id}
    </delete>

    <delete id="deleteKnowledgeBaseByIds" parameterType="String">
        delete from s_knowledge_base where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>