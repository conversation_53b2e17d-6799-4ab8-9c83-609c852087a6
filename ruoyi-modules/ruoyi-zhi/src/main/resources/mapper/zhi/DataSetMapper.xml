<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhi.mapper.DataSetMapper">
    
    <resultMap type="com.ruoyi.zhi.domain.DataSet" id="DataSetResult">
        <result property="id"    column="id"    />
        <result property="groupPk"    column="group_pk"    />
        <result property="datasetId"    column="dataset_id"    />
        <result property="groupName"    column="group_name"    />
        <result property="displayName"    column="display_name"    />
        <result property="versionId"    column="version_id"    />
        <result property="userId"    column="user_id"    />
        <result property="dataType"    column="data_type"    />
        <result property="projectType"    column="project_type"    />
        <result property="templateType"    column="template_type"    />
        <result property="storageId"    column="storage_id"    />
        <result property="storagePath"    column="storage_path"    />
        <result property="storageName"    column="storage_name"    />
        <result property="rawStoragePath"    column="raw_storage_path"    />
        <result property="region"    column="region"    />
        <result property="importProgress"    column="import_progress"    />
        <result property="releaseStatus"    column="release_status"    />
        <result property="etlStatus"    column="etl_status"    />
        <result property="entityCount"    column="entity_count"    />
        <result property="annotatedEntityCount"    column="annotated_entity_count"    />
        <result property="labelCount"    column="label_count"    />
        <result property="characterCount"    column="character_count"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="menuRouting"    column="menu_routing"    />
        <result property="courseName"    column="course_name"    />
    </resultMap>

    <sql id="selectDataSetVo">
        select id, group_pk, dataset_id, group_name, display_name, version_id, user_id, data_type, project_type, template_type, storage_id, storage_path, storage_name, raw_storage_path, region, import_progress, release_status, etl_status, entity_count, annotated_entity_count, label_count, character_count, create_by, create_time, update_by, update_time,menu_routing, course_name from s_data_set
    </sql>

    <select id="selectDataSetList" parameterType="com.ruoyi.zhi.domain.DataSet" resultMap="DataSetResult">
        <include refid="selectDataSetVo"/>
        <where>  
            <if test="groupPk != null  and groupPk != ''"> and group_pk = #{groupPk}</if>
            <if test="datasetId != null  and datasetId != ''"> and dataset_id = #{datasetId}</if>
            <if test="groupName != null  and groupName != ''"> and group_name like concat('%', #{groupName}, '%')</if>
            <if test="displayName != null  and displayName != ''"> and display_name like concat('%', #{displayName}, '%')</if>
            <if test="versionId != null "> and version_id = #{versionId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="dataType != null "> and data_type = #{dataType}</if>
            <if test="projectType != null "> and project_type = #{projectType}</if>
            <if test="templateType != null "> and template_type = #{templateType}</if>
            <if test="storageId != null  and storageId != ''"> and storage_id = #{storageId}</if>
            <if test="storagePath != null  and storagePath != ''"> and storage_path = #{storagePath}</if>
            <if test="storageName != null  and storageName != ''"> and storage_name like concat('%', #{storageName}, '%')</if>
            <if test="rawStoragePath != null  and rawStoragePath != ''"> and raw_storage_path = #{rawStoragePath}</if>
            <if test="region != null  and region != ''"> and region = #{region}</if>
            <if test="importProgress != null "> and import_progress = #{importProgress}</if>
            <if test="releaseStatus != null "> and release_status = #{releaseStatus}</if>
            <if test="etlStatus != null "> and etl_status = #{etlStatus}</if>
            <if test="entityCount != null "> and entity_count = #{entityCount}</if>
            <if test="annotatedEntityCount != null "> and annotated_entity_count = #{annotatedEntityCount}</if>
            <if test="labelCount != null "> and label_count = #{labelCount}</if>
            <if test="characterCount != null "> and character_count = #{characterCount}</if>
            <if test="menuRouting != null "> and menu_routing = #{menuRouting}</if>
            <if test="createBy != null "> and create_by = #{createBy}</if>
            <if test="courseName != null "> and course_name = #{courseName}</if>
        </where>
        order by course_name,create_time Desc
    </select>
    
    <select id="selectDataSetById" parameterType="Long" resultMap="DataSetResult">
        <include refid="selectDataSetVo"/>
        where id = #{id}
    </select>
    <select id="selectDataSetAll" parameterType="com.ruoyi.zhi.domain.DataSet" resultMap="DataSetResult">
        <include refid="selectDataSetVo"/>
        <where>
            <if test="menuRouting != null "> and menu_routing = #{menuRouting}</if>
            <if test="createBy != null "> and create_by = #{createBy}</if>
        </where>
    </select>

    <insert id="insertDataSet" parameterType="com.ruoyi.zhi.domain.DataSet">
        insert into s_data_set
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,
            <if test="groupPk != null">group_pk,</if>
            <if test="datasetId != null">dataset_id,</if>
            <if test="groupName != null">group_name,</if>
            <if test="displayName != null">display_name,</if>
            <if test="versionId != null">version_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="dataType != null">data_type,</if>
            <if test="projectType != null">project_type,</if>
            <if test="templateType != null">template_type,</if>
            <if test="storageId != null">storage_id,</if>
            <if test="storagePath != null">storage_path,</if>
            <if test="storageName != null">storage_name,</if>
            <if test="rawStoragePath != null">raw_storage_path,</if>
            <if test="region != null">region,</if>
            <if test="importProgress != null">import_progress,</if>
            <if test="releaseStatus != null">release_status,</if>
            <if test="etlStatus != null">etl_status,</if>
            <if test="entityCount != null">entity_count,</if>
            <if test="annotatedEntityCount != null">annotated_entity_count,</if>
            <if test="labelCount != null">label_count,</if>
            <if test="characterCount != null">character_count,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="menuRouting != null">menu_routing,</if>
            <if test="courseName != null">course_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id},
            <if test="groupPk != null">#{groupPk},</if>
            <if test="datasetId != null">#{datasetId},</if>
            <if test="groupName != null">#{groupName},</if>
            <if test="displayName != null">#{displayName},</if>
            <if test="versionId != null">#{versionId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="dataType != null">#{dataType},</if>
            <if test="projectType != null">#{projectType},</if>
            <if test="templateType != null">#{templateType},</if>
            <if test="storageId != null">#{storageId},</if>
            <if test="storagePath != null">#{storagePath},</if>
            <if test="storageName != null">#{storageName},</if>
            <if test="rawStoragePath != null">#{rawStoragePath},</if>
            <if test="region != null">#{region},</if>
            <if test="importProgress != null">#{importProgress},</if>
            <if test="releaseStatus != null">#{releaseStatus},</if>
            <if test="etlStatus != null">#{etlStatus},</if>
            <if test="entityCount != null">#{entityCount},</if>
            <if test="annotatedEntityCount != null">#{annotatedEntityCount},</if>
            <if test="labelCount != null">#{labelCount},</if>
            <if test="characterCount != null">#{characterCount},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="menuRouting != null">#{menuRouting},</if>
            <if test="courseName != null">#{courseName},</if>
         </trim>
    </insert>

    <update id="updateDataSet" parameterType="com.ruoyi.zhi.domain.DataSet">
        update s_data_set
        <trim prefix="SET" suffixOverrides=",">
            <if test="groupPk != null">group_pk = #{groupPk},</if>
            <if test="datasetId != null">dataset_id = #{datasetId},</if>
            <if test="groupName != null">group_name = #{groupName},</if>
            <if test="displayName != null">display_name = #{displayName},</if>
            <if test="versionId != null">version_id = #{versionId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="dataType != null">data_type = #{dataType},</if>
            <if test="projectType != null">project_type = #{projectType},</if>
            <if test="templateType != null">template_type = #{templateType},</if>
            <if test="storageId != null">storage_id = #{storageId},</if>
            <if test="storagePath != null">storage_path = #{storagePath},</if>
            <if test="storageName != null">storage_name = #{storageName},</if>
            <if test="rawStoragePath != null">raw_storage_path = #{rawStoragePath},</if>
            <if test="region != null">region = #{region},</if>
            <if test="importProgress != null">import_progress = #{importProgress},</if>
            <if test="releaseStatus != null">release_status = #{releaseStatus},</if>
            <if test="etlStatus != null">etl_status = #{etlStatus},</if>
            <if test="entityCount != null">entity_count = #{entityCount},</if>
            <if test="annotatedEntityCount != null">annotated_entity_count = #{annotatedEntityCount},</if>
            <if test="labelCount != null">label_count = #{labelCount},</if>
            <if test="characterCount != null">character_count = #{characterCount},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="menuRouting != null">menu_routing = #{menuRouting},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataSetById" parameterType="Long">
        delete from s_data_set where id = #{id}
    </delete>

    <delete id="deleteDataSetByIds" parameterType="String">
        delete from s_data_set where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectDataSetPublished" parameterType="com.ruoyi.zhi.domain.DataSet" resultMap="DataSetResult">
        <include refid="selectDataSetVo"/>
        <where>
            <if test="menuRouting != null "> and menu_routing = #{menuRouting}</if>
            <if test="createBy != null "> and create_by = #{createBy}</if>
            <if test="releaseStatus != null "> and release_status = #{releaseStatus}</if>
        </where>
    </select>

    <select id="selectdataset_idByIds" parameterType="String" resultMap="DataSetResult">
        <include refid="selectDataSetVo"/> where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>