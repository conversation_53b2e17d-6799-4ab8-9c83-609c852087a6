<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhi.mapper.TextbookDataTemporaryMapper">
    
    <resultMap type="com.ruoyi.zhi.domain.TextbookDataTemporary" id="TextbookDataTemporaryResult">
        <result property="id"    column="id"    />
        <result property="publishingHouse"    column="publishing_house"    />
        <result property="author"    column="author"    />
    </resultMap>

    <sql id="selectTextbookDataTemporaryVo">
        select id, publishing_house, author from s_textbook_data_temporary
    </sql>

    <select id="selectTextbookDataTemporaryList" parameterType="com.ruoyi.zhi.domain.TextbookDataTemporary" resultMap="TextbookDataTemporaryResult">
        <include refid="selectTextbookDataTemporaryVo"/>
        <where>  
            <if test="publishingHouse != null  and publishingHouse != ''"> and publishing_house = #{publishingHouse}</if>
            <if test="author != null  and author != ''"> and author = #{author}</if>
        </where>
    </select>
    
    <select id="selectTextbookDataTemporaryById" parameterType="Long" resultMap="TextbookDataTemporaryResult">
        <include refid="selectTextbookDataTemporaryVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTextbookDataTemporary" parameterType="com.ruoyi.zhi.domain.TextbookDataTemporary">
        insert into s_textbook_data_temporary
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="publishingHouse != null">publishing_house,</if>
            <if test="author != null">author,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="publishingHouse != null">#{publishingHouse},</if>
            <if test="author != null">#{author},</if>
         </trim>
    </insert>

    <update id="updateTextbookDataTemporary" parameterType="com.ruoyi.zhi.domain.TextbookDataTemporary">
        update s_textbook_data_temporary
        <trim prefix="SET" suffixOverrides=",">
            <if test="publishingHouse != null">publishing_house = #{publishingHouse},</if>
            <if test="author != null">author = #{author},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTextbookDataTemporaryById" parameterType="Long">
        delete from s_textbook_data_temporary where id = #{id}
    </delete>

    <delete id="deleteTextbookDataTemporaryByIds" parameterType="String">
        delete from s_textbook_data_temporary where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>