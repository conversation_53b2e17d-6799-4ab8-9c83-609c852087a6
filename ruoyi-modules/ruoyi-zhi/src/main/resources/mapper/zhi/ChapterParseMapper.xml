<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhi.mapper.ChapterParseMapper">
    
    <resultMap type="com.ruoyi.zhi.domain.ChapterParse" id="ChapterParseResult">
        <result property="id"    column="id"    />
        <result property="textbookId"    column="textbook_id"    />
        <result property="chapter"    column="chapter"    />
        <result property="parseStatus"    column="parse_status"    />
    </resultMap>

    <sql id="selectChapterParseVo">
        select id, textbook_id, chapter, parse_status from s_chapter_parse
    </sql>

    <select id="selectChapterParseList" parameterType="com.ruoyi.zhi.domain.ChapterParse" resultMap="ChapterParseResult">
        <include refid="selectChapterParseVo"/>
        <where>  
            <if test="textbookId != null "> and textbook_id = #{textbookId}</if>
            <if test="chapter != null  and chapter != ''"> and chapter = #{chapter}</if>
            <if test="parseStatus != null  and parseStatus != ''"> and parse_status = #{parseStatus}</if>
        </where>
    </select>
    
    <select id="selectChapterParseById" parameterType="Long" resultMap="ChapterParseResult">
        <include refid="selectChapterParseVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertChapterParse" parameterType="com.ruoyi.zhi.domain.ChapterParse">
        insert into s_chapter_parse
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="textbookId != null">textbook_id,</if>
            <if test="chapter != null">chapter,</if>
            <if test="parseStatus != null">parse_status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="textbookId != null">#{textbookId},</if>
            <if test="chapter != null">#{chapter},</if>
            <if test="parseStatus != null">#{parseStatus},</if>
         </trim>
    </insert>

    <update id="updateChapterParse" parameterType="com.ruoyi.zhi.domain.ChapterParse">
        update s_chapter_parse
        <trim prefix="SET" suffixOverrides=",">
            <if test="textbookId != null">textbook_id = #{textbookId},</if>
            <if test="chapter != null">chapter = #{chapter},</if>
            <if test="parseStatus != null">parse_status = #{parseStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteChapterParseById" parameterType="Long">
        delete from s_chapter_parse where id = #{id}
    </delete>

    <delete id="deleteChapterParseByIds" parameterType="String">
        delete from s_chapter_parse where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>