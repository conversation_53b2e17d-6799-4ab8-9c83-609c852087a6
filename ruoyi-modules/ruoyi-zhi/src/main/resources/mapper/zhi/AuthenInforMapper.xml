<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhi.mapper.AuthenInforMapper">

    <resultMap type="com.ruoyi.zhi.domain.AuthenInfor" id="AuthenInforResult">
        <result property="id"    column="id"    />
        <result property="menuRouting"    column="menu_routing"    />
        <result property="apiKey"    column="api_key"    />
        <result property="secretKey"    column="secret_key"    />
        <result property="apiUrl"    column="api_url"    />
        <result property="ak"    column="ak"    />
        <result property="sk"    column="sk"    />
        <result property="domainName"    column="domain_name"    />
        <result property="bosBucketName"    column="bos_bucket_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remarks"    column="remarks"    />
    </resultMap>

    <sql id="selectAuthenInforVo">
        select id, menu_routing, api_key, secret_key, api_url, ak, sk, domain_name, bos_bucket_name, create_by, create_time, update_by, update_time,remarks from s_authen_infor
    </sql>

    <select id="selectAuthenInforList" parameterType="com.ruoyi.zhi.domain.AuthenInfor" resultMap="AuthenInforResult">
        <include refid="selectAuthenInforVo"/>
        <where>
            <if test="menuRouting != null  and menuRouting != ''"> and menu_routing = #{menuRouting}</if>
            <if test="apiKey != null  and apiKey != ''"> and api_key = #{apiKey}</if>
            <if test="secretKey != null  and secretKey != ''"> and secret_key = #{secretKey}</if>
            <if test="apiUrl != null  and apiUrl != ''"> and api_url = #{apiUrl}</if>
            <if test="ak != null  and ak != ''"> and ak = #{ak}</if>
            <if test="sk != null  and sk != ''"> and sk = #{sk}</if>
            <if test="domainName != null  and domainName != ''"> and domain_name like concat('%', #{domainName}, '%')</if>
            <if test="bosBucketName != null  and bosBucketName != ''"> and bos_bucket_name like concat('%', #{bosBucketName}, '%')</if>
        </where>
    </select>

    <select id="selectAuthenInforById" parameterType="Long" resultMap="AuthenInforResult">
        <include refid="selectAuthenInforVo"/>
        where id = #{id}
    </select>

    <insert id="insertAuthenInfor" parameterType="com.ruoyi.zhi.domain.AuthenInfor" useGeneratedKeys="true" keyProperty="id">
        insert into s_authen_infor
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="menuRouting != null">menu_routing,</if>
            <if test="apiKey != null">api_key,</if>
            <if test="secretKey != null">secret_key,</if>
            <if test="apiUrl != null">api_url,</if>
            <if test="ak != null">ak,</if>
            <if test="sk != null">sk,</if>
            <if test="domainName != null">domain_name,</if>
            <if test="bosBucketName != null">bos_bucket_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remarks != null">remarks,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="menuRouting != null">#{menuRouting},</if>
            <if test="apiKey != null">#{apiKey},</if>
            <if test="secretKey != null">#{secretKey},</if>
            <if test="apiUrl != null">#{apiUrl},</if>
            <if test="ak != null">#{ak},</if>
            <if test="sk != null">#{sk},</if>
            <if test="domainName != null">#{domainName},</if>
            <if test="bosBucketName != null">#{bosBucketName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remarks != null">#{remarks},</if>
        </trim>
    </insert>

    <update id="updateAuthenInfor" parameterType="com.ruoyi.zhi.domain.AuthenInfor">
        update s_authen_infor
        <trim prefix="SET" suffixOverrides=",">
            <if test="menuRouting != null">menu_routing = #{menuRouting},</if>
            <if test="apiKey != null">api_key = #{apiKey},</if>
            <if test="secretKey != null">secret_key = #{secretKey},</if>
            <if test="apiUrl != null">api_url = #{apiUrl},</if>
            <if test="ak != null">ak = #{ak},</if>
            <if test="sk != null">sk = #{sk},</if>
            <if test="domainName != null">domain_name = #{domainName},</if>
            <if test="bosBucketName != null">bos_bucket_name = #{bosBucketName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAuthenInforById" parameterType="Long">
        delete from s_authen_infor where id = #{id}
    </delete>

    <delete id="deleteAuthenInforByIds" parameterType="String">
        delete from s_authen_infor where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="countMenuRouting" parameterType="String" resultType="int">
        select count(1)   from s_authen_infor     where menu_routing = #{menuRouting}
    </select>

    <select id="countUpdateMenuRouting" parameterType="com.ruoyi.zhi.domain.AuthenInfor" resultType="int">
        select count(1)   from s_authen_infor     where menu_routing = #{menuRouting} and id != #{id}
    </select>

    <select id="selectAuthenInforListByIds" parameterType="String" resultMap="AuthenInforResult">
        <include refid="selectAuthenInforVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectAuthenInforAll"  resultMap="AuthenInforResult">
        <include refid="selectAuthenInforVo"/>
    </select>
</mapper>