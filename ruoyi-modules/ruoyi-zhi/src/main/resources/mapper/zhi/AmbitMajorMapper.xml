<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.zhi.mapper.AmbitMajorMapper">

    <resultMap type="com.ruoyi.zhi.domain.AmbitMajor" id="AmbitMajorResult">
        <result property="id"    column="id"    />
        <result property="collegeId"    column="college_id"    />
        <result property="collegeName"    column="college_name"    />
        <result property="majorId"    column="major_id"    />
        <result property="majorName"    column="major_name"    />
        <result property="disciplineId"    column="discipline_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="univerName"    column="univer_name"    />
        <result property="univerId"    column="univer_id"    />
    </resultMap>

    <sql id="selectAmbitMajorVo">
        select id,college_id,college_name,major_id, major_name, discipline_id, create_by, create_time, update_by, update_time,univer_name,univer_id from s_ambit_major
    </sql>

    <select id="selectAmbitMajorList" parameterType="com.ruoyi.zhi.domain.AmbitMajor" resultMap="AmbitMajorResult">
        select distinct univer_id,univer_name,college_id,college_name, major_id,major_name, discipline_id, create_by, create_time from s_ambit_major
        <where>
            <if test="majorName != null  and majorName != ''"> and major_name like concat('%', #{majorName}, '%')</if>
            <if test="disciplineId != null "> and discipline_id = #{disciplineId}</if>
            <if test="univerId != null "> and univer_id = #{univerId}</if>
        </where>
    </select>

    <select id="selectAmbitMajorById" parameterType="Long" resultMap="AmbitMajorResult">
        <include refid="selectAmbitMajorVo"/>
        where id = #{id}
    </select>
    <select id="selectAmbitMajorByMajorId" parameterType="Long" resultMap="AmbitMajorResult">
        select distinct college_name,major_name, discipline_id,univer_name from s_ambit_major
        where major_id = #{id}
    </select>
    <insert id="insertAmbitMajor" parameterType="com.ruoyi.zhi.domain.AmbitMajor" useGeneratedKeys="true" keyProperty="id">
        insert into s_ambit_major
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="collegeName != null">college_name,</if>
            <if test="majorName != null">major_name,</if>
            <if test="majorId != null">major_id,</if>
            <if test="collegeId != null">college_id,</if>
            <if test="disciplineId != null">discipline_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="univerName != null">univer_name,</if>
            <if test="univerId != null">univer_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="collegeName != null">#{collegeName},</if>
            <if test="majorName != null">#{majorName},</if>
            <if test="majorId != null">#{majorId},</if>
            <if test="collegeId != null">#{collegeId},</if>
            <if test="disciplineId != null">#{disciplineId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="univerName != null">#{univerName},</if>
            <if test="univerId != null">#{univerId},</if>
         </trim>
    </insert>

    <update id="updateAmbitMajor" parameterType="com.ruoyi.zhi.domain.AmbitMajor">
        update s_ambit_major
        <trim prefix="SET" suffixOverrides=",">
            <if test="majorName != null">major_name = #{majorName},</if>
            <if test="collegeName != null">college_name = #{collegeName},</if>
            <if test="majorId != null">major_id = #{majorId},</if>
            <if test="collegeId != null">college_id = #{collegeId},</if>
            <if test="disciplineId != null">discipline_id = #{disciplineId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="univerId != null">univer_id = #{univerId},</if>
            <if test="univerName != null">univer_name = #{univerName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAmbitMajorById" parameterType="Long">
        delete from s_ambit_major where id = #{id}
    </delete>

    <delete id="deleteAmbitMajorByIds" parameterType="String">
        delete from s_ambit_major where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="countAmbitMajor" parameterType="com.ruoyi.zhi.domain.AmbitMajor" resultType="int">
       select count(1) from s_ambit_major
        where discipline_id = #{disciplineId} and major_id = #{majorId}
    </select>

    <select id="selectCollegeList" parameterType="Long" resultMap="AmbitMajorResult">
        select  college_id,college_name,create_by,univer_name,univer_id from s_ambit_major
        <where>
            <if test="collegeName != null  and collegeName != ''"> and college_name like concat('%', #{collegeName}, '%')</if>
            <if test="disciplineId != null "> and discipline_id = #{disciplineId}</if>
            <if test="univerId != null "> and univer_id = #{univerId}</if>
        </where>
        GROUP BY  college_id,college_name
    </select>
    <select id="selectUniverList" parameterType="Long" resultMap="AmbitMajorResult">
        select  univer_id,univer_name,create_by from s_ambit_major
        <where>
            <if test="majorName != null  and majorName != ''"> and major_name like concat('%', #{majorName}, '%')</if>
            <if test="disciplineId != null "> and discipline_id = #{disciplineId}</if>
            <if test="univerId != null "> and univer_id = #{univerId}</if>
        </where>
        GROUP BY  univer_id,univer_name
    </select>
</mapper>
