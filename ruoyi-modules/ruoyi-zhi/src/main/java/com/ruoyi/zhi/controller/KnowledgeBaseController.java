package com.ruoyi.zhi.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.zhi.domain.FileVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.zhi.domain.KnowledgeBase;
import com.ruoyi.zhi.service.IKnowledgeBaseService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 知识库信息Controller
 * 
 * <AUTHOR>
 * @date 2024-06-19
 */
@RestController
@RequestMapping("/base")
public class KnowledgeBaseController extends BaseController
{
    @Autowired
    private IKnowledgeBaseService knowledgeBaseService;

    /**
     * 查询知识库信息列表
     */
    @RequiresPermissions("create:base:list")
    @GetMapping("/list")
    public TableDataInfo list(KnowledgeBase knowledgeBase)
    {
        startPage();
        List<KnowledgeBase> list = knowledgeBaseService.selectKnowledgeBaseList(knowledgeBase);
        return getDataTable(list);
    }

    /**
     * 导出知识库信息列表
     */
    @RequiresPermissions("create:base:export")
    @Log(title = "知识库信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KnowledgeBase knowledgeBase)
    {
        List<KnowledgeBase> list = knowledgeBaseService.selectKnowledgeBaseList(knowledgeBase);
        ExcelUtil<KnowledgeBase> util = new ExcelUtil<KnowledgeBase>(KnowledgeBase.class);
        util.exportExcel(response, list, "知识库信息数据");
    }

    /**
     * 获取知识库信息详细信息
     */
    @RequiresPermissions("create:base:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(knowledgeBaseService.selectKnowledgeBaseById(id));
    }

    /**
     * 新增知识库信息
     */
    @RequiresPermissions("create:base:add")
    @Log(title = "知识库信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KnowledgeBase knowledgeBase)
    {
        return toAjax(knowledgeBaseService.insertKnowledgeBase(knowledgeBase));
    }

    /**
     * 修改知识库信息
     */
    @RequiresPermissions("create:base:edit")
    @Log(title = "知识库信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KnowledgeBase knowledgeBase)
    {
        return toAjax(knowledgeBaseService.updateKnowledgeBase(knowledgeBase));
    }

    /**
     * 删除知识库信息
     */
    @RequiresPermissions("create:base:remove")
    @Log(title = "知识库信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(knowledgeBaseService.deleteKnowledgeBaseByIds(ids));
    }
    /**
     * 导入知识库信息文件
     */
    @RequiresPermissions("create:base:importFile")
    @Log(title = "导入知识库文件", businessType = BusinessType.INSERT)
    @PostMapping("/importFile")
    public AjaxResult importFile(@RequestBody KnowledgeBase knowledgeBase)
    {
        return knowledgeBaseService.importKnowledgeBaseFile(knowledgeBase);
    }

}
