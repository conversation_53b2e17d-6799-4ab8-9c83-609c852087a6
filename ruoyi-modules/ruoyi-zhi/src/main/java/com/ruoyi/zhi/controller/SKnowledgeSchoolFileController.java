package com.ruoyi.zhi.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.zhi.domain.SKnowledgeSchoolFile;
import com.ruoyi.zhi.service.ISKnowledgeSchoolFileService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 知识库文件Controller
 *
 * <AUTHOR>
 * @date 2024-11-28
 */
@RestController
@RequestMapping("/schoolfile")
public class SKnowledgeSchoolFileController extends BaseController
{
    @Autowired
    private ISKnowledgeSchoolFileService sKnowledgeSchoolFileService;

    /**
     * 查询知识库文件列表
     */
   // @RequiresPermissions("zhi:schoolfile:list")
    @GetMapping("/list")
    public TableDataInfo list(SKnowledgeSchoolFile sKnowledgeSchoolFile)
    {
        startPage();
        List<SKnowledgeSchoolFile> list = sKnowledgeSchoolFileService.selectSKnowledgeSchoolFileList(sKnowledgeSchoolFile);
        return getDataTable(list);
    }

    /**
     * 导出知识库文件列表
     */
   // @RequiresPermissions("zhi:schoolfile:export")
    @Log(title = "知识库文件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SKnowledgeSchoolFile sKnowledgeSchoolFile)
    {
        List<SKnowledgeSchoolFile> list = sKnowledgeSchoolFileService.selectSKnowledgeSchoolFileList(sKnowledgeSchoolFile);
        ExcelUtil<SKnowledgeSchoolFile> util = new ExcelUtil<SKnowledgeSchoolFile>(SKnowledgeSchoolFile.class);
        util.exportExcel(response, list, "知识库文件数据");
    }

    /**
     * 获取知识库文件详细信息
     */
   // @RequiresPermissions("zhi:schoolfile:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sKnowledgeSchoolFileService.selectSKnowledgeSchoolFileById(id));
    }

    /**
     * 新增知识库文件
     */
  //  @RequiresPermissions("zhi:schoolfile:add")
    @Log(title = "知识库文件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SKnowledgeSchoolFile sKnowledgeSchoolFile)
    {
        return toAjax(sKnowledgeSchoolFileService.insertSKnowledgeSchoolFile(sKnowledgeSchoolFile));
    }

    /**
     * 修改知识库文件
     */
 //   @RequiresPermissions("zhi:schoolfile:edit")
    @Log(title = "知识库文件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SKnowledgeSchoolFile sKnowledgeSchoolFile)
    {
        return toAjax(sKnowledgeSchoolFileService.updateSKnowledgeSchoolFile(sKnowledgeSchoolFile));
    }

    /**
     * 删除知识库文件
     */
//    @RequiresPermissions("zhi:schoolfile:remove")
    @Log(title = "知识库文件", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sKnowledgeSchoolFileService.deleteSKnowledgeSchoolFileByIds(ids));
    }
}
