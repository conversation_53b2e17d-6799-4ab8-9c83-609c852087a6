package com.ruoyi.zhi.polling;

import com.baidubce.http.ApiExplorerClient;
import com.baidubce.http.HttpMethodName;
import com.baidubce.model.ApiExplorerRequest;
import com.baidubce.model.ApiExplorerResponse;
import com.baidubce.util.JsonUtils;
import com.ruoyi.baidu.api.BaiduApiService;
import com.ruoyi.baidu.api.dto.BaiduDto;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.zhi.domain.ModelTraining;
import com.ruoyi.zhi.domain.NacosClient;
import com.ruoyi.zhi.mapper.ModelTrainingMapper;
import com.ruoyi.zhi.service.IAuthenInforService;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@Component
@RefreshScope
public class StudentStartupRunner implements CommandLineRunner {

    @Resource
    private ModelTrainingMapper modelTrainingMapper;

    @Autowired
    private IAuthenInforService authenInforService;

    @Autowired
    private BaiduApiService baiduApiService;


//    //个人
//    @Value("${dmx.ak}")
//    private String ak;
//
//    @Value("${dmx.sk}")
//    private String sk;
//
//    //企业
//    @Value("${dmxqy.ak}")
//    private String qyak;
//
//    @Value("${dmxqy.sk}")
//    private String qysk;

    /**
     * 系统启动自启-轮询执行训练任务
     *
     * @param args
     * @throws Exception
     */
    @Override
    public void run(String... args) throws Exception {
        //循环执行训练任务
        try {
            System.out.println("===========================");
            polling();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    private void polling() throws InterruptedException {
        try {
            while (true) { // 无限循环
                List<ModelTraining> menuRoutingList = modelTrainingMapper.selectDisMenuRouting();
                menuRoutingList.stream().forEach(modelTraining -> {

                    //查看是否有正在进行的训练任务
                    ModelTraining runningModelTraining = modelTrainingMapper.selectModelTrainingCarry(modelTraining);
                    if (com.ruoyi.common.core.utils.StringUtils.isNotNull(runningModelTraining)) {
                        //存在未完成训练任务
                        try {
                            incomplete(runningModelTraining);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    } else {
                        //不存在未完成训练任务
                        try {
                            completed(modelTraining);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    }

                });
                Thread.sleep(50000); // 50秒
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    /**
     * 不存在未完成训练任务
     *
     * @throws InterruptedException
     */
    private void completed(ModelTraining modelTraining) throws InterruptedException {
        //查询是否有需要执行的训练任务
        try {
            ModelTraining lineModelTraining = modelTrainingMapper.selectModelTraining(modelTraining);
            if (com.ruoyi.common.core.utils.StringUtils.isNotNull(lineModelTraining)) {
                //默认增量训练,查询最新一次训练完成的训练任务
                ModelTraining doneModelTraining = modelTrainingMapper.selectModelTrainingByIdDone(modelTraining);
                if(Objects.isNull(doneModelTraining)){
                    return;
                }
                //设置训练作业id
                lineModelTraining.setJobId(doneModelTraining.getJobId());
                //设置模型版本id
                lineModelTraining.setModelId(doneModelTraining.getModelId());
                //设置归属模型id
                lineModelTraining.setModelSetId(doneModelTraining.getModelSetId());
                //将上一次训练任务id作为本次训练基础任务id
                lineModelTraining.setIncrementTaskId(doneModelTraining.getTaskId());
                //创建模型精调任务
                String taskId = modelFineTuningTask(lineModelTraining);
                //修改任务运行状态和任务id
                lineModelTraining.setTaskId(taskId);
                lineModelTraining.setTaskStatus("Running");
                modelTrainingMapper.updateModelTraining(lineModelTraining);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 存在未完成训练任务执行
     *
     * @param runningModelTraining
     * @throws InterruptedException
     */
    private void incomplete(ModelTraining runningModelTraining) throws InterruptedException {
        try {
            //调用百度API获取模型精调任务详情
            String runStatus = fineTuningTaskDetails(runningModelTraining);
            runningModelTraining.setTaskStatus(runStatus);
            //更新任务运行状态
            modelTrainingMapper.updateModelTrainingById(runningModelTraining);

            //完成训练新建模型版本
            if (runStatus.equals("Done")) {
                String modelId = newModelVersion(runningModelTraining);
                runningModelTraining.setModelId(modelId);
                modelTrainingMapper.updateModelTraining(runningModelTraining);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private String newModelVersion(ModelTraining modelTraining) {
        String ak = modelTraining.getAk();
        String sk = modelTraining.getSk();
        String modelSetId = modelTraining.getModelSetId();
        String taskId = modelTraining.getTaskId();
        BaiduDto baiduDto = new BaiduDto();
        baiduDto.setAk(ak);
        baiduDto.setSk(sk);
        baiduDto.setModelSetId(modelSetId);
        baiduDto.setTaskId(taskId);

        return baiduApiService.newModelVersion(baiduDto, SecurityConstants.INNER);

//        try {
//            String path = "https://qianfan.baidubce.com/v2/model";
//            String headerJson = "{\"Content-Type\":\"application/json\"}";
//            String queryJson = "{\"Action\":\"CreateCustomModel\"}";
//            String jsonBody =
//                    "{\"modelSetId\":\"" + modelTraining.getModelSetId() + "\"," +
//                            "\"sourceType\":\"" + "Train" + "\"," +
//                            "\"trainMeta\":" +
//                            "{\"taskId\":\"" + modelTraining.getTaskId() + "\"}}";
//            System.out.println(jsonBody);
//            ApiExplorerRequest request = new ApiExplorerRequest(HttpMethodName.POST, path);
//            // 设置鉴权信息
//            request.setCredentials(modelTraining.getAk(), modelTraining.getSk());
//            // 设置header参数
//            HashMap<String, String> headerJsonMap = JsonUtils.fromJsonString(headerJson, HashMap.class);
//            Iterator<Entry<String, String>> headerIterator = headerJsonMap.entrySet().iterator();
//            while (headerIterator.hasNext()) {
//                Entry<String, String> next = headerIterator.next();
//                request.addHeaderParameter(next.getKey(), next.getValue());
//            }
//            // 设置query参数
//            if (StringUtils.isNotEmpty(queryJson)) {
//                HashMap<String, String> QueryJsonMap = JsonUtils.fromJsonString(queryJson, HashMap.class);
//                Iterator<Entry<String, String>> queryIterator = QueryJsonMap.entrySet().iterator();
//                while (queryIterator.hasNext()) {
//                    Entry<String, String> next = queryIterator.next();
//                    request.addQueryParameter(next.getKey(), next.getValue());
//                }
//            }
//            // 设置jsonBody参数
//            if (StringUtils.isNotEmpty(jsonBody)) {
//                request.setJsonBody(jsonBody);
//            }
//            ApiExplorerClient client = new ApiExplorerClient();
//
//            System.out.println(request);
//            ApiExplorerResponse response = client.sendRequest(request);
//            // 返回结果格式为Json字符串
//            JSONObject jsonObject = new JSONObject(response.getResult());
//            System.out.println(response.getResult());
//            // 获取result的值
//            JSONObject resultObject = jsonObject.getJSONObject("result");
//            // 返回模型版本id
//            return resultObject.getString("modelId");
//        } catch (JSONException e) {
//            e.printStackTrace();
//        }
//        return "";
    }


    /**
     * 调用百度千帆接口  查询训练任务执行状态
     *
     * @return
     */
    private String fineTuningTaskDetails(ModelTraining modelTraining) {
        String ak = modelTraining.getAk();
        String sk = modelTraining.getSk();
        String taskId = modelTraining.getTaskId();
        BaiduDto baiduDto = new BaiduDto();
        baiduDto.setAk(ak);
        baiduDto.setSk(sk);
        baiduDto.setTaskId(taskId);
        return baiduApiService.fineTuningTaskDetails(baiduDto,SecurityConstants.INNER);
//        try {
//            String path = "https://qianfan.baidubce.com/v2/finetuning";
//            String headerJson = "{\"Content-Type\":\"application/json\"}";
//            String queryJson = "{\"Action\":\"DescribeFineTuningTask\"}";
//            String jsonBody = "{\"taskId\":\"" + modelTraining.getTaskId() + "\"}";
//
//            ApiExplorerRequest request = new ApiExplorerRequest(HttpMethodName.POST, path);
//            // 设置鉴权信息
//            request.setCredentials(modelTraining.getAk(), modelTraining.getSk());        // 设置header参数
//            HashMap<String, String> headerJsonMap = JsonUtils.fromJsonString(headerJson, HashMap.class);
//            Iterator<Entry<String, String>> headerIterator = headerJsonMap.entrySet().iterator();
//            while (headerIterator.hasNext()) {
//                Entry<String, String> next = headerIterator.next();
//                request.addHeaderParameter(next.getKey(), next.getValue());
//            }
//            // 设置query参数
//            if (StringUtils.isNotEmpty(queryJson)) {
//                HashMap<String, String> QueryJsonMap = JsonUtils.fromJsonString(queryJson, HashMap.class);
//                Iterator<Entry<String, String>> queryIterator = QueryJsonMap.entrySet().iterator();
//                while (queryIterator.hasNext()) {
//                    Entry<String, String> next = queryIterator.next();
//                    request.addQueryParameter(next.getKey(), next.getValue());
//                }
//            }
//            // 设置jsonBody参数
//            if (StringUtils.isNotEmpty(jsonBody)) {
//                request.setJsonBody(jsonBody);
//            }
//            ApiExplorerClient client = new ApiExplorerClient();
//
//            ApiExplorerResponse response = client.sendRequest(request);
//            // 返回结果格式为Json字符串
//            JSONObject jsonObject = new JSONObject(response.getResult());
//            // 获取result的值
//            JSONObject resultObject = jsonObject.getJSONObject("result");
//            // 任务运行状态
//            System.out.println(response.getResult());
//            return resultObject.getString("runStatus");
//        } catch (JSONException e) {
//            e.printStackTrace();
//        }
//        return "";
    }

    /**
     * 创建模型精调任务
     *
     * @param modelTraining
     */
    private String modelFineTuningTask(ModelTraining modelTraining) {

        String ak = modelTraining.getAk();
        String sk = modelTraining.getSk();
        Map<String,String> map = new HashMap<>();
        map.put("jobId",modelTraining.getJobId());
        map.put("incrementTaskId",modelTraining.getIncrementTaskId());
        map.put("modelId",modelTraining.getModelSetId());
        map.put("modelVersionId",modelTraining.getModelId());
        map.put("epoch",modelTraining.getEpoch().toString());
        map.put("loggingSteps",modelTraining.getLoggingSteps());
        map.put("warmupRatio",modelTraining.getWarmupRatio());
        map.put("weightDecay",modelTraining.getWeightDecay());
        map.put("loraRank",modelTraining.getWeightDecay());
        map.put("loraAllLinear",modelTraining.getLoraAllLinear());
        map.put("maxSeqLen",modelTraining.getMaxSeqlen());
        map.put("versionId",modelTraining.getDatasetId());
        BaiduDto baiduDto = new BaiduDto();
        baiduDto.setAk(ak);
        baiduDto.setSk(sk);
        baiduDto.setMap(map);
        return baiduApiService.modelFineTuningTask(baiduDto,SecurityConstants.INNER);
//        try {
//            String path = "https://qianfan.baidubce.com/v2/finetuning";
//            String headerJson = "{\"Content-Type\":\"application/json\"}";
//            String queryJson = "{\"Action\":\"CreateFineTuningTask\"}";
//
//
//            String jsonBody =
//                    "{\"jobId\":\"" + modelTraining.getJobId() + "\"," +
//                            "\"incrementTaskId\":\"" + modelTraining.getIncrementTaskId() + "\"," +
//                            "\"parameterScale\":\"" + "LoRA" + "\"," +
//                            "\"modelConfig\":" +
//                            "{\"modelId\":\"" + modelTraining.getModelSetId() + "\",\"modelVersionId\":\"" + modelTraining.getModelId() + "\"}," +
//                            "\"hyperParameterConfig\":" +
//                            "{\"epoch\":" + modelTraining.getEpoch() + "," +
//                            "\"learningRate\":" + "0.0003" + "," +
//                            "\"loggingSteps\":" + modelTraining.getLoggingSteps() + "," +//保存日志间隔
//                            "\"warmupRatio\":" + modelTraining.getWarmupRatio() + "," +//预热比例
//                            "\"weightDecay\":" + modelTraining.getWeightDecay() + "," +//正则化系数
//                            "\"gradientAccumulationSteps\":" + "0" + "," +
//                            "\"pseudoSamplingProb\":" + "0" + "," +
//                            "\"seed\":" + "42" + "," +
//                            "\"lrSchedulerType\":\"" + "linear" + "\"," +
//                            "\"numCycles\":" + "0.5" + "," +
//                            "\"lrEnd\":" + "0.0000001" + "," +
//                            "\"power\":" + "1" + "," +
//                            "\"loraRank\":" + modelTraining.getLoraRank() + "," +//LoRA 策略中的秩
//                            "\"loraAllLinear\":\"" + modelTraining.getLoraAllLinear() + "\"," +
//                            "\"maxSeqLen\":" + modelTraining.getMaxSeqlen() + "}," +
//                            "\"datasetConfig\":" +
//                            "{\"sourceType\":\"Platform\"," +
//                            "\"versions\":" +
//                            "[{\"versionId\":\"" + modelTraining.getDatasetId() + "\"," +
//                            "\"samplingRate\":" + 1 + "}]," +
//                            "\"splitRatio\":20}}";
//
//
//            System.out.println(jsonBody);
//            ApiExplorerRequest request = new ApiExplorerRequest(HttpMethodName.POST, path);
//            // 设置鉴权信息
//            request.setCredentials(modelTraining.getAk(), modelTraining.getSk());        // 设置header参数
//            HashMap<String, String> headerJsonMap = JsonUtils.fromJsonString(headerJson, HashMap.class);
//            Iterator<Entry<String, String>> headerIterator = headerJsonMap.entrySet().iterator();
//            while (headerIterator.hasNext()) {
//                Entry<String, String> next = headerIterator.next();
//                request.addHeaderParameter(next.getKey(), next.getValue());
//            }
//            // 设置query参数
//            if (StringUtils.isNotEmpty(queryJson)) {
//                HashMap<String, String> QueryJsonMap = JsonUtils.fromJsonString(queryJson, HashMap.class);
//                Iterator<Entry<String, String>> queryIterator = QueryJsonMap.entrySet().iterator();
//                while (queryIterator.hasNext()) {
//                    Entry<String, String> next = queryIterator.next();
//                    request.addQueryParameter(next.getKey(), next.getValue());
//                }
//            }
//            // 设置jsonBody参数
//            if (StringUtils.isNotEmpty(jsonBody)) {
//                request.setJsonBody(jsonBody);
//            }
//            ApiExplorerClient client = new ApiExplorerClient();
//
//            System.out.println(request);
//            ApiExplorerResponse response = client.sendRequest(request);
//            // 返回结果格式为Json字符串
//            JSONObject jsonObject = new JSONObject(response.getResult());
//            System.out.println(response.getResult());
//            // 获取result的值
//            JSONObject resultObject = jsonObject.getJSONObject("result");
//            // 返回任务id
//            return resultObject.getString("taskId");
//        } catch (JSONException e) {
//            e.printStackTrace();
//        }
//        return "";
    }


}
