package com.ruoyi.zhi.service.impl;

import com.baidubce.appbuilder.model.knowledgebase.Document;
import com.ruoyi.baidu.api.BaiduApiService;
import com.ruoyi.baidu.api.dto.BaiduDto;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.domain.SysFileInfo;
import com.ruoyi.zhi.domain.FileVo;
import com.ruoyi.zhi.domain.NacosClient;
import com.ruoyi.zhi.domain.SKnowledgeSchool;
import com.ruoyi.zhi.domain.SKnowledgeSchoolFile;
import com.ruoyi.zhi.service.SRulesAndDisciplineService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class SRulesAndDisciplineServiceImpl implements SRulesAndDisciplineService {

    @Resource
    private BaiduApiService baiduApiService;
    @Resource
    private NacosClient nacosClient;
    @Resource
    private SKnowledgeSchoolServiceImpl sKnowledgeSchoolService;
    @Resource
    private SKnowledgeSchoolFileServiceImpl sKnowledgeSchoolFileService;
    @Resource
    private RemoteFileService remoteFileService;

    //导入文件到知识库，返回文件id


    @Override
    public void add(FileVo fileVo) {
        List<SKnowledgeSchool> sKnowledgeSchools = sKnowledgeSchoolService.selectSKnowledgeSchoolList(SKnowledgeSchool.builder().kbName(fileVo.getKbName()).build());
        if (sKnowledgeSchools.size() <= 0){
           throw new RuntimeException("知识库不存在");
        }
        SKnowledgeSchool sKnowledgeSchool = sKnowledgeSchools.get(0);

        List<String> filePathList=new ArrayList<>();
        filePathList.add(fileVo.getPreview());
        String parseFlag="1";
        String[] strings = importFileNew(nacosClient.getSecretkey(), sKnowledgeSchool.getKbId(), filePathList, sKnowledgeSchool.getIsEnhanced() == 1 ? true : false,parseFlag.equals("0") ? "qa" : "raw_text");

        for (String s:strings) {
            SKnowledgeSchoolFile build = new SKnowledgeSchoolFile();
            build.setFileId(s);
            build.setFileName(fileVo.getName());
            build.setFilePath(fileVo.getPreview());
            build.setIndexingStatus(fileVo.getKbName());
            build.setCreateBy(SecurityUtils.getUsername());
            build.setCreateTime(new Date());
            build.setKbId(sKnowledgeSchool.getKbId());
            sKnowledgeSchoolFileService.insertSKnowledgeSchoolFile(build);
        }
    }
    @Override
    public void initDocument(String KbName,String after,String before){
        Document[] document = getDocument(KbName,after,before);
        while (document.length > 0 ){
            String lastFileId=document[document.length-1].getId();
            for (Document d:document) {
                SKnowledgeSchoolFile build = new SKnowledgeSchoolFile();
                build.setFileId(d.getId());
                build.setFileName(d.getName());
                String fileId = d.getMeta().getFileId();
                SysFileInfo fileInfo = remoteFileService.getFileInfo(fileId);
                if (fileInfo!=null){
                    build.setFilePath(fileInfo.getFilePath());
                }
                build.setIndexingStatus(KbName);
                build.setCreateBy(SecurityUtils.getUsername());
                build.setCreateTime(new Date());
                build.setWordCount(Long.valueOf(d.getWordCount()));
                sKnowledgeSchoolFileService.insertSKnowledgeSchoolFile(build);
            }
            document=getDocument(KbName,lastFileId,null);
        }


    }

    /**
     *
     * @param secretkey key
     * @param datasetId 知识库id
     * @param filePath 上传文件路径
     * @param Enhanced 是否增强
     * @param contentFormat 文件是否分片 （qa不支持分片 raw_text 支持分片）
     * @return
     */
    public String[] importFileNew(String secretkey, String datasetId, List<String> filePath, Boolean Enhanced , String contentFormat) {
        BaiduDto baiduDto = new BaiduDto();
        baiduDto.setSecretkey(secretkey);
        baiduDto.setDatasetId(datasetId);
        baiduDto.setFilePath(filePath);
        baiduDto.setEnhanced(Enhanced);
        baiduDto.setContentFormat(contentFormat);
        return baiduApiService.importFileNew(baiduDto, SecurityConstants.INNER);
    }
    @Override
    public Document[] getDocument(String KbName,String after,String before){
        if(KbName.isEmpty()){
            return new Document[0];
        }
        List<SKnowledgeSchool> sKnowledgeSchools = sKnowledgeSchoolService.selectSKnowledgeSchoolList(SKnowledgeSchool.builder().kbName(KbName).build());
        if (sKnowledgeSchools.size() <= 0){
            throw new RuntimeException("知识库不存在");
        }
        SKnowledgeSchool sKnowledgeSchool = sKnowledgeSchools.get(0);
        BaiduDto baiduDto = new BaiduDto();
        baiduDto.setSecretkey(nacosClient.getSecretkey());
        baiduDto.setDatasetId(sKnowledgeSchool.getKbId());
        baiduDto.setAfter(after);
        baiduDto.setBefore(before);
        return baiduApiService.getDocuments(baiduDto, SecurityConstants.INNER);
    }

    //创建知识库,返回知识库id
    public String creatdataSetNew(String secretkey, String name ,String description) {
        BaiduDto baiduDto = new BaiduDto();
        baiduDto.setSecretkey(secretkey);
        baiduDto.setName(name);
        baiduDto.setDescription(description);
        return baiduApiService.creatdataSetNew(baiduDto, SecurityConstants.INNER);
    }
    //删除文件，返回成功失败
    public boolean deleteFile(String secretkey,String knowledgeBaseId, String fileId) {
        BaiduDto baiduDto = new BaiduDto();
        baiduDto.setSecretkey(secretkey);
        baiduDto.setDatasetId(knowledgeBaseId);
        baiduDto.setFileId(fileId);
        return baiduApiService.delKnowledgeBaseFile(baiduDto, SecurityConstants.INNER);
    }
}
