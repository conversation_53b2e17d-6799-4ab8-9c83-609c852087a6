package com.ruoyi.zhi.controller;


import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.zhi.domain.RulesContent;
import com.ruoyi.zhi.service.IRulesContentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 规则内容Controller
 * 
 * <AUTHOR>
 * @date 2023-12-13
 */
@RestController
@RequestMapping("/content")
public class RulesContentController extends BaseController
{
    @Autowired
    private IRulesContentService rulesContentService;

    /**
     * 查询规则内容列表
     */
    @GetMapping("/list")
    public TableDataInfo list(RulesContent rulesContent)
    {
        startPage();
        List<RulesContent> list = rulesContentService.selectRulesContentList(rulesContent);
        return getDataTable(list);
    }

    /**
     * 获取规则内容详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rulesContentService.selectRulesContentById(id));
    }

    /**
     * 新增规则内容
     */
    @Log(title = "规则内容", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RulesContent rulesContent)
    {
        return toAjax(rulesContentService.insertRulesContent(rulesContent));
    }

    /**
     * 修改规则内容
     */
    @Log(title = "规则内容", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RulesContent rulesContent)
    {
        return toAjax(rulesContentService.updateRulesContent(rulesContent));
    }

    /**
     * 删除规则内容
     */
    @Log(title = "规则内容", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rulesContentService.deleteRulesContentByIds(ids));
    }
}
