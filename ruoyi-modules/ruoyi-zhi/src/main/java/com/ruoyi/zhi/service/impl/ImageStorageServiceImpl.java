package com.ruoyi.zhi.service.impl;

import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.List;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.zhi.domain.ImageStorage;
import com.ruoyi.zhi.utils.Snowflake;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import com.ruoyi.zhi.mapper.ImageStorageMapper;
import com.ruoyi.zhi.service.IImageStorageService;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;

/**
 * 图片存储Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-06
 */
@Service
@RefreshScope
public class ImageStorageServiceImpl implements IImageStorageService 
{
    @Resource
    private ImageStorageMapper imageStorageMapper;

    @Value("${imgFile.imgUrl}")
    private String imgUrl;

    /**
     * 查询图片存储
     * 
     * @param id 图片存储主键
     * @return 图片存储
     */
    @Override
    public ImageStorage selectImageStorageById(Long id)
    {
        return imageStorageMapper.selectImageStorageById(id);
    }

    /**
     * 查询图片存储列表
     * 
     * @param imageStorage 图片存储
     * @return 图片存储
     */
    @Override
    public List<ImageStorage> selectImageStorageList(ImageStorage imageStorage)
    {
        List<ImageStorage> imageStorageList = imageStorageMapper.selectImageStorageList(imageStorage);
        imageStorageList.forEach(imageStorage1 -> {
            try {
                if (StringUtils.isNotEmpty(imageStorage1.getFilePath())) {
                    String processing = processing(imageStorage1.getFilePath());
                    imageStorage1.setProcessing(processing);
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        });
        return imageStorageList;
    }

    /**
     * 根据图片路径将图片转换成字节
     * @param filePath
     * @return
     * @throws IOException
     */
    public String  processing(String filePath) throws IOException {
            File file = new File(filePath); // 图片的实际路径
            BufferedImage image = ImageIO.read(file);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(image, getFileExtension(filePath), baos);
            baos.flush();
            byte[] imageBytes = baos.toByteArray();
            baos.close();

            return Base64.getEncoder().encodeToString(imageBytes);
    }

    /**
     * 新增图片存储
     *
     * @return 结果
     */
    @Override
    public int insertImageStorage(MultipartFile file, Long dataId,String imageNumber) throws Exception {
        if(imageStorageMapper.countImageStorage(imageNumber)>0){
            throw new Exception("图片编号已存在");
        }
        Snowflake snowflake = new Snowflake(1, 1);
        long id = snowflake.generateId();
        ImageStorage imageStorage = new ImageStorage();
        imageStorage.setId(id);
        imageStorage.setDataId(dataId);
        imageStorage.setImageNumber(imageNumber);
        try {
            if (!file.isEmpty()) {
                LocalDate currentDate = LocalDate.now();

                // 定义日期格式
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

                // 格式化当前日期
                String formattedCurrentDate = currentDate.format(formatter);
                String fileName = file.getOriginalFilename();
                String filePath = imgUrl + formattedCurrentDate+"/"+id+"."+ getFileExtension(fileName);
                //创建目录
                createDirectory(filePath);
                System.out.println(filePath);
                File dest = new File(filePath);
                file.transferTo(dest); // 保存文件
                imageStorage.setFileName(fileName);
                imageStorage.setFilePath(filePath);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return imageStorageMapper.insertImageStorage(imageStorage);
    }

    /**
     * 根据图片名称或路径获取图片后缀
     * @param fileName
     * @return
     */
    public String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return ""; // 如果文件名为空，返回空字符串
        }
        int dotIndex = fileName.lastIndexOf('.'); // 查找最后一个'.'的位置
        if (dotIndex == -1 || dotIndex == fileName.length() - 1) {
            return ""; // 如果没有找到'.'或者'.'在字符串末尾，说明没有扩展名
        }
        return fileName.substring(dotIndex + 1); // 返回'.'之后的部分，即文件扩展名
    }

    /**
     * 修改图片存储
     * 
     * @param imageStorage 图片存储
     * @return 结果
     */
    @Override
    public int updateImageStorage(ImageStorage imageStorage)
    {
        return imageStorageMapper.updateImageStorage(imageStorage);
    }

    /**
     * 批量删除图片存储
     * 
     * @param ids 需要删除的图片存储主键
     * @return 结果
     */
    @Override
    public int deleteImageStorageByIds(Long[] ids)
    {
        return imageStorageMapper.deleteImageStorageByIds(ids);
    }

    /**
     * 删除图片存储信息
     * 
     * @param id 图片存储主键
     * @return 结果
     */
    @Override
    public int deleteImageStorageById(Long id)
    {
        ImageStorage imageStorage = imageStorageMapper.selectImageStorageById(id);
        if (StringUtils.isNotEmpty(imageStorage.getFilePath())) {
            File file = new File(imageStorage.getFilePath());

            // 检查文件是否存在
            if (file.exists()) {
                // 尝试删除文件
                boolean isDeleted = file.delete();

            }
        }
        return imageStorageMapper.deleteImageStorageById(id);
    }

    public  void createDirectory(String filePath) {

        Path path = Paths.get(filePath);
        if (!Files.exists(path)) {
            try {
                Files.createDirectories(path);
            } catch (IOException e) {
            }
        }
    }

}
