package com.ruoyi.zhi.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.zhi.domain.KnowledgeBaseFile;
import com.ruoyi.zhi.domain.UnionCourses;
import com.ruoyi.zhi.dto.DownLoadDto;
import com.ruoyi.zhi.service.IUnionCoursesService;
import com.ruoyi.zhi.vo.AmbitTeacherVo;
import com.ruoyi.zhi.vo.UnionCoursesVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 联盟课程Controller
 *
 * <AUTHOR>
 * @date 2024-10-28
 */
@RestController
@RequestMapping("/unionCourses")
@Slf4j
public class UnionCoursesController extends BaseController {
    @Resource
    private IUnionCoursesService unionCoursesService;

    /**
     * 查询联盟课程列表
     */
    @GetMapping("/list")
    public TableDataInfo list(UnionCourses unionCourses) {
        startPage();
        List<UnionCourses> list = unionCoursesService.selectUnionCoursesList(unionCourses);
        return getDataTable(list);
    }

    /**
     * 查询联盟课程列表vo
     */
    @GetMapping("/list2")
    public TableDataInfo list2(UnionCourses unionCourses) {
        startPage();
        List<UnionCoursesVo> list = unionCoursesService.selectUnionCoursesVoListAndCourseUser(unionCourses);
        return getDataTable(list);
    }

    /**
     * 导出联盟课程列表
     */
    @Log(title = "联盟课程", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, UnionCourses unionCourses) {
        List<UnionCourses> list = unionCoursesService.selectUnionCoursesList(unionCourses);
        ExcelUtil<UnionCourses> util = new ExcelUtil<UnionCourses>(UnionCourses.class);
        util.exportExcel(response, list, "联盟课程数据");
    }

    /**
     * 获取联盟课程详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(unionCoursesService.selectUnionCoursesById(id));
    }

    /**
     * 获取联盟课程详细信息VO
     */
    @GetMapping(value = "vo/{id}")
    public AjaxResult getInfoVo(@PathVariable("id") Long id) {
        return success(unionCoursesService.selectUnionCoursesVoById(id));
    }

    /**
     * 新增联盟课程
     */
    @Log(title = "新增联盟课程", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UnionCourses unionCourses) {
        try {
            toAjax(unionCoursesService.insertUnionCourses(unionCourses));
        } catch (Exception e) {
            log.error(e.getMessage());
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success();
    }

    /**
     * 修改联盟课程
     */
    @Log(title = "修改联盟课程", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UnionCourses unionCourses) {
        try {
            toAjax(unionCoursesService.updateUnionCourses(unionCourses));
        } catch (Exception e) {
            log.error(e.getMessage());
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success();
    }

    /**
     * 删除联盟课程
     */
    @Log(title = "删除联盟课程", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(unionCoursesService.deleteUnionCoursesByIds(ids));
    }

    /**
     * 获取联盟图谱数据
     */
    @PostMapping(value = "/unionCourseMapping")
    @Log(title = "获取联盟图谱数据", businessType = BusinessType.OTHER)
    public AjaxResult obtainConsortiumGraphData(@RequestBody UnionCoursesVo vo) {
//        UnionCoursesVo data = unionCoursesService.buildUnionCourseCourseGraphData(vo);
        UnionCoursesVo data = unionCoursesService.buildUnionCourseCourseGraphData2(vo);
        return AjaxResult.success(data);
    }


    /**
     * 获取联盟下教师单个数据
     */
    @GetMapping(value = "/unionCourseTeacher")
    @Log(title = "获取联盟下教师单个数据", businessType = BusinessType.OTHER)
    public AjaxResult getUnionCourseTeacher(UnionCoursesVo unionCoursesVo) {
        AmbitTeacherVo unionCourseTeacher = unionCoursesService.getUnionCourseTeacher(unionCoursesVo);
        return AjaxResult.success(unionCourseTeacher);
    }

    /**
     * 获取联盟下单个教材数据
     */
    @GetMapping(value = "/unionCourseBook")
    @Log(title = "获取联盟下单个教材数据", businessType = BusinessType.OTHER)
    public AjaxResult getUnionCourseBook(UnionCoursesVo unionCoursesVo) {
        KnowledgeBaseFile vo = unionCoursesService.getUnionCourseBook(unionCoursesVo);
        return AjaxResult.success(vo);
    }


    /**
     * 获取联盟下所有的教师
     */
    @GetMapping(value = "/unionCourseTeacherList")
    @Log(title = "获取联盟下所有的教师", businessType = BusinessType.OTHER)
    public TableDataInfo getUnionCourseTeacherList(UnionCoursesVo unionCoursesVo) {
        startPage();
        List<AmbitTeacherVo> list = unionCoursesService.getUnionCourseTeacherList(unionCoursesVo);
        return getDataTable(list);
    }

    /**
     * 获取联盟下所有的教材
     */
    @GetMapping(value = "/unionCourseBookList")
    @Log(title = "获取联盟下所有的教材", businessType = BusinessType.OTHER)
    public TableDataInfo getUnionCourseBookList(UnionCoursesVo unionCoursesVo) {
        startPage();
        List<KnowledgeBaseFile> list = unionCoursesService.getUnionCourseBookList(unionCoursesVo);
        return getDataTable(list);
    }

    /**
     * 下载
     */
    @Log(title = "下载", businessType = BusinessType.OTHER)
    @PostMapping("/downLoad")
    public void export(HttpServletRequest request, HttpServletResponse response, @RequestBody DownLoadDto downLoadDto) {
        unionCoursesService.downLoad(downLoadDto.getFilePath(), request, response);
    }

}
