package com.ruoyi.zhi.exception;

public class CustomException extends RuntimeException
{
    private static final long serialVersionUID = 1L;

    private String code;

    private String message;

    public CustomException(String message)
    {
        this.message = message;
    }

    public CustomException(String code,String message)
    {
        this.code = code;
        this.message = message;
    }

    public CustomException(String message, Throwable e)
    {
        super(message, e);
        this.message = message;
    }

    @Override
    public String getMessage()
    {
        return message;
    }

    public String getCode()
    {
        return code;
    }
}
