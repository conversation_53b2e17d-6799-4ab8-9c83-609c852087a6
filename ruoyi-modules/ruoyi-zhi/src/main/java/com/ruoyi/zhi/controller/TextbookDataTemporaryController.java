package com.ruoyi.zhi.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.zhi.domain.TextbookDataTemporary;
import com.ruoyi.zhi.service.ITextbookDataTemporaryService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 知识库知识图谱临时数据-教材出版社、作者Controller
 * 
 * <AUTHOR>
 * @date 2024-09-27
 */
@RestController
@RequestMapping("/textboooktemporary")
public class TextbookDataTemporaryController extends BaseController
{
    @Autowired
    private ITextbookDataTemporaryService textbookDataTemporaryService;

    /**
     * 查询知识库知识图谱-教材出版社、作者列表
     */
    @RequiresPermissions("zhi:temporary:list")
    @GetMapping("/list")
    public TableDataInfo list(TextbookDataTemporary textbookDataTemporary)
    {
        startPage();
        List<TextbookDataTemporary> list = textbookDataTemporaryService.selectTextbookDataTemporaryList(textbookDataTemporary);
        return getDataTable(list);
    }

    /**
     * 导出知识库知识图谱-教材出版社、作者列表
     */
    @RequiresPermissions("zhi:temporary:export")
    @Log(title = "知识库知识图谱-教材出版社、作者", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TextbookDataTemporary textbookDataTemporary)
    {
        List<TextbookDataTemporary> list = textbookDataTemporaryService.selectTextbookDataTemporaryList(textbookDataTemporary);
        ExcelUtil<TextbookDataTemporary> util = new ExcelUtil<TextbookDataTemporary>(TextbookDataTemporary.class);
        util.exportExcel(response, list, "知识库知识图谱-教材出版社、作者数据");
    }

    /**
     * 获取知识库知识图谱-教材出版社、作者详细信息
     */
    @RequiresPermissions("zhi:temporary:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(textbookDataTemporaryService.selectTextbookDataTemporaryById(id));
    }

    /**
     * 新增知识库知识图谱-教材出版社、作者
     */
    @RequiresPermissions("zhi:temporary:add")
    @Log(title = "知识库知识图谱-教材出版社、作者", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TextbookDataTemporary textbookDataTemporary)
    {
        return toAjax(textbookDataTemporaryService.insertTextbookDataTemporary(textbookDataTemporary));
    }

    /**
     * 修改知识库知识图谱-教材出版社、作者
     */
    @RequiresPermissions("zhi:temporary:edit")
    @Log(title = "知识库知识图谱-教材出版社、作者", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TextbookDataTemporary textbookDataTemporary)
    {
        return toAjax(textbookDataTemporaryService.updateTextbookDataTemporary(textbookDataTemporary));
    }

    /**
     * 删除知识库知识图谱-教材出版社、作者
     */
    @RequiresPermissions("zhi:temporary:remove")
    @Log(title = "知识库知识图谱-教材出版社、作者", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(textbookDataTemporaryService.deleteTextbookDataTemporaryByIds(ids));
    }
}
