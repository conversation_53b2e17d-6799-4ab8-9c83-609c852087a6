package com.ruoyi.zhi.controller;

import java.util.Collections;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.zhi.domain.ApplicationScenarioKnowledgeBase;
import com.ruoyi.zhi.mapper.ApplicationScenarioKnowledgeBaseFileMapper;
import com.ruoyi.zhi.mapper.ApplicationScenarioKnowledgeBaseMapper;
import com.ruoyi.zhi.service.ApplicationScenarioKnowledgeBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.zhi.domain.ApplicationScenarioKnowledgeBaseFile;
import com.ruoyi.zhi.service.IApplicationScenarioKnowledgeBaseFileService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 应用场景知识库文件Controller
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@RestController
@RequestMapping("/file")
public class ApplicationScenarioKnowledgeBaseFileController extends BaseController
{
    @Autowired
    private IApplicationScenarioKnowledgeBaseFileService applicationScenarioKnowledgeBaseFileService;

    @Autowired
    private ApplicationScenarioKnowledgeBaseMapper applicationScenarioKnowledgeBaseMapper;
    /**
     * 查询应用场景知识库文件列表
     */
    @RequiresPermissions("zsk:file:list")
    @GetMapping("/list/{id}")
    public TableDataInfo list(@PathVariable("id") Long id)
    {

        ApplicationScenarioKnowledgeBase kbRow = applicationScenarioKnowledgeBaseMapper.selectOne(
                new LambdaQueryWrapper<ApplicationScenarioKnowledgeBase>()
                        .select(ApplicationScenarioKnowledgeBase::getKbId)                 // 只查一列
                        .eq(ApplicationScenarioKnowledgeBase::getApplicationScenarioId,     // WHERE application_scenario_id = ?
                                id)
                        .last("limit 1")
        );

        if (kbRow == null || kbRow.getKbId() == null) {
            // 场景下没有知识库 → 返回空表
            return getDataTable(Collections.emptyList());
        }
        String kbId = kbRow.getKbId();
        ApplicationScenarioKnowledgeBaseFile file = new ApplicationScenarioKnowledgeBaseFile();
        file.setKbId( kbId);
        startPage();
        List<ApplicationScenarioKnowledgeBaseFile> list = applicationScenarioKnowledgeBaseFileService.selectApplicationScenarioKnowledgeBaseFileList(file);
        return getDataTable(list);
    }

    /**
     * 导出应用场景知识库文件列表
     */
    @RequiresPermissions("zsk:file:export")
    @Log(title = "应用场景知识库文件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ApplicationScenarioKnowledgeBaseFile applicationScenarioKnowledgeBaseFile)
    {
        List<ApplicationScenarioKnowledgeBaseFile> list = applicationScenarioKnowledgeBaseFileService.selectApplicationScenarioKnowledgeBaseFileList(applicationScenarioKnowledgeBaseFile);
        ExcelUtil<ApplicationScenarioKnowledgeBaseFile> util = new ExcelUtil<ApplicationScenarioKnowledgeBaseFile>(ApplicationScenarioKnowledgeBaseFile.class);
        util.exportExcel(response, list, "应用场景知识库文件数据");
    }

    /**
     * 获取应用场景知识库文件详细信息
     */
    @RequiresPermissions("zsk:file:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(applicationScenarioKnowledgeBaseFileService.selectApplicationScenarioKnowledgeBaseFileById(id));
    }

    /**
     * 新增应用场景知识库文件
     */
    @RequiresPermissions("zsk:file:add")
    @Log(title = "应用场景知识库文件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ApplicationScenarioKnowledgeBaseFile applicationScenarioKnowledgeBaseFile)
    {
        return toAjax(applicationScenarioKnowledgeBaseFileService.insertApplicationScenarioKnowledgeBaseFile(applicationScenarioKnowledgeBaseFile));
    }

    /**
     * 修改应用场景知识库文件
     */
    @RequiresPermissions("zsk:file:edit")
    @Log(title = "应用场景知识库文件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ApplicationScenarioKnowledgeBaseFile applicationScenarioKnowledgeBaseFile)
    {
        return toAjax(applicationScenarioKnowledgeBaseFileService.updateApplicationScenarioKnowledgeBaseFile(applicationScenarioKnowledgeBaseFile));
    }

    /**
     * 删除应用场景知识库文件
     */
    @RequiresPermissions("zsk:file:remove")
    @Log(title = "应用场景知识库文件", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(applicationScenarioKnowledgeBaseFileService.deleteApplicationScenarioKnowledgeBaseFileByIds(ids));
    }
}
