package com.ruoyi.zhi.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.zhi.domain.PoductionTask;
import com.ruoyi.zhi.service.IPoductionTaskService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 数据集制作任务Controller
 * 
 * <AUTHOR>
 * @date 2024-07-18
 */
@RestController
@RequestMapping("/poduction")
public class PoductionTaskController extends BaseController
{
    @Autowired
    private IPoductionTaskService poductionTaskService;

    /**
     * 查询数据集制作任务列表
     */
    @RequiresPermissions("create:task:list")
    @GetMapping("/list")
    public TableDataInfo list(PoductionTask poductionTask)
    {
        startPage();
        List<PoductionTask> list = poductionTaskService.selectPoductionTaskList(poductionTask);
        return getDataTable(list);
    }

    /**
     * 导出数据集制作任务列表
     */
    @RequiresPermissions("create:task:export")
    @Log(title = "数据集制作任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PoductionTask poductionTask)
    {
        List<PoductionTask> list = poductionTaskService.selectPoductionTaskList(poductionTask);
        ExcelUtil<PoductionTask> util = new ExcelUtil<PoductionTask>(PoductionTask.class);
        util.exportExcel(response, list, "数据集制作任务数据");
    }

    /**
     * 获取数据集制作任务详细信息
     */
    @RequiresPermissions("create:task:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(poductionTaskService.selectPoductionTaskById(id));
    }

    /**
     * 新增数据集制作任务
     */
    @RequiresPermissions("create:task:add")
    @Log(title = "数据集制作任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PoductionTask poductionTask)
    {
        return toAjax(poductionTaskService.insertPoductionTask(poductionTask));
    }

    /**
     * 修改数据集制作任务
     */
    @RequiresPermissions("create:task:edit")
    @Log(title = "数据集制作任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PoductionTask poductionTask)
    {
        return toAjax(poductionTaskService.updatePoductionTask(poductionTask));
    }

    /**
     * 删除数据集制作任务
     */
    @RequiresPermissions("create:task:remove")
    @Log(title = "数据集制作任务", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(poductionTaskService.deletePoductionTaskByIds(ids));
    }
}
