package com.ruoyi.zhi.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.zhi.domain.TextbookData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.zhi.domain.TextbookKeywordAnalysis;
import com.ruoyi.zhi.service.ITextbookKeywordAnalysisService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 知识库知识图谱-教材关键词解析Controller
 * 
 * <AUTHOR>
 * @date 2024-08-27
 */
@RestController
@RequestMapping("/analysis")
public class TextbookKeywordAnalysisController extends BaseController
{
    @Autowired
    private ITextbookKeywordAnalysisService textbookKeywordAnalysisService;

    /**
     * 查询知识库知识图谱-教材关键词解析列表
     */
    @RequiresPermissions("zhi:analysis:list")
    @GetMapping("/list")
    public TableDataInfo list(TextbookKeywordAnalysis textbookKeywordAnalysis)
    {
        startPage();
        List<TextbookKeywordAnalysis> list = textbookKeywordAnalysisService.selectTextbookKeywordAnalysisList(textbookKeywordAnalysis);
        return getDataTable(list);
    }

    /**
     * 导出知识库知识图谱-教材关键词解析列表
     */
    @RequiresPermissions("zhi:analysis:export")
    @Log(title = "知识库知识图谱-教材关键词解析", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TextbookKeywordAnalysis textbookKeywordAnalysis)
    {
        List<TextbookKeywordAnalysis> list = textbookKeywordAnalysisService.selectTextbookKeywordAnalysisList(textbookKeywordAnalysis);
        ExcelUtil<TextbookKeywordAnalysis> util = new ExcelUtil<TextbookKeywordAnalysis>(TextbookKeywordAnalysis.class);
        util.exportExcel(response, list, "知识库知识图谱-教材关键词解析数据");
    }

    /**
     * 发起解析
     */
    @RequiresPermissions("zhi:analysis:initiate")
    @GetMapping(value = "/initiate/{id}")
    public AjaxResult getInitiate(@PathVariable("id") Long id) throws Exception {
        return success(textbookKeywordAnalysisService.initiateParsing(id));
    }


    @RequiresPermissions("zhi:analysis:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) throws Exception {
        return success(textbookKeywordAnalysisService.getInfo(id));
    }

    /**
     * 新增知识库知识图谱-教材关键词解析
     */
    @RequiresPermissions("zhi:analysis:add")
    @Log(title = "知识库知识图谱-教材关键词解析", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TextbookKeywordAnalysis textbookKeywordAnalysis)
    {
        return toAjax(textbookKeywordAnalysisService.insertTextbookKeywordAnalysis(textbookKeywordAnalysis));
    }

    /**
     * 修改知识库知识图谱-教材关键词解析
     */
    @RequiresPermissions("zhi:analysis:edit")
    @Log(title = "知识库知识图谱-教材关键词解析", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TextbookKeywordAnalysis textbookKeywordAnalysis)
    {
        return toAjax(textbookKeywordAnalysisService.updateTextbookKeywordAnalysis(textbookKeywordAnalysis));
    }

    /**
     * 删除知识库知识图谱-教材关键词解析
     */
    @RequiresPermissions("zhi:analysis:remove")
    @Log(title = "知识库知识图谱-教材关键词解析", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(textbookKeywordAnalysisService.deleteTextbookKeywordAnalysisByIds(ids));
    }


    /**
     * 提交解析
     */
    @RequiresPermissions("apiece:kbfile:examine")
    @PostMapping("/submit")
    public AjaxResult submitAnalysis(@RequestBody TextbookData textbookData) throws Exception {
        return success(textbookKeywordAnalysisService.submitAnalysis(textbookData));
    }

    /**
     * 导入
     * @return
     */
    @RequiresPermissions("zhi:analysis:extract")
    @PostMapping("/import")
    public AjaxResult extract(@RequestParam("file") MultipartFile file, Long textbookId, String isCover) throws Exception {
        return toAjax(textbookKeywordAnalysisService.importExcel(file,textbookId,isCover));
    }

}
