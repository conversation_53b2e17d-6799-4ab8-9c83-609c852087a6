package com.ruoyi.zhi.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

import java.util.List;

/**
 * 学科专业对象 s_ambit_major
 * 
 * <AUTHOR>
 * @date 2024-08-29
 */
@Data
public class AmbitMajor extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    private Long id;
    /** 专业ID */
    private Long majorId;

    /**
     * 学院id
     */
    private Long collegeId;

    /** 专业名称 */
    @Excel(name = "专业名称")
    private String majorName;

    /**
     * 学院名称
     */
    private String collegeName;

    /** 课程名称 */
    @Excel(name = "课程名称")
    private String courseName;


    /** 学科ID */
    @Excel(name = "学科ID")
    private Long disciplineId;

    /**
     * 学校id
     */
    private Long univerId;

    /**
     * 学校名称
     */
    private String univerName;

    @TableField(exist = false)
    private Long[] fileIds;


    private String keyword;
    /** 字段（前端传入的） **/
    private String keywordValue;
    /** 字段 **/
    private String field;
    /** 文件ID **/
    private String fileId;

    private String category;
    /** 统计数量 **/
    private Long count;
    /** 返回前端数据关联关系 */
    List<LiteratrueLinks> literatrueLinks;

    List<LiteratrueNodesData> literatrueNodesData;


    private List<AmbitMajor> children;

    private String flag;
    private Long[] affiliatedUnit;


    private String[] affiliatedUnitName;

    /** 是否联盟课 */
    private String isAllianceCourse;

    /** 文件是否能解析 0-否 1-是*/
    @TableField(exist = false)
    private String parseFlag;

    /** 知识库备注 **/
    @TableField(exist = false)
    private String description;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public Long getMajorId() {
        return majorId;
    }

    public void setMajorId(Long majorId) {
        this.majorId = majorId;
    }

    public void setMajorName(String majorName)
    {
        this.majorName = majorName;
    }

    public String getMajorName() 
    {
        return majorName;
    }
    public void setCourseName(String courseName) 
    {
        this.courseName = courseName;
    }

    public String getCourseName() 
    {
        return courseName;
    }
    public void setDisciplineId(Long kbId)
    {
        this.disciplineId = kbId;
    }

    public Long getDisciplineId()
    {
        return disciplineId;
    }

    public Long[] getFileIds() {
        return fileIds;
    }

    public void setFileIds(Long[] fileIds) {
        this.fileIds = fileIds;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getKeywordValue() {
        return keywordValue;
    }

    public void setKeywordValue(String keywordValue) {
        this.keywordValue = keywordValue;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public Long getCount() {
        return count;
    }

    public void setCount(Long count) {
        this.count = count;
    }

    public List<LiteratrueLinks> getLiteratrueLinks() {
        return literatrueLinks;
    }

    public void setLiteratrueLinks(List<LiteratrueLinks> literatrueLinks) {
        this.literatrueLinks = literatrueLinks;
    }

    public List<LiteratrueNodesData> getLiteratrueNodesData() {
        return literatrueNodesData;
    }

    public void setLiteratrueNodesData(List<LiteratrueNodesData> literatrueNodesData) {
        this.literatrueNodesData = literatrueNodesData;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Long getCollegeId() {
        return collegeId;
    }

    public void setCollegeId(Long collegeId) {
        this.collegeId = collegeId;
    }

    public String getCollegeName() {
        return collegeName;
    }

    public void setCollegeName(String collegeName) {
        this.collegeName = collegeName;
    }

    public List<AmbitMajor> getChildren() {
        return children;
    }

    public void setChildren(List<AmbitMajor> children) {
        this.children = children;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public Long getUniverId() {
        return univerId;
    }

    public void setUniverId(Long univerId) {
        this.univerId = univerId;
    }

    public String getUniverName() {
        return univerName;
    }

    public void setUniverName(String univerName) {
        this.univerName = univerName;
    }

    public Long[] getAffiliatedUnit() {
        return affiliatedUnit;
    }

    public void setAffiliatedUnit(Long[] affiliatedUnit) {
        this.affiliatedUnit = affiliatedUnit;
    }

    public String[] getAffiliatedUnitName() {
        return affiliatedUnitName;
    }

    public void setAffiliatedUnitName(String[] affiliatedUnitName) {
        this.affiliatedUnitName = affiliatedUnitName;
    }

    public String getIsAllianceCourse() {
        return isAllianceCourse;
    }

    public void setIsAllianceCourse(String isAllianceCourse) {
        this.isAllianceCourse = isAllianceCourse;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("majorName", getMajorName())
            .append("courseName", getCourseName())
            .append("disciplineId", getDisciplineId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
