package com.ruoyi.zhi.service.impl;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.excel.EasyExcel;
import com.ruoyi.baidu.api.BaiduApiService;
import com.ruoyi.baidu.api.dto.BaiduDto;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.easyexcel.EasyExcelUtils;
import com.ruoyi.common.core.utils.easyexcel.Message;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.RemoteDictTypeService;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.RemoteUniversityService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysDictData;
import com.ruoyi.system.api.domain.SysFileInfo;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.domain.University;
import com.ruoyi.system.api.model.LoginUser;
import com.ruoyi.zhi.domain.*;
import com.ruoyi.zhi.mapper.*;
import com.ruoyi.zhi.utils.Snowflake;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.zhi.service.IAmbitMajorService;

import javax.annotation.Resource;

/**
 * 学科专业Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-29
 */
@Service
public class AmbitMajorServiceImpl implements IAmbitMajorService {
    @Autowired
    private AmbitMajorMapper ambitMajorMapper;

    @Autowired
    private NacosClient nacosClient;

    @Autowired
    private KnowledgeBaseFileMapper knowledgeBaseFileMapper;

    @Autowired
    private BaiduApiService baiduApiService;

    @Autowired
    private KnowledgeBaseMapper knowledgeBaseMapper;

    @Autowired
    private AmbitTeacherMapper ambitTeacherMapper;

    @Autowired
    private RemoteUniversityService remoteUniversityService;

    @Autowired
    private KnowledgeInformationMapper knowledgeInformationMapper;

    @Resource
    private RemoteFileService remoteFileService;
    /**
     * 查询学科专业
     *
     * @param id 学科专业主键
     * @return 学科专业
     */
    @Override
    public AmbitMajor selectAmbitMajorById(Long id) {
        return ambitMajorMapper.selectAmbitMajorById(id);
    }

    /**
     * 查询学科专业列表
     *
     * @param ambitMajor 学科专业
     * @return 学科专业
     */
    @Override
    public List<AmbitMajor> selectAmbitMajorList(AmbitMajor ambitMajor) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser sysUser = loginUser.getSysUser();
        Long universityId = sysUser.getUniversityId();
        Snowflake snowflake = new Snowflake(1, 1);
        ambitMajor.setUniverId(universityId);
        //查询学校
        List<AmbitMajor> univerList = ambitMajorMapper.selectUniverList(ambitMajor);

        //查询学院
        List<AmbitMajor> majorList = ambitMajorMapper.selectCollegeList(ambitMajor);
        //查询专业
        List<AmbitMajor> ambitMajorList = ambitMajorMapper.selectAmbitMajorList(ambitMajor);

        majorList.forEach(ambitMajor1 -> {
            List<AmbitMajor> ambitMajors = new ArrayList<>();
            for (AmbitMajor major : ambitMajorList) {
                if (major.getCollegeId().equals(ambitMajor1.getCollegeId())){
                    major.setId(snowflake.generateId());
                    major.setFlag("Y");
                    Long[] longs = new Long[3];
                    longs[0] = major.getUniverId();
                    longs[1] = major.getCollegeId();
                    longs[2] = major.getMajorId();
                    major.setAffiliatedUnit(longs);
                    ambitMajors.add(major);
                }
            }
            ambitMajor1.setChildren(ambitMajors);
            ambitMajor1.setId(snowflake.generateId());
            ambitMajor1.setFlag("N");

        });

        univerList.forEach(ambitMajor1 -> {
            List<AmbitMajor> ambitMajors = new ArrayList<>();
            for (AmbitMajor major : majorList) {
                if (major.getUniverId().equals(ambitMajor1.getUniverId())){
                    ambitMajors.add(major);
                }
            }
            ambitMajor1.setChildren(ambitMajors);
            ambitMajor1.setId(snowflake.generateId());
            ambitMajor1.setFlag("N");
        });
        return univerList;
    }

    /**
     * 新增学科专业
     *
     * @param ambitMajor 学科专业
     * @return 结果
     */
    @Override
    public int insertAmbitMajor(AmbitMajor ambitMajor) throws Exception {
        Long[] affiliatedUnit = ambitMajor.getAffiliatedUnit();
        ambitMajor.setUniverId(affiliatedUnit != null && affiliatedUnit.length > 0 ? affiliatedUnit[0] : null); //
        ambitMajor.setCollegeId(affiliatedUnit != null && affiliatedUnit.length > 1 ? affiliatedUnit[1] : null); //
        ambitMajor.setMajorId(affiliatedUnit != null && affiliatedUnit.length > 2 ? affiliatedUnit[2] : null); //

        University university = new University();
        university.setId(ambitMajor.getUniverId());
        university.setCollegeId(ambitMajor.getCollegeId());
        university.setMajorId(ambitMajor.getMajorId());
        AjaxResult info = remoteUniversityService.getInfo(university, SecurityConstants.INNER);
        LinkedHashMap  data = (LinkedHashMap) info.get("data");
        if(data!=null){
            String univerName = (String) data.get("univerName");
            String colleName = (String) data.get("colleName");
            String majorName = (String) data.get("majorName");
            ambitMajor.setUniverName(univerName);
            ambitMajor.setCollegeName(colleName);
            ambitMajor.setMajorName(majorName);
        }
        //查看专业学科是否存在
        int a = ambitMajorMapper.countAmbitMajor(ambitMajor);
        if (a == 0) {
            ambitMajor.setCreateBy(SecurityUtils.getUsername());
            ambitMajor.setCreateTime(DateUtils.getNowDate());
            ambitMajorMapper.insertAmbitMajor(ambitMajor);
        }
        Snowflake snowflake = new Snowflake(1, 1);
        long id = snowflake.generateId();
        //查看课程联盟课属性
        KnowledgeBase knowledgeBase = knowledgeBaseMapper.selectKnowledgeBaseById(ambitMajor.getDisciplineId());
        String kbId = knowledgeBase.getKbId();
        if (ambitMajor.getFileIds() != null && ambitMajor.getFileIds().length > 0) {
            remoteFileService.relationFile(ambitMajor.getFileIds(),String.valueOf(id));
            List<SysFileInfo> fileInfoList = remoteFileService.getFileInfoList(String.valueOf(id));
                List<String> filePathList = fileInfoList.stream()
                        .map(SysFileInfo::getFilePath)
                        .collect(Collectors.toList());
                String[] strings = importFileNew(nacosClient.getSecretkey(), kbId, filePathList, knowledgeBase.getIsEnhanced(),ambitMajor.getParseFlag().equals("0")?"qa":"raw_text");
                for (int i = 0; i < strings.length; i++) {
                    SysFileInfo sysFileInfo = fileInfoList.get(i);
                    KnowledgeBaseFile knowledgeBaseFile = new KnowledgeBaseFile();
                    knowledgeBaseFile.setId(id);
                    knowledgeBaseFile.setFileName(sysFileInfo.getFileOriginName());
                    knowledgeBaseFile.setFilePath(sysFileInfo.getFilePath());
                    knowledgeBaseFile.setFileId(strings[i]);
                    knowledgeBaseFile.setKbId(kbId);
                    knowledgeBaseFile.setMajorId(ambitMajor.getMajorId());
                    knowledgeBaseFile.setCourseName(ambitMajor.getCourseName());
                    knowledgeBaseFile.setDisciplineId(ambitMajor.getDisciplineId());
                    knowledgeBaseFile.setParseStatus("notStarted");
                    knowledgeBaseFile.setSubmitStatus("F");
                    knowledgeBaseFile.setExamineFlag("0");
                    knowledgeBaseFile.setParseFlag(ambitMajor.getParseFlag());
                    knowledgeBaseFile.setCreateBy(SecurityUtils.getUsername());
                    knowledgeBaseFile.setCreateTime(DateUtils.getNowDate());
                    knowledgeBaseFile.setIsAllianceCourse(ambitMajor.getIsAllianceCourse());
                    int i1 = knowledgeBaseFileMapper.insertKnowledgeBaseFile(knowledgeBaseFile);
                }
           if(ambitMajor.getParseFlag().equals("0")){
               return insertKnowledgeInformation(fileInfoList);
            }
        }else {
            List<String> list = knowledgeBaseFileMapper.selectIsAllianceCourseList(ambitMajor.getMajorId(), ambitMajor.getCourseName());
            if (list.size()==0) {


                KnowledgeBaseFile knowledgeBaseFile = new KnowledgeBaseFile();
                knowledgeBaseFile.setId(id);
                knowledgeBaseFile.setKbId(kbId);
                knowledgeBaseFile.setMajorId(ambitMajor.getMajorId());
                knowledgeBaseFile.setCourseName(ambitMajor.getCourseName());
                knowledgeBaseFile.setDisciplineId(ambitMajor.getDisciplineId());
                knowledgeBaseFile.setParseStatus("notStarted");
                knowledgeBaseFile.setSubmitStatus("F");
                knowledgeBaseFile.setCreateTime(DateUtils.getNowDate());
                knowledgeBaseFile.setCreateBy(SecurityUtils.getUsername());
                knowledgeBaseFile.setIsAllianceCourse(ambitMajor.getIsAllianceCourse());
                int i1 = knowledgeBaseFileMapper.insertKnowledgeBaseFile(knowledgeBaseFile);
            }else {
                throw new Exception("该课程已存在");
            }
        }

        return 1;
    }

    //导入文件到知识库，返回文件id
    public String[] importFile(String secretkey, String datasetId, List<String> filePath, Boolean Enhanced) {
        BaiduDto baiduDto = new BaiduDto();
        baiduDto.setSecretkey(secretkey);
        baiduDto.setDatasetId(datasetId);
        baiduDto.setFilePath(filePath);
        baiduDto.setEnhanced(Enhanced);
        return baiduApiService.importFile(baiduDto, SecurityConstants.INNER);
    }

    //导入文件到知识库，返回文件id
    public String[] importFileNew(String secretkey,String datasetId, List<String> filePath,Boolean Enhanced ,String contentFormat) {
        BaiduDto baiduDto = new BaiduDto();
        baiduDto.setSecretkey(secretkey);
        baiduDto.setDatasetId(datasetId);
        baiduDto.setFilePath(filePath);
        baiduDto.setEnhanced(Enhanced);
        baiduDto.setContentFormat(contentFormat);
        return baiduApiService.importFileNew(baiduDto, SecurityConstants.INNER);
    }

    /**
     * 修改学科专业
     *
     * @param ambitMajor 学科专业
     * @return 结果
     */
    @Override
    public int updateAmbitMajor(AmbitMajor ambitMajor) {
        ambitMajor.setUpdateTime(DateUtils.getNowDate());
        return ambitMajorMapper.updateAmbitMajor(ambitMajor);
    }

    /**
     * 批量删除学科专业
     *
     * @param ids 需要删除的学科专业主键
     * @return 结果
     */
    @Override
    public int deleteAmbitMajorByIds(Long[] ids) {
        return ambitMajorMapper.deleteAmbitMajorByIds(ids);
    }

    /**
     * 删除学科专业信息
     *
     * @param id 学科专业主键
     * @return 结果
     */
    @Override
    public int deleteAmbitMajorById(Long id) {
        return ambitMajorMapper.deleteAmbitMajorById(id);
    }


    @Override
    public AjaxResult selectGroupAmbit(AmbitMajor ambitMajor) {
        Snowflake snowflake = new Snowflake(1, 1);
        Long majorId = ambitMajor.getMajorId();

        List<KnowledgeBaseFile> knowledgeBaseFileList = knowledgeBaseFileMapper.selectKnowledgeBaseFileByMajorId(majorId);





        // 初始化节点数据列表
        List<LiteratrueNodesData> literatrueNodesDataList = new ArrayList<>();
        // 初始化连接数据列表
        List<LiteratrueLinks> literatrueLinksList = new ArrayList<>();

        List<Long> secondNadeList =new ArrayList<>();
//       添加顶级节点
        literatrueNodesDataList.add(createLiteratrueNodesData(0L, 1L, ambitMajor.getMajorName(),ambitMajor.getMajorName()));
        //二级节点
        String[] arr ={"教材","课程","教师"};
        for (int i =0;i<arr.length;i++){
            literatrueNodesDataList.add(createLiteratrueNodesData(i+1L, 2L,arr[i] ,""));
            secondNadeList.add(i+1L);
        }
        //三级节点
        //教材信息
        for (KnowledgeBaseFile knowledgeBaseFile : knowledgeBaseFileList) {
            addNodesAndLinksTmp(knowledgeBaseFile.getFileName(), 1L, 3L, literatrueNodesDataList, literatrueLinksList,"",snowflake);
        }
        //课程信息
        HashSet<String> stringHashSet = new HashSet<>();
        for (KnowledgeBaseFile knowledgeBaseFile : knowledgeBaseFileList) {
            stringHashSet.add(knowledgeBaseFile.getCourseName());
        }
        for (String s : stringHashSet) {
            addNodesAndLinksTmp(s, 2L, 4L, literatrueNodesDataList, literatrueLinksList,"",snowflake);
        }
        //教师信息
        List<AmbitTeacher> ambitTeacherList =  ambitTeacherMapper.selectAmbitTeacherByMajorId(majorId);
        for (AmbitTeacher ambitTeacher : ambitTeacherList) {
            addNodesAndLinksTmp(ambitTeacher.getName(), 3L, 5L, literatrueNodesDataList, literatrueLinksList,"",snowflake);

        }





//        Snowflake snowflake = new Snowflake(1, 1);
//        if(sTextbookData.getKeywordValue().equals("academic_discipline")&&StringUtils.isBlank(sTextbookData.getKeyword())){
//            sTextbookData.setKeyword("新文科");
//        }
//        // 设置默认关键词
//        List<SysDictData> knowledgeGraphList = remoteDictTypeService.dictTypeGetInfo("knowledge_graph", SecurityConstants.INNER);
//        List<SysDictData> knowledgeTypeList = remoteDictTypeService.dictTypeGetInfo("knowledge_type", SecurityConstants.INNER);
//        // 初始化节点数据列表
//        List<LiteratrueNodesData> literatrueNodesDataList = new ArrayList<>();
//        // 初始化连接数据列表
//        List<LiteratrueLinks> literatrueLinksList = new ArrayList<>();
//
//        // 添加顶级节点
//        literatrueNodesDataList.add(createLiteratrueNodesData(0L, 1L, sTextbookData.getKeyword(),sTextbookData.getKeywordValue()));
//        //二级节点
//        List<Long> secondNadeList =new ArrayList<>();
//        List<TextbookData>  textbookDataList = new ArrayList<>();
//        SysDictData result = knowledgeGraphList.stream()
//                .filter(data -> sTextbookData.getKeywordValue().equals(data.getDictLabel()))
//                .findFirst()
//                .orElse(null);
//        if (result==null){
//            return  AjaxResult.error("此关键词未定义相关信息");
//        }
//
//
//        if("knowledge_category".equals(sTextbookData.getKeywordValue())){
//            TextbookKeywordData textbookKeywordData = new TextbookKeywordData();
//            textbookKeywordData.setKnowledgeCategory(sTextbookData.getKeyword());
//            List<String> category =sTextbookKeywordDataMapper.getCategory(textbookKeywordData);
//            for (int i = 0; i <category.size() ; i++) {
//                literatrueNodesDataList.add(createLiteratrueNodesData(i+1L, 2L, category.get(i),"category"));
//                sTextbookData.setCategory(category.get(i));
//                sTextbookData.setField("keyword");
//                textbookDataList = sTextbookDataMapper.selectTextbookDataKnowledgeGraph(sTextbookData);
//                addNodesAndLinks(textbookDataList, i+1L, 3L+i, literatrueNodesDataList, literatrueLinksList,"keyword",snowflake);
//                secondNadeList.add(i+1L);
//            }
//        }else{
//            String[] split =  result.getDictValue().split(",");
//            for (int i = 0; i <split.length ; i++) {
//                String dictValue = split[i];
//                SysDictData resultNodesData = knowledgeTypeList.stream()
//                        .filter(data -> dictValue.equals(data.getDictValue()))
//                        .findFirst()
//                        .orElse(null);
//                if("knowledge".equals(dictValue)){
//                    literatrueNodesDataList.add(createLiteratrueNodesData(i+1L, 2L, resultNodesData.getDictLabel(),resultNodesData.getDictValue()));
//                    SysDictData result1 = knowledgeGraphList.stream()
//                            .filter(data -> dictValue.equals(data.getDictLabel()))
//                            .findFirst()
//                            .orElse(null);
//                    List<String> category =Arrays.asList(result1.getDictValue().split(","));
//                    addNodesAndLinksBYknowledgeCategory(category,i+1L, 3L+i, literatrueNodesDataList, literatrueLinksList,"knowledge_category",snowflake);
//                    secondNadeList.add(i+1L);
//                } else {
//                    literatrueNodesDataList.add(createLiteratrueNodesData(i+1L, 2L, resultNodesData.getDictLabel(),resultNodesData.getDictValue()));
//                    sTextbookData.setField(dictValue);
//                    textbookDataList = sTextbookDataMapper.selectTextbookDataKnowledgeGraph(sTextbookData);
//
//                    addNodesAndLinks(textbookDataList, i+1L, 3L+i, literatrueNodesDataList, literatrueLinksList,dictValue,snowflake);
//                    secondNadeList.add(i+1L);
//                }
//            }
//        }
//
//        // 添加源节点链接
        addSourceNodeLinks(literatrueLinksList,secondNadeList);
        ambitMajor.setLiteratrueLinks(literatrueLinksList);
        ambitMajor.setLiteratrueNodesData(literatrueNodesDataList);
        return AjaxResult.success(ambitMajor);
//        return null;
    }

    private LiteratrueNodesData createLiteratrueNodesData(Long id, Long gradeFlag, String name,String keywordValue) {
        LiteratrueNodesData literatrueNodesData = new LiteratrueNodesData();
        literatrueNodesData.setId(id);
        literatrueNodesData.setName(name);
        literatrueNodesData.setGradeFlag(gradeFlag);
        literatrueNodesData.setKeywordValue(keywordValue);
        return literatrueNodesData;
    }

    /**
     * 方法用于添加节点和链接层级数据
     * @param textbookData 节点数据
     * @param sourceId   父级节点
     * @param gradeFlag  （控制谱图节点颜色）
     * @param nodesDataList 节点集合
     * @param linksList 节点关联关系集合
     * @param dictValue 节点类型
     * @param snowflake 生成id
     */
    private void addNodesAndLinks(List<TextbookData> textbookData, Long sourceId, Long gradeFlag,
                                  List<LiteratrueNodesData> nodesDataList, List<LiteratrueLinks> linksList,String dictValue,Snowflake snowflake) {

        for (TextbookData item : textbookData) {
            String name = null;
            if(dictValue.equals("academic_discipline")){
                name =  item.getAcademicDiscipline();
            }else if(dictValue.equals("colle_name")){
                name =  item.getColleName();
            }else if(dictValue.equals("major_name")){
                name =  item.getMajorName();
            }else if(dictValue.equals("publishing_house")){
                name =  item.getPublishingHouse();
            }else if(dictValue.equals("textbook")){
                name =  item.getTextbook();
            }else if(dictValue.equals("author")){
                name =  item.getAuthor();
            }else{
                name =  item.getKeyword();
            }

            long id = snowflake.generateId();
            LiteratrueNodesData literatrueNodesData = createLiteratrueNodesData(id, gradeFlag,name,dictValue);
            literatrueNodesData.setCount(item.getCount());
            nodesDataList.add(literatrueNodesData);
            LiteratrueLinks literatrueLinks = new LiteratrueLinks();
            literatrueLinks.setSource(sourceId);
            literatrueLinks.setTarget(id);
            linksList.add(literatrueLinks);
        }
    }

    /**
     * 方法用于添加节点和链接层级数据
     * @param data 节点数据
     * @param sourceId   父级节点
     * @param gradeFlag  （控制谱图节点颜色）
     * @param nodesDataList 节点集合
     * @param linksList 节点关联关系集合
     * @param dictValue 节点类型
     * @param snowflake 生成id
     */
    private void addNodesAndLinksTmp(String data, Long sourceId, Long gradeFlag,
                                  List<LiteratrueNodesData> nodesDataList, List<LiteratrueLinks> linksList,String dictValue,Snowflake snowflake) {
            long id = snowflake.generateId();
            LiteratrueNodesData literatrueNodesData = createLiteratrueNodesData(id, gradeFlag,data,dictValue);
            nodesDataList.add(literatrueNodesData);
            LiteratrueLinks literatrueLinks = new LiteratrueLinks();
            literatrueLinks.setSource(sourceId);
            literatrueLinks.setTarget(id);
            linksList.add(literatrueLinks);
    }


    /**
     * 方法用于添加节点和链接层级数据
     * @param textbookData 节点数据
     * @param sourceId   父级节点
     * @param gradeFlag  （控制谱图节点颜色）
     * @param nodesDataList 节点集合
     * @param linksList 节点关联关系集合
     * @param dictValue 节点类型
     * @param snowflake 生成id
     */
    private void addNodesAndLinksBYknowledgeCategory(List<String> textbookData, Long sourceId, Long gradeFlag,
                                                     List<LiteratrueNodesData> nodesDataList, List<LiteratrueLinks> linksList,String dictValue,Snowflake snowflake) {

        for (String item : textbookData) {

            long id = snowflake.generateId();
            LiteratrueNodesData literatrueNodesData = createLiteratrueNodesData(id, gradeFlag,item,dictValue);
            literatrueNodesData.setCount(null);
            nodesDataList.add(literatrueNodesData);
            LiteratrueLinks literatrueLinks = new LiteratrueLinks();
            literatrueLinks.setSource(sourceId);
            literatrueLinks.setTarget(id);
            linksList.add(literatrueLinks);
        }
    }

    /**
     * 初始节点链接子节点数据
     * @param linksList
     * @param secondNadeList
     */
    //方法用于添加源节点链接
    private void addSourceNodeLinks(List<LiteratrueLinks> linksList,List<Long> secondNadeList) {
        Long sourceNodeId = 0L;
        for (Long item : secondNadeList) {
            LiteratrueLinks literatrueLinks = new LiteratrueLinks();
            literatrueLinks.setSource(sourceNodeId);
            literatrueLinks.setTarget(item);
            linksList.add(literatrueLinks);
        }
    }

    /**
     * 可解析文件-信息入库
     * @return
     */
    public int insertKnowledgeInformation(List<SysFileInfo> fileInfoList) {
        // 读取文件
        Message message = new Message();
        InputStream inputStream = null;
        EasyExcelUtils<KnowledgeInformation> easyExcelUtils = new EasyExcelUtils<>(KnowledgeInformation.class, true, 1);  // 创建工具类时传递class，用于后面比对表头使用
        List<KnowledgeInformation> roadDataList = new ArrayList<>();

        try {
            for (SysFileInfo sysFileInfo : fileInfoList) {
                inputStream = new FileInputStream(sysFileInfo.getFilePath());
                // 读取所有的sheet
                EasyExcel.read(inputStream, KnowledgeInformation.class, easyExcelUtils)
                        .headRowNumber(easyExcelUtils.getHeadStartRow())  // 设置从哪一行开始读取表头
                        .doReadAll();

                message = easyExcelUtils.getMessage();
                if (Message.OK == message.getType()) {
                    List<KnowledgeInformation> list =  easyExcelUtils.getList();
                    if (list != null && !list.isEmpty()) {
                        Date nowDate = new Date();
                        for (int i = 0; i < list.size(); i++) { // 设置其他非excel字段的值
                            KnowledgeInformation roadData = (KnowledgeInformation) list.get(i);
                            roadData.setFileId(Long.parseLong(sysFileInfo.getBusiId()));
                            roadDataList.add(roadData);
                        }
                    }
                }
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        if(roadDataList.size()>0){
            return knowledgeInformationMapper.insertKnowledgeInformationList(roadDataList);
        }

        return 1;
    }


    @Override
    public AjaxResult courseAtlas(AmbitMajor ambitMajor) {
        Snowflake snowflake = new Snowflake(1, 1);
        //课程名称
        String courseName = ambitMajor.getCourseName();


        List<KnowledgeBaseFile> knowledgeBaseFileList = knowledgeBaseFileMapper.selectKnowledgeBaseFileByCourseName(courseName);





        // 初始化节点数据列表
        List<LiteratrueNodesData> literatrueNodesDataList = new ArrayList<>();
        // 初始化连接数据列表
        List<LiteratrueLinks> literatrueLinksList = new ArrayList<>();

        List<Long> secondNadeList =new ArrayList<>();
//       添加顶级节点
        literatrueNodesDataList.add(createLiteratrueNodesData(0L, 1L, courseName,courseName));
        //二级节点
        String[] arr ={"教材","教师"};
        for (int i =0;i<arr.length;i++){
            literatrueNodesDataList.add(createLiteratrueNodesData(i+1L, 2L,arr[i] ,""));
            secondNadeList.add(i+1L);
        }
        //三级节点
        //教材信息
        for (KnowledgeBaseFile knowledgeBaseFile : knowledgeBaseFileList) {
            addNodesAndLinksTmp(knowledgeBaseFile.getFileName(), 1L, 3L, literatrueNodesDataList, literatrueLinksList,"",snowflake);
        }

        //教师信息
        List<AmbitTeacher> ambitTeacherList =  ambitTeacherMapper.selectAmbitTeacherByCourseName(courseName);
        for (AmbitTeacher ambitTeacher : ambitTeacherList) {
            addNodesAndLinksTmp(ambitTeacher.getName(), 2L, 5L, literatrueNodesDataList, literatrueLinksList,"",snowflake);

        }

//        // 添加源节点链接
        addSourceNodeLinks(literatrueLinksList,secondNadeList);
        ambitMajor.setLiteratrueLinks(literatrueLinksList);
        ambitMajor.setLiteratrueNodesData(literatrueNodesDataList);
        return AjaxResult.success(ambitMajor);
//        return null;
    }

    @Override
    public String getAllianceCourse(AmbitMajor ambitMajor) {
        Long[] affiliatedUnit = ambitMajor.getAffiliatedUnit();

        if(affiliatedUnit!=null&&affiliatedUnit.length>2){
            ambitMajor.setMajorId(affiliatedUnit[2]); //

        }

        List<String> list = knowledgeBaseFileMapper.selectIsAllianceCourseList(ambitMajor.getMajorId(), ambitMajor.getCourseName());
        if(list.size()>0){
            return list.get(0);
        }else {
            return "";
        }
    }
}
