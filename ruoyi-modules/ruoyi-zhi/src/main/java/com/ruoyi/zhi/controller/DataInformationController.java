package com.ruoyi.zhi.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.zhi.domain.DataInformation;
import com.ruoyi.zhi.service.IDataInformationService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 数据集明细prompt+responseController
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
@RestController
@RequestMapping("/information")
public class DataInformationController extends BaseController
{
    @Autowired
    private IDataInformationService dataInformationService;

    /**
     * 查询数据集明细prompt+response列表
     */
    @RequiresPermissions("create:information:list")
    @GetMapping("/list")
    public TableDataInfo list(DataInformation dataInformation)
    {
        startPage();
        List<DataInformation> list = dataInformationService.selectDataInformationList(dataInformation);
        return getDataTable(list);
    }

    /**
     * 导出数据集明细prompt+response列表
     */
    @RequiresPermissions("create:information:export")
    @Log(title = "数据集明细prompt+response", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DataInformation dataInformation)
    {
        List<DataInformation> list = dataInformationService.selectDataInformationList(dataInformation);
        ExcelUtil<DataInformation> util = new ExcelUtil<DataInformation>(DataInformation.class);
        util.exportExcel(response, list, "数据集明细prompt+response数据");
    }

    /**
     * 获取数据集明细prompt+response详细信息
     */
    @RequiresPermissions("create:information:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dataInformationService.selectDataInformationById(id));
    }

    /**
     * 新增数据集明细prompt+response
     */
    @RequiresPermissions("create:information:add")
    @Log(title = "数据集明细prompt+response", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DataInformation dataInformation)
    {
        return toAjax(dataInformationService.insertDataInformation(dataInformation));
    }

    /**
     * 修改数据集明细prompt+response
     */
    @RequiresPermissions("create:information:edit")
    @Log(title = "数据集明细prompt+response", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DataInformation dataInformation)
    {
        return toAjax(dataInformationService.updateDataInformation(dataInformation));
    }

    /**
     * 删除数据集明细prompt+response
     */
    @RequiresPermissions("create:information:remove")
    @Log(title = "数据集明细prompt+response", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dataInformationService.deleteDataInformationByIds(ids));
    }
}
