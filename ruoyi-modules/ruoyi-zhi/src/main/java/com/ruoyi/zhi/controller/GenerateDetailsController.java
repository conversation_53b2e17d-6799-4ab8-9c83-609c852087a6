package com.ruoyi.zhi.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.zhi.domain.GenerateDetails;
import com.ruoyi.zhi.service.IGenerateDetailsService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 数据集制作详情Controller
 * 
 * <AUTHOR>
 * @date 2024-07-18
 */
@RestController
@RequestMapping("/generate")
public class GenerateDetailsController extends BaseController
{
    @Autowired
    private IGenerateDetailsService generateDetailsService;

    /**
     * 查询数据集制作详情列表
     */
    @RequiresPermissions("create:details:list")
    @GetMapping("/list")
    public TableDataInfo list(GenerateDetails generateDetails)
    {
        startPage();
        List<GenerateDetails> list = generateDetailsService.selectGenerateDetailsList(generateDetails);
        return getDataTable(list);
    }

    /**
     * 导出数据集制作详情列表
     */
    @RequiresPermissions("create:details:export")
    @Log(title = "数据集制作详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GenerateDetails generateDetails)
    {
        List<GenerateDetails> list = generateDetailsService.selectGenerateDetailsList(generateDetails);
        ExcelUtil<GenerateDetails> util = new ExcelUtil<GenerateDetails>(GenerateDetails.class);
        util.exportExcel(response, list, "Sheet1");
    }

    /**
     * 获取数据集制作详情详细信息
     */
    @RequiresPermissions("create:details:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(generateDetailsService.selectGenerateDetailsById(id));
    }

    /**
     * 新增数据集制作详情
     */
    @RequiresPermissions("create:details:add")
    @Log(title = "数据集制作详情", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GenerateDetails generateDetails)
    {
        return toAjax(generateDetailsService.insertGenerateDetails(generateDetails));
    }

    /**
     * 修改数据集制作详情
     */
    @RequiresPermissions("create:details:edit")
    @Log(title = "数据集制作详情", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GenerateDetails generateDetails)
    {
        return toAjax(generateDetailsService.updateGenerateDetails(generateDetails));
    }

    /**
     * 删除数据集制作详情
     */
    @RequiresPermissions("create:details:remove")
    @Log(title = "数据集制作详情", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(generateDetailsService.deleteGenerateDetailsByIds(ids));
    }
}
