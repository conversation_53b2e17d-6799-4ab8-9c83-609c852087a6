package com.ruoyi.zhi.controller;

import java.io.ByteArrayInputStream;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.zhi.domain.DataSet;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.CacheControl;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.zhi.domain.DataInformationTemporary;
import com.ruoyi.zhi.service.IDataInformationTemporaryService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 临时数据集明细prompt+responseController
 * 
 * <AUTHOR>
 * @date 2024-05-28
 */
@RestController
@RequestMapping("/temporaryDes")
public class DataInformationTemporaryController extends BaseController
{
    @Autowired
    private IDataInformationTemporaryService dataInformationTemporaryService;

    /**
     * 查询临时数据集明细prompt+response列表
     */
    @RequiresPermissions("create:temporaryDes:list")
    @GetMapping("/list")
    public TableDataInfo list(DataInformationTemporary dataInformationTemporary)
    {
        startPage();
        List<DataInformationTemporary> list = dataInformationTemporaryService.selectDataInformationTemporaryList(dataInformationTemporary);
        return getDataTable(list);
    }

    /**
     * 导出临时数据集明细prompt+response列表
     */
    @RequiresPermissions("create:temporaryDes:export")
    @Log(title = "临时数据集明细prompt+response", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DataInformationTemporary dataInformationTemporary)
    {
        List<DataInformationTemporary> list = dataInformationTemporaryService.selectDataInformationTemporaryList(dataInformationTemporary);
        ExcelUtil<DataInformationTemporary> util = new ExcelUtil<DataInformationTemporary>(DataInformationTemporary.class);
        util.exportExcel(response, list, "临时数据集明细prompt+response数据");
    }

    /**
     * 获取临时数据集明细prompt+response详细信息
     */
    @RequiresPermissions("create:temporaryDes:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dataInformationTemporaryService.selectDataInformationTemporaryById(id));
    }

    /**
     * 新增临时数据集明细prompt+response
     */
    @RequiresPermissions("create:temporaryDes:add")
    @Log(title = "临时数据集明细prompt+response", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DataInformationTemporary dataInformationTemporary)
    {
        return toAjax(dataInformationTemporaryService.insertDataInformationTemporary(dataInformationTemporary));
    }

    /**
     * 修改临时数据集明细prompt+response
     */
    @RequiresPermissions("create:temporaryDes:edit")
    @Log(title = "临时数据集明细prompt+response", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DataInformationTemporary dataInformationTemporary)
    {
        return toAjax(dataInformationTemporaryService.updateDataInformationTemporary(dataInformationTemporary));
    }

    /**
     * 删除临时数据集明细prompt+response
     */
    @RequiresPermissions("create:temporaryDes:remove")
    @Log(title = "临时数据集明细prompt+response", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dataInformationTemporaryService.deleteDataInformationTemporaryByIds(ids));
    }

    /**
     * 导出临时数据集明细
     * @return
     */

    @ApiOperation(value = "导出数据集列表", notes = "导出数据集列表", httpMethod = "POST")
    @Log(title = "数据集", businessType = BusinessType.EXPORT)
    @RequiresPermissions("create:temporaryDes:export")
    @GetMapping("/export/{dataId}")
    public ResponseEntity<InputStreamResource> exportData(@PathVariable("dataId") Long dataId) throws IOException {
        // 设置响应头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.TEXT_PLAIN);
        headers.setContentDispositionFormData("attachment", "output.txt");
        headers.setCacheControl(CacheControl.noCache());
        byte[] bytes = dataInformationTemporaryService.exportData(dataId);
        return ResponseEntity
                .ok()
                .headers(headers)
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(new InputStreamResource(new ByteArrayInputStream(bytes)));

    }
}
