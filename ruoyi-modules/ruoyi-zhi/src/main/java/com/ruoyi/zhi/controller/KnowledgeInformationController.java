package com.ruoyi.zhi.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.zhi.domain.KnowledgeInformation;
import com.ruoyi.zhi.service.IKnowledgeInformationService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 数据集文件问题Controller
 * 
 * <AUTHOR>
 * @date 2024-09-11
 */
@RestController
@RequestMapping("/knowledgeInformation")
public class KnowledgeInformationController extends BaseController
{
    @Autowired
    private IKnowledgeInformationService knowledgeInformationService;

    /**
     * 查询数据集文件问题列表
     */
    @RequiresPermissions("zhi:knowledgeInformation:list")
    @GetMapping("/list")
    public TableDataInfo list(KnowledgeInformation knowledgeInformation)
    {
        startPage();
        List<KnowledgeInformation> list = knowledgeInformationService.selectKnowledgeInformationList(knowledgeInformation);
        return getDataTable(list);
    }

    /**
     * 导出数据集文件问题列表
     */
    @RequiresPermissions("zhi:knowledgeInformation:export")
    @Log(title = "数据集文件问题", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KnowledgeInformation knowledgeInformation)
    {
        List<KnowledgeInformation> list = knowledgeInformationService.selectKnowledgeInformationList(knowledgeInformation);
        ExcelUtil<KnowledgeInformation> util = new ExcelUtil<KnowledgeInformation>(KnowledgeInformation.class);
        util.exportExcel(response, list, "数据集文件问题数据");
    }

    /**
     * 获取数据集文件问题详细信息
     */
    @RequiresPermissions("zhi:knowledgeInformation:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(knowledgeInformationService.selectKnowledgeInformationById(id));
    }

    /**
     * 新增数据集文件问题
     */
    @RequiresPermissions("zhi:knowledgeInformation:add")
    @Log(title = "数据集文件问题", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KnowledgeInformation knowledgeInformation)
    {
        return toAjax(knowledgeInformationService.insertKnowledgeInformation(knowledgeInformation));
    }

    /**
     * 修改数据集文件问题
     */
    @RequiresPermissions("zhi:knowledgeInformation:edit")
    @Log(title = "数据集文件问题", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KnowledgeInformation knowledgeInformation)
    {
        return toAjax(knowledgeInformationService.updateKnowledgeInformation(knowledgeInformation));
    }

    /**
     * 删除数据集文件问题
     */
    @RequiresPermissions("zhi:knowledgeInformation:remove")
    @Log(title = "数据集文件问题", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(knowledgeInformationService.deleteKnowledgeInformationByIds(ids));
    }
}
