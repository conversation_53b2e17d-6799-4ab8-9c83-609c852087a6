package com.ruoyi.zhi.controller;

import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.core.utils.ServletUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.zhi.domain.AmbitTeacher;
import com.ruoyi.zhi.domain.DataSet;
import com.ruoyi.zhi.domain.PoductionTask;
import com.ruoyi.zhi.service.IDataSetService;
import com.ruoyi.zhi.utils.Snowflake;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.FileNotFoundException;
import java.util.List;

import static com.ruoyi.common.core.web.page.TableSupport.PAGE_NUM;
import static com.ruoyi.common.core.web.page.TableSupport.PAGE_SIZE;

/**
 * 数据集Controller   数据集管理页面
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
@RestController
@RequestMapping("/dataSet")
public class DataSetController extends BaseController
{
    @Autowired
    private IDataSetService dataSetService;

    /**
     * 查询数据集列表
     */
    @RequiresPermissions("create:dataSet:list")
    @GetMapping("/list")
    public TableDataInfo list(DataSet dataSet)
    {
        if(ServletUtils.getParameter(PAGE_NUM)!=null&&ServletUtils.getParameter(PAGE_SIZE)!=null){
            startPage();
        }
        List<DataSet> list = dataSetService.selectDataSetList(dataSet);
        return getDataTable(list);
    }


    /**
     * 查询是否是负责人
     */
    @RequiresPermissions("create:dataSet:cheackChargeFlag")
    @PostMapping("/cheackChargeFlag")
    public Boolean cheackChargeFlag()
    {
        return dataSetService.cheackChargeFlag();
    }

    /**
     * 查询负责人课程
     */
    @RequiresPermissions("create:dataSet:cheackChargeClass")
    @PostMapping("/cheackChargeClass")
    public AjaxResult cheackChargeClass(@RequestBody AmbitTeacher ambitTeacher)
    {
        return success(dataSetService.cheackChargeClass(ambitTeacher));
    }


    /**
     * 导出数据集列表
     */
    @RequiresPermissions("create:dataSet:export")
    @Log(title = "数据集", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DataSet dataSet)
    {
        List<DataSet> list = dataSetService.selectDataSetList(dataSet);
        ExcelUtil<DataSet> util = new ExcelUtil<DataSet>(DataSet.class);
        util.exportExcel(response, list, "数据集数据");
    }

    /**
     * 导出数据集
     * @param id
     * @param request
     * @param response
     */

    @RequiresPermissions("create:dataSet:fileDownload")
    @PostMapping("/fileDownload")
    public void fileDownload(@RequestParam Long id, HttpServletRequest request, HttpServletResponse response){
        dataSetService.fileDownload(id,request,response);
    }
    /**
     * 获取数据集详细信息
     */
    @RequiresPermissions("create:dataSet:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dataSetService.selectDataSetById(id));
    }

    /**
     * 新增数据集
     */
    @RequiresPermissions("create:dataSet:add")
    @Log(title = "数据集", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DataSet dataSet)
    {
        return toAjax(dataSetService.insertDataSet(dataSet));
    }

    /**
     * 修改数据集
     */
    @RequiresPermissions("create:dataSet:edit")
    @Log(title = "数据集", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DataSet dataSet)
    {
        return toAjax(dataSetService.updateDataSet(dataSet));
    }

    /**
     * 删除数据集
     */
    @RequiresPermissions("create:dataSet:remove")
    @Log(title = "数据集", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dataSetService.deleteDataSetByIds(ids));
    }

    /**
     * 获取已发布数据集
     * @return
     */
    @RequiresPermissions("create:dataSet:published")
    @PostMapping("/published")
    public AjaxResult selectDataSetPublished(@RequestBody String menuRouting)
    {
        return success(dataSetService.selectDataSetPublished(menuRouting));
    }

    /**
     * 查询数据集列表
     */
    @RequiresPermissions("create:dataSet:all")
    @PostMapping("/all")
    public AjaxResult selectDataSetAll(@RequestBody String menuRouting)
    {
        List<DataSet> list = dataSetService.selectDataSetAll(menuRouting);
        return success(list);
    }

    /**
     * 数据集发布
     * @param dataSet
     * @return
     */
    @RequiresPermissions("create:dataSet:release")
    @Log(title = "数据集", businessType = BusinessType.INSERT)
    @PostMapping("/release")
    public AjaxResult release(@RequestBody DataSet dataSet) throws FileNotFoundException {
        int flag= dataSetService.dataRelease(dataSet);
        if(flag==0){
            return error("发布失败");
        }
        return success(flag);
    }


    /**
     *
     */
    @RequiresPermissions("create:dataSet:add")
    @Log(title = "数据生成转存数据集", businessType = BusinessType.INSERT)
    @PostMapping("/poductionToDataSet")
    public AjaxResult poductionToDataSet(@RequestBody PoductionTask poductionTask) {

        if (dataSetService.poductionToDataSet(poductionTask)){
            return success("成功制作");
        }else {
            return error("数据集制作失败，请稍后再试。。。");
        }

    }

}
