package com.ruoyi.zhi.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.zhi.domain.ChapterParse;
import com.ruoyi.zhi.service.IChapterParseService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 知识库章节解析Controller
 * 
 * <AUTHOR>
 * @date 2024-10-14
 */
@RestController
@RequestMapping("/parse")
public class ChapterParseController extends BaseController
{
    @Autowired
    private IChapterParseService chapterParseService;

    /**
     * 查询知识库章节解析列表
     */
    @RequiresPermissions("zhi:parse:list")
    @GetMapping("/list")
    public TableDataInfo list(ChapterParse chapterParse)
    {
        startPage();
        List<ChapterParse> list = chapterParseService.selectChapterParseList(chapterParse);
        return getDataTable(list);
    }

    /**
     * 导出知识库章节解析列表
     */
    @RequiresPermissions("zhi:parse:export")
    @Log(title = "知识库章节解析", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ChapterParse chapterParse)
    {
        List<ChapterParse> list = chapterParseService.selectChapterParseList(chapterParse);
        ExcelUtil<ChapterParse> util = new ExcelUtil<ChapterParse>(ChapterParse.class);
        util.exportExcel(response, list, "知识库章节解析数据");
    }

    /**
     * 获取知识库章节解析详细信息
     */
    @RequiresPermissions("zhi:parse:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(chapterParseService.selectChapterParseById(id));
    }

    /**
     * 新增知识库章节解析
     */
    @RequiresPermissions("zhi:parse:add")
    @Log(title = "知识库章节解析", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ChapterParse chapterParse)
    {
        return toAjax(chapterParseService.insertChapterParse(chapterParse));
    }

    /**
     * 修改知识库章节解析
     */
    @RequiresPermissions("zhi:parse:edit")
    @Log(title = "知识库章节解析", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ChapterParse chapterParse)
    {
        return toAjax(chapterParseService.updateChapterParse(chapterParse));
    }

    /**
     * 删除知识库章节解析
     */
    @RequiresPermissions("zhi:parse:remove")
    @Log(title = "知识库章节解析", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(chapterParseService.deleteChapterParseByIds(ids));
    }
}
