package com.ruoyi.zhi.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.zhi.domain.CleaningTask;
import com.ruoyi.zhi.service.ICleaningTaskService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 清洗任务Controller
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
@RestController
@RequestMapping("/task")
public class CleaningTaskController extends BaseController
{
    @Autowired
    private ICleaningTaskService cleaningTaskService;

    /**
     * 查询清洗任务列表
     */
    @RequiresPermissions("create:task:list")
    @GetMapping("/list")
    public TableDataInfo list(CleaningTask cleaningTask)
    {
        startPage();
        List<CleaningTask> list = cleaningTaskService.selectCleaningTaskList(cleaningTask);
        return getDataTable(list);
    }

    /**
     * 导出清洗任务列表
     */
    @RequiresPermissions("create:task:export")
    @Log(title = "清洗任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CleaningTask cleaningTask)
    {
        List<CleaningTask> list = cleaningTaskService.selectCleaningTaskList(cleaningTask);
        ExcelUtil<CleaningTask> util = new ExcelUtil<CleaningTask>(CleaningTask.class);
        util.exportExcel(response, list, "清洗任务数据");
    }

    /**
     * 获取清洗任务详细信息
     */
    @RequiresPermissions("create:task:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(cleaningTaskService.selectCleaningTaskById(id));
    }

    /**
     * 新增清洗任务
     */
    @RequiresPermissions("create:task:add")
    @Log(title = "清洗任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CleaningTask cleaningTask)
    {
        return toAjax(cleaningTaskService.insertCleaningTask(cleaningTask));
    }

    /**
     * 修改清洗任务
     */
    @RequiresPermissions("create:task:edit")
    @Log(title = "清洗任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CleaningTask cleaningTask)
    {
        return toAjax(cleaningTaskService.updateCleaningTask(cleaningTask));
    }

    /**
     * 删除清洗任务
     */
    @RequiresPermissions("create:task:remove")
    @Log(title = "清洗任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(cleaningTaskService.deleteCleaningTaskByIds(ids));
    }
}
