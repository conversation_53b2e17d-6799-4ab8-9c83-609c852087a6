package com.ruoyi.zhi.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.zhi.domain.ApplicationScenarioPrompt;
import com.ruoyi.zhi.service.IApplicationScenarioPromptService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 应用场景promptController
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@RestController
@RequestMapping("/prompt")
public class ApplicationScenarioPromptController extends BaseController
{
    @Autowired
    private IApplicationScenarioPromptService applicationScenarioPromptService;

    /**
     * 查询应用场景prompt列表
     */
    @RequiresPermissions("zhi:prompt:list")
    @GetMapping("/list")
    public TableDataInfo list(ApplicationScenarioPrompt applicationScenarioPrompt)
    {
        startPage();
        List<ApplicationScenarioPrompt> list = applicationScenarioPromptService.selectApplicationScenarioPromptList(applicationScenarioPrompt);
        return getDataTable(list);
    }

    /**
     * 导出应用场景prompt列表
     */
    @RequiresPermissions("zhi:prompt:export")
    @Log(title = "应用场景prompt", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ApplicationScenarioPrompt applicationScenarioPrompt)
    {
        List<ApplicationScenarioPrompt> list = applicationScenarioPromptService.selectApplicationScenarioPromptList(applicationScenarioPrompt);
        ExcelUtil<ApplicationScenarioPrompt> util = new ExcelUtil<ApplicationScenarioPrompt>(ApplicationScenarioPrompt.class);
        util.exportExcel(response, list, "应用场景prompt数据");
    }

    /**
     * 获取应用场景prompt详细信息
     */
    @RequiresPermissions("zhi:prompt:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(applicationScenarioPromptService.selectApplicationScenarioPromptById(id));
    }

    /**
     * 新增应用场景prompt
     */
    @RequiresPermissions("zhi:prompt:add")
    @Log(title = "应用场景prompt", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ApplicationScenarioPrompt applicationScenarioPrompt)
    {
        return toAjax(applicationScenarioPromptService.insertApplicationScenarioPrompt(applicationScenarioPrompt));
    }

    /**
     * 修改应用场景prompt
     */
    @RequiresPermissions("zhi:prompt:edit")
    @Log(title = "应用场景prompt", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ApplicationScenarioPrompt applicationScenarioPrompt)
    {
        return toAjax(applicationScenarioPromptService.updateApplicationScenarioPrompt(applicationScenarioPrompt));
    }

    /**
     * 删除应用场景prompt
     */
    @RequiresPermissions("zhi:prompt:remove")
    @Log(title = "应用场景prompt", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(applicationScenarioPromptService.deleteApplicationScenarioPromptByIds(ids));
    }
}
