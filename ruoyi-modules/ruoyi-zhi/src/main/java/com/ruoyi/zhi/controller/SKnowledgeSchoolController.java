package com.ruoyi.zhi.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.zhi.domain.SKnowledgeSchool;
import com.ruoyi.zhi.service.ISKnowledgeSchoolService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 知识库信息Controller
 *
 * <AUTHOR>
 * @date 2024-11-27
 */
@RestController
@RequestMapping("/school")
public class SKnowledgeSchoolController extends BaseController
{
    @Autowired
    private ISKnowledgeSchoolService sKnowledgeSchoolService;

    /**
     * 查询知识库信息列表
     */
//    @RequiresPermissions("zhi:school:list")
    @GetMapping("/list")
    public TableDataInfo list(SKnowledgeSchool sKnowledgeSchool)
    {
        startPage();
        List<SKnowledgeSchool> list = sKnowledgeSchoolService.selectSKnowledgeSchoolList(sKnowledgeSchool);
        return getDataTable(list);
    }

    /**
     * 导出知识库信息列表
     */
//    @RequiresPermissions("zhi:school:export")
    @Log(title = "知识库信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SKnowledgeSchool sKnowledgeSchool)
    {
        List<SKnowledgeSchool> list = sKnowledgeSchoolService.selectSKnowledgeSchoolList(sKnowledgeSchool);
        ExcelUtil<SKnowledgeSchool> util = new ExcelUtil<SKnowledgeSchool>(SKnowledgeSchool.class);
        util.exportExcel(response, list, "知识库信息数据");
    }

    /**
     * 获取知识库信息详细信息
     */
//    @RequiresPermissions("zhi:school:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sKnowledgeSchoolService.selectSKnowledgeSchoolById(id));
    }

    /**
     * 新增知识库信息
     */
//    @RequiresPermissions("zhi:school:add")
    @Log(title = "知识库信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SKnowledgeSchool sKnowledgeSchool)
    {
        return toAjax(sKnowledgeSchoolService.insertSKnowledgeSchool(sKnowledgeSchool));
    }

    /**
     * 修改知识库信息
     */
//    @RequiresPermissions("zhi:school:edit")
    @Log(title = "知识库信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SKnowledgeSchool sKnowledgeSchool)
    {
        return toAjax(sKnowledgeSchoolService.updateSKnowledgeSchool(sKnowledgeSchool));
    }

    /**
     * 删除知识库信息
     */
//    @RequiresPermissions("zhi:school:remove")
    @Log(title = "知识库信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sKnowledgeSchoolService.deleteSKnowledgeSchoolByIds(ids));
    }
}
