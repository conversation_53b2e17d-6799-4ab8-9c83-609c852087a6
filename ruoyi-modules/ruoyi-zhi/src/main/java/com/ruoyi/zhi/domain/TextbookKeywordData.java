package com.ruoyi.zhi.domain;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import lombok.Data;

/**
 * 知识图谱-教材关键词数据对象 s_textbook_keyword_data
 * 
 * <AUTHOR>
 * @date 2024-08-09
 */
@Data
public class TextbookKeywordData extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 教材id */
    @Excel(name = "教材id")
    private Long textbookId;

    /** 知识点类别 */
    @Excel(name = "知识点类别")
    private String knowledgeCategory;

    /** 类别 */
    @Excel(name = "类别")
    private String category;

    /** 关键词 */
    @Excel(name = "关键词")
    private String keyword;

    /** 统计数量 **/
    private Long count;


}
