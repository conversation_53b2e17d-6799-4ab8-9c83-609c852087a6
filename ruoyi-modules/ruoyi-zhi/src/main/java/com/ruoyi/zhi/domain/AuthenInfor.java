package com.ruoyi.zhi.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 鉴权信息对象 s_authen_infor
 *
 * <AUTHOR>
 * @date 2024-06-14
 */
public class AuthenInfor extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 菜单路由 */
    @Excel(name = "菜单路由")
    private String menuRouting;

    /** 应用apiAk */
    @Excel(name = "应用apiAk")
    private String apiKey;

    /** 应用apiSK */
    @Excel(name = "应用apiSK")
    private String secretKey;

    /** 服务apiURL */
    @Excel(name = "服务apiURL")
    private String apiUrl;

    /** 百度千帆ak */
    @Excel(name = "百度千帆ak")
    private String ak;

    /** 百度千帆sk */
    @Excel(name = "百度千帆sk")
    private String sk;

    /** BOS域名 */
    @Excel(name = "BOS域名")
    private String domainName;

    /** Bucket名称 */
    @Excel(name = "Bucket名称")
    private String bosBucketName;

    private String remarks;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setMenuRouting(String menuRouting)
    {
        this.menuRouting = menuRouting;
    }

    public String getMenuRouting()
    {
        return menuRouting;
    }
    public void setApiKey(String apiKey)
    {
        this.apiKey = apiKey;
    }

    public String getApiKey()
    {
        return apiKey;
    }
    public void setSecretKey(String secretKey)
    {
        this.secretKey = secretKey;
    }

    public String getSecretKey()
    {
        return secretKey;
    }
    public void setApiUrl(String apiUrl)
    {
        this.apiUrl = apiUrl;
    }

    public String getApiUrl()
    {
        return apiUrl;
    }
    public void setAk(String ak)
    {
        this.ak = ak;
    }

    public String getAk()
    {
        return ak;
    }
    public void setSk(String sk)
    {
        this.sk = sk;
    }

    public String getSk()
    {
        return sk;
    }
    public void setDomainName(String domainName)
    {
        this.domainName = domainName;
    }

    public String getDomainName()
    {
        return domainName;
    }
    public void setBosBucketName(String bosBucketName)
    {
        this.bosBucketName = bosBucketName;
    }

    public String getBosBucketName()
    {
        return bosBucketName;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("menuRouting", getMenuRouting())
                .append("apiKey", getApiKey())
                .append("secretKey", getSecretKey())
                .append("apiUrl", getApiUrl())
                .append("ak", getAk())
                .append("sk", getSk())
                .append("domainName", getDomainName())
                .append("bosBucketName", getBosBucketName())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}