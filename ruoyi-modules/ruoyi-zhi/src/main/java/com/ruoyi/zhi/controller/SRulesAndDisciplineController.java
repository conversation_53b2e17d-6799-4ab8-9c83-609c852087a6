package com.ruoyi.zhi.controller;

import com.baidubce.appbuilder.model.knowledgebase.Document;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.zhi.domain.FileVo;
import com.ruoyi.zhi.domain.SKnowledgeSchool;
import com.ruoyi.zhi.domain.SKnowledgeSchoolFile;
import com.ruoyi.zhi.service.SRulesAndDisciplineService;
import com.ruoyi.zhi.service.impl.SKnowledgeSchoolFileServiceImpl;
import com.ruoyi.zhi.service.impl.SKnowledgeSchoolServiceImpl;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


@RestController
@RequestMapping("/rulesAndDiscipline")
public class SRulesAndDisciplineController extends BaseController {
    @Resource
    private SKnowledgeSchoolServiceImpl sKnowledgeSchoolService;
    @Resource
    private SKnowledgeSchoolFileServiceImpl sKnowledgeSchoolFileService;

    @Resource
    private SRulesAndDisciplineService sRulesAndDisciplineService;

    /**
     * 新增知识库文档
     * @param fileVo
     * @return
     */
    @PostMapping("/add")
    public AjaxResult add(FileVo fileVo){
        sRulesAndDisciplineService.add(fileVo);
        return success();
    }

    /**
     * 获取知识库文档
     * @param fileVo
     * @return
     */
    @PostMapping("/getDocument")
    public AjaxResult getDocument(@RequestBody FileVo fileVo){
        return success(sRulesAndDisciplineService.getDocument(fileVo.getKbName(),null,null));
    }

    /**
     * 拉取知识库文档到数据库
     * @param fileVo
     * @return
     */
    @PostMapping("/initDocument")
    public AjaxResult initDocument(FileVo fileVo){
        sRulesAndDisciplineService.initDocument(fileVo.getKbName(),null,null);
        return success();
    }

    @GetMapping("/listS")
    public TableDataInfo list(SKnowledgeSchool sKnowledgeSchool)
    {
        startPage();
        List<SKnowledgeSchool> list = sKnowledgeSchoolService.selectSKnowledgeSchoolList(sKnowledgeSchool);
        return getDataTable(list);
    }

    @GetMapping("/listSf")
    public TableDataInfo list(SKnowledgeSchoolFile sKnowledgeSchoolFile)
    {
        startPage();
        List<SKnowledgeSchoolFile> list = sKnowledgeSchoolFileService.selectSKnowledgeSchoolFileList(sKnowledgeSchoolFile);
        return getDataTable(list);
    }
}
