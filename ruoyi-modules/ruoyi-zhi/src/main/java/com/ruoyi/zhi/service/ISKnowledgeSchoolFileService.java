package com.ruoyi.zhi.service;

import java.util.List;
import com.ruoyi.zhi.domain.SKnowledgeSchoolFile;

/**
 * 知识库文件Service接口
 *
 * <AUTHOR>
 * @date 2024-11-28
 */
public interface ISKnowledgeSchoolFileService
{
    /**
     * 查询知识库文件
     *
     * @param id 知识库文件主键
     * @return 知识库文件
     */
    public SKnowledgeSchoolFile selectSKnowledgeSchoolFileById(Long id);

    /**
     * 查询知识库文件列表
     *
     * @param sKnowledgeSchoolFile 知识库文件
     * @return 知识库文件集合
     */
    public List<SKnowledgeSchoolFile> selectSKnowledgeSchoolFileList(SKnowledgeSchoolFile sKnowledgeSchoolFile);

    /**
     * 新增知识库文件
     *
     * @param sKnowledgeSchoolFile 知识库文件
     * @return 结果
     */
    public int insertSKnowledgeSchoolFile(SKnowledgeSchoolFile sKnowledgeSchoolFile);

    /**
     * 修改知识库文件
     *
     * @param sKnowledgeSchoolFile 知识库文件
     * @return 结果
     */
    public int updateSKnowledgeSchoolFile(SKnowledgeSchoolFile sKnowledgeSchoolFile);

    /**
     * 批量删除知识库文件
     *
     * @param ids 需要删除的知识库文件主键集合
     * @return 结果
     */
    public int deleteSKnowledgeSchoolFileByIds(Long[] ids);

    /**
     * 删除知识库文件信息
     *
     * @param id 知识库文件主键
     * @return 结果
     */
    public int deleteSKnowledgeSchoolFileById(Long id);
}
