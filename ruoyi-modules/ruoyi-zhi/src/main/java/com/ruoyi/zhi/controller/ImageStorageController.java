package com.ruoyi.zhi.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.zhi.domain.ImageStorage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.zhi.service.IImageStorageService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 图片存储Controller
 * 
 * <AUTHOR>
 * @date 2024-06-06
 */
@RestController
@RequestMapping("/storage")
public class ImageStorageController extends BaseController
{
    @Autowired
    private IImageStorageService imageStorageService;

    /**
     * 查询图片存储列表
     */
    @RequiresPermissions("create:storage:list")
    @GetMapping("/list")
    public TableDataInfo list(ImageStorage imageStorage)
    {
        startPage();
        List<ImageStorage> list = imageStorageService.selectImageStorageList(imageStorage);
        return getDataTable(list);
    }

    /**
     * 导出图片存储列表
     */
    @RequiresPermissions("create:storage:export")
    @Log(title = "图片存储", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response,ImageStorage imageStorage)
    {
        List<ImageStorage> list = imageStorageService.selectImageStorageList(imageStorage);
        ExcelUtil<ImageStorage> util = new ExcelUtil<ImageStorage>(ImageStorage.class);
        util.exportExcel(response, list, "图片存储数据");
    }

    /**
     * 获取图片存储详细信息
     */
    @RequiresPermissions("create:storage:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(imageStorageService.selectImageStorageById(id));
    }

    /**
     * 新增图片存储
     */
    @RequiresPermissions("create:storage:add")
    @Log(title = "图片存储", businessType = BusinessType.INSERT)
    @PostMapping("/upload")
    public AjaxResult add(@RequestParam("file") MultipartFile file, @RequestParam("dataId") Long dataId,@RequestParam("imageNumber") String imageNumber) throws Exception {
        return toAjax(imageStorageService.insertImageStorage(file,dataId,imageNumber));
    }

    /**
     * 修改图片存储
     */
    @RequiresPermissions("create:storage:edit")
    @Log(title = "图片存储", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ImageStorage imageStorage)
    {
        return toAjax(imageStorageService.updateImageStorage(imageStorage));
    }

    /**
     * 删除图片存储
     */
    @RequiresPermissions("create:storage:remove")
    @Log(title = "图片存储", businessType = BusinessType.DELETE)
	@DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id)
    {
        return toAjax(imageStorageService.deleteImageStorageById(id));
    }
}
