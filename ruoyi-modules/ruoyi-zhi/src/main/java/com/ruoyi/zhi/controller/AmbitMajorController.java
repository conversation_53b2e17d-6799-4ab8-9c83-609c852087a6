package com.ruoyi.zhi.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.zhi.domain.TextbookData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.zhi.domain.AmbitMajor;
import com.ruoyi.zhi.service.IAmbitMajorService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 学科专业Controller
 * 
 * <AUTHOR>
 * @date 2024-08-29
 */
@RestController
@RequestMapping("/major")
public class AmbitMajorController extends BaseController
{
    @Autowired
    private IAmbitMajorService ambitMajorService;

    /**
     * 查询学科专业列表
     */
    @RequiresPermissions("zhi:major:list")
    @GetMapping("/list")
    public AjaxResult list(AmbitMajor ambitMajor)
    {
        List<AmbitMajor> list = ambitMajorService.selectAmbitMajorList(ambitMajor);
        return success(list);
    }


    /**
     * 导出学科专业列表
     */
    @RequiresPermissions("zhi:major:export")
    @Log(title = "学科专业", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AmbitMajor ambitMajor)
    {
        List<AmbitMajor> list = ambitMajorService.selectAmbitMajorList(ambitMajor);
        ExcelUtil<AmbitMajor> util = new ExcelUtil<AmbitMajor>(AmbitMajor.class);
        util.exportExcel(response, list, "学科专业数据");
    }

    /**
     * 获取学科专业详细信息
     */
    @RequiresPermissions("zhi:major:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(ambitMajorService.selectAmbitMajorById(id));
    }

    /**
     * 新增学科专业
     */
    @RequiresPermissions("zhi:major:add")
    @Log(title = "学科专业", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AmbitMajor ambitMajor) throws Exception {
        return toAjax(ambitMajorService.insertAmbitMajor(ambitMajor));
    }

    /**
     * 修改学科专业
     */
    @RequiresPermissions("zhi:major:edit")
    @Log(title = "学科专业", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AmbitMajor ambitMajor)
    {
        return toAjax(ambitMajorService.updateAmbitMajor(ambitMajor));
    }

    /**
     * 删除学科专业
     */
    @RequiresPermissions("zhi:major:remove")
    @Log(title = "学科专业", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(ambitMajorService.deleteAmbitMajorByIds(ids));
    }


    /**
     * 专业文献整理
     */
    @RequiresPermissions("zhi:textbookdata:list")
    @PostMapping("tbKnowledgeMapping")
    public AjaxResult getKnowledgeMapping(@RequestBody AmbitMajor ambitMajor)
    {
        return ambitMajorService.selectGroupAmbit(ambitMajor);
    }

    /**
     * 课程图谱
     */
    @RequiresPermissions("zhi:textbookdata:list")
    @PostMapping("courseAtlas")
    public AjaxResult courseAtlas(@RequestBody AmbitMajor ambitMajor)
    {
        return ambitMajorService.courseAtlas(ambitMajor);

    }


    /**
     * 查看联盟课属性
     * @param ambitMajor
     * @return
     */
    @PostMapping("getAllianceCourse")
    public AjaxResult getAllianceCourse(@RequestBody AmbitMajor ambitMajor)
    {
        return success(ambitMajorService.getAllianceCourse(ambitMajor));

    }
}
