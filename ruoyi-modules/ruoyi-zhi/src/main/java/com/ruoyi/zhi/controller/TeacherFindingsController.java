package com.ruoyi.zhi.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.zhi.domain.TeacherFindings;
import com.ruoyi.zhi.service.ITeacherFindingsService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 教师成果Controller
 * 
 * <AUTHOR>
 * @date 2024-08-29
 */
@RestController
@RequestMapping("/findings")
public class TeacherFindingsController extends BaseController
{
    @Autowired
    private ITeacherFindingsService teacherFindingsService;

    /**
     * 查询教师成果列表
     */
    @RequiresPermissions("zhi:findings:list")
    @GetMapping("/list")
    public TableDataInfo list(TeacherFindings teacherFindings)
    {
        startPage();
        List<TeacherFindings> list = teacherFindingsService.selectTeacherFindingsList(teacherFindings);
        return getDataTable(list);
    }

    /**
     * 导出教师成果列表
     */
    @RequiresPermissions("zhi:findings:export")
    @Log(title = "教师成果", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TeacherFindings teacherFindings)
    {
        List<TeacherFindings> list = teacherFindingsService.selectTeacherFindingsList(teacherFindings);
        ExcelUtil<TeacherFindings> util = new ExcelUtil<TeacherFindings>(TeacherFindings.class);
        util.exportExcel(response, list, "教师成果数据");
    }

    /**
     * 获取教师成果详细信息
     */
    @RequiresPermissions("zhi:findings:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(teacherFindingsService.selectTeacherFindingsById(id));
    }

    /**
     * 新增教师成果
     */
    @RequiresPermissions("zhi:findings:add")
    @Log(title = "教师成果", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TeacherFindings teacherFindings)
    {
        return toAjax(teacherFindingsService.insertTeacherFindings(teacherFindings));
    }

    /**
     * 修改教师成果
     */
    @RequiresPermissions("zhi:findings:edit")
    @Log(title = "教师成果", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TeacherFindings teacherFindings)
    {
        return toAjax(teacherFindingsService.updateTeacherFindings(teacherFindings));
    }

    /**
     * 删除教师成果
     */
    @RequiresPermissions("zhi:findings:remove")
    @Log(title = "教师成果", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(teacherFindingsService.deleteTeacherFindingsByIds(ids));
    }
}
