package com.ruoyi.zhi.service;

import java.util.List;
import com.ruoyi.zhi.domain.TextbookDataTemporary;

/**
 * 知识库知识图谱-教材出版社、作者Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-27
 */
public interface ITextbookDataTemporaryService 
{
    /**
     * 查询知识库知识图谱-教材出版社、作者
     * 
     * @param id 知识库知识图谱-教材出版社、作者主键
     * @return 知识库知识图谱-教材出版社、作者
     */
    public TextbookDataTemporary selectTextbookDataTemporaryById(Long id);

    /**
     * 查询知识库知识图谱-教材出版社、作者列表
     * 
     * @param textbookDataTemporary 知识库知识图谱-教材出版社、作者
     * @return 知识库知识图谱-教材出版社、作者集合
     */
    public List<TextbookDataTemporary> selectTextbookDataTemporaryList(TextbookDataTemporary textbookDataTemporary);

    /**
     * 新增知识库知识图谱-教材出版社、作者
     * 
     * @param textbookDataTemporary 知识库知识图谱-教材出版社、作者
     * @return 结果
     */
    public int insertTextbookDataTemporary(TextbookDataTemporary textbookDataTemporary);

    /**
     * 修改知识库知识图谱-教材出版社、作者
     * 
     * @param textbookDataTemporary 知识库知识图谱-教材出版社、作者
     * @return 结果
     */
    public int updateTextbookDataTemporary(TextbookDataTemporary textbookDataTemporary);

    /**
     * 批量删除知识库知识图谱-教材出版社、作者
     * 
     * @param ids 需要删除的知识库知识图谱-教材出版社、作者主键集合
     * @return 结果
     */
    public int deleteTextbookDataTemporaryByIds(Long[] ids);

    /**
     * 删除知识库知识图谱-教材出版社、作者信息
     * 
     * @param id 知识库知识图谱-教材出版社、作者主键
     * @return 结果
     */
    public int deleteTextbookDataTemporaryById(Long id);
}
