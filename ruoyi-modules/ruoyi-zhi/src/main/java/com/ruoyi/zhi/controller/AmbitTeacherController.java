package com.ruoyi.zhi.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.zhi.domain.AmbitTeacher;
import com.ruoyi.zhi.service.IAmbitTeacherService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 专业教师Controller
 * 
 * <AUTHOR>
 * @date 2024-08-29
 */
@RestController
@RequestMapping("/teacher")
public class AmbitTeacherController extends BaseController
{
    @Autowired
    private IAmbitTeacherService ambitTeacherService;

    /**
     * 查询专业教师列表
     */
    @RequiresPermissions("zhi:teacher:list")
    @GetMapping("/list")
    public TableDataInfo list(AmbitTeacher ambitTeacher)
    {
        startPage();
        List<AmbitTeacher> list = ambitTeacherService.selectAmbitTeacherList(ambitTeacher);
        return getDataTable(list);
    }

    /**
     * 导出专业教师列表
     */
    @RequiresPermissions("zhi:teacher:export")
    @Log(title = "专业教师", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AmbitTeacher ambitTeacher)
    {
        List<AmbitTeacher> list = ambitTeacherService.selectAmbitTeacherList(ambitTeacher);
        ExcelUtil<AmbitTeacher> util = new ExcelUtil<AmbitTeacher>(AmbitTeacher.class);
        util.exportExcel(response, list, "专业教师数据");
    }

    /**
     * 获取专业教师详细信息
     */
    @RequiresPermissions("zhi:teacher:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(ambitTeacherService.selectAmbitTeacherById(id));
    }

    /**
     * 新增专业教师
     */
    @RequiresPermissions("zhi:teacher:add")
    @Log(title = "专业教师", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AmbitTeacher ambitTeacher)
    {
        return toAjax(ambitTeacherService.insertAmbitTeacher(ambitTeacher));
    }

    /**
     * 修改专业教师
     */
    @RequiresPermissions("zhi:teacher:edit")
    @Log(title = "专业教师", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AmbitTeacher ambitTeacher)
    {
        return toAjax(ambitTeacherService.updateAmbitTeacher(ambitTeacher));
    }

    /**
     * 删除专业教师
     */
    @RequiresPermissions("zhi:teacher:remove")
    @Log(title = "专业教师", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(ambitTeacherService.deleteAmbitTeacherByIds(ids));
    }

    @RequiresPermissions("zhi:teacher:list")
    @GetMapping("/knowledge/list")
    public TableDataInfo getList(AmbitTeacher ambitTeacher)
    {
        startPage();
        List<AmbitTeacher> list = ambitTeacherService.selectKnowledgeList(ambitTeacher);
        return getDataTable(list);
    }

    @PostMapping("/count")
    public AjaxResult countNumber() throws Exception {
        return success(ambitTeacherService.countNumber());
    }
}
