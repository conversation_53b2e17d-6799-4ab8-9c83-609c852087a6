package com.ruoyi.zhi.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.zhi.domain.KnowledgeNumber;
import com.ruoyi.zhi.service.IKnowledgeNumberService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 知识点数量Controller
 * 
 * <AUTHOR>
 * @date 2025-02-08
 */
@RestController
@RequestMapping("/number")
public class KnowledgeNumberController extends BaseController
{
    @Autowired
    private IKnowledgeNumberService knowledgeNumberService;

    /**
     * 查询知识点数量列表
     */
    @RequiresPermissions("zhi:number:list")
    @GetMapping("/list")
    public TableDataInfo list(KnowledgeNumber knowledgeNumber)
    {
        startPage();
        List<KnowledgeNumber> list = knowledgeNumberService.selectKnowledgeNumberList(knowledgeNumber);
        return getDataTable(list);
    }

    /**
     * 导出知识点数量列表
     */
    @RequiresPermissions("zhi:number:export")
    @Log(title = "知识点数量", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KnowledgeNumber knowledgeNumber)
    {
        List<KnowledgeNumber> list = knowledgeNumberService.selectKnowledgeNumberList(knowledgeNumber);
        ExcelUtil<KnowledgeNumber> util = new ExcelUtil<KnowledgeNumber>(KnowledgeNumber.class);
        util.exportExcel(response, list, "知识点数量数据");
    }

    /**
     * 获取知识点数量详细信息
     */
    @RequiresPermissions("zhi:number:query")
    @GetMapping(value = "/{yearMonth}")
    public AjaxResult getInfo(@PathVariable("yearMonth") String yearMonth)
    {
        return success(knowledgeNumberService.selectKnowledgeNumberByYearMonth(yearMonth));
    }

    /**
     * 新增知识点数量
     */
    @RequiresPermissions("zhi:number:add")
    @Log(title = "知识点数量", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KnowledgeNumber knowledgeNumber)
    {
        return toAjax(knowledgeNumberService.insertKnowledgeNumber(knowledgeNumber));
    }

    /**
     * 修改知识点数量
     */
    @RequiresPermissions("zhi:number:edit")
    @Log(title = "知识点数量", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KnowledgeNumber knowledgeNumber)
    {
        return toAjax(knowledgeNumberService.updateKnowledgeNumber(knowledgeNumber));
    }

    /**
     * 删除知识点数量
     */
    @RequiresPermissions("zhi:number:remove")
    @Log(title = "知识点数量", businessType = BusinessType.DELETE)
	@DeleteMapping("/{yearMonths}")
    public AjaxResult remove(@PathVariable String[] yearMonths)
    {
        return toAjax(knowledgeNumberService.deleteKnowledgeNumberByYearMonths(yearMonths));
    }

    @GetMapping("/getNumber")
    public AjaxResult getNumber(){
        return success(knowledgeNumberService.getNumber());
    }
}
