package com.ruoyi.zhi.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.zhi.domain.AmbitMajor;
import com.ruoyi.zhi.domain.FileVo;
import com.ruoyi.zhi.domain.KnowledgeBaseFile;
import com.ruoyi.zhi.domain.TextbookKeywordAnalysis;
import com.ruoyi.zhi.service.IKnowledgeBaseFileService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 知识库文件Controller
 * 
 * <AUTHOR>
 * @date 2024-06-19
 */
@RestController
@RequestMapping("/kbfile")
public class KnowledgeBaseFileController extends BaseController
{
    @Autowired
    private IKnowledgeBaseFileService knowledgeBaseFileService;

    /**
     * 查询知识库文件列表
     */
    @RequiresPermissions("create:file:list")
    @GetMapping("/list")
    public TableDataInfo list(KnowledgeBaseFile knowledgeBaseFile)
    {
        startPage();
        List<KnowledgeBaseFile> list = knowledgeBaseFileService.selectKnowledgeBaseFileList(knowledgeBaseFile);
        return getDataTable(list);
    }

    /**
     * 导出知识库文件列表
     */
    @RequiresPermissions("create:file:export")
    @Log(title = "知识库文件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KnowledgeBaseFile knowledgeBaseFile)
    {
        List<KnowledgeBaseFile> list = knowledgeBaseFileService.selectKnowledgeBaseFileList(knowledgeBaseFile);
        ExcelUtil<KnowledgeBaseFile> util = new ExcelUtil<KnowledgeBaseFile>(KnowledgeBaseFile.class);
        util.exportExcel(response, list, "知识库文件数据");
    }

    /**
     * 获取知识库文件详细信息
     */
    @RequiresPermissions("create:file:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(knowledgeBaseFileService.selectKnowledgeBaseFileById(id));
    }

    /**
     * 新增知识库文件
     */
    @RequiresPermissions("create:file:add")
    @Log(title = "知识库文件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KnowledgeBaseFile knowledgeBaseFile)
    {
        return toAjax(knowledgeBaseFileService.insertKnowledgeBaseFile(knowledgeBaseFile));
    }

    /**
     * 修改知识库文件
     */
    @RequiresPermissions("create:file:edit")
    @Log(title = "知识库文件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KnowledgeBaseFile knowledgeBaseFile)
    {
        return toAjax(knowledgeBaseFileService.updateKnowledgeBaseFile(knowledgeBaseFile));
    }

    /**
     * 删除知识库文件
     */
    @RequiresPermissions("create:file:remove")
    @Log(title = "知识库文件", businessType = BusinessType.DELETE)
	@DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id)
    {
        return toAjax(knowledgeBaseFileService.deleteKnowledgeBaseFileById(id));
    }
    /**
     * 批量上传文件
     *
     * <AUTHOR>
     * @date 2021/8/17 22:15
     */
    @PostMapping("/upload")
    public AjaxResult upload(@RequestParam("file") MultipartFile file, String modeltype) {
        FileVo fileVo = knowledgeBaseFileService.uploadFile(file, modeltype);
        if(fileVo == null){
            return AjaxResult.error("文件过大");
        }else{
            return AjaxResult.success(fileVo);
        }
    }

    /**
     * 单一文件下载
     */
   @GetMapping("/fileDownload")
    public void fileDownload(@RequestParam Long id, HttpServletRequest request, HttpServletResponse response){
        knowledgeBaseFileService.uploadFile(id, request,response);
    }
    /**
     * 单一文件下载
     */
    @PostMapping("/fileDownloadPost")
    public void fileDownloadPost(@RequestParam Long titleId, HttpServletRequest request, HttpServletResponse response){
        knowledgeBaseFileService.uploadFile(titleId, request,response);
    }

    /**
     * 修改知识库审核、提交状态；新增或修改出版社、作者
     */
    @RequiresPermissions("create:file:updataStatus")
    @Log(title = "知识库文件", businessType = BusinessType.UPDATE)
    @PostMapping("/updataStatus")
    public AjaxResult updateKnowledgeBaseFileStatus(@RequestBody KnowledgeBaseFile knowledgeBaseFile)
    {
        return toAjax(knowledgeBaseFileService.updateKnowledgeBaseFileStatus(knowledgeBaseFile));
    }

    @RequiresPermissions("create:file:list")
    @GetMapping("/knowledge/list")
    public TableDataInfo getList(KnowledgeBaseFile knowledgeBaseFile)
    {
        startPage();
        List<KnowledgeBaseFile> list = knowledgeBaseFileService.selectKnowledgeBaseList(knowledgeBaseFile);
        return getDataTable(list);
    }


    /**
     * 发起解析
     */
    @RequiresPermissions("zhi:analysis:initiate")
    @PostMapping(value = "/initiate")
    public AjaxResult initiateParsing(@RequestBody KnowledgeBaseFile knowledgeBaseFile) throws Exception {
        return success(knowledgeBaseFileService.initiateParsing(knowledgeBaseFile));
    }
}
