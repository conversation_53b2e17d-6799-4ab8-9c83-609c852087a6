package com.ruoyi.zhi.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.zhi.domain.DataSet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.zhi.domain.DataTemporary;
import com.ruoyi.zhi.service.IDataTemporaryService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 临时数据集Controller
 * 
 * <AUTHOR>
 * @date 2024-05-28
 */
@RestController
@RequestMapping("/temporary")
public class DataTemporaryController extends BaseController
{
    @Autowired
    private IDataTemporaryService dataTemporaryService;

    /**
     * 查询临时数据集列表
     */
    @RequiresPermissions("create:temporary:list")
    @GetMapping("/list")
    public TableDataInfo list(DataTemporary dataTemporary)
    {
        startPage();
        List<DataTemporary> list = dataTemporaryService.selectDataTemporaryList(dataTemporary);
        return getDataTable(list);
    }

    /**
     * 导出临时数据集列表
     */
    @RequiresPermissions("create:temporary:export")
    @Log(title = "临时数据集", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DataTemporary dataTemporary)
    {
        List<DataTemporary> list = dataTemporaryService.selectDataTemporaryList(dataTemporary);
        ExcelUtil<DataTemporary> util = new ExcelUtil<DataTemporary>(DataTemporary.class);
        util.exportExcel(response, list, "临时数据集数据");
    }

    /**
     * 获取临时数据集详细信息
     */
    @RequiresPermissions("create:temporary:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dataTemporaryService.selectDataTemporaryById(id));
    }

    /**
     * 新增临时数据集
     */
    @RequiresPermissions("create:temporary:add")
    @Log(title = "临时数据集", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DataTemporary dataTemporary)
    {
        return toAjax(dataTemporaryService.insertDataTemporary(dataTemporary));
    }

    /**
     * 修改临时数据集
     */
    @RequiresPermissions("create:temporary:edit")
    @Log(title = "临时数据集", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DataTemporary dataTemporary)
    {
        return toAjax(dataTemporaryService.updateDataTemporary(dataTemporary));
    }

    /**
     * 删除临时数据集
     */
    @RequiresPermissions("create:temporary:remove")
    @Log(title = "临时数据集", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dataTemporaryService.deleteDataTemporaryByIds(ids));
    }

    /**
     * 提取数据集
     * @return
     */
    @RequiresPermissions("create:temporary:extract")
    @PostMapping("/extract")
    public AjaxResult extract(@RequestParam("file") MultipartFile[] files, Long dataId, Long rulesId)
    {
        return toAjax(dataTemporaryService.extract(files,dataId,rulesId));
    }

}
