package com.ruoyi.zhi.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.utils.ServletUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.zhi.domain.ModelTraining;
import com.ruoyi.zhi.service.IModelTrainingService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

import static com.ruoyi.common.core.web.page.TableSupport.PAGE_NUM;
import static com.ruoyi.common.core.web.page.TableSupport.PAGE_SIZE;

/**
 * 模型训练任务Controller
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
@RestController
@RequestMapping("/training")
public class ModelTrainingController extends BaseController
{
    @Autowired
    private IModelTrainingService modelTrainingService;

    /**
     * 查询模型训练任务列表
     */
    @RequiresPermissions("create:training:list")
    @GetMapping("/list")
    public TableDataInfo list(ModelTraining modelTraining)
    {
        if(ServletUtils.getParameter(PAGE_NUM)!=null&&ServletUtils.getParameter(PAGE_SIZE)!=null){
            startPage();
        }
        List<ModelTraining> list = modelTrainingService.selectModelTrainingList(modelTraining);
        return getDataTable(list);
    }

    /**
     * 导出模型训练任务列表
     */
    @RequiresPermissions("create:training:export")
    @Log(title = "模型训练任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ModelTraining modelTraining)
    {
        List<ModelTraining> list = modelTrainingService.selectModelTrainingList(modelTraining);
        ExcelUtil<ModelTraining> util = new ExcelUtil<ModelTraining>(ModelTraining.class);
        util.exportExcel(response, list, "模型训练任务数据");
    }

    /**
     * 获取模型训练任务详细信息
     */
    @RequiresPermissions("create:training:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(modelTrainingService.selectModelTrainingById(id));
    }

    /**
     * 新增模型训练任务
     */
    @RequiresPermissions("create:training:add")
    @Log(title = "模型训练任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ModelTraining modelTraining)
    {
        return toAjax(modelTrainingService.insertModelTraining(modelTraining));
    }

    /**
     * 修改模型训练任务
     */
    @RequiresPermissions("create:training:edit")
    @Log(title = "模型训练任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ModelTraining modelTraining)
    {
        return toAjax(modelTrainingService.updateModelTraining(modelTraining));
    }

    /**
     * 删除模型训练任务
     */
    @RequiresPermissions("create:training:remove")
    @Log(title = "模型训练任务", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(modelTrainingService.deleteModelTrainingByIds(ids));
    }
}
