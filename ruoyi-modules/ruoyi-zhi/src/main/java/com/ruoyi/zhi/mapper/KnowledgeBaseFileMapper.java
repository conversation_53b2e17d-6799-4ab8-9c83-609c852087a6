package com.ruoyi.zhi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.zhi.domain.KnowledgeBaseFile;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 知识库文件Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-19
 */
public interface KnowledgeBaseFileMapper  extends BaseMapper<KnowledgeBaseFile>
{
    /**
     * 查询知识库文件
     * 
     * @param id 知识库文件主键
     * @return 知识库文件
     */
    public KnowledgeBaseFile selectKnowledgeBaseFileById(Long id);

    /**
     * 查询知识库文件列表
     * 
     * @param knowledgeBaseFile 知识库文件
     * @return 知识库文件集合
     */
    public List<KnowledgeBaseFile> selectKnowledgeBaseFileList(KnowledgeBaseFile knowledgeBaseFile);

    /**
     * 新增知识库文件
     * 
     * @param knowledgeBaseFile 知识库文件
     * @return 结果
     */
    public int insertKnowledgeBaseFile(KnowledgeBaseFile knowledgeBaseFile);

    /**
     * 修改知识库文件
     * 
     * @param knowledgeBaseFile 知识库文件
     * @return 结果
     */
    public int updateKnowledgeBaseFile(KnowledgeBaseFile knowledgeBaseFile);

    /**
     * 删除知识库文件
     * 
     * @param id 知识库文件主键
     * @return 结果
     */
    public int deleteKnowledgeBaseFileById(Long id);

    /**
     * 批量删除知识库文件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKnowledgeBaseFileByIds(Long[] ids);

    List<KnowledgeBaseFile> selectKnowledgeBaseFileByIds(Long[] fileIds);

    List<KnowledgeBaseFile> selectKnowledgeBaseFileListBykbId(String kbId);

    List<KnowledgeBaseFile> selectKnowledgeBaseFileByMajorId(Long majorId);

    List<KnowledgeBaseFile> selectCourseNameById(KnowledgeBaseFile knowledgeBaseFile);

    List<KnowledgeBaseFile> selectKnowledgeBaseFileByCourseName(String courseName);

    List<String> selectIsAllianceCourseList(@Param("majorId") Long majorId, @Param("courseName") String courseName);


    List<KnowledgeBaseFile> selectKnowledgeBaseFileListAndTextBook(KnowledgeBaseFile knowledgeBaseFile);
}
