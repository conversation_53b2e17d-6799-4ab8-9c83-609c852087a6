package com.ruoyi.zhi.mapper;

import java.util.List;
import com.ruoyi.zhi.domain.DataSet;

/**
 * 数据集Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
public interface DataSetMapper 
{
    /**
     * 查询数据集
     * 
     * @param id 数据集主键
     * @return 数据集
     */
    public DataSet selectDataSetById(Long id);

    /**
     * 查询数据集列表
     * 
     * @param dataSet 数据集
     * @return 数据集集合
     */
    public List<DataSet> selectDataSetList(DataSet dataSet);

    /**
     * 新增数据集
     * 
     * @param dataSet 数据集
     * @return 结果
     */
    public int insertDataSet(DataSet dataSet);

    /**
     * 修改数据集
     * 
     * @param dataSet 数据集
     * @return 结果
     */
    public int updateDataSet(DataSet dataSet);

    /**
     * 删除数据集
     * 
     * @param id 数据集主键
     * @return 结果
     */
    public int deleteDataSetById(Long id);

    /**
     * 批量删除数据集
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDataSetByIds(Long[] ids);

    List<DataSet> selectDataSetAll(DataSet dataSet);

    List<DataSet> selectDataSetPublished(DataSet dataSet);
    List<DataSet> selectdataset_idByIds(Long[] ids);
}
