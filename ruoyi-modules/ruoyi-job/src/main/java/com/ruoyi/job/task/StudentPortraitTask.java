package com.ruoyi.job.task;
import com.ruoyi.system.api.MySelfPortraitService;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
@Component("studentPortraitTask")
public class StudentPortraitTask {

    @Resource
    private MySelfPortraitService Service;
//    @Scheduled(cron = "*/10 * * * * ?")

    public void scheduledInsertPortrait() {
        System.out.println("定时任务开始：插入学生画像数据...");
        Service.generateAndInsertPortrait();
        System.out.println("定时任务完成");
    }
}