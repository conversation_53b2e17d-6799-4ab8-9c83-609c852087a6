package com.ruoyi.plat.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.PostConstruct;
import java.io.File;

/**
 * 通用映射配置
 *
 * <AUTHOR>
 */

@Configuration
@RefreshScope
public class ResourcesConfig implements WebMvcConfigurer {


    @Value("${file.path.file-path-win-2}")
    private String localFilePathWin_2;

    @Value("${file.path.filePathlinux-2}")
    private String localFilePathLinux_2;

    @Value("${file.path.prefix-2}")
    private String prefix_2;

	@Value("${file.domain}")
	private String domain;

	@Value("${file.path.localFilePathYLinux}")
	private String localFilePathYLinux;

    private String localFilePath;

    private String localFilePath_2;

    @PostConstruct
    public void init() {
        String os = System.getProperty("os.name").toLowerCase();
        if (os.contains("win")) {
            localFilePath_2 = localFilePathWin_2;
        } else if (os.contains("nix") || os.contains("nux") || os.contains("mac")) {
            localFilePath_2 = localFilePathLinux_2;
        } else {
            throw new UnsupportedOperationException("Unsupported operating system: " + os);
        }
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler( prefix_2+ "/**")
                .addResourceLocations("file:" + localFilePath_2 + File.separator);
    }

//    @Override
//    public void addCorsMappings(CorsRegistry registry) {
//        // 配置CORS支持
//        registry.addMapping("/**") // 所有的路径都允许CORS
//                .allowedOrigins("*") // 允许所有来源
//                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS") // 允许的方法
//                .maxAge(3600); // 最大缓存时间
//    }

	public String getLocalFilePathWin_2() {
		return localFilePathWin_2;
	}

	public void setLocalFilePathWin_2(String localFilePathWin_2) {
		this.localFilePathWin_2 = localFilePathWin_2;
	}

	public String getLocalFilePathLinux_2() {
		return localFilePathLinux_2;
	}

	public void setLocalFilePathLinux_2(String localFilePathLinux_2) {
		this.localFilePathLinux_2 = localFilePathLinux_2;
	}

	public String getPrefix_2() {
		return prefix_2;
	}

	public void setPrefix_2(String prefix_2) {
		this.prefix_2 = prefix_2;
	}

	public String getDomain() {
		return domain;
	}

	public void setDomain(String domain) {
		this.domain = domain;
	}

	public String getLocalFilePath() {
		return localFilePath;
	}

	public void setLocalFilePath(String localFilePath) {
		this.localFilePath = localFilePath;
	}

	public String getLocalFilePath_2() {
		return localFilePath_2;
	}

	public void setLocalFilePath_2(String localFilePath_2) {
		this.localFilePath_2 = localFilePath_2;
	}

	public String getLocalFilePathYLinux() {
		return localFilePathYLinux;
	}

	public void setLocalFilePathYLinux(String localFilePathYLinux) {
		this.localFilePathYLinux = localFilePathYLinux;
	}
}
