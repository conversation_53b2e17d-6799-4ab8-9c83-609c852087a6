package com.ruoyi.plat.controller;

import java.util.List;
import java.io.IOException;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.plat.domain.*;
import com.ruoyi.plat.mapper.StudentStudyRecordMapper;
import org.json.JSONException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.plat.service.IStudentStudyRecordService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 学生学习历史Controller
 *
 * <AUTHOR>
 * @date 2024-08-13
 */
@RestController
@RequestMapping("/studyrecord")
public class StudentStudyRecordController extends BaseController
{
    @Autowired
    private IStudentStudyRecordService studentStudyRecordService;

    @Resource
    private StudentStudyRecordMapper studentStudyRecordMapper;

    /**
     * 查询学生学习历史列表
     */
    @RequiresPermissions("create:studyrecord:list")
    @GetMapping("/list")
    public TableDataInfo list(StudentStudyRecord studentStudyRecord)
    {
        startPage();
        List<StudentStudyRecord> list = studentStudyRecordService.selectStudentStudyRecordList(studentStudyRecord);
        return getDataTable(list);
    }


    @GetMapping("/list2")
    public TableDataInfo list2(StudentStudyRecord studentStudyRecord)
    {
        startPage();
        List<StudentStudyRecord> list = studentStudyRecordService.selectStudentStudyRecordList2(studentStudyRecord);
        return getDataTable(list);
    }

    @GetMapping("/list3")
    public TableDataInfo list3(StudentStudyRecord studentStudyRecord)
    {
        String username = SecurityUtils.getUsername();
        List<String> roleKey = studentStudyRecordService.selectUserRoleKeyByUserName(username);

        if (roleKey.contains("teacher")){
            studentStudyRecord.setCreateUser(SecurityUtils.getUsername());
        }
        startPage();
        List<StudentStudyRecord> list = studentStudyRecordService.selectStudentStudyRecordList3(studentStudyRecord);
        return getDataTable(list);
    }
	@PostMapping("/list3_1")
	public AjaxResult list3_1(@RequestBody StudentStudyRecord studentStudyRecord)
	{
		//String username = SecurityUtils.getUsername();
		String username = studentStudyRecord.getUserName();
		List<String> roleKey = studentStudyRecordService.selectUserRoleKeyByUserName(username);

		if (roleKey.contains("teacher")){
			studentStudyRecord.setCreateUser(SecurityUtils.getUsername());
		}
		List<StudentStudyRecord> list = studentStudyRecordService.selectStudentStudyRecordList3_1(studentStudyRecord);
		return AjaxResult.success(list);
	}

    /**
     * 导出学生学习历史列表
     */
    @RequiresPermissions("create:studyrecord:export")
    @Log(title = "学生学习历史", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StudentStudyRecord studentStudyRecord)
    {
        List<StudentStudyRecord> list = studentStudyRecordService.selectStudentStudyRecordList(studentStudyRecord);
        ExcelUtil<StudentStudyRecord> util = new ExcelUtil<StudentStudyRecord>(StudentStudyRecord.class);
        util.exportExcel(response, list, "学生学习历史数据");
    }

    /**
     * 获取学生学习历史详细信息
     */
    @RequiresPermissions("create:studyrecord:query")
    @GetMapping(value = "/{presentationId}")
    public AjaxResult getInfo(@PathVariable("presentationId") Long presentationId)
    {
        return success(studentStudyRecordService.selectStudentStudyRecordByPresentationId(presentationId));
    }

    /**
     * 新增学生学习历史
     */
    @RequiresPermissions("create:studyrecord:add")
    @Log(title = "学生学习历史", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StudentStudyRecord studentStudyRecord)
    {
        return toAjax(studentStudyRecordService.insertStudentStudyRecord(studentStudyRecord));
    }

    /**
     * 修改学生学习历史
     */
    // @RequiresPermissions("create:studyrecord:edit")
    @Log(title = "学生学习历史", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody StudentStudyRecord studentStudyRecord)
    {
        studentStudyRecord.setUpdateTime(DateUtils.getNowDate());
        String username = SecurityUtils.getUsername();
        studentStudyRecord.setCreateBy(username);
        StudentStudyRecord studentStudyRecordDo = studentStudyRecordMapper.selectStudentStudyRecordByPresentationIdAndCreadBy(studentStudyRecord);
        if (studentStudyRecordDo==null){
            return error("未学习，请学习听课...");
        }


        return toAjax(studentStudyRecordService.updateStudentStudyRecord(studentStudyRecord));
    }

    /**
     * 删除学生学习历史
     */
    @RequiresPermissions("create:studyrecord:remove")
    @Log(title = "学生学习历史", businessType = BusinessType.DELETE)
    @DeleteMapping("/{presentationIds}")
    public AjaxResult remove(@PathVariable Long[] presentationIds)
    {
        return toAjax(studentStudyRecordService.deleteStudentStudyRecordByPresentationIds(presentationIds));
    }

    /**
     * 校验ppt和数字人形象是否在用户本地
     */
    @RequiresPermissions("create:studyrecord:check")
    @Log(title = "学生学习历史", businessType = BusinessType.INSERT)
    @PostMapping("/checkFilePath")
    public AjaxResult checkFilePath(@RequestBody StudentStudyRecord studentStudyRecord)
    {
        return studentStudyRecordService.checkFilePptPath(studentStudyRecord);
    }
    /**
     * 查询ppt
     */
    @RequiresPermissions("create:studyrecord:list")
    @Log(title = "学生学习历史", businessType = BusinessType.INSERT)
    @PostMapping("/selectPptDigital")
    public AjaxResult selectPptDigital(@RequestBody StudentStudyRecord studentStudyRecord)
    {
        return studentStudyRecordService.selectPptDigital(studentStudyRecord);
    }

    /**
     * 查询学生学习进度信息
     */
    @RequiresPermissions("create:studyrecord:addOrUpdate")
    @Log(title = "学生学习历史", businessType = BusinessType.INSERT)
    @PostMapping("/addOrUpdate")
    public AjaxResult addOrUpdateStudentStudyRecord(@RequestBody StudentStudyRecord studentStudyRecord)
    {
        return toAjax(studentStudyRecordService.addOrUpdateStudentStudyRecord(studentStudyRecord));
    }

    /**
     * 查询演讲稿
     */
    //@RequiresPermissions("create:studyrecord:list")
    @Log(title = "学生学习历史", businessType = BusinessType.INSERT)
    @PostMapping("/getDigitalHumanSpeech")
    public AjaxResult selectDigitalHumanAndSpeech(@RequestBody StudentStudyRecord studentStudyRecord)
    {
        return studentStudyRecordService.selectDigitalHumanAndSpeech(studentStudyRecord);
    }
    @Log(title = "学生学习历史", businessType = BusinessType.INSERT)
    @PostMapping("/getDigitalHumanSpeech3")
    public AjaxResult selectDigitalHumanAndSpeech3(@RequestBody StudentStudyRecord studentStudyRecord)
    {
        return studentStudyRecordService.selectDigitalHumanAndSpeech3(studentStudyRecord);
    }
    /**
     * 通过路径下载文件
     */
    @RequiresPermissions("create:studyrecord:fileDownload")
    @RequestMapping("/fileDownload")
    public void fileDownloadByFileObjectName(@RequestBody FileDownloadRequest fileDownloadRequest, HttpServletRequest request, HttpServletResponse response) {
        studentStudyRecordService.fileDownload(fileDownloadRequest.getFilePath(), request, response);
    }

    /**
     * 通过路径下载文件
     */
    //@RequiresPermissions("create:studyrecord:fileDownload")
    @RequestMapping("/getImageThumbnail")
    public AjaxResult getImageThumbnail(PptImageWHVO pptImageWHVO) {
        return success(studentStudyRecordService.getImageWH2(pptImageWHVO));
    }

    /**
     * 查询数字人
     */
    @RequiresPermissions("create:studyrecord:list")
    @Log(title = "学生学习历史", businessType = BusinessType.INSERT)
    @PostMapping("/getDigitalHuman")
    public AjaxResult getDigitalHuman(@RequestBody PlatParam platParam)
    {
        return studentStudyRecordService.getDigitalHuman(platParam);
    }

    //@RequiresPermissions("create:studyrecord:list")
    @Log(title = "学生学习历史", businessType = BusinessType.INSERT)
    @PostMapping("/getDigitalHuman2")
    public AjaxResult getDigitalHuman2(@RequestBody PlatParam platParam)
    {
        return studentStudyRecordService.getDigitalHuman2(platParam);
    }
    @Log(title = "学生学习历史", businessType = BusinessType.INSERT)
    @PostMapping("/getDigitalHuman3")
    public AjaxResult getDigitalHuman3(@RequestBody PlatParam platParam)
    {
        return studentStudyRecordService.getDigitalHuman3(platParam);
    }

    /**
     * 测评
     */
    @RequiresPermissions("create:evaluation:list")
    @Log(title = "学生测评", businessType = BusinessType.INSERT)
    @PostMapping("/evaluation")
    public AjaxResult evaluation(@RequestBody StudentStudyRecord studentStudyRecord) throws Exception {
        return success(studentStudyRecordService.evaluation(studentStudyRecord));
    }

    /**
     * 测评提交
     */
    @RequiresPermissions("create:evaluation:list")
    @Log(title = "测评提交", businessType = BusinessType.INSERT)
    @PostMapping("/submit")
    public AjaxResult submit(@RequestBody StudentStudyRecord studentStudyRecord) throws IOException, JSONException {
        return success(studentStudyRecordService.submit(studentStudyRecord));
    }

    /**
     * 查看知识点
     */
    @RequiresPermissions("create:evaluation:list")
    @Log(title = "查看知识点", businessType = BusinessType.INSERT)
    @GetMapping("/points")
    public TableDataInfo points(StudentStudyRecord studentStudyRecord) throws IOException, JSONException {
        startPage();
        List<TextbookKeywordAnalysis> list = studentStudyRecordService.points(studentStudyRecord);
        return getDataTable(list);
    }
}
