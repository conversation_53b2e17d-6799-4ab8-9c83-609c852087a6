package com.ruoyi.plat.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.plat.domain.KnowledgePoints;
import com.ruoyi.plat.service.IKnowledgePointsService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 学生知识点掌握Controller
 * 
 * <AUTHOR>
 * @date 2024-10-16
 */
@RestController
@RequestMapping("/points")
public class KnowledgePointsController extends BaseController
{
    @Autowired
    private IKnowledgePointsService knowledgePointsService;

    /**
     * 查询学生知识点掌握列表
     */
    @RequiresPermissions("plat:points:list")
    @GetMapping("/list")
    public TableDataInfo list(KnowledgePoints knowledgePoints)
    {
        startPage();
        List<KnowledgePoints> list = knowledgePointsService.selectKnowledgePointsList(knowledgePoints);
        return getDataTable(list);
    }

    /**
     * 导出学生知识点掌握列表
     */
    @RequiresPermissions("plat:points:export")
    @Log(title = "学生知识点掌握", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KnowledgePoints knowledgePoints)
    {
        List<KnowledgePoints> list = knowledgePointsService.selectKnowledgePointsList(knowledgePoints);
        ExcelUtil<KnowledgePoints> util = new ExcelUtil<KnowledgePoints>(KnowledgePoints.class);
        util.exportExcel(response, list, "学生知识点掌握数据");
    }

    /**
     * 获取学生知识点掌握详细信息
     */
    @RequiresPermissions("plat:points:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(knowledgePointsService.selectKnowledgePointsById(id));
    }

    /**
     * 新增学生知识点掌握
     */
    @RequiresPermissions("plat:points:add")
    @Log(title = "学生知识点掌握", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KnowledgePoints knowledgePoints)
    {
        return toAjax(knowledgePointsService.insertKnowledgePoints(knowledgePoints));
    }

    /**
     * 修改学生知识点掌握
     */
    @RequiresPermissions("plat:points:edit")
    @Log(title = "学生知识点掌握", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KnowledgePoints knowledgePoints)
    {
        return toAjax(knowledgePointsService.updateKnowledgePoints(knowledgePoints));
    }

    /**
     * 删除学生知识点掌握
     */
    @RequiresPermissions("plat:points:remove")
    @Log(title = "学生知识点掌握", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(knowledgePointsService.deleteKnowledgePointsByIds(ids));
    }
}
