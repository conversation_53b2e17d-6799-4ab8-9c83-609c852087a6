package com.ruoyi.plat.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.plat.domain.PlatUserFigure;

import java.util.List;

/**
 * 用户动作绑定Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
public interface PlatUserFigureMapper extends BaseMapper<PlatUserFigure>
{
    /**
     * 查询用户动作绑定
     *
     * @param id 用户动作绑定主键
     * @return 用户动作绑定
     */
    public PlatUserFigure selectPlatUserFigureById(Long id);

    /**
     * 查询用户动作绑定列表
     *
     * @param platUserFigure 用户动作绑定
     * @return 用户动作绑定集合
     */
    public List<PlatUserFigure> selectPlatUserFigureList(PlatUserFigure platUserFigure);

    /**
     * 新增用户动作绑定
     *
     * @param platUserFigure 用户动作绑定
     * @return 结果
     */
    public int insertPlatUserFigure(PlatUserFigure platUserFigure);

    /**
     * 修改用户动作绑定
     *
     * @param platUserFigure 用户动作绑定
     * @return 结果
     */
    public int updatePlatUserFigure(PlatUserFigure platUserFigure);

    /**
     * 删除用户动作绑定
     *
     * @param id 用户动作绑定主键
     * @return 结果
     */
    public int deletePlatUserFigureById(Long id);

    /**
     * 批量删除用户动作绑定
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePlatUserFigureByIds(Long[] ids);

    PlatUserFigure selectPlatUserFigureListOne(String userName);

    PlatUserFigure selectPlatUserFigureByFigureIdAnduserName(PlatUserFigure platUserFigure);

    PlatUserFigure selectPlatUserFigureByFigureIdAnduserName2(PlatUserFigure platUserFigure);

    int updatePlatUserFigureByUserName(PlatUserFigure platUserFigure);
}
