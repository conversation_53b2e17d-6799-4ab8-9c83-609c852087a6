<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.plat.mapper.PlatMotionDetailMapper">

    <resultMap type="com.ruoyi.plat.domain.PlatMotionDetail" id="PlatMotionDetailResult">
        <result property="id"    column="id"    />
        <result property="motionId"    column="motion_id"    />
        <result property="imgOrder"    column="img_order"    />
        <result property="imgUrl"    column="img_url"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="base64String"    column="base64String"    />
    </resultMap>

    <sql id="selectPlatMotionDetailVo">
        select id, motion_id, img_order, img_url, create_by, create_time, update_by, update_time, remark, base64String from plat_motion_detail
    </sql>

    <select id="selectPlatMotionDetailList" parameterType="com.ruoyi.plat.domain.PlatMotionDetail" resultMap="PlatMotionDetailResult">
        <include refid="selectPlatMotionDetailVo"/>
        <where>
            <if test="motionId != null "> and motion_id = #{motionId}</if>
            <if test="imgOrder != null "> and img_order = #{imgOrder}</if>
            <if test="imgUrl != null  and imgUrl != ''"> and img_url = #{imgUrl}</if>
        </where>
    </select>

    <select id="selectPlatMotionDetailById" parameterType="Long" resultMap="PlatMotionDetailResult">
        <include refid="selectPlatMotionDetailVo"/>
        where id = #{id}
    </select>

    <insert id="insertPlatMotionDetail" parameterType="com.ruoyi.plat.domain.PlatMotionDetail">
        insert into plat_motion_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="motionId != null">motion_id,</if>
            <if test="imgOrder != null">img_order,</if>
            <if test="imgUrl != null and imgUrl != ''">img_url,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="motionId != null">#{motionId},</if>
            <if test="imgOrder != null">#{imgOrder},</if>
            <if test="imgUrl != null and imgUrl != ''">#{imgUrl},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updatePlatMotionDetail" parameterType="com.ruoyi.plat.domain.PlatMotionDetail">
        update plat_motion_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="motionId != null">motion_id = #{motionId},</if>
            <if test="imgOrder != null">img_order = #{imgOrder},</if>
            <if test="imgUrl != null and imgUrl != ''">img_url = #{imgUrl},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePlatMotionDetailById" parameterType="Long">
        delete from plat_motion_detail where id = #{id}
    </delete>

    <delete id="deletePlatMotionDetailByIds" parameterType="String">
        delete from plat_motion_detail where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertBatch">
        insert into plat_motion_detail (motion_id, img_order, img_url, create_by, create_time, remark, base64String)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.motionId}, #{item.imgOrder}, #{item.imgUrl}, #{item.createBy}, #{item.createTime}, #{item.remark}, #{item.base64String})
        </foreach>
    </insert>

    <select id="getAllImgByFigureID" parameterType="Long" resultMap="PlatMotionDetailResult">
        <include refid="selectPlatMotionDetailVo"/>
        where motion_id in (select id from plat_motion where figure_id = #{figureID})
    </select>

    <select id="selectBase64StringIsNull" resultMap="PlatMotionDetailResult">
        <include refid="selectPlatMotionDetailVo"/>
        where base64String is null
    </select>

    <update id="updateBase64StringById" parameterType="com.ruoyi.plat.domain.PlatMotionDetail">
        update plat_motion_detail set
            base64String = #{base64String}
        where id = #{id}
    </update>


    <select id="selectIdByMotionId" parameterType="long" resultType="long">
        select id
        from plat_motion_detail
        where motion_id = #{motionId}
    </select>
</mapper>
