<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.intelligent.mapper.DialogueDetailsMapper">

    <resultMap type="com.ruoyi.intelligent.domain.DialogueDetails" id="DialogueDetailsResult">
        <result property="id"    column="id"    />
        <result property="dialogueId"    column="dialogue_id"    />
        <result property="orderIn"    column="order_in"    />
        <result property="issue"    column="issue"    />
        <result property="content"    column="content"    />
        <result property="prompt"    column="prompt"    />
        <result property="likeStomp"    column="like_stomp"    />
        <result property="reasoningContent"    column="reasoning_content"    />
        <result property="sessionId"    column="session_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="difyConversationId"    column="dify_conversation_id"    />
        <result property="difyMetaData"    column="dify_meta_data"    />
    </resultMap>

    <sql id="selectDialogueDetailsVo">
        select id, dialogue_id, order_in, issue, content, prompt ,like_stomp,reasoning_content,session_id,create_time,dify_conversation_id,dify_meta_data from s_dialogue_details
    </sql>

    <select id="selectDialogueDetailsList" parameterType="Long" resultMap="DialogueDetailsResult">
        <include refid="selectDialogueDetailsVo"/>
        where dialogue_id = #{id} ORDER BY order_in ASC
    </select>

    <select id="selectDialogueDetailsById" parameterType="Long" resultMap="DialogueDetailsResult">
        <include refid="selectDialogueDetailsVo"/>
        where dialogue_id = #{id} ORDER BY order_in ASC
    </select>

    <insert id="insertDialogueDetails" parameterType="com.ruoyi.intelligent.domain.DialogueDetails" useGeneratedKeys="true" keyProperty="id">
        insert into s_dialogue_details
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dialogueId != null">dialogue_id,</if>
            <if test="orderIn != null">order_in,</if>
            <if test="issue != null">issue,</if>
            <if test="content != null">content,</if>
            <if test="prompt != null">prompt,</if>
            <if test="likeStomp != null">like_stomp,</if>
            <if test="reasoningContent != null">reasoning_content,</if>
            <if test="sessionId != null">session_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="difyConversationId != null">dify_conversation_id,</if>
            <if test="difyMetaData != null">dify_meta_data,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dialogueId != null">#{dialogueId},</if>
            <if test="orderIn != null">#{orderIn},</if>
            <if test="issue != null">#{issue},</if>
            <if test="content != null">#{content},</if>
            <if test="prompt != null">#{prompt},</if>
            <if test="likeStomp != null">#{likeStomp},</if>
            <if test="reasoningContent != null">#{reasoningContent},</if>
            <if test="sessionId != null">#{sessionId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="difyConversationId != null">#{difyConversationId},</if>
            <if test="difyMetaData != null">#{difyMetaData},</if>
        </trim>
    </insert>

    <update id="updateDialogueDetails" parameterType="com.ruoyi.intelligent.domain.DialogueDetails">
        update s_dialogue_details
        <trim prefix="SET" suffixOverrides=",">
            <if test="dialogueId != null">dialogue_id = #{dialogueId},</if>
            <if test="orderIn != null">order_in = #{orderIn},</if>
            <if test="issue != null">issue = #{issue},</if>
            <if test="content != null">content = #{content},</if>
            <if test="prompt != null">prompt = #{prompt},</if>
            <if test="likeStomp != null">like_stomp = #{likeStomp},</if>
            <if test="reasoningContent != null">reasoning_content = #{reasoningContent},</if>
            <if test="sessionId != null">session_id = #{sessionId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="difyConversationId != null">dify_conversation_id = #{difyConversationId},</if>
            <if test="difyMetaData != null ">dify_meta_data = #{difyMetaData},</if>

        </trim>
        where id = #{id}
    </update>

    <update id="updateLikeStompByDialogueIdAndOrderIn" parameterType="com.ruoyi.intelligent.domain.DialogueDetails">
        update s_dialogue_details
        <trim prefix="SET" suffixOverrides=",">
            <if test="likeStomp != null">like_stomp = #{likeStomp},</if>
        </trim>
        where id = #{id} or session_id = #{sessionId}
    </update>

    <delete id="deleteDialogueDetailsById" parameterType="Long">
        delete from s_dialogue_details where id = #{id}
    </delete>

    <delete id="deleteDialogueDetailsByIds" parameterType="String">
        delete from s_dialogue_details where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectMaxOrderById" parameterType="Long" resultType="Long">
        select max(order_in) from s_dialogue_details
        where dialogue_id = #{id}
    </select>
    <select id="selectDialogueDetailsListBySessionId"
            resultMap="DialogueDetailsResult">
        <include refid="selectDialogueDetailsVo"/>
        where session_id = #{sessionId}
    </select>

</mapper>
