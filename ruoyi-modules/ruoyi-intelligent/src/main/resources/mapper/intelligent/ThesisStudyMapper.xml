<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.intelligent.mapper.ThesisStudyMapper">

    <resultMap type="com.ruoyi.intelligent.domain.ThesisStudy" id="ThesisStudyResult">
        <result property="id"    column="id"    />
        <result property="conversationId"    column="conversation_id"    />
        <result property="fileId"    column="file_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="content"    column="content"    />

    </resultMap>

    <sql id="selectThesisStudyVo">
        select id, conversation_id, file_id, create_by, create_time, update_by, update_time from s_thesis_study
    </sql>

    <select id="selectThesisStudyList" parameterType="com.ruoyi.intelligent.domain.ThesisStudy" resultMap="ThesisStudyResult">
        select t1.id, t2.content
        from s_thesis_study t1 left join s_thesis_details t2
        on t1.id = t2.thesis_id and  t2.order_in=1 where t1.create_by = #{createBy}  ORDER BY t1.create_time DESC
    </select>

    <select id="selectThesisStudyById" parameterType="Long" resultMap="ThesisStudyResult">
        <include refid="selectThesisStudyVo"/>
        where id = #{id}
    </select>

    <insert id="insertThesisStudy" parameterType="com.ruoyi.intelligent.domain.ThesisStudy" useGeneratedKeys="true" keyProperty="id">
        insert into s_thesis_study
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,
            <if test="conversationId != null">conversation_id,</if>
            <if test="fileId != null">file_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id},
            <if test="conversationId != null">#{conversationId},</if>
            <if test="fileId != null">#{fileId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateThesisStudy" parameterType="com.ruoyi.intelligent.domain.ThesisStudy">
        update s_thesis_study
        <trim prefix="SET" suffixOverrides=",">
            <if test="conversationId != null">conversation_id = #{conversationId},</if>
            <if test="fileId != null">file_id = #{fileId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteThesisStudyById" parameterType="Long">
        delete from s_thesis_study where id = #{id}
    </delete>

    <delete id="deleteThesisStudyByIds" parameterType="String">
        delete from s_thesis_study where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>