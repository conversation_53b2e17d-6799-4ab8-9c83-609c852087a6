<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.intelligent.mapper.SImgFormulaMapper">

    <resultMap type="com.ruoyi.intelligent.domain.SImgFormula" id="SImgFormulaResult">
        <result property="id"    column="id"    />
        <result property="imgName"    column="img_name"    />
        <result property="imgPath"    column="img_path"    />
        <result property="imgExplain"    column="img_explain"    />
        <result property="imgMark"    column="img_mark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSImgFormulaVo">
        select id, img_name, img_path, img_explain, img_mark,create_by, create_time, update_by, update_time from s_img_formula
    </sql>

    <select id="selectSImgFormulaList" parameterType="com.ruoyi.intelligent.domain.SImgFormula" resultMap="SImgFormulaResult">
        <include refid="selectSImgFormulaVo"/>
        <where>
            <if test="imgName != null  and imgName != ''"> and img_name like concat('%', #{imgName}, '%')</if>
            <if test="imgPath != null  and imgPath != ''"> and img_path = #{imgPath}</if>
            <if test="imgExplain != null  and imgExplain != ''"> and img_explain = #{imgExplain}</if>
            <if test="imgMark != null "> and img_mark = #{imgMark}</if>
            <if test="createBy != null "> and create_by = #{createBy}</if>
            <if test="universityId != null "> and university_id = #{universityId}</if>
        </where>
    </select>

    <select id="selectSImgFormulaById" parameterType="Long" resultMap="SImgFormulaResult">
        <include refid="selectSImgFormulaVo"/>
        where id = #{id}
    </select>

    <select id="selectSImgFormulaByName" parameterType="String" resultMap="SImgFormulaResult">
        <include refid="selectSImgFormulaVo"/>
        where img_name like concat('%', #{imgName}, '%')
    </select>

    <select id="selectSImgFormulaByNameReverse" parameterType="String" resultMap="SImgFormulaResult">
        <include refid="selectSImgFormulaVo"/>
        WHERE #{imgName} LIKE CONCAT('%', img_name, '%')
    </select>


    <insert id="insertSImgFormula" parameterType="com.ruoyi.intelligent.domain.SImgFormula" useGeneratedKeys="true" keyProperty="id">
        insert into s_img_formula
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="imgName != null">img_name,</if>
            <if test="imgPath != null">img_path,</if>
            <if test="imgExplain != null">img_explain,</if>
            <if test="imgMark != null">img_mark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="universityId != null">university_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="imgName != null">#{imgName},</if>
            <if test="imgPath != null">#{imgPath},</if>
            <if test="imgExplain != null">#{imgExplain},</if>
            <if test="imgMark != null">#{imgMark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="universityId != null">#{universityId},</if>
         </trim>
    </insert>

    <update id="updateSImgFormula" parameterType="com.ruoyi.intelligent.domain.SImgFormula">
        update s_img_formula
        <trim prefix="SET" suffixOverrides=",">
            <if test="imgName != null">img_name = #{imgName},</if>
            <if test="imgPath != null">img_path = #{imgPath},</if>
            <if test="imgExplain != null">img_explain = #{imgExplain},</if>
            <if test="imgMark != null">img_mark = #{imgMark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSImgFormulaById" parameterType="Long">
        delete from s_img_formula where id = #{id}
    </delete>

    <delete id="deleteSImgFormulaByIds" parameterType="String">
        delete from s_img_formula where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectSImgFormulaListByImgName" parameterType="java.util.Set" resultMap="SImgFormulaResult">
        <include refid="selectSImgFormulaVo"/>
        WHERE img_name IN
        <foreach item="item" index="index" collection="collection" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectAmbitByTeaId" parameterType="string" resultType="long">
        select id
        from s_ambit_teacher
        where teacher_id=#{teacherId}
    </select>
    <select id="selectTeaIdByUserId" parameterType="Long" resultType="string">
        select job_id from sys_user where user_id=#{userId}
    </select>
</mapper>
