package com.ruoyi.intelligent.service;

import com.ruoyi.intelligent.domain.ThesisDetails;

import java.util.List;

/**
 * 论文研读明细Service接口
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
public interface IThesisDetailsService
{
    /**
     * 查询论文研读明细
     *
     * @param id 论文研读明细主键
     * @return 论文研读明细
     */
    public ThesisDetails selectThesisDetailsById(Long id);

    /**
     * 查询论文研读明细列表
     *
     * @param thesisDetails 论文研读明细
     * @return 论文研读明细集合
     */
    public List<ThesisDetails> selectThesisDetailsList(ThesisDetails thesisDetails);

    /**
     * 新增论文研读明细
     *
     * @param thesisDetails 论文研读明细
     * @return 结果
     */
    public int insertThesisDetails(ThesisDetails thesisDetails);

    /**
     * 修改论文研读明细
     *
     * @param thesisDetails 论文研读明细
     * @return 结果
     */
    public int updateThesisDetails(ThesisDetails thesisDetails);
    public int updateLikeStompById(ThesisDetails thesisDetails);

    /**
     * 批量删除论文研读明细
     *
     * @param ids 需要删除的论文研读明细主键集合
     * @return 结果
     */
    public int deleteThesisDetailsByIds(Long[] ids);

    /**
     * 删除论文研读明细信息
     *
     * @param id 论文研读明细主键
     * @return 结果
     */
    public int deleteThesisDetailsById(Long id);
}
