package com.ruoyi.intelligent.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.intelligent.utils.Snowflake;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.domain.SysFileInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.intelligent.mapper.SceneManagementMapper;
import com.ruoyi.intelligent.domain.SceneManagement;
import com.ruoyi.intelligent.service.ISceneManagementService;

import javax.annotation.Resource;

/**
 * 场景管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
@Service
public class SceneManagementServiceImpl implements ISceneManagementService 
{
    @Autowired
    private SceneManagementMapper sceneManagementMapper;

    @Resource
    private RemoteFileService remoteFileService;

    /**
     * 查询场景管理
     * 
     * @param id 场景管理主键
     * @return 场景管理
     */
    @Override
    public SceneManagement selectSceneManagementById(Long id)
    {
        List<SysFileInfo> fileInfoList = remoteFileService.getFileInfoList(id.toString());
        SceneManagement sceneManagement = sceneManagementMapper.selectSceneManagementById(id);
        sceneManagement.setFileList(fileInfoList);
        return sceneManagement;
    }

    /**
     * 查询场景管理列表
     * 
     * @param sceneManagement 场景管理
     * @return 场景管理
     */
    @Override
    public List<SceneManagement> selectSceneManagementList(SceneManagement sceneManagement)
    {
        return sceneManagementMapper.selectSceneManagementList(sceneManagement);
    }

    /**
     * 新增场景管理
     * 
     * @param sceneManagement 场景管理
     * @return 结果
     */
    @Override
    public int insertSceneManagement(SceneManagement sceneManagement)
    {
        Snowflake snowflake = new Snowflake(1, 1);
        long id = snowflake.generateId();
        if(ObjectUtils.isNotEmpty(sceneManagement.getFileIds())){
            remoteFileService.relationFile(sceneManagement.getFileIds(),String.valueOf(id));
        }
        /** 获取文件路径 **/
        List<SysFileInfo> sysFileInfoList= remoteFileService.getFileInfoList(String.valueOf(id));
        sceneManagement.setId(id);
        sceneManagement.setSceneIcon(sysFileInfoList.get(0).getFilePath());
        sceneManagement.setCreateTime(DateUtils.getNowDate());
        sceneManagement.setCreateBy(SecurityUtils.getUsername());
        return sceneManagementMapper.insertSceneManagement(sceneManagement);
    }

    /**
     * 修改场景管理
     * 
     * @param sceneManagement 场景管理
     * @return 结果
     */
    @Override
    public int updateSceneManagement(SceneManagement sceneManagement)
    {
        Long id = sceneManagement.getId();
        if(ObjectUtils.isNotEmpty(sceneManagement.getFileIds())){
            remoteFileService.deleteFile(id.toString());
            remoteFileService.relationFile(sceneManagement.getFileIds(),String.valueOf(id));
            /** 获取文件路径 **/
            List<SysFileInfo> sysFileInfoList= remoteFileService.getFileInfoList(String.valueOf(id));
            sceneManagement.setSceneIcon(sysFileInfoList.get(0).getFilePath());
        }
        sceneManagement.setUpdateTime(DateUtils.getNowDate());
        return sceneManagementMapper.updateSceneManagement(sceneManagement);
    }

    /**
     * 批量删除场景管理
     * 
     * @param ids 需要删除的场景管理主键
     * @return 结果
     */
    @Override
    public int deleteSceneManagementByIds(Long[] ids)
    {
        remoteFileService.deleteFile(ids[0].toString());

        return sceneManagementMapper.deleteSceneManagementByIds(ids);
    }

    /**
     * 删除场景管理信息
     * 
     * @param id 场景管理主键
     * @return 结果
     */
    @Override
    public int deleteSceneManagementById(Long id)
    {
        return sceneManagementMapper.deleteSceneManagementById(id);
    }
}
