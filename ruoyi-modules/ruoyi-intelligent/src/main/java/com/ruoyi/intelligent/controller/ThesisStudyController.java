package com.ruoyi.intelligent.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.baidubce.appbuilder.base.exception.AppBuilderServerException;
import com.ruoyi.intelligent.domain.ThesisDetails;
import com.ruoyi.intelligent.domain.ThesisStudy;
import com.ruoyi.intelligent.service.IThesisDetailsService;
import com.ruoyi.intelligent.service.IThesisStudyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import reactor.core.publisher.Flux;

/**
 * 论文研读Controller
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@RestController
@RequestMapping("/study")
public class ThesisStudyController extends BaseController
{
    @Autowired
    private IThesisStudyService thesisStudyService;

    @Autowired
    private IThesisDetailsService thesisDetailsService;

    /**
     * 查询论文研读列表
     */
    @RequiresPermissions("create:study:list")
    @GetMapping("/list")
    public AjaxResult list(ThesisStudy thesisStudy)
    {
        List<ThesisStudy> list = thesisStudyService.selectThesisStudyList(thesisStudy);
        return success(list);
    }

    /**
     * 导出论文研读列表
     */
    @RequiresPermissions("create:study:export")
    @Log(title = "论文研读", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ThesisStudy thesisStudy)
    {
        List<ThesisStudy> list = thesisStudyService.selectThesisStudyList(thesisStudy);
        ExcelUtil<ThesisStudy> util = new ExcelUtil<ThesisStudy>(ThesisStudy.class);
        util.exportExcel(response, list, "论文研读数据");
    }

    /**
     * 获取论文研读详细信息
     */
    @RequiresPermissions("create:study:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(thesisStudyService.selectThesisStudyById(id));
    }

    /**
     * 新增论文研读
     */
    @RequiresPermissions("create:study:add")
    @Log(title = "论文研读", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ThesisStudy thesisStudy) throws Exception {
        return success(thesisStudyService.insertThesisStudy(thesisStudy));
    }

    /**
     * 修改论文研读
     */
    @RequiresPermissions("create:study:edit")
    @Log(title = "论文研读", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ThesisStudy thesisStudy)
    {
        return success(thesisStudyService.updateThesisStudy(thesisStudy));
    }

    /**
     * 删除论文研读
     */
    @RequiresPermissions("create:study:remove")
    @Log(title = "论文研读", businessType = BusinessType.DELETE)
	@DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id)
    {
        return toAjax(thesisStudyService.deleteThesisStudyById(id));
    }


    /**
     * 新增论文研读
     */
    @RequiresPermissions("create:study:add")
    @Log(title = "论文研读", businessType = BusinessType.INSERT)
    @PostMapping("/addLiu")
    public Flux<String> addLiu(@RequestBody ThesisStudy thesisStudy) throws Exception {
        return thesisStudyService.insertThesisStudyLiu(thesisStudy);
    }

    /**
     * 修改论文研读
     */
    @RequiresPermissions("create:study:edit")
    @Log(title = "论文研读", businessType = BusinessType.UPDATE)
    @PostMapping("/updateLiu")
    public Flux<String> editLiu(@RequestBody ThesisStudy thesisStudy) throws IOException, AppBuilderServerException {
        return thesisStudyService.updateThesisStudyLiu(thesisStudy);
    }

    /**
     * 修改点赞点踩
     */
//    @RequiresPermissions("create:details:edit")
    @Log(title = "修改点赞点踩", businessType = BusinessType.UPDATE)
    @PutMapping("/likeStomp")
    public AjaxResult likeStomp(@RequestBody ThesisDetails thesisDetails)
    {
        return toAjax(thesisDetailsService.updateLikeStompById(thesisDetails));
    }

}
