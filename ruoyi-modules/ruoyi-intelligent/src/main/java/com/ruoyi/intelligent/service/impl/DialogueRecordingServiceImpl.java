package com.ruoyi.intelligent.service.impl;

import com.baidubce.appbuilder.base.exception.AppBuilderServerException;
import com.baidubce.appbuilder.console.appbuilderclient.AppBuilderClient;
import com.baidubce.appbuilder.model.appbuilderclient.AppBuilderClientIterator;
import com.baidubce.appbuilder.model.appbuilderclient.AppBuilderClientResult;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.ruoyi.baidu.api.BaiduApiService;
import com.ruoyi.baidu.api.dto.BaiduDto;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.intelligent.config.DifyConfigProperties;
import com.ruoyi.intelligent.domain.DialogueDetails;
import com.ruoyi.intelligent.domain.DialogueRecording;
import com.ruoyi.intelligent.domain.Messages;
import com.ruoyi.intelligent.domain.SImgFormula;
import com.ruoyi.intelligent.dto.ChatDifyDto;
import com.ruoyi.intelligent.dto.MessageDto;
import com.ruoyi.intelligent.mapper.DialogueDetailsMapper;
import com.ruoyi.intelligent.mapper.DialogueRecordingMapper;
import com.ruoyi.intelligent.mapper.SImgFormulaMapper;
import com.ruoyi.intelligent.service.IDialogueRecordingService;
import com.ruoyi.intelligent.utils.Snowflake;
import com.ruoyi.intelligent.websocket.WxWebSocket;
import com.ruoyi.system.api.ConfigService;
import com.ruoyi.system.api.RemoteAuthenInforService;
import com.ruoyi.system.api.RemoteDictTypeService;
import com.ruoyi.system.api.RemoteUniversityService;
import com.ruoyi.system.api.domain.*;
import com.ruoyi.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jetbrains.annotations.NotNull;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * 对话记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
@Slf4j
@Service
@RefreshScope
public class DialogueRecordingServiceImpl implements IDialogueRecordingService {
    @Resource
    private DialogueRecordingMapper dialogueRecordingMapper;

    @Resource
    private DialogueDetailsMapper dialogueDetailsMapper;

    @Resource
    private RemoteUniversityService remoteUniversityService;

    @Resource
    private ConfigService configService;

    @Resource
    private RemoteAuthenInforService remoteAuthenInforService;

    @Resource
    private BaiduApiService baiduApiService;

    @Resource
    private RemoteDictTypeService remoteDictTypeService;

    @Resource
    private TokenService tokenService;

    private final String JSON_REGEX = "^\\{\"imagePath\"\\s*:\\s*\".*?\",\\s*\"content\"\\s*:\\s*\".*?\"\\}$";

    @Resource
    private RedisService redisService;

    @Value("${file.path.file-path-win}")
    String localFilePathWin;

    @Value("${file.path.filePathlinux}")
    String localFilePathLinux;

    @Resource
    private SImgFormulaMapper sImgFormulaMapper;

    @Resource
    private WxWebSocket wxWebSocket;

    @Resource
    private DifyConfigProperties difyConfigProperties;


    private static final OkHttpClient HTTP_CLIENT = new OkHttpClient().newBuilder()
            .connectTimeout(300, TimeUnit.SECONDS)
            .readTimeout(600, TimeUnit.SECONDS)
            .writeTimeout(600, TimeUnit.SECONDS)
            .build();

    private final Snowflake snowflake = new Snowflake(1, 1);

    private final Gson gson = new Gson();

    /**
     * 查询对话记录
     *
     * @param id 对话记录主键
     * @return 对话记录
     */

    @Override
    public DialogueRecording selectDialogueRecordingById(Long id) {
        // 正则匹配json 格式
        String JSON_REGEX = "^\\{\"imagePath\"\\s*:\\s*\".*?\",\\s*\"content\"\\s*:\\s*\".*?\"\\}$";

        DialogueRecording dialogueRecording = dialogueRecordingMapper.selectDialogueRecordingById(id);
        List<DialogueDetails> dialogueDetailsList = dialogueDetailsMapper.selectDialogueDetailsById(id);
        // 获取content 分离图片和文字内容
        for (DialogueDetails dialogueDetails : dialogueDetailsList) {
            String content = dialogueDetails.getContent();
            // 检查 content 是否为空，空则跳过当前循环
            if (content == null || content.trim().isEmpty()) {
                continue;
            }
            // 存在图片路径 获取图片路径中的数据 设置 将图片数据的值返回
            if (Pattern.matches(JSON_REGEX, content)) {
                try {
                    JSONObject jsonObject = new JSONObject(content);
                    String imagePath = jsonObject.get("imagePath").toString();
                    dialogueDetails.setProcessing("data:image/png;base64," + encodeFileToBase64(imagePath));
                    dialogueDetails.setContent(jsonObject.get("content").toString());
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
        }

        dialogueRecording.setDialogueDetailsList(dialogueDetailsList);
        return dialogueRecording;
    }


    public static String encodeFileToBase64(String filePath) throws IOException {
        byte[] fileBytes = Files.readAllBytes(Paths.get(filePath));
        return Base64.getEncoder().encodeToString(fileBytes);
    }

    /**
     * 查询对话记录列表
     *
     * @param dialogueRecording 对话记录
     * @return 对话记录
     */
    @Override
    public List<DialogueRecording> selectDialogueRecordingList(DialogueRecording dialogueRecording) {
        dialogueRecording.setCreateTime(DateUtils.getNowDate());
        dialogueRecording.setCreateBy(SecurityUtils.getUsername());
        List<DialogueRecording> dialogueRecordings = dialogueRecordingMapper.selectDialogueRecordingList(dialogueRecording);
        for (DialogueRecording dialogueRecording1 : dialogueRecordings) {
            String content = dialogueRecording1.getContent();
            // 检查 content 是否为空，空则跳过当前循环
            if (content == null || content.trim().isEmpty()) {
                continue;
            }
            // 存在图片路径 获取图片路径中的数据 设置 图片展示的标记 将图片数据的值返回
            if (Pattern.matches(JSON_REGEX, content)) {
                try {
                    JSONObject jsonObject = new JSONObject(content);
                    dialogueRecording1.setContent(jsonObject.get("content").toString());
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
        }
        return dialogueRecordings;
    }


    /**
     * 用户发送内容包含什么
     *
     * @param content
     * @param passContent
     * @return
     */
    private Boolean checkContentPass(String content, String passContent) {
        String[] passArray = passContent.split(",");

        for (String pp : passArray) {
            String[] words = content.replaceAll("[！？，。]", "-").split("-");
            for (String w : words) {
                if (w.equals(pp) && words.length == 1) {
                    return true;
                }
            }
        }

        return false;
    }


    /**
     * @param messagesList
     * @param apiKey
     * @param secretKey
     * @param apiURL
     * @return
     * @throws IOException
     * @throws JSONException
     */
    public String sendContent(List<Messages> messagesList, String apiKey, String secretKey, String apiURL) throws IOException, JSONException {
        BaiduDto baiduDto = new BaiduDto();
        baiduDto.setMessages(gson.toJson(messagesList));
        baiduDto.setApiKey(apiKey);
        baiduDto.setSecretkey(secretKey);
        baiduDto.setApiUrl(apiURL);
        return baiduApiService.sendContent(baiduDto, SecurityConstants.INNER);
    }


    /**
     * 删除对话记录信息
     * 同时删除包含的图片
     *
     * @param id 对话记录主键
     * @return 结果
     */
    @Override
    public int deleteDialogueRecordingById(Long id) {
        List<DialogueDetails> dialogueDetails = dialogueDetailsMapper.selectDialogueDetailsById(id);
        String path = "";
        String os = System.getProperty("os.name").toLowerCase();
        if (os.contains("win")) {
            path = localFilePathWin + "/png/7/";
        } else if (os.contains("nix") || os.contains("nux") || os.contains("mac")) {
            path = localFilePathLinux + "/png/7/";
        } else {
            throw new UnsupportedOperationException("Unsupported operating system: " + os);
        }

        for (DialogueDetails d : dialogueDetails) {
            if (d.getContent().contains(path)) {
                deleteFile(new File(d.getContent()));
            }
        }
        dialogueDetailsMapper.deleteDialogueDetailsById(id);
        return dialogueRecordingMapper.deleteDialogueRecordingById(id);
    }


    /**
     * 保存讲学讲义
     *
     * @param dialogueRecording
     * @return
     */
    @Override
    public int preserveFullTextContent(DialogueRecording dialogueRecording) throws Exception {
        if (dialogueRecording.getId() == null) {
            throw new Exception("请先发起对话再进行保存");
        }
        return dialogueRecordingMapper.updateDialogueRecording(dialogueRecording);
    }

    @Override
    public Flux<String> insertDialogueRecordingLiu(DialogueRecording dialogueRecording) throws IOException, AppBuilderServerException {

        String language = dialogueRecording.getLanguage();
        int a;
        if (language.equals("CN")) {
            a = 0;
        } else {
            a = 1;
        }
        String yuYan = "";
        if (!"".equals(language) && language != null) {
            List<SysDictData> voiceType = remoteDictTypeService.dictTypeGetInfo("voice_type", SecurityConstants.INNER);
            for (SysDictData sysDictData : voiceType) {
                if (sysDictData.getDictValue().equals(language)) {
                    yuYan = ",用" + sysDictData.getDictLabel() + "回复";
                }
            }
        }
        if (dialogueRecording.getPromptId() == null) {
            String msg = configService.getConfigKey2(
                    dialogueRecording.getInvocation() + "_" + dialogueRecording.getMenuRouting().replace("/", ""),
                    SecurityConstants.INNER).get("msg").toString();
            //msg 返回内容  系统设置  37/56
            if (msg != null && !msg.equals("")) {
                dialogueRecording.setPromptId(Long.valueOf(msg));
            }
        }
        //体验中心pc端询问 为空跳过
        PromptTemplate promptTemplate = new PromptTemplate();
        if (StringUtils.isNotNull(dialogueRecording.getPromptId())) {
            promptTemplate = remoteUniversityService.getPromptInfo(dialogueRecording.getPromptId());

        }
        //新增对话记录

        long id = dialogueRecording.getId();
        dialogueRecording.setCreateTime(DateUtils.getNowDate());
        if (!Objects.isNull(tokenService.getLoginUser())) {
            dialogueRecording.setCreateBy(tokenService.getLoginUser().getUsername());
        }


        dialogueRecordingMapper.insertDialogueRecording(dialogueRecording);
        //对话记录明细对象
        DialogueDetails dialogueDetails = new DialogueDetails();
        dialogueDetails.setOrderIn(1l);
        dialogueDetails.setIssue("user");
        dialogueDetails.setDialogueId(id);
        //增加对话记录明细          templateContent模板内容(体验中心跳过)
        if (StringUtils.isNotNull(promptTemplate) && StringUtils.isNotNull(promptTemplate.getTemplateContent())) {
            dialogueDetails.setPrompt(promptTemplate.getTemplateContent());

        }
        //询问内容  如果问ai是什么创建拦截
        Boolean mark = true;
        String tempContent = dialogueRecording.getContent();
        String passContent = configService.getConfigKey2("pass_content", SecurityConstants.INNER).get("msg").toString();
        //判断tempContent内容
        if (checkContentPass(tempContent, passContent)) {
            mark = false;
        }
        //---通过枚举控制  指定词汇 回复指定自定义模板
        if (Objects.nonNull(dialogueRecording.getImagePath())) {
            String filePath = dialogueRecording.getImagePath();
            String replaced = filePath.replace("\\", "/");
            JSONObject jsonObject = new JSONObject();
            try {
                jsonObject.put("content", dialogueRecording.getContent());
                jsonObject.put("imagePath", replaced);
            } catch (JSONException e) {
                throw new RuntimeException(e);
            }
            String jsonString = jsonObject.toString();
            dialogueDetails.setContent(jsonString);
        } else {
            dialogueDetails.setContent(dialogueRecording.getContent());
        }
        dialogueDetailsMapper.insertDialogueDetails(dialogueDetails);

        dialogueDetails.setOrderIn(2l);
        dialogueDetails.setIssue("assistant");
        dialogueDetails.setDialogueId(id);
        String PcOrWxMark = dialogueRecording.getMenuRouting();
        String modelContent = "";
        if (PcOrWxMark.equals("applet")) {
            modelContent = configService.getConfigKey2("model_content_wx", SecurityConstants.INNER).get("msg").toString();
        } else {
            modelContent = configService.getConfigKey2("model_content_pc", SecurityConstants.INNER).get("msg").toString();
        }
        if (!mark) {
            String content = modelContent;
            return Flux.create(sink -> {
//                Thread thread = new Thread(() -> {
                AppBuilderClientIterator itor = null;
                try {
                    sink.next(content);
                    sink.complete();
                    dialogueDetails.setContent(content);
                    dialogueDetailsMapper.insertDialogueDetails(dialogueDetails);
                } catch (Exception e) {
                    sink.error(e);
                }
//
            });
        }

        //体验中心进入
        if ("txtToImage".equals(dialogueRecording.getInvocation()) || "model".equals(dialogueRecording.getInvocation())|| "knowledgeBase".equals(dialogueRecording.getInvocation())) {

            //构建聊天记录
            List<Messages> messagesList = new ArrayList<>();
            Messages messages = new Messages();
            messages.setRole("user");
            //有模板 训练（可能）  体验中心else
            if (StringUtils.isNotNull(promptTemplate) && StringUtils.isNotNull(promptTemplate.getTemplateContent())) {
                messages.setContent(promptTemplate.getTemplateContent().replace("{}", dialogueDetails.getContent()).replaceAll("\\r\\n|\\r|\\n", "") + yuYan);

            } else {
                //使用者 问题内容
                messages.setContent(dialogueRecording.getContent().replaceAll("\\r\\n|\\r|\\n", "") + yuYan);
            }
            messagesList.add(messages);

            //发送聊天内容
            String apiKey = remoteAuthenInforService.getAuthenInfo(dialogueRecording.getMenuRouting(), "apiKey");
            String secretKey = remoteAuthenInforService.getAuthenInfo(dialogueRecording.getMenuRouting(), "secretKey");
            String apiURL = remoteAuthenInforService.getAuthenInfo(dialogueRecording.getMenuRouting(), "apiUrl");

            if (dialogueRecording.getTxtToImage() == 1) {
                BaiduDto baiduDto = new BaiduDto();
                baiduDto.setQuery(dialogueRecording.getContent());

                String appId = configService.getConfigKey2("txtToImage_appId", SecurityConstants.INNER).get("msg").toString();
                String secretkey = configService.getConfigKey2("txtToImage_secretkey", SecurityConstants.INNER).get("msg").toString();
                baiduDto.setAppid(appId);
                baiduDto.setSecretkey(secretkey);

                baiduDto.setConversationId("");
                SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
                String cataloguePath = "";

                String os = System.getProperty("os.name").toLowerCase();
                if (os.contains("win")) {
                    cataloguePath = localFilePathWin + "/png/7/" + formatter.format(new Date()) + "/";
                } else if (os.contains("nix") || os.contains("nux") || os.contains("mac")) {
                    cataloguePath = localFilePathLinux + "/png/7/" + formatter.format(new Date()) + "/";
                } else {
                    throw new UnsupportedOperationException("Unsupported operating system: " + os);
                }

                //String cataloguePath= basePath+"/png/7/"+formatter.format(new Date())+"/";

                cataloguePath = cataloguePath.replace("//", "/");
                cataloguePath = cataloguePath.replace("\\", "/");
                baiduDto.setBasePath(cataloguePath);

                Map<String, String> image = baiduApiService.getImage(baiduDto, SecurityConstants.INNER);
                String base64File = image.get("base64File");
                String imagePath = image.get("imagePath");
                File file = new File(imagePath);
                if (!file.exists()) {
                    Flux<String> flux = Flux.just("输入内容包含违禁词，或受网络波动，请修改后再试！");
                    dialogueDetails.setContent("输入内容包含违禁词，或受网络波动，请修改后再试！");
                    dialogueDetailsMapper.insertDialogueDetails(dialogueDetails);
                    return flux;
                }
                Flux<String> flux = Flux.just("<<<:" + image.get("imagePath") + ">>>");
                dialogueDetails.setContent("<<<:" + image.get("imagePath") + ">>>");
                dialogueDetailsMapper.insertDialogueDetails(dialogueDetails);
                return flux;
            }

            if (Objects.nonNull(dialogueRecording.getImagePath())) {
                BaiduDto baiduDto = new BaiduDto();
                List<String> filePathList = new ArrayList<>();
                String appid = configService.getConfigKey2("imageToText_appId", SecurityConstants.INNER).get("msg").toString();
                String secretkey = configService.getConfigKey2("txtToImage_secretkey", SecurityConstants.INNER).get("msg").toString();
                // 构建百度Dto
                String filePath = dialogueRecording.getImagePath();
                String replaced = filePath.replace("\\", "/");
                filePathList.add(replaced);
                baiduDto.setFilePath(filePathList);
                baiduDto.setSecretkey(secretkey);
                baiduDto.setAppid(appid);
                baiduDto.setQuery(dialogueRecording.getContent());
                // 图片解析数据
                String imageInfo = baiduApiService.getImageInfo(baiduDto, SecurityConstants.INNER);
                dialogueDetails.setContent(imageInfo);
                dialogueDetailsMapper.insertDialogueDetails(dialogueDetails);
                Flux<String> flux = Flux.just(imageInfo);
                return flux;
//                System.out.println(imageInfo);
            }
            //ai调用 返回内容
            if ("knowledgeBase".equals(dialogueRecording.getInvocation())) {
                if (StringUtils.isNotNull(promptTemplate) && StringUtils.isNotNull(promptTemplate.getTemplateContent())) {
                    return knowledgeBaseLiu(promptTemplate.getTemplateContent().replace("{}", dialogueDetails.getContent()).replaceAll("\\r\\n|\\r|\\n", "") + yuYan, dialogueRecording.getMenuRouting().replace("/", ""), dialogueDetails, a);

                } else {
                    //也可以处理替换
                    return knowledgeBaseLiu(dialogueRecording.getContent().replaceAll("\\r\\n|\\r|\\n", "") + yuYan, dialogueRecording.getMenuRouting().replace("/", ""), dialogueDetails, a);
                }
            }
            return sendContentLiu(messagesList, apiKey, secretKey, apiURL, dialogueDetails, a);
        } else {
            //模板回复（可能）  体验中心else
            if (StringUtils.isNotNull(promptTemplate) && StringUtils.isNotNull(promptTemplate.getTemplateContent())) {
                return knowledgeBaseLiu(promptTemplate.getTemplateContent().replace("{}", dialogueDetails.getContent()).replaceAll("\\r\\n|\\r|\\n", "") + yuYan, dialogueRecording.getMenuRouting().replace("/", ""), dialogueDetails, a);

            } else {
                //也可以处理替换
                return knowledgeBaseLiu(dialogueRecording.getContent().replaceAll("\\r\\n|\\r|\\n", "") + yuYan, dialogueRecording.getMenuRouting().replace("/", ""), dialogueDetails, a);
            }
        }
    }


    @Override
    public Flux<String> updateDialogueRecordingLiu(DialogueRecording dialogueRecording) throws IOException, AppBuilderServerException {
        String language = dialogueRecording.getLanguage();
        int a;
        if (language.equals("CN")) {
            a = 0;
        } else {
            a = 1;
        }
        String yuYan = "";
        if (!"".equals(language) && language != null) {
            List<SysDictData> voiceType = remoteDictTypeService.dictTypeGetInfo("voice_type", SecurityConstants.INNER);
            for (SysDictData sysDictData : voiceType) {
                if (sysDictData.getDictValue().equals(language)) {
                    yuYan = ",用" + sysDictData.getDictLabel() + "回复";
                }
            }
        }

        if (dialogueRecording.getPromptId() == null) {
            String msg = configService.getConfigKey2(dialogueRecording.getInvocation() + "_" + dialogueRecording.getMenuRouting().replace("/", ""), SecurityConstants.INNER).get("msg").toString();
            if (msg != null && !msg.equals("")) {
                dialogueRecording.setPromptId(Long.valueOf(msg));
            }
        }
        PromptTemplate promptTemplate = new PromptTemplate();
        if (StringUtils.isNotNull(promptTemplate) && StringUtils.isNotNull(promptTemplate.getTemplateContent())) {
            promptTemplate = remoteUniversityService.getPromptInfo(dialogueRecording.getPromptId());

        }
        Long id = dialogueRecording.getId();
        //查询对话记录最大次序
        Long maxOrder = dialogueDetailsMapper.selectMaxOrderById(id);
        //增加对话记录明细
        DialogueDetails dialogueDetails = new DialogueDetails();
        if (maxOrder != null) {
            dialogueDetails.setOrderIn(maxOrder + 1);
        } else {
            dialogueDetails.setOrderIn(1l);
        }
        //询问内容  如果问ai是什么创建拦截
        Boolean mark = true;
        String tempContent = dialogueRecording.getContent();
        String passContent = configService.getConfigKey2("pass_content", SecurityConstants.INNER).get("msg").toString();
        //判断tempContent内容
        if (checkContentPass(tempContent, passContent)) {
            mark = false;
        }
        if (Objects.nonNull(dialogueRecording.getImagePath())) {
            String filePath = dialogueRecording.getImagePath();
            String replaced = filePath.replace("\\", "/");
            JSONObject jsonObject = new JSONObject();
            try {
                jsonObject.put("content", dialogueRecording.getContent());
                jsonObject.put("imagePath", replaced);
            } catch (JSONException e) {
                throw new RuntimeException(e);
            }
            String jsonString = jsonObject.toString();
            dialogueDetails.setContent(jsonString);
        } else {
            dialogueDetails.setContent(dialogueRecording.getContent().replaceAll("\\r\\n|\\r|\\n", ""));
        }
        dialogueDetails.setIssue("user");
        dialogueDetails.setDialogueId(id);
        if (StringUtils.isNotNull(promptTemplate) && StringUtils.isNotNull(promptTemplate.getTemplateContent())) {
            dialogueDetails.setPrompt(promptTemplate.getTemplateContent());

        }
        dialogueDetailsMapper.insertDialogueDetails(dialogueDetails);
        if (maxOrder != null) {
            dialogueDetails.setOrderIn(maxOrder + 1);
        } else {
            dialogueDetails.setOrderIn(2l);
        }
        dialogueDetails.setIssue("assistant");
        dialogueDetails.setDialogueId(id);

        String PcOrWxMark = dialogueRecording.getMenuRouting();
        String modelContent = "";
        String pattern = "<<<:.*?>>>";
        //System.out.println(PcOrWxMark);
        if (PcOrWxMark.equals("applet")) {
            modelContent = configService.getConfigKey2("model_content_wx", SecurityConstants.INNER).get("msg").toString();
            //System.out.println("==================modelContentwx===================================="+modelContent);
        } else {
            modelContent = configService.getConfigKey2("model_content_pc", SecurityConstants.INNER).get("msg").toString();
            //System.out.println("======================modelContentpc================================"+modelContent);
        }
        if (!mark) {
            String content = modelContent;
            return Flux.create(sink -> {
//                Thread thread = new Thread(() -> {
                AppBuilderClientIterator itor = null;
                try {
                    sink.next(content);
                    sink.complete();
                    dialogueDetails.setContent(content);
                    dialogueDetailsMapper.insertDialogueDetails(dialogueDetails);
                } catch (Exception e) {
                    sink.error(e);
                }
//                });
//                thread.start();
            });
        }

        if ("txtToImage".equals(dialogueRecording.getInvocation()) || "model".equals(dialogueRecording.getInvocation())|| "knowledgeBase".equals(dialogueRecording.getInvocation())) {
            //查看聊天记录
            List<DialogueDetails> dialogueDetailsList = dialogueDetailsMapper.selectDialogueDetailsList(id);
            //构建聊天记录
            List<Messages> messagesList = new ArrayList<>();
            for (DialogueDetails details : dialogueDetailsList) {
                Messages messages = new Messages();
                messages.setRole(details.getIssue());
                if ("user".equals(details.getIssue())) {
                    if (StringUtils.isNotNull(promptTemplate.getTemplateContent())) {
                        messages.setContent(promptTemplate.getTemplateContent().replace("{}", details.getContent()) + yuYan);

                    } else {
                        messages.setContent(details.getContent() + yuYan);
                    }
                } else {
                    //
                    messages.setContent(details.getContent().replaceAll(pattern, ""));
                }
                messagesList.add(messages);
            }

            //发送聊天内容
            String apiKey = remoteAuthenInforService.getAuthenInfo(dialogueRecording.getMenuRouting(), "apiKey");
            String secretKey = remoteAuthenInforService.getAuthenInfo(dialogueRecording.getMenuRouting(), "secretKey");
            String apiURL = remoteAuthenInforService.getAuthenInfo(dialogueRecording.getMenuRouting(), "apiUrl");
            //
            if (dialogueRecording.getTxtToImage() == 1) {
                BaiduDto baiduDto = new BaiduDto();
                baiduDto.setQuery(dialogueRecording.getContent());

                String appId = configService.getConfigKey2("txtToImage_appId", SecurityConstants.INNER).get("msg").toString();
                String secretkey = configService.getConfigKey2("txtToImage_secretkey", SecurityConstants.INNER).get("msg").toString();
                baiduDto.setAppid(appId);
                baiduDto.setSecretkey(secretkey);

                baiduDto.setConversationId("");
                SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");

                String cataloguePath = "";

                String os = System.getProperty("os.name").toLowerCase();
                if (os.contains("win")) {
                    cataloguePath = localFilePathWin + "/png/7/" + formatter.format(new Date()) + "/";
                } else if (os.contains("nix") || os.contains("nux") || os.contains("mac")) {
                    cataloguePath = localFilePathLinux + "/png/7/" + formatter.format(new Date()) + "/";
                } else {
                    throw new UnsupportedOperationException("Unsupported operating system: " + os);
                }

                cataloguePath = cataloguePath.replace("//", "/");
                cataloguePath = cataloguePath.replace("\\", "/");
                baiduDto.setBasePath(cataloguePath);

                Map<String, String> image = baiduApiService.getImage(baiduDto, SecurityConstants.INNER);

                String imagePath = image.get("imagePath");
                File file = new File(imagePath);
                if (!file.exists()) {
                    Flux<String> flux = Flux.just("输入内容包含违禁词，或受网络波动，请修改后再试！");
                    dialogueDetails.setContent("输入内容包含违禁词，或受网络波动，请修改后再试！");
                    dialogueDetailsMapper.insertDialogueDetails(dialogueDetails);
                    return flux;
                }
                Flux<String> flux = Flux.just("<<<:" + image.get("imagePath") + ">>>");
                dialogueDetails.setContent("<<<:" + image.get("imagePath") + ">>>");
                dialogueDetailsMapper.insertDialogueDetails(dialogueDetails);
                return flux;
            }
            // 图生文
            if (Objects.nonNull(dialogueRecording.getImagePath())) {
                BaiduDto baiduDto = new BaiduDto();
                List<String> filePathList = new ArrayList<>();
                String appid = configService.getConfigKey2("imageToText_appId", SecurityConstants.INNER).get("msg").toString();
                String secretkey = configService.getConfigKey2("txtToImage_secretkey", SecurityConstants.INNER).get("msg").toString();
                String filePath = dialogueRecording.getImagePath();
                String replaced = filePath.replace("\\", "/");
                filePathList.add(replaced);
                baiduDto.setFilePath(filePathList);
                baiduDto.setSecretkey(secretkey);
                baiduDto.setAppid(appid);
                baiduDto.setQuery(dialogueRecording.getContent());
                String imageInfo = baiduApiService.getImageInfo(baiduDto, SecurityConstants.INNER);
                dialogueDetails.setContent(Objects.nonNull(imageInfo) ? imageInfo : "图片解析失败请再试一次");
                dialogueDetailsMapper.insertDialogueDetails(dialogueDetails);
                Flux<String> flux = Flux.just(imageInfo);
                return flux;
            }
            if ("knowledgeBase".equals(dialogueRecording.getInvocation())) {
                if (StringUtils.isNotNull(promptTemplate) && StringUtils.isNotNull(promptTemplate.getTemplateContent())) {
                    return knowledgeBaseLiu(promptTemplate.getTemplateContent().replace("{}", dialogueDetails.getContent()).replaceAll("\\r\\n|\\r|\\n", "") + yuYan, dialogueRecording.getMenuRouting().replace("/", ""), dialogueDetails, a);

                } else {
                    //也可以处理替换
                    return knowledgeBaseLiu(dialogueRecording.getContent().replaceAll("\\r\\n|\\r|\\n", "") + yuYan, dialogueRecording.getMenuRouting().replace("/", ""), dialogueDetails, a);
                }
            }
            return sendContentLiu(messagesList, apiKey, secretKey, apiURL, dialogueDetails, a);


        } else {
            if (StringUtils.isNotNull(promptTemplate) && StringUtils.isNotNull(promptTemplate.getTemplateContent())) {
                return knowledgeBaseLiu(promptTemplate.getTemplateContent().replace("{}", dialogueDetails.getContent()).replaceAll("\\r\\n|\\r|\\n", "") + yuYan, dialogueRecording.getMenuRouting().replace("/", ""), dialogueDetails, a);

            } else {
                //也可以处理替换
                return knowledgeBaseLiu(dialogueRecording.getContent().replaceAll("\\r\\n|\\r|\\n", "") + yuYan, dialogueRecording.getMenuRouting().replace("/", ""), dialogueDetails, a);
            }
        }

    }

    @Override
    public int stop(Long id) {
        redisService.deleteObject("A" + id);
        return 2;
    }


    private Flux<String> knowledgeBaseLiu(String query, String menuRouting, DialogueDetails dialogueDetails, int a) throws IOException, AppBuilderServerException {
        Map<String, String> map = getAuthentication(query, menuRouting);
        String appid = map.get("appid");
        String secretkey = map.get("secretkey");
        System.setProperty("APPBUILDER_TOKEN", secretkey);
        AppBuilderClient builder = new AppBuilderClient(appid);
        String conversationId = builder.createConversation();
        return Flux.create(sink -> {
            Thread thread = new Thread(() -> {
                String content = "";
                AppBuilderClientIterator itor = null;
                Long dialogueId = dialogueDetails.getDialogueId();
                redisService.setCacheObject("A" + dialogueId, true);
                try {
                    itor = builder.run(query, conversationId, null, true);
                    while (itor.hasNext() && redisService.hasKey("A" + dialogueId)) {
                        AppBuilderClientResult response = itor.next();
                        String answer = response.getAnswer();
                        if (answer != null && !answer.isEmpty()) {
                            sink.next(answer);
                            content += answer;
                        }
                    }
                    if (redisService.hasKey("A" + dialogueId)) {
                        Set<String> cacheSet = redisService.getCacheSet("sImg" + a);
                        Iterator<String> iterator = cacheSet.iterator();
                        while (iterator.hasNext()) {
                            String item = iterator.next();
                            if (!content.contains(item)) {
                                iterator.remove(); // 移除不包含的元素
                            }
                        }
                        if (cacheSet.size() > 0) {
                            List<SImgFormula> sImgFormulas = sImgFormulaMapper.selectSImgFormulaListByImgName(cacheSet);
                            for (SImgFormula sImgFormula : sImgFormulas) {
                                sink.next("<<<:" + sImgFormula.getImgPath() + ">>>");
                                content += "<<<:" + sImgFormula.getImgPath() + ">>>";
                            }
                        }

                    }
                    sink.complete();
                    redisService.deleteObject("A" + dialogueId);
                    dialogueDetails.setContent(content);
                    dialogueDetailsMapper.insertDialogueDetails(dialogueDetails);
                } catch (Exception e) {
                    sink.error(e);
                }
            });
            thread.start();
        });
    }

    private Flux<String> sendContentLiu(List<Messages> messagesList, String apiKey, String secretKey, String apiURL, DialogueDetails dialogueDetails, int a) {
        Gson gson = new Gson();
        MediaType mediaType = MediaType.parse("application/json");
        return Flux.create(sink -> {
            RequestBody body = RequestBody.create(mediaType,
                    "{\n" +
                            "\"messages\":" + gson.toJson(messagesList) + "" +
                            "  ,\n" +
                            "  \"stream\": true\n" +
                            "}");

            Request request = null;
            System.out.println(messagesList.toString());
            try {
                request = new Request.Builder()
                        .url(apiURL + "?access_token=" + getAccessToken(apiKey, secretKey))
                        .method("POST", body)
                        .addHeader("Content-Type", "application/json")
                        .build();
            } catch (IOException e) {
                e.printStackTrace();
            } catch (JSONException e) {
                e.printStackTrace();
            }

            Call call = HTTP_CLIENT.newCall(request);
            call.enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    sink.error(e);
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().byteStream(), StandardCharsets.UTF_8))) {
                        String line;
                        String content = "";
                        Long dialogueId = dialogueDetails.getDialogueId();
                        redisService.setCacheObject("A" + dialogueId, true);
                        while ((line = reader.readLine()) != null && redisService.hasKey("A" + dialogueId)) {
                            if (line != null && !line.equals("")) {
                                String jsonString = line.replaceFirst("data: ", "");
                                // 解析JSON字符串
                                JSONObject jsonObject = new JSONObject(jsonString);
                                if (jsonObject.has("result") && !jsonObject.isNull("result")) {
                                    String result = jsonObject.getString("result");
                                    if (!result.isEmpty()) {
                                        sink.next(result);
                                        content += result;
                                    }
                                }
                                // 检查是否是消息的结束
                                boolean isEnd = jsonObject.getBoolean("is_end");
                                if (isEnd) {
                                    // 如果是消息的结束，你可以在这里添加逻辑，比如关闭连接或重置状态
                                    break; // 或者选择退出循环
                                }
                            }
                        }

                        if (redisService.hasKey("A" + dialogueId)) {
                            Set<String> cacheSet = redisService.getCacheSet("sImg" + a);
                            Iterator<String> iterator = cacheSet.iterator();
                            while (iterator.hasNext()) {
                                String item = iterator.next();
                                if (!content.contains(item)) {
                                    iterator.remove(); // 移除不包含的元素
                                }
                            }
                            if (cacheSet.size() > 0) {
                                List<SImgFormula> sImgFormulas = sImgFormulaMapper.selectSImgFormulaListByImgName(cacheSet);
                                for (SImgFormula sImgFormula : sImgFormulas) {
                                    sink.next("<<<:" + sImgFormula.getImgPath() + ">>>");
                                    content += "<<<:" + sImgFormula.getImgPath() + ">>>";
                                }
                            }

                        }

                        sink.complete();
                        redisService.deleteObject("A" + dialogueId);
                        dialogueDetails.setContent(content);
                        dialogueDetailsMapper.insertDialogueDetails(dialogueDetails);
                    } catch (Exception e) {
                        sink.error(e);
                    }
                }
            });
        });
    }


    /**
     * 从用户的AK，SK生成鉴权签名（Access Token）
     *
     * @return 鉴权签名（Access Token）
     * @throws IOException IO异常
     */
    String getAccessToken(String apiKey, String secretKey) throws IOException, JSONException {
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        RequestBody body = RequestBody.create(mediaType, "grant_type=client_credentials&client_id=" + apiKey
                + "&client_secret=" + secretKey);
        Request request = new Request.Builder()
                .url("https://aip.baidubce.com/oauth/2.0/token")
                .method("POST", body)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        System.out.println(response);
        return new JSONObject(response.body().string()).getString("access_token");
    }


    /**
     * @return
     * @throws IOException
     * @throws AppBuilderServerException
     */
    private Map<String, String> getAuthentication(String query, String menuRouting) {
        Map<String, String> map = new HashMap<>();
        String appid = configService.getConfigKey2("Zappid", SecurityConstants.INNER).get("msg").toString();
        String secretkey = configService.getConfigKey2("Zsecretkey", SecurityConstants.INNER).get("msg").toString();
        //调用特定专业知识库
        String hashes = extractContentBetweenHashes(query);
        if (hashes != null && !hashes.equals("")) {
            //根据专业名称查询专业id
            MajorInfo majorInfo = new MajorInfo();
            majorInfo.setMajorName(hashes);
            List<MajorInfo> majorInfoList = remoteUniversityService.getMajorInfoList(majorInfo);
            if (majorInfoList.size() > 0) {
                KnowledgeSpeciality knowledgeSpeciality = remoteAuthenInforService.getKnowledge(majorInfoList.get(0).getId());
                if (knowledgeSpeciality != null) {
                    map.put("appid", knowledgeSpeciality.getAppId());
                    map.put("secretkey", knowledgeSpeciality.getSecretKey());
                    return map;
                } else {
                    map.put("appid", appid);
                    map.put("secretkey", secretkey);
                    return map;
                }
            } else {
                map.put("appid", appid);
                map.put("secretkey", secretkey);
                return map;
            }
        }
        //根据路由匹配知识库
        if (menuRouting != null && !menuRouting.equals("")) {
            List<KnowledgeRouting> knowledgeRoutingList = remoteAuthenInforService.getKnowledgeRoutingById(menuRouting);
            if (knowledgeRoutingList.size() > 0) {
                map.put("appid", knowledgeRoutingList.get(0).getAppId());
                map.put("secretkey", knowledgeRoutingList.get(0).getSecretKey());
                return map;
            }
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser sysUser = loginUser.getSysUser();
        if (sysUser.getMajorId() == null) {
            map.put("appid", appid);
            map.put("secretkey", secretkey);
            return map;
        }
        Long majorId = sysUser.getMajorId();
        KnowledgeSpeciality knowledgeSpeciality = remoteAuthenInforService.getKnowledge(majorId);
        if (knowledgeSpeciality != null) {
            map.put("appid", knowledgeSpeciality.getAppId());
            map.put("secretkey", knowledgeSpeciality.getSecretKey());
            return map;
        } else {
            map.put("appid", appid);
            map.put("secretkey", secretkey);
            return map;
        }

    }


    private String extractContentBetweenHashes(String str) {
        String patternString = "##(.*?)##";
        Pattern pattern = Pattern.compile(patternString);
        Matcher matcher = pattern.matcher(str);

        if (matcher.find()) {
            return matcher.group(1); // group(1) 获取第一个括号内的匹配内容
        }
        return null; // 如果没有找到匹配项，则返回 null
    }


    @Override
    public Map<String, String> getImage(String make) throws IOException, AppBuilderServerException {
        BaiduDto baiduDto = new BaiduDto();
        baiduDto.setQuery(make);

        String appId = configService.getConfigKey2("txtToImage_appId", SecurityConstants.INNER).get("msg").toString();
        String secretkey = configService.getConfigKey2("txtToImage_secretkey", SecurityConstants.INNER).get("msg").toString();
        baiduDto.setAppid(appId);
        baiduDto.setSecretkey(secretkey);

        baiduDto.setConversationId("");
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");

        String cataloguePath = "";

        String os = System.getProperty("os.name").toLowerCase();
        if (os.contains("win")) {
            cataloguePath = localFilePathWin + "/png/7/" + formatter.format(new Date()) + "/";
        } else if (os.contains("nix") || os.contains("nux") || os.contains("mac")) {
            cataloguePath = localFilePathLinux + "/png/7/" + formatter.format(new Date()) + "/";
        } else {
            throw new UnsupportedOperationException("Unsupported operating system: " + os);
        }

        cataloguePath = cataloguePath.replace("//", "/");
        cataloguePath = cataloguePath.replace("\\", "/");
        baiduDto.setBasePath(cataloguePath);

        Map<String, String> image = baiduApiService.getImage(baiduDto, SecurityConstants.INNER);
        String base64File = image.get("base64File");
        String imageBase64 = "data:image/png;base64," + base64File;
//            Flux<String> flux=Flux.just("data:image/png;base64,"+base64File);
        String imagePath = image.get("imagePath");
        Map<String, String> map = new HashMap<>();
        map.put("imageBase64", imageBase64);
        map.put("imagePath", imagePath);

        return map;
    }

    @Override
    public List<String> relatedIssues(String content) throws IOException, JSONException {


        String s = configService.getConfigKey2("relatedIssues", SecurityConstants.INNER).get("msg").toString();
        //构建聊天记录
        List<Messages> messagesList = new ArrayList<>();
        Messages messages = new Messages();
        messages.setRole("user");

        //使用者 问题内容
        messages.setContent(s.replace("{}", content).replaceAll("\\r\\n|\\r|\\n", ""));

        messagesList.add(messages);

        //发送聊天内容
        String apiKey = remoteAuthenInforService.getAuthenInfo("/", "apiKey");
        String secretKey = remoteAuthenInforService.getAuthenInfo("/", "secretKey");
        String apiURL = remoteAuthenInforService.getAuthenInfo("/", "apiUrl");
        //ai调用 返回内容
        content = sendContent(messagesList, apiKey, secretKey, apiURL);
        String[] split = content.split("\\r?\\n");
        List<String> stringList = new ArrayList<>();
        for (String s1 : split) {
            stringList.add(s1.replaceFirst("^\\d+\\.\\s*", ""));
        }
        return stringList;
    }


    @Override
    public String relatedAnswer(String content) throws JSONException, IOException {
        //构建聊天记录
        List<Messages> messagesList = new ArrayList<>();
        Messages messages = new Messages();
        messages.setRole("user");

        //使用者 问题内容
        messages.setContent(content);
        messagesList.add(messages);

        //发送聊天内容
        String apiKey = remoteAuthenInforService.getAuthenInfo("/", "apiKey");
        String secretKey = remoteAuthenInforService.getAuthenInfo("/", "secretKey");
        String apiURL = remoteAuthenInforService.getAuthenInfo("/", "apiUrl");
        //ai调用 返回内容
        content = sendContent(messagesList, apiKey, secretKey, apiURL);
        return content;
    }


    @Override
    public Flux<String> insertDialogueRecordingDeepSeek(DialogueRecording dialogueRecording) throws IOException {
        String language = dialogueRecording.getLanguage();
        String yuYan = "";
        if (!"".equals(language) && language != null) {
            List<SysDictData> voiceType = remoteDictTypeService.dictTypeGetInfo("voice_type", SecurityConstants.INNER);
            for (SysDictData sysDictData : voiceType) {
                if (sysDictData.getDictValue().equals(language)) {
                    yuYan = ",用" + sysDictData.getDictLabel() + "回复";
                }
            }
        }

        //新增对话记录
        long id = dialogueRecording.getId();
        dialogueRecording.setCreateTime(DateUtils.getNowDate());
        if (!Objects.isNull(tokenService.getLoginUser())) {
            dialogueRecording.setCreateBy(tokenService.getLoginUser().getUsername());
        }

        dialogueRecordingMapper.insertDialogueRecording(dialogueRecording);
        //对话记录明细对象
        DialogueDetails dialogueDetails = new DialogueDetails();
        dialogueDetails.setOrderIn(1l);
        dialogueDetails.setIssue("user");
        dialogueDetails.setDialogueId(id);

        //询问内容  如果问ai是什么创建拦截
        Boolean mark = true;
        String tempContent = dialogueRecording.getContent();
        String passContent = configService.getConfigKey2("pass_content", SecurityConstants.INNER).get("msg").toString();
        //判断tempContent内容
        if (checkContentPass(tempContent, passContent)) {
            mark = false;
        }
        //---通过枚举控制  指定词汇 回复指定自定义模板
        if (Objects.nonNull(dialogueRecording.getImagePath())) {
            String filePath = dialogueRecording.getImagePath();
            String replaced = filePath.replace("\\", "/");
            JSONObject jsonObject = new JSONObject();
            try {
                jsonObject.put("content", dialogueRecording.getContent());
                jsonObject.put("imagePath", replaced);
            } catch (JSONException e) {
                throw new RuntimeException(e);
            }
            String jsonString = jsonObject.toString();
            dialogueDetails.setContent(jsonString);
        } else {
            dialogueDetails.setContent(dialogueRecording.getContent());
        }
        dialogueDetailsMapper.insertDialogueDetails(dialogueDetails);

        dialogueDetails.setOrderIn(2l);
        dialogueDetails.setIssue("assistant");
        dialogueDetails.setDialogueId(id);
        String PcOrWxMark = dialogueRecording.getMenuRouting();
        String modelContent = "";
        if (PcOrWxMark.equals("applet")) {
            modelContent = configService.getConfigKey2("model_content_wx", SecurityConstants.INNER).get("msg").toString();
        } else {
            modelContent = configService.getConfigKey2("model_content_pc", SecurityConstants.INNER).get("msg").toString();
        }
        if (!mark) {
            String content = modelContent;
            return Flux.create(sink -> {
                try {
                    sink.next(new Messages("content", content).toString());
                    sink.complete();
                    dialogueDetails.setContent(content);
                    dialogueDetailsMapper.insertDialogueDetails(dialogueDetails);
                } catch (Exception e) {
                    sink.error(e);
                }
            });
        }
        //构建聊天记录
        List<Messages> messagesList = new ArrayList<>();
        Messages messages = new Messages();
        messages.setRole("user");
        messages.setContent(dialogueRecording.getContent().replaceAll("\\r\\n|\\r|\\n", "") + yuYan);
        messagesList.add(messages);
        if (dialogueRecording.getDeepThinking()) {
            //ai调用 返回内容
            return sendContentDeepSeek(messagesList, dialogueDetails);
        } else {
            //ai调用 返回内容
            return sendContentDeepSeek3(messagesList, dialogueDetails);
        }


    }

    private Flux<String> sendContentDeepSeek(List<Messages> messagesList, DialogueDetails dialogueDetails) throws IOException {
        Gson gson = new Gson();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, "{\"model\":\"deepseek-r1\"," +
                "\"messages\":" + gson.toJson(messagesList) + "," +
                "\"stream\":true,\"disable_search\":false,\"enable_citation\":false}");
        Request request = new Request.Builder()
                .url("https://qianfan.baidubce.com/v2/chat/completions")
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .addHeader("appid", "")
                .addHeader("Authorization", "Bearer bce-v3/ALTAK-dyQhjZkw5RjqzaBOcJ5lc/04a5392ac58e7e75a61e7ce5a4a92a62613b075c")
                .build();
        try {
            Response response = HTTP_CLIENT.newCall(request).execute();
        } catch (IOException e) {
            return Flux.create(sink -> {
                sink.next(new Messages("content", "服务器繁忙,请稍后重试...").toString());
                sink.complete();
            });
        }
        return Flux.create(sink -> {

            Call call = HTTP_CLIENT.newCall(request);
            call.enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    sink.error(e);
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().byteStream(), StandardCharsets.UTF_8))) {
                        String line;
                        String content = "";
                        String reasoningContent = "";
                        Long dialogueId = dialogueDetails.getDialogueId();
                        redisService.setCacheObject("A" + dialogueId, true);
                        while ((line = reader.readLine()) != null && redisService.hasKey("A" + dialogueId)) {
                            if (line != null && !line.equals("")) {
                                String jsonString = line.replaceFirst("data: ", "");
                                if (jsonString.equals("[DONE]")) {
                                    break;
                                }
                                // 解析JSON字符串
                                JSONObject jsonObject = new JSONObject(jsonString);
                                JSONArray choicesArray = jsonObject.getJSONArray("choices");

                                // 获取choices数组中的第一个元素
                                JSONObject firstChoice = choicesArray.getJSONObject(0);

                                // 获取delta对象
                                JSONObject deltaObject = firstChoice.getJSONObject("delta");
                                // 获取content字段的值
                                String contentValue = deltaObject.get("content").toString();
                                if (!contentValue.equals("null")) {
                                    sink.next(new Messages("content", contentValue).toString());
                                    content += contentValue;
                                } else {
                                    String reasoningContentvalue = deltaObject.get("reasoning_content").toString();
                                    if (!reasoningContentvalue.equals("null")) {
                                        sink.next(new Messages("reasoningContent", reasoningContentvalue).toString());
                                        reasoningContent += reasoningContentvalue;
                                    }
                                }

                            }
                        }


                        sink.complete();
                        redisService.deleteObject("A" + dialogueId);
                        dialogueDetails.setContent(content);
                        dialogueDetails.setReasoningContent(reasoningContent);
                        dialogueDetailsMapper.insertDialogueDetails(dialogueDetails);
                    } catch (Exception e) {
                        sink.error(e);
                    }
                }
            });
        });
    }

    private Flux<String> sendContentDeepSeek3(List<Messages> messagesList, DialogueDetails dialogueDetails) throws IOException {
        Gson gson = new Gson();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, "{\"model\":\"deepseek-v3\"," +
                "\"messages\":" + gson.toJson(messagesList) + "," +
                "\"stream\":true}");
        Request request = new Request.Builder()
                .url("https://qianfan.baidubce.com/v2/chat/completions")
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .addHeader("appid", "")
                .addHeader("Authorization", "Bearer bce-v3/ALTAK-dyQhjZkw5RjqzaBOcJ5lc/04a5392ac58e7e75a61e7ce5a4a92a62613b075c")
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        return Flux.create(sink -> {

            Call call = HTTP_CLIENT.newCall(request);
            call.enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    sink.error(e);
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().byteStream(), StandardCharsets.UTF_8))) {
                        String line;
                        String content = "";
                        Long dialogueId = dialogueDetails.getDialogueId();
                        redisService.setCacheObject("A" + dialogueId, true);
                        while ((line = reader.readLine()) != null && redisService.hasKey("A" + dialogueId)) {
                            if (line != null && !line.equals("")) {
                                String jsonString = line.replaceFirst("data: ", "");
                                if (jsonString.equals("[DONE]")) {
                                    break;
                                }
                                // 解析JSON字符串
                                JSONObject jsonObject = new JSONObject(jsonString);
                                JSONArray choicesArray = jsonObject.getJSONArray("choices");

                                // 获取choices数组中的第一个元素
                                JSONObject firstChoice = choicesArray.getJSONObject(0);

                                // 获取delta对象
                                JSONObject deltaObject = firstChoice.getJSONObject("delta");
                                // 获取content字段的值
                                String contentValue = deltaObject.get("content").toString();
                                if (!contentValue.equals("null")) {
                                    sink.next(new Messages("content", contentValue).toString());
                                    content += contentValue;
                                }

                            }
                        }
                        sink.complete();
                        redisService.deleteObject("A" + dialogueId);
                        dialogueDetails.setContent(content);
                        dialogueDetailsMapper.insertDialogueDetails(dialogueDetails);
                    } catch (Exception e) {
                        sink.error(e);
                    }
                }
            });
        });
    }


    @Override
    public Flux<String> updateDialogueRecordingDeepSeek(DialogueRecording dialogueRecording) throws IOException {
        String language = dialogueRecording.getLanguage();
        String yuYan = "";
        if (!"".equals(language) && language != null) {
            List<SysDictData> voiceType = remoteDictTypeService.dictTypeGetInfo("voice_type", SecurityConstants.INNER);
            for (SysDictData sysDictData : voiceType) {
                if (sysDictData.getDictValue().equals(language)) {
                    yuYan = ",用" + sysDictData.getDictLabel() + "回复";
                }
            }
        }

        Long id = dialogueRecording.getId();
        //查询对话记录最大次序
        Long maxOrder = dialogueDetailsMapper.selectMaxOrderById(id);
        //增加对话记录明细
        DialogueDetails dialogueDetails = new DialogueDetails();
        if (maxOrder != null) {
            dialogueDetails.setOrderIn(maxOrder + 1);
        } else {
            dialogueDetails.setOrderIn(1l);
        }
        if (dialogueRecording.getPromptId() == null) {
            String msg = configService.getConfigKey2(dialogueRecording.getInvocation() + "_" + dialogueRecording.getMenuRouting().replace("/", ""), SecurityConstants.INNER).get("msg").toString();
            if (msg != null && !msg.equals("")) {
                dialogueRecording.setPromptId(Long.valueOf(msg));
            }
        }
        PromptTemplate promptTemplate = new PromptTemplate();
        if (StringUtils.isNotNull(promptTemplate) && StringUtils.isNotNull(promptTemplate.getTemplateContent())) {
            promptTemplate = remoteUniversityService.getPromptInfo(dialogueRecording.getPromptId());

        }
        //询问内容  如果问ai是什么创建拦截
        Boolean mark = true;
        String tempContent = dialogueRecording.getContent();
        String passContent = configService.getConfigKey2("pass_content", SecurityConstants.INNER).get("msg").toString();
        //判断tempContent内容
        if (checkContentPass(tempContent, passContent)) {
            mark = false;
        }
        if (Objects.nonNull(dialogueRecording.getImagePath())) {
            String filePath = dialogueRecording.getImagePath();
            String replaced = filePath.replace("\\", "/");
            JSONObject jsonObject = new JSONObject();
            try {
                jsonObject.put("content", dialogueRecording.getContent());
                jsonObject.put("imagePath", replaced);
            } catch (JSONException e) {
                throw new RuntimeException(e);
            }
            String jsonString = jsonObject.toString();
            dialogueDetails.setContent(jsonString);
        } else {
            dialogueDetails.setContent(dialogueRecording.getContent().replaceAll("\\r\\n|\\r|\\n", ""));
        }
        dialogueDetails.setIssue("user");
        dialogueDetails.setDialogueId(id);
        if (StringUtils.isNotNull(promptTemplate) && StringUtils.isNotNull(promptTemplate.getTemplateContent())) {
            dialogueDetails.setPrompt(promptTemplate.getTemplateContent());

        }
        dialogueDetailsMapper.insertDialogueDetails(dialogueDetails);
        if (maxOrder != null) {
            dialogueDetails.setOrderIn(maxOrder + 1);
        } else {
            dialogueDetails.setOrderIn(2l);
        }
        dialogueDetails.setIssue("assistant");
        dialogueDetails.setDialogueId(id);

        String PcOrWxMark = dialogueRecording.getMenuRouting();
        String modelContent = "";
        String pattern = "<<<:.*?>>>";
        if (PcOrWxMark.equals("applet")) {
            modelContent = configService.getConfigKey2("model_content_wx", SecurityConstants.INNER).get("msg").toString();
        } else {
            modelContent = configService.getConfigKey2("model_content_pc", SecurityConstants.INNER).get("msg").toString();
        }
        if (!mark) {
            String content = modelContent;
            return Flux.create(sink -> {
                try {
                    sink.next(new Messages("content", content).toString());
                    sink.complete();
                    dialogueDetails.setContent(content);
                    dialogueDetailsMapper.insertDialogueDetails(dialogueDetails);
                } catch (Exception e) {
                    sink.error(e);
                }
            });
        }

        //查看聊天记录
        List<DialogueDetails> dialogueDetailsList = dialogueDetailsMapper.selectDialogueDetailsList(id);
        //构建聊天记录
        List<Messages> messagesList = new ArrayList<>();
        for (DialogueDetails details : dialogueDetailsList) {
            Messages messages = new Messages();
            messages.setRole(details.getIssue());
            if ("user".equals(details.getIssue())) {
                messages.setContent(details.getContent() + yuYan);
            } else {
                messages.setContent(details.getContent().replaceAll(pattern, ""));
            }
            messagesList.add(messages);
        }
        if (dialogueRecording.getDeepThinking()) {
            //ai调用 返回内容
            return sendContentDeepSeek(messagesList, dialogueDetails);
        } else {
            //ai调用 返回内容
            return sendContentDeepSeek3(messagesList, dialogueDetails);
        }

    }


    // 文件删除方法
    private void deleteFile(File file) {
        try {
            if (file.exists() && !file.delete()) {
                throw new IOException("删除文件失败: " + file.getAbsolutePath());
            }
        } catch (Exception e) {

        }
    }


    @Override
    public Boolean stopChatSession(String sessionId) {
        redisService.deleteObject("chat_session:" + sessionId);
        return true;
    }

    @Override
    public Map<String, String> relatedIssuesMap(String content) throws IOException, JSONException {
        String s = configService.getConfigKey2("relatedIssues", SecurityConstants.INNER).get("msg").toString();
        //构建聊天记录
        List<Messages> messagesList = new ArrayList<>();
        Messages messages = new Messages();
        messages.setRole("user");

        //使用者 问题内容
        messages.setContent(s.replace("{}", content).replaceAll("\\r\\n|\\r|\\n", ""));
        messagesList.add(messages);

        //发送聊天内容
        String apiKey = remoteAuthenInforService.getAuthenInfo("/", "apiKey");
        String secretKey = remoteAuthenInforService.getAuthenInfo("/", "secretKey");
        String apiURL = remoteAuthenInforService.getAuthenInfo("/", "apiUrl");
        //ai调用 返回内容
        content = sendContent(messagesList, apiKey, secretKey, apiURL);
        String[] split = content.split("\\r?\\n");
        HashMap<String, String> map = new HashMap<>();
        int count = 0;
        for (int i = split.length - 1; i >= 0; i--) {
            String str = split[i].replaceFirst("^\\d+\\.\\s*", "");
            if (count >= 3) break;
            if (StringUtils.isNotBlank(str)) {
                map.put(String.valueOf(count), str);
            }
            count++;
        }
        return map;
    }

    @Override
    public Flux<String> insertDialogueRecordingDeepSeekFlux(DialogueRecording dialogueRecording) {
        String language = dialogueRecording.getLanguage();
        String yuYan = "";
        if (!"".equals(language) && language != null) {
            List<SysDictData> voiceType = remoteDictTypeService.dictTypeGetInfo("voice_type", SecurityConstants.INNER);
            for (SysDictData sysDictData : voiceType) {
                if (sysDictData.getDictValue().equals(language)) {
                    yuYan = ",用" + sysDictData.getDictLabel() + "回复";
                }
            }
        }

        //新增对话记录
        long id = dialogueRecording.getId();
        dialogueRecording.setCreateTime(DateUtils.getNowDate());
        if (!Objects.isNull(tokenService.getLoginUser())) {
            dialogueRecording.setCreateBy(tokenService.getLoginUser().getUsername());
        }

        dialogueRecordingMapper.insertDialogueRecording(dialogueRecording);
        //对话记录明细对象
        DialogueDetails dialogueDetails = new DialogueDetails();
        dialogueDetails.setOrderIn(1L);
        dialogueDetails.setIssue("user");
        dialogueDetails.setDialogueId(id);

        //询问内容  如果问ai是什么创建拦截
        boolean mark = true;
        String tempContent = dialogueRecording.getContent();
        String passContent = configService.getConfigKey2("pass_content", SecurityConstants.INNER).get("msg").toString();
        //判断tempContent内容
        if (checkContentPass(tempContent, passContent)) {
            mark = false;
        }
        //---通过枚举控制  指定词汇 回复指定自定义模板
        if (Objects.nonNull(dialogueRecording.getImagePath())) {
            String filePath = dialogueRecording.getImagePath();
            String replaced = filePath.replace("\\", "/");
            JSONObject jsonObject = new JSONObject();
            try {
                jsonObject.put("content", dialogueRecording.getContent());
                jsonObject.put("imagePath", replaced);
            } catch (JSONException e) {
                throw new RuntimeException(e);
            }
            String jsonString = jsonObject.toString();
            dialogueDetails.setContent(jsonString);
        } else {
            dialogueDetails.setContent(dialogueRecording.getContent());
        }
        dialogueDetailsMapper.insertDialogueDetails(dialogueDetails);

        dialogueDetails.setOrderIn(2L);
        dialogueDetails.setIssue("assistant");
        dialogueDetails.setDialogueId(id);
        String PcOrWxMark = dialogueRecording.getMenuRouting();
        String modelContent = "";
        if (PcOrWxMark.equals("applet")) {
            modelContent = configService.getConfigKey2("model_content_wx", SecurityConstants.INNER).get("msg").toString();
        } else {
            modelContent = configService.getConfigKey2("model_content_pc", SecurityConstants.INNER).get("msg").toString();
        }
        if (!mark) {
            String content = modelContent;
            String sessionId = String.valueOf(snowflake.generateId());
            dialogueDetails.setSessionId(sessionId);

            return Flux.create(sink -> {
                try {
                    Messages messageData = Messages.builder().role("content")
                            .content(content).sessionId(sessionId).build();
                    sink.next("data: " + gson.toJson(messageData) + "\n\n");
                    sink.complete();

                    dialogueDetails.setContent(content);
                    dialogueDetailsMapper.insertDialogueDetails(dialogueDetails);

                } catch (Exception e) {
                    sink.error(e);
                }
            });
        }
        //构建聊天记录
        List<Messages> messagesList = new ArrayList<>();
        Messages messages = new Messages();
        messages.setRole("user");
        messages.setContent(dialogueRecording.getContent().replaceAll("\\r\\n|\\r|\\n", "") + yuYan);
        messagesList.add(messages);

        if (dialogueRecording.getDeepThinking()) {
            //ai调用 返回内容
            return sendContentDeepSeekFlux(messagesList, dialogueDetails, "deepseek-r1");
        } else {
            //ai调用 返回内容
            return sendContentDeepSeekFlux(messagesList, dialogueDetails, "deepseek-v3");
        }

    }

    @Override
    public Flux<String> chatModelFlux(DialogueRecording dialogueRecording) {
        // 确定语言类型提示
        String languageTypeTip = determineLanguageTypeTip(dialogueRecording.getLanguage());

        // 获取dialogueRecording id
        Long id = dialogueRecording.getId();

        // 构建新的聊天对话集合
        List<Messages> messagesList = new ArrayList<>();


        if (id == null) {
            // 如果id为空,新增对话记录以及对话记录明细
            // 构建dialogueRecording基础数据
            dialogueRecording.setCreateTime(DateUtils.getNowDate());
            if (!Objects.isNull(tokenService.getLoginUser())) {
                dialogueRecording.setCreateBy(tokenService.getLoginUser().getUsername());
            }
            dialogueRecordingMapper.insertDialogueRecording(dialogueRecording);

            //对话记录明细对象
            DialogueDetails dialogueDetail = new DialogueDetails();
            dialogueDetail.setOrderIn(1L);
            dialogueDetail.setIssue("user");
            dialogueDetail.setDialogueId(dialogueRecording.getId());
            dialogueDetail.setContent(dialogueRecording.getContent());
            dialogueDetail.setSessionId(String.valueOf(snowflake.generateId()));
            dialogueDetail.setCreateTime(DateUtils.getNowDate());
            dialogueDetailsMapper.insertDialogueDetails(dialogueDetail);


            StringBuilder problem = new StringBuilder();
            Messages messages = Messages.builder().role("user").build();

            if (StringUtils.isNotBlank(dialogueRecording.getLanguage())) {
                problem.append("【当前对话语言：").append(dialogueRecording.getLanguage()).append("】");
            }
            if (StringUtils.isNotBlank(dialogueRecording.getSceneTheme())) {
                problem.append("【当前对话场景主题：").append(dialogueRecording.getSceneTheme()).append("】");
            }
            problem.append(dialogueRecording.getContent().replaceAll("\\r\\n|\\r|\\n", ""));

            if (StringUtils.isNotBlank(problem.toString())) {
                messages.setContent(problem.toString());
                messagesList.add(messages);
            }

            return sendContentDeepSeekFlux(messagesList, dialogueDetail,
                    Boolean.TRUE.equals(dialogueRecording.getDeepThinking()) ? "deepseek-r1" : "deepseek-v3");

        } else {
            // 存在对话记录,在原有的对话记录下新增对话记录明细
            DialogueDetails dialogueDetail = new DialogueDetails();

            //查询对话记录最大次序
            Long maxOrder = dialogueDetailsMapper.selectMaxOrderById(id);
            dialogueDetail.setOrderIn(maxOrder == null ? 1L : maxOrder + 1);
            dialogueDetail.setIssue("user");
            dialogueDetail.setDialogueId(dialogueRecording.getId());
            dialogueDetail.setContent(dialogueRecording.getContent());
            dialogueDetail.setSessionId(String.valueOf(snowflake.generateId()));
            dialogueDetail.setCreateTime(DateUtils.getNowDate());
            dialogueDetailsMapper.insertDialogueDetails(dialogueDetail);

            //查看聊天记录
            List<DialogueDetails> dialogueDetailsList = dialogueDetailsMapper.selectDialogueDetailsList(id);

            String pattern = "<<<:.*?>>>";
            for (DialogueDetails details : dialogueDetailsList) {
                Messages messages = new Messages();
                messages.setRole(details.getIssue());
                if ("user".equals(details.getIssue())) {
                    messages.setContent(languageTypeTip + details.getContent());
                } else {
                    messages.setContent(details.getContent().replaceAll(pattern, ""));
                }
                messagesList.add(messages);
            }

            return sendContentDeepSeekFlux(messagesList, dialogueDetail,
                    Boolean.TRUE.equals(dialogueRecording.getDeepThinking()) ? "deepseek-r1" : "deepseek-v3");

        }
    }

    @Override
    public String startDifyWsFlux(ChatDifyDto chatDifyDto) {

        // 获取dialogueRecordingId
        String dialogueRecordingId = chatDifyDto.getDialogueRecordingId();

        DialogueRecording dialogueRecording = new DialogueRecording();
        DialogueDetails dialogueDetails = new DialogueDetails();
        if (StringUtils.isBlank(dialogueRecordingId)) {
            // 新会话
            dialogueRecording.setCreateTime(DateUtils.getNowDate());
            dialogueRecording.setCreateBy(tokenService.getLoginUser().getUsername());
            dialogueRecording.setMenuRouting(chatDifyDto.getMenuRouting());
            dialogueRecording.setInvocation(chatDifyDto.getInvocation());
            dialogueRecordingMapper.insertDialogueRecording(dialogueRecording);

            dialogueDetails.setOrderIn(1L);
            dialogueDetails.setIssue("user");
            dialogueDetails.setDifyConversationId(chatDifyDto.getDifyConversationId());
            dialogueDetails.setDialogueId(dialogueRecording.getId());
            dialogueDetails.setContent(chatDifyDto.getContent());
            dialogueDetails.setSessionId(String.valueOf(snowflake.generateId()));
            dialogueDetails.setCreateTime(DateUtils.getNowDate());
            dialogueDetailsMapper.insertDialogueDetails(dialogueDetails);
        } else {
            Long maxOrder = dialogueDetailsMapper.selectMaxOrderById(Long.valueOf(chatDifyDto.getDialogueRecordingId()));
            dialogueDetails.setOrderIn(maxOrder == null ? 1L : maxOrder + 1);
            dialogueDetails.setIssue("user");
            dialogueDetails.setDifyConversationId(chatDifyDto.getDifyConversationId());
            dialogueDetails.setDialogueId(Long.valueOf(chatDifyDto.getDialogueRecordingId()));
            dialogueDetails.setContent(chatDifyDto.getContent());
            dialogueDetails.setSessionId(String.valueOf(snowflake.generateId()));
            dialogueDetails.setCreateTime(DateUtils.getNowDate());
            dialogueDetailsMapper.insertDialogueDetails(dialogueDetails);
        }

        chatDifyDto.setDialogueDetails(dialogueDetails);


        Flux<String> flux = sendContentToSelfDify(chatDifyDto);
        // 收集所有结果并阻塞等待返回
        List<String> resultList = flux.collectList().block();
        // 这里可以对 resultList 做处理
        return "success";
    }

    @Override
    public Flux<String> chatDifyFlux(ChatDifyDto chatDifyDto) {
        return sendContentToSelfDify(chatDifyDto);
    }

    private Flux<String> sendContentToSelfDify(ChatDifyDto chatDifyDto) {
        SysUser sysUser = SecurityUtils.getLoginUser().getSysUser();
        String userKey = sysUser.getUserId() + "-" + sysUser.getUserName();

        String DIFY_BASE_URL = difyConfigProperties.getDifyBaseUrl();
        String DIFY_APP_API_KEY = difyConfigProperties.getAppApiKey1();


        DialogueDetails dialogueDetails = chatDifyDto.getDialogueDetails();
        Long dialogueRecordingId = dialogueDetails.getDialogueId();


        String sessionId = String.valueOf(snowflake.generateId());
        dialogueDetails.setSessionId(sessionId);

        if (StringUtils.isBlank(chatDifyDto.getResponseMode())) {
            chatDifyDto.setResponseMode("streaming");
        }
        if (StringUtils.isBlank(chatDifyDto.getDifyConversationId())) {
            chatDifyDto.setDifyConversationId("");
        }

        // 构建 JSON 请求体
        JsonObject requestBody = new JsonObject();
        requestBody.add("inputs", new JsonObject());
        requestBody.addProperty("query", chatDifyDto.getContent());
        requestBody.addProperty("response_mode", "streaming");
        requestBody.addProperty("conversation_id", chatDifyDto.getDifyConversationId());
        requestBody.addProperty("user", userKey);
        MediaType mediaType = MediaType.get("application/json; charset=utf-8");
        // 构建 HTTP 请求
        RequestBody body = RequestBody.create(gson.toJson(requestBody), mediaType);
        Request request = new Request.Builder()
                .url(DIFY_BASE_URL + "/chat-messages")
                .post(body)
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer " + DIFY_APP_API_KEY)
                .build();

        return Flux.create(sink -> {
            Call call = HTTP_CLIENT.newCall(request);

            // 流式中断时的清理逻辑
            sink.onDispose(() -> {
                if (!call.isCanceled()) {
                    call.cancel();
                }
            });

            call.enqueue(new Callback() {
                @Override
                public void onFailure(@NotNull Call call, @NotNull IOException e) {
                    sink.error(e); // 请求失败时发送错误
                }

                @Override
                public void onResponse(@NotNull Call call, @NotNull Response response) {
                    if (!response.isSuccessful()) {
                        sink.error(new IOException("请求失败: response=" + response));
                        return;
                    }

                    if (response.body() == null) {
                        sink.error(new IOException("请求失败: response=" + response));
                        return;
                    }
                    String deepSeekThingStartSign1 = "<think>\n";
                    String deepSeekThingEndSign1 = "</think>";
                    String deepSeekThingStartSign2 = "<details style=\"color:gray;background-color: #f8f8f8;padding: 8px;border-radius: 4px;\" open> <summary> Thinking... </summary>";
                    String deepSeekThingEndSign2 = "</details>";

                    boolean thing = false;
                    redisService.setCacheObject("chat_session:" + sessionId, true, 20L, TimeUnit.MINUTES);


                    try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().byteStream(),
                            StandardCharsets.UTF_8))) {
                        String line;
                        StringBuilder content = new StringBuilder();
                        StringBuilder reasoningContent = new StringBuilder();
                        Object metadata = "";
                        while ((line = reader.readLine()) != null && redisService.hasKey("chat_session:" + sessionId)) {
                            if (StringUtils.isBlank(line)) {
                                continue;
                            }
                            if (line.startsWith("data: ")) {
                                String jsonString = line.substring(5);
//                                log.info("收到流数据: {}", jsonString);
                                // 解析 JSON
                                JsonObject jsonObject = gson.fromJson(jsonString, JsonObject.class);
                                String event = jsonObject.get("event").getAsString();

                                MessageDto msgDto = MessageDto.builder().build();
                                msgDto.setDialogueRecordingId(String.valueOf(dialogueRecordingId));
                                msgDto.setSessionId(dialogueDetails.getSessionId());
                                String dify_conversation_id = jsonObject.get("conversation_id").getAsString();
                                String dify_message_id = jsonObject.get("message_id").getAsString();
                                String dify_task_id = jsonObject.get("task_id").getAsString();
                                dialogueDetails.setDifyConversationId(dify_conversation_id);

                                if ("message".equals(event)) {
                                    // 消息
                                    String answer = jsonObject.get("answer").getAsString();
                                    if (answer.contains(deepSeekThingStartSign1) || answer.contains(deepSeekThingStartSign2)) {
                                        thing = true;
                                    }
                                    if (answer.contains(deepSeekThingEndSign1) || answer.contains(deepSeekThingEndSign2)) {
                                        thing = false;
                                    }
                                    answer = answer.replaceAll(deepSeekThingStartSign1, "");
                                    answer = answer.replaceAll(deepSeekThingStartSign2, "");
                                    answer = answer.replaceAll(deepSeekThingEndSign1, "");
                                    answer = answer.replaceAll(deepSeekThingEndSign2, "");
                                    if (thing) {
                                        msgDto.setType("reasoningContent");
                                        reasoningContent.append(answer);
                                    } else {
                                        msgDto.setType("content");
                                        content.append(answer);
                                    }
                                    msgDto.setContent(answer);
                                    msgDto.setDifyConversationId(dify_conversation_id);
                                    msgDto.setDifyMessageId(dify_message_id);
                                    msgDto.setDifyTaskId(dify_task_id);


                                }
                                if ("message_end".equals(event)) {
                                    metadata = jsonObject.getAsJsonObject("metadata");
                                    msgDto.setType("message_end");
                                    msgDto.setMetadata(metadata);
                                    msgDto.setDifyConversationId(dify_conversation_id);
                                    msgDto.setDifyMessageId(dify_message_id);
                                    msgDto.setDifyTaskId(dify_task_id);
                                }
                                String gsonJson = gson.toJson(msgDto);
                                sink.next(gsonJson);
                                if (chatDifyDto.getEnableWs()) {
//                                    log.info("发送消息到ws{}", gsonJson);
                                    wxWebSocket.sendMessageToUser(chatDifyDto.getWsId(), gsonJson);
                                }

                            }
                        }
                        // 构建模型回复数据
                        dialogueDetails.setIssue("assistant");
                        dialogueDetails.setContent(content.toString());
                        dialogueDetails.setReasoningContent(reasoningContent.toString());
                        dialogueDetails.setDifyMetaData(metadata.toString());
                        // 完成流式响应
                        sink.complete();

                        DialogueDetails tmpDialogueDetails = new DialogueDetails();
                        tmpDialogueDetails.setId(dialogueDetails.getId());
                        tmpDialogueDetails.setDifyConversationId(dialogueDetails.getDifyConversationId());
                        dialogueDetailsMapper.updateDialogueDetails(tmpDialogueDetails);

                        dialogueDetails.setId(null);
                        dialogueDetailsMapper.insertDialogueDetails(dialogueDetails);
                        redisService.deleteObject("chat_session:" + sessionId);
                    } catch (Exception e) {
                        sink.error(e);
                        redisService.deleteObject("chat_session:" + sessionId);
                    } finally {
                        response.close(); // 确保资源释放
                    }
                }
            });
        });
    }


    @Override
    public String stopChatToDify(ChatDifyDto chatDifyDto) {
        SysUser sysUser = SecurityUtils.getLoginUser().getSysUser();
        String userKey = sysUser.getUserId() + "-" + sysUser.getUserName();

        String DIFY_BASE_URL = difyConfigProperties.getDifyBaseUrl();
        String DIFY_APP_API_KEY = difyConfigProperties.getAppApiKey1();

        if (StringUtils.isBlank(chatDifyDto.getDifyTaskId())) {
            return "false";
        }
        redisService.deleteObject("chat_session:" + chatDifyDto.getSessionId());
        // 构建 JSON 请求体
        JsonObject requestBody = new JsonObject();
        requestBody.addProperty("user", "{\"user\": \"" + userKey + "\"}");
        MediaType mediaType = MediaType.get("application/json; charset=utf-8");
        // 构建 HTTP 请求
        RequestBody body = RequestBody.create(gson.toJson(requestBody), mediaType);
        Request request = new Request.Builder()
                .url(DIFY_BASE_URL + "/chat-messages/" + chatDifyDto.getDifyTaskId() + "/stop")
                .post(body)
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer " + DIFY_APP_API_KEY)
                .build();
        // 执行请求并获取响应
        try (Response response = HTTP_CLIENT.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("请求失败: response={}", response);
            } else {
                if (response.body() != null) {
                    System.out.println("请求成功: " + response.body().string());
                    return "success";
                }
            }
        } catch (IOException e) {
            log.error(e.getMessage());
            return "false";
        }
        return "false";
    }

    @Override
    public List<String> getRelatedIssues(ChatDifyDto chatDifyDto) {
        String sessionId = chatDifyDto.getSessionId();
        if (StringUtils.isBlank(sessionId)) {
            return new ArrayList<>();
        }
        List<Messages> messagesList = new ArrayList<>();

        List<DialogueDetails> dialogueDetailsList = dialogueDetailsMapper.selectDialogueDetailsListBySessionId(chatDifyDto.getSessionId());


        // 构建消息列表
        for (DialogueDetails details : dialogueDetailsList) {
            Messages messages = new Messages();
            messages.setRole(details.getIssue());
            messages.setContent(details.getReasoningContent() + details.getContent());
            messagesList.add(messages);
        }
        // 添加用户请求消息
        Messages userMessage = new Messages();
        userMessage.setRole("user");
        userMessage.setContent("请用中文对以上记录进行内容提取，并总结出3个与之相关的核心问题。严格要求：每个问题不超过10个字，直接列出问题不加解释。\n" +
                "（每个问题使用数字标号进行标记,每个问题结尾带有‘？’标志,不需要任何多余的回复和解释，只需要总结内容,提取三个问题回复即可）");
        messagesList.add(userMessage);
        // 构建 JSON 请求体
        JsonObject requestBody = new JsonObject();
        requestBody.addProperty("model", "deepseek-v3");
        requestBody.add("messages", gson.toJsonTree(messagesList));
        requestBody.addProperty("stream", false);
        requestBody.addProperty("disable_search", false);
        requestBody.addProperty("enable_citation", false);
        MediaType mediaType = MediaType.parse("application/json");
        // 构建 HTTP 请求
        RequestBody body = RequestBody.create(gson.toJson(requestBody), mediaType);
        Request request = new Request.Builder()
                .url("https://qianfan.baidubce.com/v2/chat/completions")
                .post(body)
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer bce-v3/ALTAK-dyQhjZkw5RjqzaBOcJ5lc/04a5392ac58e7e75a61e7ce5a4a92a62613b075c")
                .build();
        // 执行请求
        // 执行请求并处理响应
        try (Response response = HTTP_CLIENT.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("API请求失败: " + response.code() + " - " + response);
            }

            if (response.body() != null) {
                String responseBody = response.body().string();
                JsonObject jsonObject = gson.fromJson(responseBody, JsonObject.class);
                JsonArray choices = jsonObject.getAsJsonArray("choices");
                if (choices != null && !choices.isEmpty()) {
                    JsonObject choice = choices.get(0).getAsJsonObject();
                    JsonObject message = choice.getAsJsonObject("message");
                    String content = message.get("content").getAsString();

                    // 按行分割内容，提取问题列表

                    return Arrays.stream(content.split("\n"))
                            .filter(line -> line.matches("^\\d+\\..*")) // 匹配以数字开头的行
                            .map(line -> line.replaceFirst("^\\d+\\.\\s*", "")) // 去除数字和点
                            .collect(Collectors.toList()); // 返回提取出的问题
                }
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return new ArrayList<>();
    }

    @Override
    public String getDifyAgentIntroduction(ChatDifyDto chatDifyDto) {
        SysUser sysUser = SecurityUtils.getLoginUser().getSysUser();
        String userKey = sysUser.getUserId() + "-" + sysUser.getUserName();

        HashMap<String, String> dataMap = new HashMap<>();

        String DIFY_BASE_URL = difyConfigProperties.getDifyBaseUrl();
        String DIFY_APP_API_KEY = difyConfigProperties.getAppApiKey1();

        // 构建url请求
        HttpUrl url = Objects.requireNonNull(Objects.requireNonNull(HttpUrl.parse(DIFY_BASE_URL + "/parameters"))
                .newBuilder()
                .build());
        // 构建 HTTP 请求
        Request request = new Request.Builder()
                .url(url)  // 使用 HttpUrl 构建的 URL
                .get()  // GET 请求
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer " + DIFY_APP_API_KEY)
                .build();

        // 执行请求并获取响应
        try (Response response = HTTP_CLIENT.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("请求失败: response={}", response);
            } else {
                if (response.body() != null) {
                    String responseBody = response.body().string();
                    JsonObject jsonResponse = gson.fromJson(responseBody, JsonObject.class);

                    // 检查字段是否存在，若存在则返回，否则返回默认开场白
                    if (jsonResponse.has("opening_statement")) {
                        return jsonResponse.get("opening_statement").getAsString();
                    } else {
                        return "🌟 你好呀！小治已经升级为智能助手啦！\n" +
                                "🚀 知识宇宙一键深潜，寻找你想要的答案。\n" +
                                "💡 有什么想了解的，随时来找我聊天吧！";
                    }
                } else {
                    log.error("请求失败,{}", response);
                }
            }
        } catch (IOException e) {
            log.error("请求异常: {}", e.getMessage());
            return "🌟 你好呀！小治已经升级为智能助手啦！\n" +
                    "🚀 知识宇宙一键深潜，寻找你想要的答案。\n" +
                    "💡 有什么想了解的，随时来找我聊天吧！";
        }
        return "";
    }

    // 确定语言类型 提示
    private String determineLanguageTypeTip(String language) {
        String languageTip = "【当前对话语言类型：中文" + "】";
        if (StringUtils.isBlank(language)) {
            return languageTip;
        }
        List<SysDictData> voiceType = remoteDictTypeService.dictTypeGetInfo("voice_type", SecurityConstants.INNER);
        for (SysDictData sysDictData : voiceType) {
            if (sysDictData.getDictValue().equals(language)) {
                return "【当前对话语言类型：" + sysDictData.getDictLabel() + "】";
            }
        }
        return languageTip;
    }

    @Override
    public Flux<String> updateDialogueRecordingDeepSeekFlux(DialogueRecording dialogueRecording) {
        String language = dialogueRecording.getLanguage();
        String yuYan = "";
        if (!"".equals(language) && language != null) {
            List<SysDictData> voiceType = remoteDictTypeService.dictTypeGetInfo("voice_type", SecurityConstants.INNER);
            for (SysDictData sysDictData : voiceType) {
                if (sysDictData.getDictValue().equals(language)) {
                    yuYan = ",用" + sysDictData.getDictLabel() + "回复";
                }
            }
        }

        Long id = dialogueRecording.getId();
        //查询对话记录最大次序
        Long maxOrder = dialogueDetailsMapper.selectMaxOrderById(id);
        //增加对话记录明细
        DialogueDetails dialogueDetails = new DialogueDetails();
        if (maxOrder != null) {
            dialogueDetails.setOrderIn(maxOrder + 1);
        } else {
            dialogueDetails.setOrderIn(1L);
        }
        if (dialogueRecording.getPromptId() == null) {
            String msg = configService.getConfigKey2(dialogueRecording.getInvocation() + "_" + dialogueRecording.getMenuRouting().replace("/", ""), SecurityConstants.INNER).get("msg").toString();
            if (msg != null && !msg.equals("")) {
                dialogueRecording.setPromptId(Long.valueOf(msg));
            }
        }
        PromptTemplate promptTemplate = new PromptTemplate();
        if (StringUtils.isNotNull(promptTemplate) && StringUtils.isNotNull(promptTemplate.getTemplateContent())) {
            promptTemplate = remoteUniversityService.getPromptInfo(dialogueRecording.getPromptId());

        }
        //询问内容  如果问ai是什么创建拦截
        boolean mark = true;
        String tempContent = dialogueRecording.getContent();
        String passContent = configService.getConfigKey2("pass_content", SecurityConstants.INNER).get("msg").toString();
        //判断tempContent内容
        if (checkContentPass(tempContent, passContent)) {
            mark = false;
        }
        if (Objects.nonNull(dialogueRecording.getImagePath())) {
            String filePath = dialogueRecording.getImagePath();
            String replaced = filePath.replace("\\", "/");
            JSONObject jsonObject = new JSONObject();
            try {
                jsonObject.put("content", dialogueRecording.getContent());
                jsonObject.put("imagePath", replaced);
            } catch (JSONException e) {
                throw new RuntimeException(e);
            }
            String jsonString = jsonObject.toString();
            dialogueDetails.setContent(jsonString);
        } else {
            dialogueDetails.setContent(dialogueRecording.getContent().replaceAll("\\r\\n|\\r|\\n", ""));
        }
        dialogueDetails.setIssue("user");
        dialogueDetails.setDialogueId(id);
        if (StringUtils.isNotNull(promptTemplate) && StringUtils.isNotNull(promptTemplate.getTemplateContent())) {
            dialogueDetails.setPrompt(promptTemplate.getTemplateContent());

        }
        dialogueDetailsMapper.insertDialogueDetails(dialogueDetails);
        if (maxOrder != null) {
            dialogueDetails.setOrderIn(maxOrder + 1);
        } else {
            dialogueDetails.setOrderIn(2L);
        }
        dialogueDetails.setIssue("assistant");
        dialogueDetails.setDialogueId(id);

        String PcOrWxMark = dialogueRecording.getMenuRouting();
        String modelContent = "";
        String pattern = "<<<:.*?>>>";
        if (PcOrWxMark.equals("applet")) {
            modelContent = configService.getConfigKey2("model_content_wx", SecurityConstants.INNER).get("msg").toString();
        } else {
            modelContent = configService.getConfigKey2("model_content_pc", SecurityConstants.INNER).get("msg").toString();
        }
        if (!mark) {
            String content = modelContent;
            String sessionId = String.valueOf(snowflake.generateId());
            return Flux.create(sink -> {
                try {
                    Messages messageData = Messages.builder()
                            .role("reasoningContent")
                            .content(content)
                            .sessionId(sessionId).build();
                    sink.next("data: " + gson.toJson(messageData) + "\n\n");
                    sink.complete();
                    dialogueDetails.setContent(content);
                    dialogueDetailsMapper.insertDialogueDetails(dialogueDetails);
                } catch (Exception e) {
                    sink.error(e);
                }
            });
        }

        //查看聊天记录
        List<DialogueDetails> dialogueDetailsList = dialogueDetailsMapper.selectDialogueDetailsList(id);
        //构建聊天记录
        List<Messages> messagesList = new ArrayList<>();
        for (DialogueDetails details : dialogueDetailsList) {
            Messages messages = new Messages();
            messages.setRole(details.getIssue());
            if ("user".equals(details.getIssue())) {
                messages.setContent(details.getContent() + yuYan);
            } else {
                messages.setContent(details.getContent().replaceAll(pattern, ""));
            }
            messagesList.add(messages);
        }
        if (dialogueRecording.getDeepThinking()) {
            //ai调用 返回内容
            return sendContentDeepSeekFlux(messagesList, dialogueDetails, "deepseek-r1");
        } else {
            //ai调用 返回内容
            return sendContentDeepSeekFlux(messagesList, dialogueDetails, "deepseek-v3");
        }
    }

    private Flux<String> sendContentDeepSeekFlux(List<Messages> messagesList, DialogueDetails dialogueDetails, String model) {
        // 参数校验
        if (messagesList == null || messagesList.isEmpty()) {
            return Flux.error(new IllegalArgumentException("messagesList 不能为空"));
        }
        if (dialogueDetails == null) {
            return Flux.error(new IllegalArgumentException("dialogueDetails 不能为空"));
        }

        // 构建 JSON 请求体
        JsonObject requestBody = new JsonObject();
        requestBody.addProperty("model", model);
        requestBody.add("messages", gson.toJsonTree(messagesList));
        requestBody.addProperty("stream", true);
        requestBody.addProperty("disable_search", false);
        requestBody.addProperty("enable_citation", false);
        MediaType mediaType = MediaType.parse("application/json");

        // 构建 HTTP 请求
        RequestBody body = RequestBody.create(gson.toJson(requestBody), mediaType);
        Request request = new Request.Builder()
                .url("https://qianfan.baidubce.com/v2/chat/completions")
                .post(body)
                .addHeader("Content-Type", "application/json")
                .addHeader("appid", "")
                .addHeader("Authorization", "Bearer bce-v3/ALTAK-dyQhjZkw5RjqzaBOcJ5lc/04a5392ac58e7e75a61e7ce5a4a92a62613b075c")
                .build();
        Long dialogueId = dialogueDetails.getDialogueId();
        String sessionId = String.valueOf(snowflake.generateId());
        dialogueDetails.setSessionId(sessionId);

        Flux<String> rawFlux = Flux.create(sink -> {

            Call call = HTTP_CLIENT.newCall(request);


            // 流式中断时的清理逻辑
            sink.onDispose(() -> {
                redisService.deleteObject("chat_session:" + sessionId);
                if (!call.isCanceled()) {
                    call.cancel();
                }
            });

            call.enqueue(new Callback() {
                @Override
                public void onFailure(@NotNull Call call, @NotNull IOException e) {
                    sink.error(e); // 请求失败时发送错误
                }

                @Override
                public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {
                    if (!response.isSuccessful()) {
                        sink.error(new IOException("请求失败: code=" + response.code()));
                        return;
                    }
                    redisService.setCacheObject("chat_session:" + sessionId, true, 20L, TimeUnit.MINUTES);


                    try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().byteStream(),
                            StandardCharsets.UTF_8))) {
                        String line;
                        StringBuilder content = new StringBuilder();
                        StringBuilder reasoningContent = new StringBuilder();

                        while ((line = reader.readLine()) != null && redisService.hasKey("chat_session:" + sessionId)) {
                            if (line.isEmpty()) {
                                continue;
                            }
                            // 解析流式数据
                            if (line.startsWith("data: ")) {
                                String jsonString = line.replaceFirst("data: ", "");
                                if (jsonString.equals("[DONE]")) break; // 结束标记

                                // 解析 JSON
                                JsonObject jsonObject = gson.fromJson(jsonString, JsonObject.class);
                                JsonArray choices = jsonObject.getAsJsonArray("choices");
                                if (choices == null || choices.isEmpty()) continue;

                                // 获取choices数组中的第一个元素
                                JsonObject firstChoice = choices.get(0).getAsJsonObject();

                                // 获取delta对象
                                JsonObject delta = firstChoice.getAsJsonObject("delta");

                                // 处理 content
                                if (delta.has("content") && !delta.get("content").isJsonNull()) {
                                    String contentValue = delta.get("content").getAsString();
                                    Messages messageData = Messages.builder()
                                            .role("content")
                                            .content(contentValue)
                                            .dialogueId(String.valueOf(dialogueId))
                                            .sessionId(sessionId).build();
                                    sink.next(gson.toJson(messageData));
                                    content.append(contentValue);
                                }

                                // 处理 reasoningContent
                                if (delta.has("reasoning_content") && !delta.get("reasoning_content").isJsonNull()) {
                                    String reasoningContentValue = delta.get("reasoning_content").getAsString();
                                    Messages messageData = Messages.builder()
                                            .role("reasoningContent")
                                            .content(reasoningContentValue)
                                            .dialogueId(String.valueOf(dialogueId))
                                            .sessionId(sessionId).build();
                                    sink.next(gson.toJson(messageData));
                                    reasoningContent.append(reasoningContentValue);
                                }
                            }
                        }


                        // 保存对话详情
                        if (!redisService.hasKey("chat_session:" + sessionId)) {
                            // 追加换行和红色文本
                            String contentValue = "<br/><br/><h4 style=\"color: red;\"><strong>用户终止 ❌</strong></h4>";
                            content.append(contentValue);

                            // 构建消息对象
                            Messages messageData = Messages.builder()
                                    .role("content")
                                    .content(contentValue)
                                    .dialogueId(String.valueOf(dialogueId))
                                    .sessionId(sessionId)
                                    .build();

                            // 发送消息
                            sink.next(gson.toJson(messageData));

                        }
                        // 构建模型回复数据
                        dialogueDetails.setContent(content.toString());
                        dialogueDetails.setIssue("assistant");
                        dialogueDetails.setReasoningContent(reasoningContent.toString());

                        // 完成流式响应
                        sink.complete();
                        // 开始插入数据
                        dialogueDetailsMapper.insertDialogueDetails(dialogueDetails);
                        redisService.deleteObject("chat_session:" + sessionId);
                    } catch (Exception e) {
                        log.error("流式处理异常: dialogueId={}", dialogueId, e);
                        sink.error(e);
                    } finally {
                        response.close(); // 确保资源释放
                    }
                }
            });
        });
        return rawFlux
                // 开启无界缓冲，背压时保留所有消息，等待下游处理
                .onBackpressureBuffer()
                // 每10条消息或50ms批量发送一次
                .bufferTimeout(10, Duration.ofMillis(50))
                // 将批量消息重新扁平化为单个消息流
                .flatMap(Flux::fromIterable);
    }


}
