<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.KeySecretTokenMapper">

    <resultMap type="com.ruoyi.system.domain.KeySecretToken" id="KeySecretTokenResult">
        <result property="id" column="id"/>
        <result property="keySecretManageId" column="key_secret_manage_id"/>
        <result property="apiKey" column="api_key"/>
        <result property="token" column="token"/>
        <result property="clientName" column="client_name"/>
        <result property="clientId" column="client_id"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="expired" column="expired"/>
        <result property="expiredTime" column="expired_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <resultMap type="com.ruoyi.system.vo.KeySecretTokenVo" id="KeySecretTokenVoResult">
        <result property="id" column="id"/>
        <result property="keySecretManageId" column="key_secret_manage_id"/>
        <result property="apiKey" column="api_key"/>
        <result property="token" column="token"/>
        <result property="clientName" column="client_name"/>
        <result property="clientId" column="client_id"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="expired" column="expired"/>
        <result property="expiredTime" column="expired_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>

        <!-- 映射子表权限列表 -->
        <collection property="keySecretTokenPermissionList" ofType="com.ruoyi.system.domain.KeySecretTokenPermission">
            <id property="id" column="skstp_id"/>
            <result property="token" column="skstp_token"/>
            <result property="apiPaths" column="api_paths"/>
            <result property="limitMethod" column="limit_method"/>
            <result property="description" column="description"/>
            <result property="status" column="skstp_status"/>
            <result property="remark" column="skstp_remark"/>
        </collection>
    </resultMap>

    <sql id="selectKeySecretTokenVo">
        select id,
               key_secret_manage_id,
               api_key,
               token,
               client_name,
               client_id,
               status,
               remark,
               expired,
               expired_time,
               create_by,
               create_time,
               update_by,
               update_time
        from s_key_secret_token
    </sql>

    <select id="selectKeySecretTokenList" parameterType="com.ruoyi.system.domain.KeySecretToken"
            resultMap="KeySecretTokenResult">
        <include refid="selectKeySecretTokenVo"/>
        <where>
            <if test="keySecretManageId != null ">and key_secret_manage_id = #{keySecretManageId}</if>
            <if test="apiKey != null  and apiKey != ''">and api_key = #{apiKey}</if>
            <if test="token != null  and token != ''">and token = #{token}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="clientName != null  and clientName != ''">and client_name like concat('%', #{clientName}, '%')
            </if>
            <if test="expired != null  and expired != ''">and expired = #{expired}</if>
            <if test="beginExpiredTime != null and endExpiredTime != null">
                and expired_time between #{beginExpiredTime} and #{endExpiredTime}
            </if>
        </where>
    </select>

    <select id="selectKeySecretTokenById" parameterType="Long" resultMap="KeySecretTokenResult">
        <include refid="selectKeySecretTokenVo"/>
        where id = #{id}
    </select>
    <select id="selectKeySecretTokenListAndPermissions" resultMap="KeySecretTokenVoResult">
        SELECT
            skst.id AS id,
            skst.key_secret_manage_id,
            skst.api_key,
            skst.token,
            skst.client_name,
            skst.client_id,
            skst.status,
            skst.remark,
            skst.expired,
            skst.expired_time,
            skst.create_by,
            skst.create_time,
            skst.update_by,
            skst.update_time,

            skstp.id AS skstp_id,
            skstp.token AS skstp_token,
            skstp.api_paths,
            skstp.limit_method,
            skstp.description,
            skstp.status AS skstp_status,
            skstp.remark AS skstp_remark
        FROM s_key_secret_token skst
                 LEFT JOIN s_key_secret_token_permission skstp
                           ON skst.token = skstp.token
        <where>
            <if test="apiKey != null  and apiKey != ''">and api_key = #{apiKey}</if>
            <if test="token != null  and token != ''">and skstp.token = #{token}</if>
        </where>
    </select>

    <insert id="insertKeySecretToken" parameterType="com.ruoyi.system.domain.KeySecretToken" useGeneratedKeys="true"
            keyProperty="id">
        insert into s_key_secret_token
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="keySecretManageId != null">key_secret_manage_id,</if>
            <if test="apiKey != null">api_key,</if>
            <if test="token != null">token,</if>
            <if test="clientName != null">client_name,</if>
            <if test="clientId != null">client_id,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="expired != null">expired,</if>
            <if test="expiredTime != null">expired_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="keySecretManageId != null">#{keySecretManageId},</if>
            <if test="apiKey != null">#{apiKey},</if>
            <if test="token != null">#{token},</if>
            <if test="clientName != null">#{clientName},</if>
            <if test="clientId != null">#{clientId},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="expired != null">#{expired},</if>
            <if test="expiredTime != null">#{expiredTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateKeySecretToken" parameterType="com.ruoyi.system.domain.KeySecretToken">
        update s_key_secret_token
        <trim prefix="SET" suffixOverrides=",">
            <if test="keySecretManageId != null">key_secret_manage_id = #{keySecretManageId},</if>
            <if test="apiKey != null">api_key = #{apiKey},</if>
            <if test="token != null">token = #{token},</if>
            <if test="clientName != null">client_name = #{clientName},</if>
            <if test="clientId != null">client_id = #{clientId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="expired != null">expired = #{expired},</if>
            <if test="expiredTime != null">expired_time = #{expiredTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKeySecretTokenById" parameterType="Long">
        delete
        from s_key_secret_token
        where id = #{id}
    </delete>

    <delete id="deleteKeySecretTokenByIds" parameterType="String">
        delete from s_key_secret_token where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
