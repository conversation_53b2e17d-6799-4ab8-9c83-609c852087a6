<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CensoredWordsMapper">
    
    <resultMap type="CensoredWords" id="CensoredWordsResult">
        <result property="id"    column="id"    />
        <result property="word"    column="word"    />
        <result property="category"    column="category"    />
        <result property="rejectReply"    column="reject_reply"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCensoredWordsVo">
        select id, word, category, reject_reply, create_by, create_time, update_by, update_time from sys_censored_words
    </sql>

    <select id="selectCensoredWordsList" parameterType="CensoredWords" resultMap="CensoredWordsResult">
        <include refid="selectCensoredWordsVo"/>
        <where>  
            <if test="word != null  and word != ''"> and word like concat('%', #{word}, '%')</if>
            <if test="category != null  and category != ''">  and category = #{category} </if>
        </where>
    </select>
    
    <select id="selectCensoredWordsById" parameterType="Long" resultMap="CensoredWordsResult">
        <include refid="selectCensoredWordsVo"/>
        where id = #{id}
    </select>

    <insert id="insertCensoredWords" parameterType="CensoredWords" useGeneratedKeys="true" keyProperty="id">
        insert into sys_censored_words
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="word != null and word != ''">word,</if>
            <if test="category != null and category != ''">category,</if>
            <if test="rejectReply != null">reject_reply,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="word != null and word != ''">#{word},</if>
            <if test="category != null and category != ''">#{category},</if>
            <if test="rejectReply != null">#{rejectReply},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
        on duplicate key update
        update_time = now(),  <!-- 如果存在重复记录，更新 update_time 为当前时间 -->
        update_by = #{updateBy};  <!-- 如果存在重复记录，更新 update_by 字段 -->
    </insert>

    <update id="updateCensoredWords" parameterType="CensoredWords">
        update sys_censored_words
        <trim prefix="SET" suffixOverrides=",">
            <if test="word != null and word != ''">word = #{word},</if>
            <if test="category != null and category != ''">category = #{category},</if>
            <if test="rejectReply != null">reject_reply = #{rejectReply},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCensoredWordsById" parameterType="Long">
        delete from sys_censored_words where id = #{id}
    </delete>

    <delete id="deleteCensoredWordsByIds" parameterType="String">
        delete from sys_censored_words where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>