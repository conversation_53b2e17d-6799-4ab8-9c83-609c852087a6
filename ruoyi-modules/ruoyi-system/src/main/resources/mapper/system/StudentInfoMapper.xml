<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.StudentInfoMapper">

    <resultMap type="com.ruoyi.system.api.domain.StudentInfo" id="StudentInfoResult">
        <result property="id"    column="id"    />
        <result property="studentId"    column="student_id"    />
        <result property="studentName"    column="student_name"    />
        <result property="sex"    column="sex"    />
        <result property="univerId"    column="univer_id"    />
        <result property="colleId"    column="colle_id"    />
        <result property="majorId"    column="major_id"    />
        <result property="classId"    column="class_id"    />
        <result property="educationalSystem"    column="educational_system"    />
        <result property="currentGrade"    column="current_grade"    />
        <result property="schoolStatus"    column="school_status"    />
        <result property="studentStatus"    column="student_status"    />
        <result property="studentCurrentStatus"    column="student_current_status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectStudentInfoVo">
        select id, student_id, student_name, sex, univer_id, colle_id, major_id, class_id, educational_system, current_grade, school_status, student_status, student_current_status, create_by, create_time, update_by, update_time from s_student_info
    </sql>

    <select id="selectStudentInfoList" parameterType="com.ruoyi.system.api.domain.StudentInfo" resultMap="StudentInfoResult">
        <include refid="selectStudentInfoVo"/>
        <where>
            <if test="studentId != null  and studentId != ''"> and student_id = #{studentId}</if>
            <if test="studentName != null  and studentName != ''"> and student_name like concat('%', #{studentName}, '%')</if>
            <if test="sex != null  and sex != ''"> and sex = #{sex}</if>
            <if test="univerId != null "> and univer_id = #{univerId}</if>
            <if test="colleId != null "> and colle_id = #{colleId}</if>
            <if test="majorId != null "> and major_id = #{majorId}</if>
            <if test="classId != null "> and class_id = #{classId}</if>
            <if test="educationalSystem != null "> and educational_system = #{educationalSystem}</if>
            <if test="currentGrade != null "> and current_grade = #{currentGrade}</if>
            <if test="schoolStatus != null  and schoolStatus != ''"> and school_status = #{schoolStatus}</if>
            <if test="studentStatus != null  and studentStatus != ''"> and student_status = #{studentStatus}</if>
            <if test="studentCurrentStatus != null  and studentCurrentStatus != ''"> and student_current_status = #{studentCurrentStatus}</if>
        </where>
    </select>

    <select id="selectStudentInfoById" parameterType="Long" resultMap="StudentInfoResult">
        <include refid="selectStudentInfoVo"/>
        where student_id = #{id}
    </select>

    <select id="getStudentInfo" parameterType="com.ruoyi.system.api.domain.StudentInfo" resultMap="StudentInfoResult">
        <include refid="selectStudentInfoVo"/>
        where student_id = #{studentId}
        and student_name like concat('%', #{studentName}, '%')
        and colle_id = #{colleId}
        <if test="majorId != null "> and major_id = #{majorId}</if>
        <if test="classId != null "> and class_id = #{classId}</if>
    </select>

    <insert id="insertStudentInfo" parameterType="com.ruoyi.system.api.domain.StudentInfo">
        insert into s_student_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="studentId != null">student_id,</if>
            <if test="studentName != null">student_name,</if>
            <if test="sex != null">sex,</if>
            <if test="univerId != null">univer_id,</if>
            <if test="colleId != null">colle_id,</if>
            <if test="majorId != null">major_id,</if>
            <if test="classId != null">class_id,</if>
            <if test="educationalSystem != null">educational_system,</if>
            <if test="currentGrade != null">current_grade,</if>
            <if test="schoolStatus != null">school_status,</if>
            <if test="studentStatus != null">student_status,</if>
            <if test="studentCurrentStatus != null">student_current_status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="studentId != null">#{studentId},</if>
            <if test="studentName != null">#{studentName},</if>
            <if test="sex != null">#{sex},</if>
            <if test="univerId != null">#{univerId},</if>
            <if test="colleId != null">#{colleId},</if>
            <if test="majorId != null">#{majorId},</if>
            <if test="classId != null">#{classId},</if>
            <if test="educationalSystem != null">#{educationalSystem},</if>
            <if test="currentGrade != null">#{currentGrade},</if>
            <if test="schoolStatus != null">#{schoolStatus},</if>
            <if test="studentStatus != null">#{studentStatus},</if>
            <if test="studentCurrentStatus != null">#{studentCurrentStatus},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateStudentInfo" parameterType="com.ruoyi.system.api.domain.StudentInfo">
        update s_student_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="studentId != null">student_id = #{studentId},</if>
            <if test="studentName != null">student_name = #{studentName},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="univerId != null">univer_id = #{univerId},</if>
            <if test="colleId != null">colle_id = #{colleId},</if>
            <if test="majorId != null">major_id = #{majorId},</if>
            <if test="classId != null">class_id = #{classId},</if>
            <if test="educationalSystem != null">educational_system = #{educationalSystem},</if>
            <if test="currentGrade != null">current_grade = #{currentGrade},</if>
            <if test="schoolStatus != null">school_status = #{schoolStatus},</if>
            <if test="studentStatus != null">student_status = #{studentStatus},</if>
            <if test="studentCurrentStatus != null">student_current_status = #{studentCurrentStatus},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteStudentInfoById" parameterType="Long">
        delete from s_student_info where id = #{id}
    </delete>

    <delete id="deleteStudentInfoByIds" parameterType="String">
        delete from s_student_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>