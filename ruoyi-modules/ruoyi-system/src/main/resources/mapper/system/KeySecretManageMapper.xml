<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.KeySecretManageMapper">

    <resultMap type="com.ruoyi.system.domain.KeySecretManage" id="KeySecretManageResult">
        <result property="id" column="id"/>
        <result property="apiKey" column="api_key"/>
        <result property="secretKey" column="secret_key"/>
        <result property="clientName" column="client_name"/>
        <result property="clientId" column="client_id"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="expired" column="expired"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectKeySecretManageVo">
        select id,
               client_name,
               client_id,
               api_key,
               secret_key,
               remark,
               status,
               expired,
               create_by,
               create_time,
               update_by,
               update_time
        from s_key_secret_manage
    </sql>

    <select id="selectKeySecretManageList" parameterType="com.ruoyi.system.domain.KeySecretManage"
            resultMap="KeySecretManageResult">
        <include refid="selectKeySecretManageVo"/>
        <where>
            <if test="apiKey != null  and apiKey != ''">and api_key = #{apiKey}</if>
            <if test="secretKey != null  and secretKey != ''">and secret_key = #{secretKey}</if>
            <if test="clientName != null  and clientName != ''">and client_name like concat('%', #{clientName}, '%')
            </if>
            <if test="clientId != null  and clientId != ''">and client_id = #{clientId}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="expired != null  and expired != ''">and expired = #{expired}</if>
        </where>
    </select>

    <select id="selectKeySecretManageById" parameterType="Long" resultMap="KeySecretManageResult">
        <include refid="selectKeySecretManageVo"/>
        where id = #{id}
    </select>

    <insert id="insertKeySecretManage" parameterType="com.ruoyi.system.domain.KeySecretManage" useGeneratedKeys="true"
            keyProperty="id">
        insert into s_key_secret_manage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="apiKey != null">api_key,</if>
            <if test="secretKey != null">secret_key,</if>
            <if test="clientName != null">client_name,</if>
            <if test="clientId != null">client_id,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="expired != null">expired,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="apiKey != null">#{apiKey},</if>
            <if test="secretKey != null">#{secretKey},</if>
            <if test="clientName != null">#{clientName},</if>
            <if test="clientId != null">#{clientId},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="expired != null">#{expired},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateKeySecretManage" parameterType="com.ruoyi.system.domain.KeySecretManage">
        update s_key_secret_manage
        <trim prefix="SET" suffixOverrides=",">
            <if test="apiKey != null">api_key = #{apiKey},</if>
            <if test="secretKey != null">secret_key = #{secretKey},</if>
            <if test="clientName != null">client_name = #{clientName},</if>
            <if test="clientId != null">client_id = #{clientId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="expired != null">expired = #{expired},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKeySecretManageById" parameterType="Long">
        delete
        from s_key_secret_manage
        where id = #{id}
    </delete>

    <delete id="deleteKeySecretManageByIds" parameterType="String">
        delete from s_key_secret_manage where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
