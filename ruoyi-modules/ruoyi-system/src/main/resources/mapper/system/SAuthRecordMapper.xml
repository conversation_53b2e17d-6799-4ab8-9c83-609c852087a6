<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SAuthRecordMapper">

    <resultMap type="com.ruoyi.system.domain.SAuthRecord" id="SAuthRecordResult">
        <result property="authId"    column="auth_id"    />
        <result property="userId"    column="user_id"    />
        <result property="roleId"    column="role_id"    />
        <result property="auditStatus"    column="audit_status"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="auditUser"    column="audit_user"    />
        <result property="auditTime"    column="audit_time"    />
        <result property="roleName" column="role_name"/>
        <result property="userName" column="user_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="expTimeStart" column="exp_time_start"/>
        <result property="expTimeEnd" column="exp_time_end"/>
    </resultMap>

    <sql id="selectSAuthRecordVo">
        select auth_id, user_id, role_id,  audit_status, remark, create_time, audit_user, audit_time from s_auth_record
    </sql>

    <select id="selectSAuthRecordList" parameterType="com.ruoyi.system.domain.SAuthRecord" resultMap="SAuthRecordResult">
        select a.auth_id, a.user_id, a.role_id, r.role_name, a.audit_status, a.remark, a.create_time, a.audit_user, a.audit_time ,
        u.user_name ,u.nick_name ,u.university_id,u.college_id,u.major_id,u.class_id,u.job_id,u.student_id,
        u.exp_time_start,u.exp_time_end from s_auth_record a
        left join sys_user u on a.user_id = u.user_id
        left join sys_role r on a.role_id = r.role_id
        <where>
            audit_status in ("0","1")
            <if test="userId != null "> and a.user_id = #{userId}</if>
            <if test="auditStatus != null  and auditStatus != ''"> and audit_status = #{auditStatus}</if>
            <if test="auditUser != null "> and audit_user = #{auditUser}</if>
            <if test="auditTime != null "> and audit_time = #{auditTime}</if>
        </where>
    </select>

    <select id="selectSAuthRecordByAuthId" parameterType="Long" resultMap="SAuthRecordResult">
        <include refid="selectSAuthRecordVo"/>
        where auth_id = #{authId}
    </select>


    <insert id="insertSAuthRecord" parameterType="com.ruoyi.system.domain.SAuthRecord">
        insert into s_auth_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="authId != null">auth_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="roleId != null">role_id,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="auditUser != null">audit_user,</if>
            <if test="auditTime != null">audit_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="authId != null">#{authId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="roleId != null">#{roleId},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="auditUser != null">#{auditUser},</if>
            <if test="auditTime != null">#{auditTime},</if>
        </trim>
    </insert>

    <update id="updateSAuthRecord" parameterType="com.ruoyi.system.domain.SAuthRecord">
        update s_auth_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="auditUser != null">audit_user = #{auditUser},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
        </trim>
        where auth_id = #{authId}
    </update>

    <delete id="deleteSAuthRecordByAuthId" parameterType="Long">
        delete from s_auth_record where auth_id = #{authId}
    </delete>

    <delete id="deleteSAuthRecordByAuthIds" parameterType="String">
        delete from s_auth_record where auth_id in
        <foreach item="authId" collection="array" open="(" separator="," close=")">
            #{authId}
        </foreach>
    </delete>

    <select id="selectSAuthRecordListAll" resultMap="SAuthRecordResult">
        <include refid="selectSAuthRecordVo"/>
        where audit_status = '0'
    </select>
</mapper>