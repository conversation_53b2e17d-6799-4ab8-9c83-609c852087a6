<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.KeySecretTokenPermissionMapper">

    <resultMap type="com.ruoyi.system.domain.KeySecretTokenPermission" id="KeySecretTokenPermissionResult">
        <result property="id"    column="id"    />
        <result property="token"    column="token"    />
        <result property="apiPaths"    column="api_paths"    />
        <result property="limitMethod"    column="limit_method"    />
        <result property="status"    column="status"    />
        <result property="description"    column="description"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectKeySecretTokenPermissionVo">
        select id, token, api_paths, limit_method, status, remark,description, create_by, create_time, update_by, update_time from s_key_secret_token_permission
    </sql>

    <select id="selectKeySecretTokenPermissionList" parameterType="com.ruoyi.system.domain.KeySecretTokenPermission" resultMap="KeySecretTokenPermissionResult">
        <include refid="selectKeySecretTokenPermissionVo"/>
        <where>
            <if test="token != null  and token != ''"> and token = #{token}</if>
            <if test="apiPaths != null  and apiPaths != ''"> and api_paths = #{apiPaths}</if>
            <if test="limitMethod != null  and limitMethod != ''"> and limit_method = #{limitMethod}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
        </where>
    </select>

    <select id="selectKeySecretTokenPermissionById" parameterType="Long" resultMap="KeySecretTokenPermissionResult">
        <include refid="selectKeySecretTokenPermissionVo"/>
        where id = #{id}
    </select>

    <insert id="insertKeySecretTokenPermission" parameterType="com.ruoyi.system.domain.KeySecretTokenPermission" useGeneratedKeys="true" keyProperty="id">
        insert into s_key_secret_token_permission
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="token != null and token != ''">token,</if>
            <if test="apiPaths != null and apiPaths != ''">api_paths,</if>
            <if test="limitMethod != null">limit_method,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="description != null">description,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="token != null and token != ''">#{token},</if>
            <if test="apiPaths != null and apiPaths != ''">#{apiPaths},</if>
            <if test="limitMethod != null">#{limitMethod},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="description != null">#{description},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateKeySecretTokenPermission" parameterType="com.ruoyi.system.domain.KeySecretTokenPermission">
        update s_key_secret_token_permission
        <trim prefix="SET" suffixOverrides=",">
            <if test="token != null and token != ''">token = #{token},</if>
            <if test="apiPaths != null and apiPaths != ''">api_paths = #{apiPaths},</if>
            <if test="limitMethod != null">limit_method = #{limitMethod},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="description != null">description = #{description},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKeySecretTokenPermissionById" parameterType="Long">
        delete from s_key_secret_token_permission where id = #{id}
    </delete>

    <delete id="deleteKeySecretTokenPermissionByIds" parameterType="String">
        delete from s_key_secret_token_permission where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
