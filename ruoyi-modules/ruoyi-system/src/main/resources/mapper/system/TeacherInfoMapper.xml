<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.TeacherInfoMapper">

    <resultMap type="com.ruoyi.system.api.domain.TeacherInfo" id="TeacherInfoResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="teacherName"    column="teacher_name"    />
        <result property="univerId"    column="univer_id"    />
        <result property="colleId"    column="colle_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="univerName"    column="univer_name"    />
        <result property="colleName"    column="colle_name"    />
    </resultMap>

    <sql id="selectTeacherInfoVo">
        select id, teacher_id, teacher_name, univer_id, colle_id, create_by, create_time, update_by, update_time from s_teacher_info
    </sql>

    <select id="selectTeacherInfoList" parameterType="com.ruoyi.system.api.domain.TeacherInfo" resultMap="TeacherInfoResult">
        <include refid="selectTeacherInfoVo"/>
        <where>
            <if test="teacherId != null  and teacherId != ''"> and teacher_id = #{teacherId}</if>
            <if test="teacherName != null  and teacherName != ''"> and teacher_name like concat('%', #{teacherName}, '%')</if>
            <if test="univerId != null "> and univer_id = #{univerId}</if>
            <if test="colleId != null "> and colle_id = #{colleId}</if>
        </where>
    </select>

    <select id="selectTeacherInfoById" parameterType="Long" resultMap="TeacherInfoResult">
        <include refid="selectTeacherInfoVo"/>
        where id = #{id}
    </select>

    <select id="getTeacherInfo" resultType="com.ruoyi.system.api.domain.TeacherInfo">
        <include refid="selectTeacherInfoVo"/>
        where teacher_id = #{teacherId}
        and teacher_name like concat('%', #{teacherName}, '%')
        and colle_id = #{colleId}
    </select>

    <insert id="insertTeacherInfo" parameterType="com.ruoyi.system.api.domain.TeacherInfo">
        insert into s_teacher_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="teacherName != null">teacher_name,</if>
            <if test="univerId != null">univer_id,</if>
            <if test="colleId != null">colle_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="teacherName != null">#{teacherName},</if>
            <if test="univerId != null">#{univerId},</if>
            <if test="colleId != null">#{colleId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateTeacherInfo" parameterType="com.ruoyi.system.api.domain.TeacherInfo">
        update s_teacher_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="teacherName != null">teacher_name = #{teacherName},</if>
            <if test="univerId != null">univer_id = #{univerId},</if>
            <if test="colleId != null">colle_id = #{colleId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTeacherInfoById" parameterType="Long">
        delete from s_teacher_info where id = #{id}
    </delete>

    <delete id="deleteTeacherInfoByIds" parameterType="String">
        delete from s_teacher_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectTeacherInfoListByName" parameterType="com.ruoyi.system.api.domain.TeacherInfo" resultMap="TeacherInfoResult">
        select  t1.teacher_id, t1.teacher_name, t1.univer_id, t1.colle_id,
         t2.univer_name,t3.colle_name
         from s_teacher_info t1 left join s_university t2 on t1.univer_id = t2.id
        left join s_college_info t3 on t1.colle_id = t3.id

        <where>
            <if test="teacherName != null  and teacherName != ''"> and t1.teacher_name like concat('%', #{teacherName}, '%')</if>
            <if test="univerId != null "> and t1.univer_id = #{univerId}</if>
        </where>
    </select>
</mapper>
