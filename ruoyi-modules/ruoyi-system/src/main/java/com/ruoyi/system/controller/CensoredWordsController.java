package com.ruoyi.system.controller;

import java.util.ArrayList;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.CensoredWords;
import com.ruoyi.system.service.ICensoredWordsService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 敏感词管理Controller
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
@RestController
@RequestMapping("/words")
public class CensoredWordsController extends BaseController
{
    @Autowired
    private ICensoredWordsService censoredWordsService;

    /**
     * 查询敏感词管理列表
     */
    @RequiresPermissions("CensoredWords:words:list")
    @GetMapping("/list")
    public TableDataInfo list(CensoredWords censoredWords)
    {
        startPage();
        List<CensoredWords> list = censoredWordsService.selectCensoredWordsList(censoredWords);
        return getDataTable(list);
    }

    /**
     * 导出敏感词管理列表
     */
    @RequiresPermissions("CensoredWords:words:export")
    @Log(title = "敏感词管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CensoredWords censoredWords)
    {
        List<CensoredWords> list = censoredWordsService.selectCensoredWordsList(censoredWords);
        ExcelUtil<CensoredWords> util = new ExcelUtil<CensoredWords>(CensoredWords.class);
        util.exportExcel(response, list, "敏感词管理数据");
    }
    /**
     * 导出敏感词表格模板
     */
    @RequiresPermissions("CensoredWords:words:exportTemplate")
    @Log(title = "敏感词管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export/template")
    public void exportTemplate(HttpServletResponse response, CensoredWords censoredWords)
    {
        List<CensoredWords> list = new ArrayList<CensoredWords>();
        ExcelUtil<CensoredWords> util = new ExcelUtil<CensoredWords>(CensoredWords.class);
        util.exportExcel(response, list, "敏感词管理数据");
    }
    /**
     * 获取敏感词管理详细信息
     */
    @RequiresPermissions("CensoredWords:words:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(censoredWordsService.selectCensoredWordsById(id));
    }

    /**
     * 新增敏感词管理
     */
    @RequiresPermissions("CensoredWords:words:add")
    @Log(title = "敏感词管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CensoredWords censoredWords)
    {
        return toAjax(censoredWordsService.insertCensoredWords(censoredWords));
    }

    /**
     * 修改敏感词管理
     */
    @RequiresPermissions("CensoredWords:words:edit")
    @Log(title = "敏感词管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CensoredWords censoredWords)
    {
        return toAjax(censoredWordsService.updateCensoredWords(censoredWords));
    }

    /**
     * 删除敏感词管理
     */
    @RequiresPermissions("CensoredWords:words:remove")
    @Log(title = "敏感词管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(censoredWordsService.deleteCensoredWordsByIds(ids));
    }
    /**
     * excel敏感词管理
     */
    @RequiresPermissions("CensoredWords:words:remove")
    @Log(title = "敏感词管理", businessType = BusinessType.UPDATE)
    @PostMapping("/byExcel")
    public AjaxResult addByExcel(@RequestParam Long id)
    {
        censoredWordsService.addByExcel(id);
        return AjaxResult.success();
    }
}
