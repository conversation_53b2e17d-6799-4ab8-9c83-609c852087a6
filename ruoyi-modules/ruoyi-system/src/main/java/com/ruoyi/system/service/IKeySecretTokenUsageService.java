package com.ruoyi.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.KeySecretTokenUsage;

import java.util.List;

/**
 * token用量Service接口
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface IKeySecretTokenUsageService extends IService<KeySecretTokenUsage>
{
    /**
     * 查询token用量
     *
     * @param id token用量主键
     * @return token用量
     */
    public KeySecretTokenUsage selectKeySecretTokenUsageById(Long id);

    /**
     * 查询token用量列表
     *
     * @param keySecretTokenUsage token用量
     * @return token用量集合
     */
    public List<KeySecretTokenUsage> selectKeySecretTokenUsageList(KeySecretTokenUsage keySecretTokenUsage);

    /**
     * 新增token用量
     *
     * @param keySecretTokenUsage token用量
     * @return 结果
     */
    public int insertKeySecretTokenUsage(KeySecretTokenUsage keySecretTokenUsage);

    /**
     * 修改token用量
     *
     * @param keySecretTokenUsage token用量
     * @return 结果
     */
    public int updateKeySecretTokenUsage(KeySecretTokenUsage keySecretTokenUsage);

    /**
     * 批量删除token用量
     *
     * @param ids 需要删除的token用量主键集合
     * @return 结果
     */
    public int deleteKeySecretTokenUsageByIds(Long[] ids);

    /**
     * 删除token用量信息
     *
     * @param id token用量主键
     * @return 结果
     */
    public int deleteKeySecretTokenUsageById(Long id);
}
