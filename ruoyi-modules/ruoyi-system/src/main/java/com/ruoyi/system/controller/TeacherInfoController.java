package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.api.domain.TeacherInfo;
import com.ruoyi.system.service.ITeacherInfoService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 教师信息表Controller
 * 
 * <AUTHOR>
 * @date 2024-07-15
 */
@RestController
@RequestMapping("/teacherinfo")
public class TeacherInfoController extends BaseController
{
    @Autowired
    private ITeacherInfoService teacherInfoService;

    /**
     * 查询教师信息表列表
     */
    @RequiresPermissions("create:teacherinfo:list")
    @GetMapping("/list")
    public TableDataInfo list(TeacherInfo teacherInfo)
    {
        startPage();
        List<TeacherInfo> list = teacherInfoService.selectTeacherInfoList(teacherInfo);
        return getDataTable(list);
    }

    /**
     * 导出教师信息表列表
     */
    @RequiresPermissions("create:teacherinfo:export")
    @Log(title = "教师信息表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TeacherInfo teacherInfo)
    {
        List<TeacherInfo> list = teacherInfoService.selectTeacherInfoList(teacherInfo);
        ExcelUtil<TeacherInfo> util = new ExcelUtil<TeacherInfo>(TeacherInfo.class);
        util.exportExcel(response, list, "教师信息表数据");
    }

    /**
     * 获取教师信息表详细信息
     */
    @RequiresPermissions("create:teacherinfo:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(teacherInfoService.selectTeacherInfoById(id));
    }

    /**
     * 新增教师信息表
     */
    @RequiresPermissions("create:teacherinfo:add")
    @Log(title = "教师信息表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TeacherInfo teacherInfo)
    {
        return toAjax(teacherInfoService.insertTeacherInfo(teacherInfo));
    }

    /**
     * 修改教师信息表
     */
    @RequiresPermissions("create:teacherinfo:edit")
    @Log(title = "教师信息表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TeacherInfo teacherInfo)
    {
        return toAjax(teacherInfoService.updateTeacherInfo(teacherInfo));
    }

    /**
     * 删除教师信息表
     */
    @RequiresPermissions("create:teacherinfo:remove")
    @Log(title = "教师信息表", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(teacherInfoService.deleteTeacherInfoByIds(ids));
    }

    /**
     * 获取教师信息表详细信息
     */
    @PostMapping("name")
    public AjaxResult getInfoByName(@RequestBody TeacherInfo teacherInfo)
    {
        return success(teacherInfoService.selectTeacherInfoByName(teacherInfo));
    }
}
