package com.ruoyi.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.KeySecretToken;
import com.ruoyi.system.vo.KeySecretTokenVo;

import java.util.List;
import java.util.Map;

/**
 * key_secret_token管理Service接口
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface IKeySecretTokenService extends IService<KeySecretToken>
{
    /**
     * 查询key_secret_token管理
     *
     * @param id key_secret_token管理主键
     * @return key_secret_token管理
     */
    public KeySecretToken selectKeySecretTokenById(Long id);

    /**
     * 查询key_secret_token管理列表
     *
     * @param keySecretToken key_secret_token管理
     * @return key_secret_token管理集合
     */
    public List<KeySecretToken> selectKeySecretTokenList(KeySecretToken keySecretToken);

    /**
     * 新增key_secret_token管理
     *
     * @param keySecretToken key_secret_token管理
     * @return 结果
     */
    public int insertKeySecretToken(KeySecretToken keySecretToken);

    /**
     * 修改key_secret_token管理
     *
     * @param keySecretToken key_secret_token管理
     * @return 结果
     */
    public int updateKeySecretToken(KeySecretToken keySecretToken);

    /**
     * 批量删除key_secret_token管理
     *
     * @param ids 需要删除的key_secret_token管理主键集合
     * @return 结果
     */
    public int deleteKeySecretTokenByIds(Long[] ids);

    /**
     * 删除key_secret_token管理信息
     *
     * @param id key_secret_token管理主键
     * @return 结果
     */
    public int deleteKeySecretTokenById(Long id);

    /**
     * 查询key_secret_token管理列表 和 token 接口权限
     */
    List<KeySecretTokenVo> selectKeySecretTokenListAndPermissions(KeySecretToken keySecretToken);

    Map<String, String> validateToken(KeySecretToken keySecretToken);
}
