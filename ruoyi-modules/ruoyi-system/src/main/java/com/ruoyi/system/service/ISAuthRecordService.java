package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.SAuthRecord;

/**
 * 认证审核记录Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-03
 */
public interface ISAuthRecordService 
{
    /**
     * 查询认证审核记录
     * 
     * @param authId 认证审核记录主键
     * @return 认证审核记录
     */
    public SAuthRecord selectSAuthRecordByAuthId(Long authId);

    /**
     * 查询认证审核记录列表
     * 
     * @param sAuthRecord 认证审核记录
     * @return 认证审核记录集合
     */
    public List<SAuthRecord> selectSAuthRecordList(SAuthRecord sAuthRecord);

    /**
     * 新增认证审核记录
     * 
     * @param sAuthRecord 认证审核记录
     * @return 结果
     */
    public int insertSAuthRecord(SAuthRecord sAuthRecord);

    /**
     * 修改认证审核记录
     * 
     * @param sAuthRecord 认证审核记录
     * @return 结果
     */
    public int updateSAuthRecord(SAuthRecord sAuthRecord);

    /**
     * 批量删除认证审核记录
     * 
     * @param authIds 需要删除的认证审核记录主键集合
     * @return 结果
     */
    public int deleteSAuthRecordByAuthIds(Long[] authIds);

    /**
     * 删除认证审核记录信息
     * 
     * @param authId 认证审核记录主键
     * @return 结果
     */
    public int deleteSAuthRecordByAuthId(Long authId);

    List<SAuthRecord> selectDataSetAll();
}
