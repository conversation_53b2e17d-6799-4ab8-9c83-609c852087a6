package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.api.domain.StudentInfo;

/**
 * 学生信息表Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-15
 */
public interface IStudentInfoService 
{
    /**
     * 查询学生信息表
     * 
     * @param id 学生信息表主键
     * @return 学生信息表
     */
    public StudentInfo selectStudentInfoById(Long id);

    /**
     * 查询学生信息表列表
     * 
     * @param studentInfo 学生信息表
     * @return 学生信息表集合
     */
    public List<StudentInfo> selectStudentInfoList(StudentInfo studentInfo);

    /**
     * 新增学生信息表
     * 
     * @param studentInfo 学生信息表
     * @return 结果
     */
    public int insertStudentInfo(StudentInfo studentInfo);

    /**
     * 修改学生信息表
     * 
     * @param studentInfo 学生信息表
     * @return 结果
     */
    public int updateStudentInfo(StudentInfo studentInfo);

    /**
     * 批量删除学生信息表
     * 
     * @param ids 需要删除的学生信息表主键集合
     * @return 结果
     */
    public int deleteStudentInfoByIds(Long[] ids);

    /**
     * 删除学生信息表信息
     * 
     * @param id 学生信息表主键
     * @return 结果
     */
    public int deleteStudentInfoById(Long id);

    /**
     * 获取学生信息表信息
     *
     * @param studentInfo 学生信息表
     * @return 结果
     */
    public StudentInfo getStudentInfo(StudentInfo studentInfo);
}
