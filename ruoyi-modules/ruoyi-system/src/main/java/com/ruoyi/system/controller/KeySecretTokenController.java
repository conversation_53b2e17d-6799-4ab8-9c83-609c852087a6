package com.ruoyi.system.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.system.domain.KeySecretToken;
import com.ruoyi.system.service.IKeySecretTokenService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * key_secret_token管理Controller
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/keySecretToken")
public class KeySecretTokenController extends BaseController
{
    @Resource
    private IKeySecretTokenService keySecretTokenService;

    /**
     * 查询key_secret_token管理列表
     */
    @GetMapping("/list")
    public TableDataInfo list(KeySecretToken keySecretToken)
    {
        startPage();
        List<KeySecretToken> list = keySecretTokenService.selectKeySecretTokenList(keySecretToken);
        return getDataTable(list);
    }

    /**
     * 导出key_secret_token管理列表
     */
    @Log(title = "导出key_secret_token管理列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KeySecretToken keySecretToken)
    {
        List<KeySecretToken> list = keySecretTokenService.selectKeySecretTokenList(keySecretToken);
        ExcelUtil<KeySecretToken> util = new ExcelUtil<KeySecretToken>(KeySecretToken.class);
        util.exportExcel(response, list, "key_secret_token管理数据");
    }

    /**
     * 获取key_secret_token管理详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(keySecretTokenService.selectKeySecretTokenById(id));
    }

    /**
     * 新增key_secret_token管理
     */
    @Log(title = "新增key_secret_token管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KeySecretToken keySecretToken)
    {
        return toAjax(keySecretTokenService.insertKeySecretToken(keySecretToken));
    }

    /**
     * 修改key_secret_token管理
     */
    @Log(title = "key_secret_token管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KeySecretToken keySecretToken)
    {
        return toAjax(keySecretTokenService.updateKeySecretToken(keySecretToken));
    }

    /**
     * 删除key_secret_token管理
     */
    @Log(title = "删除key_secret_token管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(keySecretTokenService.deleteKeySecretTokenByIds(ids));
    }

    @Log(title = "validateToken", businessType = BusinessType.DELETE)
    @PostMapping("/validateToken")
    public AjaxResult validateToken(@RequestBody KeySecretToken keySecretToken)
    {
        return success(keySecretTokenService.validateToken(keySecretToken));
    }
}
