package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.api.domain.TeacherInfo;

/**
 * 教师信息表Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-15
 */
public interface ITeacherInfoService 
{
    /**
     * 查询教师信息表
     * 
     * @param id 教师信息表主键
     * @return 教师信息表
     */
    public TeacherInfo selectTeacherInfoById(Long id);

    /**
     * 查询教师信息表列表
     * 
     * @param teacherInfo 教师信息表
     * @return 教师信息表集合
     */
    public List<TeacherInfo> selectTeacherInfoList(TeacherInfo teacherInfo);

    /**
     * 新增教师信息表
     * 
     * @param teacherInfo 教师信息表
     * @return 结果
     */
    public int insertTeacherInfo(TeacherInfo teacherInfo);

    /**
     * 修改教师信息表
     * 
     * @param teacherInfo 教师信息表
     * @return 结果
     */
    public int updateTeacherInfo(TeacherInfo teacherInfo);

    /**
     * 批量删除教师信息表
     * 
     * @param ids 需要删除的教师信息表主键集合
     * @return 结果
     */
    public int deleteTeacherInfoByIds(Long[] ids);

    /**
     * 删除教师信息表信息
     * 
     * @param id 教师信息表主键
     * @return 结果
     */
    public int deleteTeacherInfoById(Long id);

    /**
     * 获取教师信息表信息
     *
     * @param teacherInfo 教师信息表主键
     * @return 结果
     */
    public TeacherInfo getTeacherInfo(TeacherInfo teacherInfo);

    List<TeacherInfo> selectTeacherInfoByName(TeacherInfo teacherInfo);
}
