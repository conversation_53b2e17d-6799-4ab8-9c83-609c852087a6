package com.ruoyi.system.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.system.domain.KeySecretToken;
import com.ruoyi.system.domain.KeySecretTokenPermission;
import com.ruoyi.system.service.IKeySecretTokenPermissionService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Token接口权限配置Controller
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@RestController
@RequestMapping("/tokenPermission")
public class KeySecretTokenPermissionController extends BaseController
{
    @Resource
    private IKeySecretTokenPermissionService keySecretTokenPermissionService;

    /**
     * 查询Token接口权限配置列表
     */
    @GetMapping("/list")
    public TableDataInfo list(KeySecretTokenPermission keySecretTokenPermission)
    {
        startPage();
        List<KeySecretTokenPermission> list = keySecretTokenPermissionService.selectKeySecretTokenPermissionList(keySecretTokenPermission);
        return getDataTable(list);
    }

    /**
     * 导出Token接口权限配置列表
     */
    @Log(title = "Token接口权限配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KeySecretTokenPermission keySecretTokenPermission)
    {
        List<KeySecretTokenPermission> list = keySecretTokenPermissionService.selectKeySecretTokenPermissionList(keySecretTokenPermission);
        ExcelUtil<KeySecretTokenPermission> util = new ExcelUtil<KeySecretTokenPermission>(KeySecretTokenPermission.class);
        util.exportExcel(response, list, "Token接口权限配置数据");
    }

    /**
     * 获取Token接口权限配置详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(keySecretTokenPermissionService.selectKeySecretTokenPermissionById(id));
    }

    /**
     * 新增Token接口权限配置
     */
    @Log(title = "Token接口权限配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KeySecretTokenPermission keySecretTokenPermission)
    {
        return toAjax(keySecretTokenPermissionService.insertKeySecretTokenPermission(keySecretTokenPermission));
    }

    /**
     * 修改Token接口权限配置
     */
    @Log(title = "Token接口权限配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KeySecretTokenPermission keySecretTokenPermission)
    {
        return toAjax(keySecretTokenPermissionService.updateKeySecretTokenPermission(keySecretTokenPermission));
    }

    /**
     * 删除Token接口权限配置
     */
    @Log(title = "Token接口权限配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(keySecretTokenPermissionService.deleteKeySecretTokenPermissionByIds(ids));
    }


    @PostMapping( "/refreshTokenPermissionCache")
    public AjaxResult refreshCache(@RequestBody KeySecretToken keySecretToken) {
        return AjaxResult.success(keySecretTokenPermissionService.refreshCache(keySecretToken));
    }
}
