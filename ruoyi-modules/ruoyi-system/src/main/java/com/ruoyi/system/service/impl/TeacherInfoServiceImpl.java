package com.ruoyi.system.service.impl;

import java.util.List;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.TeacherInfoMapper;
import com.ruoyi.system.api.domain.TeacherInfo;
import com.ruoyi.system.service.ITeacherInfoService;

/**
 * 教师信息表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-15
 */
@Service
public class TeacherInfoServiceImpl implements ITeacherInfoService 
{
    @Autowired
    private TeacherInfoMapper teacherInfoMapper;
    @Autowired
    private RemoteUserService remoteUserService;
    /**
     * 查询教师信息表
     * 
     * @param id 教师信息表主键
     * @return 教师信息表
     */
    @Override
    public TeacherInfo selectTeacherInfoById(Long id)
    {
        return teacherInfoMapper.selectTeacherInfoById(id);
    }

    /**
     * 查询教师信息表列表
     * 
     * @param teacherInfo 教师信息表
     * @return 教师信息表
     */
    @Override
    public List<TeacherInfo> selectTeacherInfoList(TeacherInfo teacherInfo)
    {
        return teacherInfoMapper.selectTeacherInfoList(teacherInfo);
    }

    /**
     * 新增教师信息表
     * 
     * @param teacherInfo 教师信息表
     * @return 结果
     */
    @Override
    public int insertTeacherInfo(TeacherInfo teacherInfo)
    {
        return teacherInfoMapper.insertTeacherInfo(teacherInfo);
    }

    /**
     * 修改教师信息表
     * 
     * @param teacherInfo 教师信息表
     * @return 结果
     */
    @Override
    public int updateTeacherInfo(TeacherInfo teacherInfo)
    {
        return teacherInfoMapper.updateTeacherInfo(teacherInfo);
    }

    /**
     * 批量删除教师信息表
     * 
     * @param ids 需要删除的教师信息表主键
     * @return 结果
     */
    @Override
    public int deleteTeacherInfoByIds(Long[] ids)
    {
        return teacherInfoMapper.deleteTeacherInfoByIds(ids);
    }

    /**
     * 删除教师信息表信息
     * 
     * @param id 教师信息表主键
     * @return 结果
     */
    @Override
    public int deleteTeacherInfoById(Long id)
    {
        return teacherInfoMapper.deleteTeacherInfoById(id);
    }

    @Override
    public TeacherInfo getTeacherInfo(TeacherInfo teacherInfo) {
        return teacherInfoMapper.getTeacherInfo(teacherInfo);
    }

    @Override
    public List<TeacherInfo> selectTeacherInfoByName(TeacherInfo teacherInfo) {
//        if("N".equals(teacherInfo.getFlag())){
            R<LoginUser> userAndRole = remoteUserService.getUserAndRole(SecurityUtils.getUserId(), SecurityConstants.INNER);
            LoginUser data = userAndRole.getData();
            if(data.getSysUser().getUniversityId()!=null){
                teacherInfo.setUniverId(data.getSysUser().getUniversityId());
            }
//        }
        return teacherInfoMapper.selectTeacherInfoListByName(teacherInfo);
    }
}
