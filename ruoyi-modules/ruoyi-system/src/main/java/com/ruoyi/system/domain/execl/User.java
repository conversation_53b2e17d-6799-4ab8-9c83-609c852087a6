package com.ruoyi.system.domain.execl;


import lombok.Builder;

import java.util.Date;

@Builder
public class User {

    private Long userId;         // 用户ID
    private Long deptId;         // 部门ID
    private String userName;     // 用户账号
    private String nickName;     // 用户昵称
    private String userType;     // 用户类型（00系统用户）
    private String email;        // 用户邮箱
    private String phonenumber;  // 手机号码
    private String sex;          // 用户性别（0男 1女 2未知）
    private String avatar;       // 头像地址
    private String password;     // 密码
    private String status;       // 帐号状态（0正常 1停用）
    private String delFlag;      // 删除标志（0代表存在 2代表删除）
    private String loginIp;      // 最后登录IP
    private Date loginDate;      // 最后登录时间
    private String createBy;     // 创建者
    private Date createTime;     // 创建时间
    private String updateBy;     // 更新者
    private Date updateTime;     // 更新时间
    private String remark;       // 备注
    private String identityCard; // 身份证号
    private String authStatus;   // 认证状态 0-未认证 1-认证中 2-认证完成 3-认证失败
    private Long universityId;   // 学校ID
    private Long collegeId;      // 学院ID
    private Long majorId;        // 专业ID
    private Long classId;        // 班级ID
    private String jobId;        // 工号
    private String studentId;    // 学号
    private Date expTimeStart;   // 体验开始时间
    private Date expTimeEnd;     // 体验结束时间

    // Getter 和 Setter 方法
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhonenumber() {
        return phonenumber;
    }

    public void setPhonenumber(String phonenumber) {
        this.phonenumber = phonenumber;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getLoginIp() {
        return loginIp;
    }

    public void setLoginIp(String loginIp) {
        this.loginIp = loginIp;
    }

    public Date getLoginDate() {
        return loginDate;
    }

    public void setLoginDate(Date loginDate) {
        this.loginDate = loginDate;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getIdentityCard() {
        return identityCard;
    }

    public void setIdentityCard(String identityCard) {
        this.identityCard = identityCard;
    }

    public String getAuthStatus() {
        return authStatus;
    }

    public void setAuthStatus(String authStatus) {
        this.authStatus = authStatus;
    }

    public Long getUniversityId() {
        return universityId;
    }

    public void setUniversityId(Long universityId) {
        this.universityId = universityId;
    }

    public Long getCollegeId() {
        return collegeId;
    }

    public void setCollegeId(Long collegeId) {
        this.collegeId = collegeId;
    }

    public Long getMajorId() {
        return majorId;
    }

    public void setMajorId(Long majorId) {
        this.majorId = majorId;
    }

    public Long getClassId() {
        return classId;
    }

    public void setClassId(Long classId) {
        this.classId = classId;
    }

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public String getStudentId() {
        return studentId;
    }

    public void setStudentId(String studentId) {
        this.studentId = studentId;
    }

    public Date getExpTimeStart() {
        return expTimeStart;
    }

    public void setExpTimeStart(Date expTimeStart) {
        this.expTimeStart = expTimeStart;
    }

    public Date getExpTimeEnd() {
        return expTimeEnd;
    }

    public void setExpTimeEnd(Date expTimeEnd) {
        this.expTimeEnd = expTimeEnd;
    }

}
