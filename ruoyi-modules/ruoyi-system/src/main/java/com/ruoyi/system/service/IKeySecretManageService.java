package com.ruoyi.system.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.KeySecretManage;
import com.ruoyi.system.dto.KeySecretManageDto;

/**
 * key_secret管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-15
 */
public interface IKeySecretManageService extends IService<KeySecretManage>
{
    /**
     * 查询key_secret管理
     * 
     * @param id key_secret管理主键
     * @return key_secret管理
     */
    public KeySecretManage selectKeySecretManageById(Long id);

    /**
     * 查询key_secret管理列表
     * 
     * @param keySecretManage key_secret管理
     * @return key_secret管理集合
     */
    public List<KeySecretManage> selectKeySecretManageList(KeySecretManage keySecretManage);

    /**
     * 新增key_secret管理
     * 
     * @param keySecretManage key_secret管理
     * @return 结果
     */
    public int insertKeySecretManage(KeySecretManage keySecretManage);

    /**
     * 修改key_secret管理
     * 
     * @param keySecretManage key_secret管理
     * @return 结果
     */
    public int updateKeySecretManage(KeySecretManage keySecretManage);

    /**
     * 批量删除key_secret管理
     * 
     * @param ids 需要删除的key_secret管理主键集合
     * @return 结果
     */
    public int deleteKeySecretManageByIds(Long[] ids);

    /**
     * 删除key_secret管理信息
     * 
     * @param id key_secret管理主键
     * @return 结果
     */
    public int deleteKeySecretManageById(Long id);

    Map<String, String> checkKeySecret(String apikey);

    String setCacheApiKey();

    Map<String,String> createApiKeySecret();
}
