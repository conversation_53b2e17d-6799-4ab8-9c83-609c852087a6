package com.ruoyi.system.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.system.domain.KeySecretTokenUsage;
import com.ruoyi.system.service.IKeySecretTokenUsageService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * token用量Controller
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@RestController
@RequestMapping("/tokenUsage")
public class KeySecretTokenUsageController extends BaseController
{
    @Resource
    private IKeySecretTokenUsageService keySecretTokenUsageService;

    /**
     * 查询token用量列表
     */
    @GetMapping("/list")
    public TableDataInfo list(KeySecretTokenUsage keySecretTokenUsage)
    {
        startPage();
        List<KeySecretTokenUsage> list = keySecretTokenUsageService.selectKeySecretTokenUsageList(keySecretTokenUsage);
        return getDataTable(list);
    }

    /**
     * 导出token用量列表
     */
    @Log(title = "token用量", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KeySecretTokenUsage keySecretTokenUsage)
    {
        List<KeySecretTokenUsage> list = keySecretTokenUsageService.selectKeySecretTokenUsageList(keySecretTokenUsage);
        ExcelUtil<KeySecretTokenUsage> util = new ExcelUtil<KeySecretTokenUsage>(KeySecretTokenUsage.class);
        util.exportExcel(response, list, "token用量数据");
    }

    /**
     * 获取token用量详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(keySecretTokenUsageService.selectKeySecretTokenUsageById(id));
    }

    /**
     * 新增token用量
     */
    @Log(title = "token用量", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KeySecretTokenUsage keySecretTokenUsage)
    {
        return toAjax(keySecretTokenUsageService.insertKeySecretTokenUsage(keySecretTokenUsage));
    }

    /**
     * 修改token用量
     */
    @Log(title = "token用量", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KeySecretTokenUsage keySecretTokenUsage)
    {
        return toAjax(keySecretTokenUsageService.updateKeySecretTokenUsage(keySecretTokenUsage));
    }

    /**
     * 删除token用量
     */
    @Log(title = "token用量", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(keySecretTokenUsageService.deleteKeySecretTokenUsageByIds(ids));
    }
}
