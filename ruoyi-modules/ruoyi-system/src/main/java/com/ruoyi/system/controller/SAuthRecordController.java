package com.ruoyi.system.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.SAuthRecord;
import com.ruoyi.system.service.ISAuthRecordService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 认证审核记录Controller
 * 
 * <AUTHOR>
 * @date 2024-06-03
 */
@RestController
@RequestMapping("/record")
public class SAuthRecordController extends BaseController
{
    @Autowired
    private ISAuthRecordService sAuthRecordService;

    /**
     * 查询认证审核记录列表
     */
    @RequiresPermissions("system:record:list")
    @GetMapping("/list")
    public TableDataInfo list(SAuthRecord sAuthRecord)
    {
        startPage();
        List<SAuthRecord> list = sAuthRecordService.selectSAuthRecordList(sAuthRecord);
        return getDataTable(list);
    }

    /**
     * 导出认证审核记录列表
     */
    @RequiresPermissions("system:record:export")
    @Log(title = "认证审核记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SAuthRecord sAuthRecord)
    {
        List<SAuthRecord> list = sAuthRecordService.selectSAuthRecordList(sAuthRecord);
        ExcelUtil<SAuthRecord> util = new ExcelUtil<SAuthRecord>(SAuthRecord.class);
        util.exportExcel(response, list, "认证审核记录数据");
    }

    /**
     * 获取认证审核记录详细信息
     */
    @RequiresPermissions("system:record:query")
    @GetMapping(value = "/{authId}")
    public AjaxResult getInfo(@PathVariable("authId") Long authId)
    {
        return success(sAuthRecordService.selectSAuthRecordByAuthId(authId));
    }

    /**
     * 新增认证审核记录
     */
    @RequiresPermissions("system:record:add")
    @Log(title = "认证审核记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SAuthRecord sAuthRecord)
    {
        return toAjax(sAuthRecordService.insertSAuthRecord(sAuthRecord));
    }

    /**
     * 修改认证审核记录
     */
    @RequiresPermissions("system:record:edit")
    @Log(title = "认证审核记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SAuthRecord sAuthRecord)
    {
        return toAjax(sAuthRecordService.updateSAuthRecord(sAuthRecord));
    }

    /**
     * 删除认证审核记录
     */
    @RequiresPermissions("system:record:remove")
    @Log(title = "认证审核记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{authIds}")
    public AjaxResult remove(@PathVariable Long[] authIds)
    {
        return toAjax(sAuthRecordService.deleteSAuthRecordByAuthIds(authIds));
    }
    /**
     * 查询认证审核记录-未审核的用户
     */
    @RequiresPermissions("system:record:all")
    @GetMapping("/all")
    public AjaxResult selectDataSetAll()
    {
        List<SAuthRecord> list = sAuthRecordService.selectDataSetAll();
        return success(list);
    }

}
