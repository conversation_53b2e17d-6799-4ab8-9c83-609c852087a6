package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.UserConstants;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.bean.BeanValidators;
import com.ruoyi.common.core.utils.ip.IpUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.datascope.annotation.DataScope;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.RemoteUniversityService;
import com.ruoyi.system.api.domain.*;
import com.ruoyi.system.api.model.LoginUser;
import com.ruoyi.system.domain.*;
import com.ruoyi.system.domain.execl.Clazz;
import com.ruoyi.system.domain.execl.Coll;
import com.ruoyi.system.domain.execl.Major;
import com.ruoyi.system.domain.execl.Univer;
import com.ruoyi.system.dto.ImportDataDto;
import com.ruoyi.system.mapper.*;
import com.ruoyi.system.service.*;
import com.ruoyi.system.utils.SendResp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.Validator;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 用户 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements ISysUserService {
    private static final Logger log = LoggerFactory.getLogger(SysUserServiceImpl.class);

    @Resource
    private SysUserMapper userMapper;

    @Resource
    private SysRoleMapper roleMapper;

    @Resource
    private SysPostMapper postMapper;

    @Resource
    private SysUserRoleMapper userRoleMapper;

    @Resource
    private SysUserPostMapper userPostMapper;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    protected Validator validator;

    @Resource
    private SAuthRecordMapper sAuthRecordMapper;

    @Autowired
    private RedisService redisService;
    @Autowired
    private RemoteUniversityService remoteUniversityService;

    @Autowired
    private IStudentInfoService studentInfoService;
    @Autowired
    private ITeacherInfoService teacherInfoService;

    @Resource
    private ISysPermissionService permissionService;
    @Resource
    private ExternalCertificationTMapper externalCertificationMapper;

    @Autowired
    private TokenService tokenService;

    @Resource
    private ISysRoleService sysRoleService;
    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectUserList(SysUser user) {
        //登录人为admin时除外
        if(tokenService.getLoginUser().getUsername().equals("admin")){
            return userMapper.selectUserList(user);
        }
        //获取当前登录人学校id
        user.setUniversityId(tokenService.getLoginUser().getSysUser().getUniversityId());
        return userMapper.selectUserList(user);
    }

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectAllocatedList(SysUser user) {
        return userMapper.selectAllocatedList(user);
    }

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectUnallocatedList(SysUser user) {
        return userMapper.selectUnallocatedList(user);
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByUserName(String userName) {
        return userMapper.selectUserByUserName(userName);
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserById(Long userId) {
        SysUser sysUser = userMapper.selectUserById(userId);
        if (Objects.isNull(sysUser)) return new SysUser();
        Long university = sysUser.getUniversityId();
        Long college = sysUser.getCollegeId();
        Long majorId = sysUser.getMajorId();
        Long classId = sysUser.getClassId();
        Long[] affiliatedUnitArray = new Long[4];
        affiliatedUnitArray[0] = university;
        affiliatedUnitArray[1] = college;
        affiliatedUnitArray[2] = majorId;
        affiliatedUnitArray[3] = classId;
        sysUser.setAffiliatedUnit(affiliatedUnitArray);
        return sysUser;
    }

    /**
     * 查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserRoleGroup(String userName) {
        List<SysRole> list = roleMapper.selectRolesByUserName(userName);
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return list.stream().map(SysRole::getRoleName).collect(Collectors.joining(","));
    }

    /**
     * 查询用户所属岗位组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserPostGroup(String userName) {
        List<SysPost> list = postMapper.selectPostsByUserName(userName);
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return list.stream().map(SysPost::getPostName).collect(Collectors.joining(","));
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean checkUserNameUnique(SysUser user) {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkUserNameUnique(user.getUserName());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public boolean checkPhoneUnique(SysUser user) {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkPhoneUnique(user.getPhonenumber());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public boolean checkEmailUnique(SysUser user) {
        Long userId = StringUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkEmailUnique(user.getEmail());
        if (StringUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    @Override
    public void checkUserAllowed(SysUser user) {
        if (StringUtils.isNotNull(user.getUserId()) && user.isAdmin()) {
            throw new ServiceException("不允许操作超级管理员用户");
        }
    }

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    @Override
    public void checkUserDataScope(Long userId) {
        if (!SysUser.isAdmin(SecurityUtils.getUserId())) {
            SysUser user = new SysUser();
            user.setUserId(userId);
            List<SysUser> users = SpringUtils.getAopProxy(this).selectUserList(user);
            if (StringUtils.isEmpty(users)) {
                throw new ServiceException("没有权限访问用户数据！");
            }
        }
    }

    /**
     * 新增保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertUser(SysUser user) {
        // 新增用户信息
        int rows = userMapper.insertUser(user);
        // 新增用户岗位关联
        insertUserPost(user);
        // 新增用户与角色管理
        insertUserRole(user);
        return rows;
    }

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean registerUser(SysUser user) {
        Long[] rids = new Long[]{100l};
        user.setAuthStatus("0");//用户-未认证
        // 新增用户信息
        int rows = userMapper.insertUser(user);
        user.setRoleIds(rids);
        // 新增用户与角色管理
        insertUserRole(user);
        return rows > 0;
    }

    /**
     * 修改保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateUser(SysUser user) {
        Long userId = user.getUserId();
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 新增用户与角色管理
        insertUserRole(user);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPostByUserId(userId);
        // 新增用户与岗位管理
        insertUserPost(user);
        return userMapper.updateUser(user);
    }

    /**
     * 用户授权角色
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertUserAuth(Long userId, Long[] roleIds) {
        userRoleMapper.deleteUserRoleByUserId(userId);
        insertUserRole(userId, roleIds);
    }

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserStatus(SysUser user) {
        return userMapper.updateUser(user);
    }

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserProfile(SysUser user) {
        return userMapper.updateUser(user);
    }

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(String userName, String avatar) {
        return userMapper.updateUserAvatar(userName, avatar) > 0;
    }

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int resetPwd(SysUser user) {
        return userMapper.updateUser(user);
    }

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(String userName, String password) {
        return userMapper.resetUserPwd(userName, password);
    }

    /**
     * 新增用户角色信息
     *
     * @param user 用户对象
     */
    public void insertUserRole(SysUser user) {
        this.insertUserRole(user.getUserId(), user.getRoleIds());
    }

    /**
     * 新增用户岗位信息
     *
     * @param user 用户对象
     */
    public void insertUserPost(SysUser user) {
        Long[] posts = user.getPostIds();
        if (StringUtils.isNotEmpty(posts)) {
            // 新增用户与岗位管理
            List<SysUserPost> list = new ArrayList<SysUserPost>();
            for (Long postId : posts) {
                SysUserPost up = new SysUserPost();
                up.setUserId(user.getUserId());
                up.setPostId(postId);
                list.add(up);
            }
            userPostMapper.batchUserPost(list);
        }
    }

    /**
     * 新增用户角色信息
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    public void insertUserRole(Long userId, Long[] roleIds) {
        if (StringUtils.isNotEmpty(roleIds)) {
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<SysUserRole>();
            for (Long roleId : roleIds) {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                list.add(ur);
            }
            userRoleMapper.batchUserRole(list);
        }
    }

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserById(Long userId) {
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 删除用户与岗位表
        userPostMapper.deleteUserPostByUserId(userId);
        return userMapper.deleteUserById2(userId);
    }

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserByIds(Long[] userIds) {
        for (Long userId : userIds) {
            checkUserAllowed(new SysUser(userId));
            checkUserDataScope(userId);
        }
        // 删除用户与角色关联
        userRoleMapper.deleteUserRole(userIds);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPost(userIds);
        return userMapper.deleteUserByIds2(userIds);
    }

    /**
     * 导入用户数据
     *
     * @param userList        用户数据列表
     * @param importDataDto   额外参数
     * @param operName        操作用户
     * @return 结果
     */
    @Override
    public String importUser(List<SysUser> userList, ImportDataDto importDataDto, String operName) {
        if (StringUtils.isNull(userList) || userList.isEmpty()) {
            throw new ServiceException("导入用户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        String password = configService.selectConfigByKey("sys.user.initPassword");

        List<Long> roleIdList = Optional.ofNullable(importDataDto.getRoleIds())
                .map(ids -> Arrays.stream(ids.split(","))
                        .filter(StringUtils::isNotBlank)
                        .map(String::trim)
                        .map(Long::valueOf)
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());

        List<Long> userIdList = new ArrayList<>();
        for (SysUser user : userList) {
            try {
                // 验证是否存在这个用户
                SysUser u = userMapper.selectUserByUserName(user.getUserName());
                if (StringUtils.isNull(u)) {
                    BeanValidators.validateWithException(validator, user);
                    user.setPassword(SecurityUtils.encryptPassword(password));
                    user.setCreateBy(operName);
                    userMapper.insertUser(user);
                    userIdList.add(user.getUserId());
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、账号 ").append(user.getUserName()).append(" 导入成功");
                } else if (importDataDto.getUpdateSupport()) {
                    BeanValidators.validateWithException(validator, user);
                    checkUserAllowed(u);
                    checkUserDataScope(u.getUserId());
                    user.setUserId(u.getUserId());
                    user.setUpdateBy(operName);
                    userMapper.updateUser(user);
                    userIdList.add(user.getUserId());
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账号 " + user.getUserName() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账号 " + user.getUserName() + " 导入失败：";
                failureMsg.append(msg).append(e.getMessage());
                log.error(msg, e);
            }
        }
        sysRoleService.insertRolesByUserIdList(userIdList, roleIdList);
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }


    @Override
    @Transactional
    public int updateUserAuth(SysUser user) throws Exception {

        String phonenumber = user.getPhonenumber();

        //查看之前验证码是否过期
        if (!redisService.hasKey("SFRZ-" + phonenumber)) {
            throw new Exception("验证码失效或未发送");
        }
        String cacheObject = redisService.getCacheObject("SFRZ-" + phonenumber).toString();
        if (!user.getCode().equals(cacheObject)) {
            throw new Exception("验证码错误");
        }
        Long userId = user.getUserId();
        Long roleId = user.getRoleId();
        //获取所属单位
//        Long[] affiliatedUnit = user.getAffiliatedUnit();
//        user.setUniversityId(affiliatedUnit != null && affiliatedUnit.length > 0 ? affiliatedUnit[0] : null); //
//        user.setCollegeId(affiliatedUnit != null && affiliatedUnit.length > 1 ? affiliatedUnit[1] : null); //
//        user.setMajorId(affiliatedUnit != null && affiliatedUnit.length > 2 ? affiliatedUnit[2] : null); //
//        user.setClassId(affiliatedUnit != null && affiliatedUnit.length > 3 ? affiliatedUnit[3] : null);

        ExternalCertification build = ExternalCertification.builder()
                .univerName(user.getUniverName())
                .colleName(user.getCollegeName())
                .majorName(user.getMajorName()).build();
        Long deptId = insertOrUpdateDeptByUC(user.getUniverName(), user.getCollegeName());
        user.setDeptId(deptId);
        getUniver(build);
        getColl(build);

        user.setUniversityId(build.getUniverId()); //
        user.setCollegeId(build.getColleId()); //
        user.setMajorId(build.getMajorId()); //

        //教师、学生校验
        if(roleId == 101 || roleId == 104 || roleId == 102){
            if (roleId == 101 || roleId == 104) { //学生（本科生\研究生）
                StudentInfo studentInfo = new StudentInfo();
                studentInfo.setStudentId(user.getStudentId());
                studentInfo.setStudentName(user.getNickName());
                studentInfo.setColleId(user.getCollegeId());
                getMaj(build);
                user.setMajorId(build.getMajorId()); //
                studentInfo.setMajorId(user.getMajorId());
                //studentInfo.setClassId(user.getClassId());
                StudentInfo studentInfoTmp = studentInfoService.getStudentInfo(studentInfo);
//                if(studentInfoTmp==null){
//                    return 0;
//                }
            } else if (roleId == 102) { //教师
                TeacherInfo teacherInfo = new TeacherInfo();
                teacherInfo.setTeacherId(user.getJobId());
                teacherInfo.setTeacherName(user.getNickName());
                teacherInfo.setColleId(user.getCollegeId());
                TeacherInfo teacherInfoTmp = teacherInfoService.getTeacherInfo(teacherInfo);
//                if(teacherInfoTmp==null){
//                    return 0;
//                }
            }
            // 删除用户与角色关联
            userRoleMapper.deleteUserRoleByUserId(userId);
            // 新增用户与角色管理
            //insertUserRole(user);
            SysUserRole sysUserRole = new SysUserRole();
            sysUserRole.setUserId(user.getUserId());
            sysUserRole.setRoleId(user.getRoleId());
            userRoleMapper.insertUserRole(sysUserRole);
            user.setAuthStatus("2");
        }else{ //校友、体验用户需要认证审核
            //认证审核记录表新增记录
            SAuthRecord sAuthRecord = new SAuthRecord();
            sAuthRecord.setUserId(userId);
            sAuthRecord.setCreateTime(DateUtils.getNowDate());
            sAuthRecord.setAuditStatus("0");
            sAuthRecord.setRoleId(user.getRoleId());
            sAuthRecord.setRoleName(user.getRoleName());
            sAuthRecordMapper.insertSAuthRecord(sAuthRecord);
            user.setAuthStatus("1"); //认证中
        }
        return userMapper.updateUser(user);
    }

    @Override
    public SysUser selectUserAuthById(long userId) {
        SysUser sysUser = userMapper.selectUserAuthById(userId);
        Long universityId = sysUser.getUniversityId();
        Long collegeId = sysUser.getCollegeId();
        Long majorId = sysUser.getMajorId();
        Long classId = sysUser.getClassId();
        University  university = new University();
        university.setId(universityId);
        university.setCollegeId(collegeId);
        university.setMajorId(majorId);
        university.setClassId(classId);
        AjaxResult info = remoteUniversityService.getInfo(university, SecurityConstants.INNER);
        LinkedHashMap  data = (LinkedHashMap) info.get("data");
        if(data!=null){
            String univerName = (String) data.get("univerName");
            String colleName = (String) data.get("colleName");
            String majorName = (String) data.get("majorName");
            String className = (String) data.get("className");
            sysUser.setUniverName(univerName);
            sysUser.setCollegeName(colleName);
            sysUser.setMajorName(majorName);
            List<String> tempAffiliatedUnitsName = Arrays.asList(univerName, colleName, majorName, className).stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            String[] affiliatedUnitNameArray = tempAffiliatedUnitsName.toArray(new String[tempAffiliatedUnitsName.size()]);
            sysUser.setAffiliatedUnitName(affiliatedUnitNameArray);
        }
        List<Long> tempAffiliatedUnits = Arrays.asList(universityId, collegeId, majorId, classId).stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        Long[] affiliatedUnitArray = tempAffiliatedUnits.toArray(new Long[tempAffiliatedUnits.size()]);
        sysUser.setAffiliatedUnit(affiliatedUnitArray);

        return sysUser;
    }

    @Override
    public List<SysUser> selectUserByClassIds(Long[] classIds) {
        List<Long> classIdList = Stream.of(classIds).collect(Collectors.toList());
        List<SysUser> userList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(classIdList)) {
            userList = this.list(new LambdaQueryWrapper<SysUser>()
                    .in(SysUser::getClassId, classIdList));
        }

        return userList;
    }

    /**
     * 修改密码-发送短信验证码
     *
     * @param username
     */
    @Override
    public String sendSMS(String username) throws Exception {
        //查看手机号是否完成注册
        SysUser sysUser = userMapper.selectUserByUserName(username);
        if (StringUtils.isNull(sysUser)) {
            throw new Exception("该账号还未完成注册");
        }
        String phonenumber = sysUser.getPhonenumber();
        if(phonenumber==null||"".equals(phonenumber)){
            throw new Exception("没有获取到该账号手机号信息,请联系管理员");
        }
        //查看之前验证码是否过期
        if (redisService.hasKey("XGMM-" + phonenumber)) {
            throw new Exception("之前验证码还未失效");
        }

        //获取客户端ip
        String ipAddr = IpUtils.getIpAddr();
        if (redisService.hasKey("XGMMCS-" + ipAddr)) {
            int ipCount = redisService.getCacheObject("XGMMCS-" + ipAddr);
            if (ipCount >= 3) {
                throw new Exception("该机器24小时发送修改密码验证码已达上限");
            }
        }
        //该手机号24小时发送修改密码短信验证码次数
        if (redisService.hasKey("XGMMCS-" + phonenumber)) {
            int ipCount = redisService.getCacheObject("XGMMCS-" + phonenumber);
            if (ipCount >= 3) {
                throw new Exception("该手机号24小时发送修改密码验证码已达上限");
            }
        }
        //随机生成六位数字验证码
        int verificationCode = verificationCode();
        //发送短信
        SendResp.send(phonenumber, "AI才大模型", "SMS_469060263", String.valueOf(verificationCode));

        //将验证码存储到redis   设置有效时间十分钟
        redisService.setCacheObject("XGMM-" + phonenumber, verificationCode, 10L, TimeUnit.MINUTES);


        //该ip24小时发送修改密码短信验证码次数加一
        if (redisService.hasKey("XGMMCS-" + ipAddr)) {
            int ipCount = redisService.getCacheObject("XGMMCS-" + ipAddr);
            redisService.setCacheObject("XGMMCS-" + ipAddr, ipCount + 1, 24L, TimeUnit.HOURS);
        } else {
            redisService.setCacheObject("XGMMCS-" + ipAddr, 1, 24L, TimeUnit.HOURS);
        }
        //该手机号24小时发送修改密码短信验证码次数加一
        if (redisService.hasKey("XGMMCS-" + phonenumber)) {
            int ipCount = redisService.getCacheObject("XGMMCS-" + phonenumber);
            redisService.setCacheObject("XGMMCS-" + phonenumber, ipCount + 1, 24L, TimeUnit.HOURS);
        } else {
            redisService.setCacheObject("XGMMCS-" + phonenumber, 1, 24L, TimeUnit.HOURS);
        }
        return "1";
    }

	@Override
	public String sendSMS2(String phonenumber) throws Exception {
        if (StringUtils.isBlank(phonenumber)) {
            throw new Exception("当前用户手机号未完善");
        }
		//查看手机号是否完成注册
		SysUser sysUser = userMapper.selectUserByPhoneNumber(phonenumber);
		if (StringUtils.isNull(sysUser)) {
			throw new Exception("该手机号 " + phonenumber + "还未完成注册");
		}

		//查看之前验证码是否过期
		if (redisService.hasKey("XGMM-" + phonenumber)) {
            log.info("之前验证码还未失效,使用之前的验证码");
            String verificationCode = redisService.getCacheObject("XGMM-" + phonenumber);
            //发送短信
            SendResp.send(phonenumber, "AI才大模型", "SMS_469060263", verificationCode);
        }

		//获取客户端ip
		String ipAddr = IpUtils.getIpAddr();
		if (redisService.hasKey("XGMMCS-" + ipAddr)) {
			int ipCount = redisService.getCacheObject("XGMMCS-" + ipAddr);
			if (ipCount >= 3) {
				throw new Exception("该机器24小时发送修改密码验证码已达上限");
			}
		}
		//该手机号24小时发送修改密码短信验证码次数
		if (redisService.hasKey("XGMMCS-" + phonenumber)) {
			int ipCount = redisService.getCacheObject("XGMMCS-" + phonenumber);
			if (ipCount >= 3) {
				throw new Exception("该手机号24小时发送修改密码验证码已达上限");
			}
		}
		//随机生成六位数字验证码
        String verificationCode = String.valueOf(verificationCode());
		//发送短信
		SendResp.send(phonenumber, "AI才大模型", "SMS_469060263", String.valueOf(verificationCode));

		//将验证码存储到redis   设置有效时间十分钟
		redisService.setCacheObject("XGMM-" + phonenumber, verificationCode, 10L, TimeUnit.MINUTES);


		//该ip24小时发送修改密码短信验证码次数加一
		if (redisService.hasKey("XGMMCS-" + ipAddr)) {
			int ipCount = redisService.getCacheObject("XGMMCS-" + ipAddr);
			redisService.setCacheObject("XGMMCS-" + ipAddr, ipCount + 1, 24L, TimeUnit.HOURS);
		} else {
			redisService.setCacheObject("XGMMCS-" + ipAddr, 1, 24L, TimeUnit.HOURS);
		}
		//该手机号24小时发送修改密码短信验证码次数加一
		if (redisService.hasKey("XGMMCS-" + phonenumber)) {
			int ipCount = redisService.getCacheObject("XGMMCS-" + phonenumber);
			redisService.setCacheObject("XGMMCS-" + phonenumber, ipCount + 1, 24L, TimeUnit.HOURS);
		} else {
			redisService.setCacheObject("XGMMCS-" + phonenumber, 1, 24L, TimeUnit.HOURS);
		}
		return "1";
	}

	@Override
    public int revise(SysUser user) throws Exception {
        //查看手机号是否完成注册
        SysUser sysUser = userMapper.selectUserByUserName(user.getUserName());
        if (StringUtils.isNull(sysUser)) {
            throw new Exception("该账号还未完成注册");
        }
        String phonenumber = sysUser.getPhonenumber();
        if(phonenumber==null||"".equals(phonenumber)){
            throw new Exception("没有获取到该账号手机号信息,请联系管理员");
        }
        //查看之前验证码是否过期
        if (!redisService.hasKey("XGMM-" + phonenumber)) {
            throw new Exception("验证码失效或未发送");
        }
        String cacheObject = redisService.getCacheObject("XGMM-" + phonenumber).toString();
        if (!user.getCode().equals(cacheObject)) {
            throw new Exception("验证码错误");
        }
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        return userMapper.resetUserPwd(user.getUserName(), user.getPassword());
    }

	@Override
	public int revise2(SysUser user) throws Exception {
		//查看手机号是否完成注册
		SysUser sysUser = userMapper.selectUserByPhoneNumber(user.getPhonenumber());
		if (StringUtils.isNull(sysUser)) {
			throw new Exception("该账号还未完成注册");
		}
		String phonenumber = sysUser.getPhonenumber();
		if(phonenumber==null||"".equals(phonenumber)){
			throw new Exception("没有获取到该账号手机号信息,请联系管理员");
		}
		//查看之前验证码是否过期
		if (!redisService.hasKey("XGMM-" + phonenumber)) {
			throw new Exception("验证码失效或未发送");
		}
		String cacheObject = redisService.getCacheObject("XGMM-" + phonenumber).toString();
		if (!user.getCode().equals(cacheObject)) {
			throw new Exception("验证码错误");
		}
		user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
		return userMapper.resetUserPwd(user.getUserName(), user.getPassword());
	}
    @Override
    public String sendSre(SysUser user) throws Exception {
        //查看发送手机号是否完成注册
        String phonenumber = user.getPhonenumber();
        if(phonenumber==null||"".equals(phonenumber)){
            throw new Exception("请先填写手机号");
        }
        //查看之前验证码是否过期
       /* if (redisService.hasKey("SFRZ-" + username)) {
            throw new Exception("之前验证码还未失效");
        }*/

        //获取客户端ip
        String ipAddr = IpUtils.getIpAddr();
        /*if (redisService.hasKey("SFRZCS-" + ipAddr)) {
            int ipCount = redisService.getCacheObject("SFRZCS-" + ipAddr);
            if (ipCount >= 3) {
                throw new Exception("该机器24小时发送修改密码验证码已达上限");
            }
        }*/
        //该手机号24小时发送修改密码短信验证码次数
        /*if (redisService.hasKey("SFRZCS-" + username)) {
            int ipCount = redisService.getCacheObject("SFRZCS-" + username);
            if (ipCount >= 3) {
                throw new Exception("该手机号24小时发送修改密码验证码已达上限");
            }
        }*/
        //随机生成六位数字验证码
        int verificationCode = verificationCode();
        //发送短信
        SendResp.send(phonenumber, "AI才大模型", "SMS_468870300", String.valueOf(verificationCode));

        //将验证码存储到redis   设置有效时间十分钟
        redisService.setCacheObject("SFRZ-" + phonenumber, verificationCode, 10L, TimeUnit.MINUTES);


        //该ip24小时发送修改密码短信验证码次数加一
        if (redisService.hasKey("SFRZCS-" + ipAddr)) {
            int ipCount = redisService.getCacheObject("SFRZCS-" + ipAddr);
            redisService.setCacheObject("SFRZCS-" + ipAddr, ipCount + 1, 24L, TimeUnit.HOURS);
        } else {
            redisService.setCacheObject("SFRZCS-" + ipAddr, 1, 24L, TimeUnit.HOURS);
        }
        //该手机号24小时发送修改密码短信验证码次数加一
        if (redisService.hasKey("SFRZCS-" + phonenumber)) {
            int ipCount = redisService.getCacheObject("SFRZCS-" + phonenumber);
            redisService.setCacheObject("SFRZCS-" + phonenumber, ipCount + 1, 24L, TimeUnit.HOURS);
        } else {
            redisService.setCacheObject("SFRZCS-" + phonenumber, 1, 24L, TimeUnit.HOURS);
        }
        return "1";
    }

    @Override
    public String sendDl(String username) throws Exception {
        //查看手机号是否完成注册
        SysUser sysUser = userMapper.selectUserByUserName(username);
        if (StringUtils.isNull(sysUser)) {
            throw new Exception("该手机号还未完成注册");
        }
        //查看之前验证码是否过期
        if (redisService.hasKey("DL-" + username)) {
            throw new Exception("之前验证码还未失效");
        }

        //随机生成六位数字验证码
        int verificationCode = verificationCode();
        //发送短信
        SendResp.send(username, "AI才大模型", "SMS_300430521", String.valueOf(verificationCode));

        //将验证码存储到redis   设置有效时间十分钟
        redisService.setCacheObject("DL-" + username, verificationCode, 10L, TimeUnit.MINUTES);

        return "1";
    }

	@Override
	public String sendCodeByPhone(String phoneNumber) throws Exception {
		//查看手机号是否完成注册
		SysUser sysUser = userMapper.selectUserByPhoneNumber(phoneNumber);
		if (StringUtils.isNull(sysUser)) {
			throw new Exception("该手机号还未完成注册");
		}
		//查看之前验证码是否过期
		if (redisService.hasKey("DL-" + phoneNumber)) {
			throw new Exception("之前验证码还未失效");
		}

		//随机生成六位数字验证码
		int verificationCode = verificationCode();
		//发送短信
		SendResp.send(phoneNumber, "AI才大模型", "SMS_300430521", String.valueOf(verificationCode));

		//将验证码存储到redis   设置有效时间5分钟
		redisService.setCacheObject("DL-" + phoneNumber, verificationCode, 5L, TimeUnit.MINUTES);

		return "1";
	}

	@Override
	public LoginUser getUserInfoByPhoneNumber(String phoneNumber) {
		SysUser sysUser = userMapper.selectUserByPhoneNumber(phoneNumber);

		if (StringUtils.isNull(sysUser)) {
			throw new RuntimeException("手机号为：" + phoneNumber + "的用户不存在");
		}

		// 角色集合
		Set<String> roles = permissionService.getRolePermission(sysUser);
		// 权限集合
		Set<String> permissions = permissionService.getMenuPermission(sysUser);
		LoginUser loginUser = new LoginUser();
		loginUser.setSysUser(sysUser);
		loginUser.setRoles(roles);
		loginUser.setPermissions(permissions);
		return loginUser;
	}

	@Override
    public int resetUserRole(Long userId) {
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 新增用户与角色管理
        //insertUserRole(user);
        SysUserRole sysUserRole = new SysUserRole();
        sysUserRole.setUserId(userId);
        sysUserRole.setRoleId(100L);
        return  userRoleMapper.insertUserRole(sysUserRole);
    }

    public int verificationCode() {
        return ThreadLocalRandom.current().nextInt(100000, 1000000);
    }


    /**
     * @description: 根据身份代码userCode（学生学号student_id或者教师工号job_id或者用户名）获取用户信息
     * @author: zhaoTianQi
     * @date: 2024/11/25 11:30
     * @param: identityCode
     * @return: LoginUser
     */
    @Override
    public LoginUser selectUserByUserCode(String userCode) {
        SysUser sysUser = userMapper.selectUserByUserCode(userCode);

        if (StringUtils.isNull(sysUser)) {
            throw new RuntimeException("身份（学号或工号）为：" + userCode + "不存在");
        }

        // 角色集合
        Set<String> roles = permissionService.getRolePermission(sysUser);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(sysUser);
        LoginUser loginUser = new LoginUser();
        loginUser.setSysUser(sysUser);
        loginUser.setRoles(roles);
        loginUser.setPermissions(permissions);
        return loginUser;
    }

	/**
	 * @description: 根据map参数获取用户信息
	 * @author: zhaoTianQi
	 * @date: 2024/11/25 11:30
	 * @param: userDetail
	 * @return: LoginUser
	 */
	@Override
	public LoginUser getUserByMapParams(Map<String, Object> mapParams) {

		SysUser sysUser = userMapper.selectUserByMapParams(mapParams);

		if (StringUtils.isNull(sysUser)) {
			throw new RuntimeException("手机号为：" + mapParams.get("mobile") + "的用户不存在");
		}

		// 角色集合
		Set<String> roles = permissionService.getRolePermission(sysUser);
		// 权限集合
		Set<String> permissions = permissionService.getMenuPermission(sysUser);
		LoginUser loginUser = new LoginUser();
		loginUser.setSysUser(sysUser);
		loginUser.setRoles(roles);
		loginUser.setPermissions(permissions);
		return loginUser;
	}

	public  void getUniver(ExternalCertification externalCertification){

        List<Long> univers = externalCertificationMapper.selectUniverIdByName(externalCertification.getUniverName());
        if (univers.size()==0 || univers==null){
            Univer build = new Univer();
            build.setUniverName(externalCertification.getUniverName());
            build.setCreateBy(SecurityUtils.getUsername());
            build.setCreateTime(new Date());
            externalCertificationMapper.insertUniver(build);
            externalCertification.setUniverId(build.getId());
        }else {
            externalCertification.setUniverId(univers.get(0));
        }
    }
    public  void getColl(ExternalCertification externalCertification){

        List<Long> collIds = externalCertificationMapper.selectCollIdByName(externalCertification);
        if (collIds.size()==0 || collIds==null){
            Coll build = new Coll();
            build.setColleName(externalCertification.getColleName());
            build.setUniverId(externalCertification.getUniverId());
            build.setCreateBy(SecurityUtils.getUsername());
            build.setCreateTime(new Date());
            externalCertificationMapper.insertColl(build);
            externalCertification.setColleId(build.getId());
        }else {
            externalCertification.setColleId(collIds.get(0));
        }
    }

    public  void getMaj(ExternalCertification externalCertification){

        List<Long> majIds = externalCertificationMapper.selectMajIdByName(externalCertification);
        if (majIds.size()==0 || majIds==null){
            Major build = Major.builder()
                    .majorName(externalCertification.getMajorName())
                    .univerId(externalCertification.getUniverId())
                    .colleId(externalCertification.getColleId())
                    .createBy(SecurityUtils.getUsername())
                    .createTime(new Date())
                    .build();
            externalCertificationMapper.insertMajor(build);
            externalCertification.setMajorId(build.getId());
        }else {
            externalCertification.setMajorId(majIds.get(0));
        }
    }

    public  void getCla(ExternalCertification externalCertification){

        List<Long> claIds = externalCertificationMapper.selectClaIdByName(externalCertification);
        if (claIds.size()==0 || claIds==null){
            Clazz build = Clazz.builder()
                    .className(externalCertification.getClassName())
                    .univerId(externalCertification.getUniverId())
                    .colleId(externalCertification.getColleId())
                    .majorId(externalCertification.getMajorId())
                    .createBy(SecurityUtils.getUsername())
                    .createTime(new Date())
                    .build();
            externalCertificationMapper.insertCla(build);
            externalCertification.setClassId(build.getId());
        }else {
            externalCertification.setClassId(claIds.get(0));
        }
    }

    public Long insertOrUpdateDeptByUC(String univerName,String colleName){
        SysDeptP deptP = externalCertificationMapper.selectDeptInit(SysDeptP.builder()
                .ancestors(String.valueOf(0))
                .parentId(0L).build());
        SysDeptP sysDeptU = externalCertificationMapper.selectDeptByNameAndPId(SysDeptP.builder()
                .deptName(univerName)
                .parentId(deptP.getDeptId()).build());
        SysDeptP buildU = SysDeptP.builder()
                .deptName(univerName).parentId(deptP.getDeptId())
                .ancestors(deptP.getParentId() + "," + deptP.getDeptId())
                .orderNum(0).status("0").delFlag("0")
                .createBy(SecurityUtils.getUsername()).createTime(new Date())
                .build();
        if (sysDeptU == null){
            int i = externalCertificationMapper.insertOrUpdateDept(buildU);
            if(i <= 0){
                throw new RuntimeException("插入失败");
            }

            SysDeptP sysDeptC = externalCertificationMapper.selectDeptByNameAndPId(SysDeptP.builder()
                    .deptName(colleName)
                    .parentId(buildU.getDeptId()).build());
            SysDeptP buildC = SysDeptP.builder()
                    .deptName(colleName).parentId(buildU.getDeptId())
                    .ancestors(deptP.getParentId() + "," + deptP.getDeptId()+","+buildU.getDeptId())
                    .orderNum(0).status("0").delFlag("0")
                    .createBy(SecurityUtils.getUsername()).createTime(new Date())
                    .build();
            if (sysDeptC == null){
                int j = externalCertificationMapper.insertOrUpdateDept(buildC);
                if(j <= 0){
                    throw new RuntimeException("插入失败");
                }
                return buildC.getDeptId();
            }
            return sysDeptC.getDeptId();


        }else {
            SysDeptP sysDeptC = externalCertificationMapper.selectDeptByNameAndPId(SysDeptP.builder()
                    .deptName(colleName)
                    .parentId(sysDeptU.getDeptId())
                    .build());
            SysDeptP buildC = SysDeptP.builder()
                    .deptName(colleName).parentId(sysDeptU.getDeptId()).ancestors(deptP.getParentId() + "," + deptP.getDeptId()+","+sysDeptU.getDeptId())
                    .orderNum(0).status("0").delFlag("0")
                    .createBy(SecurityUtils.getUsername()).createTime(new Date())
                    .build();
            if (sysDeptC == null){
                int m = externalCertificationMapper.insertOrUpdateDept(buildC);
                if(m <= 0){
                    throw new RuntimeException("插入失败");
                }
                return buildC.getDeptId();
            }

            return sysDeptC.getDeptId();

        }

    }
}
