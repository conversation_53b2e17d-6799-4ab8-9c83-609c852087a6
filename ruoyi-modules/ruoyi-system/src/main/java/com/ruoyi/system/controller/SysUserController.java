package com.ruoyi.system.controller;

import java.io.IOException;
import java.security.KeyFactory;
import java.security.interfaces.RSAPrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.crypto.Cipher;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.system.api.domain.SysRole;
import com.ruoyi.system.dto.ImportTemplateDto;
import com.ruoyi.system.dto.ImportDataDto;
import com.ruoyi.system.vo.SysUserPartial1Vo;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.tomcat.util.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.SysDept;
import com.ruoyi.system.api.domain.SysRole;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.model.LoginUser;
import com.ruoyi.system.service.*;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.tomcat.util.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.crypto.Cipher;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.security.KeyFactory;
import java.security.interfaces.RSAPrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user")
public class SysUserController extends BaseController {
    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private ISysPostService postService;

    @Autowired
    private ISysPermissionService permissionService;

    @Autowired
    private ISysConfigService configService;

    private String privateKey = "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBALL2yjem0ENoZaz2AFPSMNATos67ehwIDqJCuBmAmIOd9p+iKYdsWAbDUJrKezu7hdJBXeJzT3beLhCtuzt0EqL7DFl8i5RgQygO9hceTyt87errhPOpojVDTPs9hDBzSTY33ouI55jPH2I+8EblKaynLs08eXmjTGXsDqM5XYyZAgMBAAECgYA2oYudbXjJ+wZ+xCHZdKKeAkCC50whXnxJICDe+BiWpRPyKyiORI6ikeD7P7BazaXOR1IHnLe3S5+4S7CKN6awQUpE+f4ayYMfRoSbH8d8ZeioWTxmP24tgYgaF9Z3wEnesc3auZIyOnqH9eo0fsvjgKiq1ptWUP5BL0oLjo7gYQJBAP8szJbAOBTCgDXcUcD1Y3eJ4UDsoWBZ0xzQD2NinseKvwoaB2KBd4OSUPkPhhlIoL9OBM9T4agPI4hFmtKEgfUCQQCziunE0VBFfVGOKvk8QIT6F08F340tJr48OIsdJnZhOVjoQHqI3c1lFRxSuSGFxtyaqfHruWcyveJScYwkr6WVAkBEuNn4l5gC70b8OnPCFdRN81I43AGyIz7Z+abLS1obv2An5k6q1tdLFfK8wNOKp6azHt3owFx7mGgnYSeLHqipAkAidgBGobJZlCMqOX9bHDsp0X1+cBkl2HDdGDFDaBWCtcIl2fJrAL+irjmgex4/EhtXqFTh3NU8/QtKrbard/c9AkBSHXD0A2aPqPN2w9JYTIsXPV3ZLGg6/oOyVJ40DMu4Y2Ndw5tr42lM1i/DHFiR78ZqTRK/kMN5bb1Sn3kze4Cu";
    @Autowired
    private TokenService tokenService;


    /**
     * 获取用户列表
     */
    @RequiresPermissions("system:user:list")
    @GetMapping("/list")
    public TableDataInfo list(SysUser user) {
        startPage();
        List<SysUser> list = userService.selectUserList(user);
        return getDataTable(list);
    }

    @Log(title = "用户管理", businessType = BusinessType.EXPORT)
    @RequiresPermissions("system:user:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysUser user) {
        List<SysUser> list = userService.selectUserList(user);
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        util.exportExcel(response, list, "用户数据");
    }

    @Log(title = "用户管理", businessType = BusinessType.IMPORT)
    @RequiresPermissions("system:user:import")
    @PostMapping("/importData")
    public AjaxResult importData(ImportDataDto importDataDto, @RequestParam("file") MultipartFile file) throws Exception {
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        List<SysUser> userList = util.importExcel(file.getInputStream());
        String operName = SecurityUtils.getUsername();
        String message = userService.importUser(userList, importDataDto, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(@RequestBody ImportTemplateDto importTemplateDto,HttpServletResponse response) throws IOException {
        String type = importTemplateDto.getType();
        switch (type) {
            case "partial1":
                new ExcelUtil<SysUserPartial1Vo>(SysUserPartial1Vo.class).importTemplateExcel(response, "用户数据");
                break;
            case "full":
            default:
                new ExcelUtil<SysUser>(SysUser.class).importTemplateExcel(response, "用户数据");
        }
    }

    /**
     * 获取当前用户信息
     */
    @InnerAuth
    @GetMapping("/info/{username}")
    public R<LoginUser> info(@PathVariable("username") String username) {
        SysUser sysUser = userService.selectUserByUserName(username);
        if (StringUtils.isNull(sysUser)) {
            logger.info("当前用户存在");
            return R.ok(null);
        }
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(sysUser);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(sysUser);
        LoginUser sysUserVo = new LoginUser();
        sysUserVo.setSysUser(sysUser);
        sysUserVo.setRoles(roles);
        sysUserVo.setPermissions(permissions);
        return R.ok(sysUserVo);
    }

    /**
     * 注册用户信息
     */
    @InnerAuth
    @PostMapping("/register")
    public R<Boolean> register(@RequestBody SysUser sysUser) {
        String username = sysUser.getUserName();
        if (!("true".equals(configService.selectConfigByKey("sys.account.registerUser")))) {
            return R.fail("当前系统没有开启注册功能！");
        }
        if (!userService.checkUserNameUnique(sysUser)) {
            return R.fail("保存用户'" + username + "'失败，注册账号已存在");
        }
        return R.ok(userService.registerUser(sysUser));
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo() {
        SysUser user = userService.selectUserById(SecurityUtils.getUserId());
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return ajax;
    }

    /**
     * 根据用户编号获取详细信息
     */
    @RequiresPermissions("system:user:query")
    @GetMapping(value = {"/", "/{userId}"})
    public AjaxResult getInfo(@PathVariable(value = "userId", required = false) Long userId) {
        userService.checkUserDataScope(userId);
        AjaxResult ajax = AjaxResult.success();
        List<SysRole> roles = roleService.selectRoleAll();
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        ajax.put("posts", postService.selectPostAll());
        if (StringUtils.isNotNull(userId)) {
            SysUser sysUser = userService.selectUserById(userId);
            ajax.put(AjaxResult.DATA_TAG, sysUser);
            ajax.put("postIds", postService.selectPostListByUserId(userId));
            ajax.put("roleIds", sysUser.getRoles().stream().map(SysRole::getRoleId).collect(Collectors.toList()));
        }
        return ajax;
    }

    /**
     * 根据用户编号获取详细信息
     */
    @RequiresPermissions("system:user:userDetails")
    @GetMapping("/userDetails")
    public AjaxResult userDetails() {
        long userId = SecurityUtils.getUserId();
        userService.checkUserDataScope(userId);
        AjaxResult ajax = AjaxResult.success();
        List<SysRole> roles = roleService.selectRoleAll();
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        ajax.put("posts", postService.selectPostAll());
        if (StringUtils.isNotNull(userId)) {
            SysUser sysUser = userService.selectUserAuthById(userId);
            sysUser.setRoleId(sysUser.getRoles().get(0).getRoleId());
            ajax.put(AjaxResult.DATA_TAG, sysUser);
            ajax.put("postIds", postService.selectPostListByUserId(userId));
            ajax.put("roleIds", sysUser.getRoles().stream().map(SysRole::getRoleId).collect(Collectors.toList()));
        }
        return ajax;
    }


    /**
     * 新增用户
     */
    @RequiresPermissions("system:user:add")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysUser user) {
        if (!userService.checkUserNameUnique(user)) {
            return error("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (StringUtils.isNotEmpty(user.getPhonenumber()) && !userService.checkPhoneUnique(user)) {
            return error("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user)) {
            return error("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setCreateBy(SecurityUtils.getUsername());
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        return toAjax(userService.insertUser(user));
    }

    /**
     * 修改用户
     */
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        if (!userService.checkUserNameUnique(user)) {
            return error("修改用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (StringUtils.isNotEmpty(user.getPhonenumber()) && !userService.checkPhoneUnique(user)) {
            return error("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user)) {
            return error("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(userService.updateUser(user));
    }

    /**
     * 修改用户认证信息
     */
    @RequiresPermissions("system:user:updateAuth")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/updateAuth")
    public AjaxResult updateAuth(@Validated @RequestBody SysUser user) throws Exception {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        if (!userService.checkUserNameUnique(user)) {
            return error("修改用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (StringUtils.isNotEmpty(user.getPhonenumber()) && !userService.checkPhoneUnique(user)) {
            return error("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user)) {
            return error("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setUpdateBy(SecurityUtils.getUsername());
        int i = userService.updateUserAuth(user);
        if (i > 0) {
            return toAjax(i);
        }
        return error("请核实信息并确保信息准确无误。");
    }


    /**
     * 删除用户
     */
    @RequiresPermissions("system:user:remove")
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public AjaxResult remove(@PathVariable Long[] userIds) {
        if (ArrayUtils.contains(userIds, SecurityUtils.getUserId())) {
            return error("当前用户不能删除");
        }
        return toAjax(userService.deleteUserByIds(userIds));
    }

    /**
     * 重置密码
     */
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    public AjaxResult resetPwd(@RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        user.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(userService.resetPwd(user));
    }

    /**
     * 状态修改
     */
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        user.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(userService.updateUserStatus(user));
    }

    /**
     * 根据用户编号获取授权角色
     */
    @RequiresPermissions("system:user:query")
    @GetMapping("/authRole/{userId}")
    public AjaxResult authRole(@PathVariable("userId") Long userId) {
        AjaxResult ajax = AjaxResult.success();
        SysUser user = userService.selectUserById(userId);
        List<SysRole> roles = roleService.selectRolesByUserId(userId);
        ajax.put("user", user);
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        return ajax;
    }

    /**
     * 用户授权角色
     */
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @PutMapping("/authRole")
    public AjaxResult insertAuthRole(Long userId, Long[] roleIds) {
        userService.checkUserDataScope(userId);
        userService.insertUserAuth(userId, roleIds);
        return success();
    }

    /**
     * 获取部门树列表
     */
    @RequiresPermissions("system:user:list")
    @GetMapping("/deptTree")
    public AjaxResult deptTree(SysDept dept) {
        return success(deptService.selectDeptTreeList(dept));
    }

    /**
     * 根据用户编号获取详细信息
     */
    @InnerAuth
    @GetMapping(value = {"/getUserInfo/{userId}"})
    public AjaxResult getUserInfo(@PathVariable(value = "userId", required = false) Long userId) {
        userService.checkUserDataScope(userId);
        AjaxResult ajax = AjaxResult.success();
        List<SysRole> roles = roleService.selectRoleAll();
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        ajax.put("posts", postService.selectPostAll());
        if (StringUtils.isNotNull(userId)) {
            SysUser sysUser = userService.selectUserById(userId);
            ajax.put(AjaxResult.DATA_TAG, sysUser);
            ajax.put("postIds", postService.selectPostListByUserId(userId));
            ajax.put("roleIds", sysUser.getRoles().stream().map(SysRole::getRoleId).collect(Collectors.toList()));
        }
        return ajax;
    }

    /**
     * 根据班级ID查询用户列表
     *
     * @param classIds
     * @return
     */
    @InnerAuth
    @GetMapping("/getUserByClassIds")
    public List<SysUser> getUserByClassIds(@RequestParam("classIds") Long[] classIds) {
        List<SysUser> userList = userService.selectUserByClassIds(classIds);
        return userList;
    }

    /**
     * 根据用户编号获取详细信息
     */
    @InnerAuth
    @GetMapping(value = {"/getUserAndRole/{userId}"})
    public R<LoginUser> getUserAndRole(@PathVariable(value = "userId", required = false) Long userId) {
        LoginUser sysUserVo = new LoginUser();
        if (StringUtils.isNotNull(userId)) {

            SysUser sysUser = userService.selectUserById(userId);
            List<SysRole> sysRoles = roleService.selectRolesByUserId(userId);
            sysUser.setRoles(sysRoles);
            sysUserVo.setSysUser(sysUser);
        }
        return R.ok(sysUserVo);
    }

    /**
     * 修改密码-发送短信验证码
     *
     * @return
     */
    @PostMapping("/send")
    public AjaxResult sendSMS(@RequestBody SysUser user) throws Exception {
//        if (System.currentTimeMillis() - Long.parseLong(decrypt(user.getDataNow())) < 3000) {
//            return success(userService.sendSMS(user.getUserName()));
        return success(userService.sendSMS2(user.getPhonenumber()));
//        } else {
//            throw new Exception("非法请求");
//        }
    }

    /**
     * 修改密码
     *
     * @return
     */
    @PostMapping("/revise")
    public AjaxResult revise(@RequestBody SysUser user) throws Exception {
//        String dataNow = user.getDataNow();
//        if (System.currentTimeMillis() - Long.parseLong(decrypt(user.getDataNow())) < 3000) {
//            return success(userService.revise(user));
        return success(userService.revise2(user));
//        } else {
//            throw new Exception("非法请求");
//        }
    }

    public String decrypt(String str) throws Exception {
        //64位解码加密后的字符串
        byte[] inputByte = Base64.decodeBase64(str.getBytes("UTF-8"));
        //base64编码的私钥
        byte[] decoded = Base64.decodeBase64(privateKey);
        RSAPrivateKey priKey = (RSAPrivateKey) KeyFactory.getInstance("RSA").generatePrivate(new PKCS8EncodedKeySpec(decoded));
        //RSA解密
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, priKey);
        String outStr = new String(cipher.doFinal(inputByte));
        return outStr;
    }

    /**
     * 身份认证-发送短信验证码
     *
     * @return
     */
    @PostMapping("/sendrz")
    public AjaxResult sendRz(@RequestBody SysUser user) throws Exception {
        return success(userService.sendSre(user));

    }

    /**
     * 小程序登录-发送短信验证码
     *
     * @return
     */
    @PostMapping("/sendDl")
    public AjaxResult sendDl(@RequestBody String username) throws Exception {
        return success(userService.sendDl(username));
    }

    @PostMapping("/sendCodeByPhone")
    public AjaxResult sendCodeByPhone(@RequestBody String phoneNumber) throws Exception {
        return success(userService.sendCodeByPhone(phoneNumber));
    }


    /**
     * 用户角色重置为游客
     */
    @InnerAuth
    @GetMapping(value = {"/resetUserRole/{userId}"})
    public AjaxResult resetUserRole(@PathVariable(value = "userId", required = false) Long userId) {
        return success(userService.resetUserRole(userId));
    }

    /**
     * 延期状态修改
     */
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/postponeStatus")
    public AjaxResult postponeStatus(@RequestBody SysUser user) {
        user.setUpdateBy(SecurityUtils.getUsername());
        Long[] roleIds = {106L};
        user.setRoleIds(roleIds);
        return toAjax(userService.updateUser(user));
    }

    /**
     * @description: 根据身份代码userCode（学生学号student_id或者教师工号job_id 或者用户名）获取用户信息
     * @author: zhaoTianQi
     * @date: 2024/11/25 11:28
     * @param: userCode
     * @return: R<LoginUser>
     **/
    @GetMapping("/getUserByUserCode")
    public R<LoginUser> getUserByCode(@RequestParam("userCode") String userCode) {
        return R.ok(userService.selectUserByUserCode(userCode));
    }

    /**
     * @description:
     * @author: zhaoTianQi
     * @date: 2024/12/16 8:45
     * @param: mapParams 参数  "mobile":"123"
     * @return: R<LoginUser>
     **/
    @PostMapping("/getUserByMapParams")
    public R<LoginUser> getUserByMapParams(@RequestBody Map<String, Object> mapParams) {
        return R.ok(userService.getUserByMapParams(mapParams));
    }

    @GetMapping("/getUserInfoByPhoneNumber")
    public R<LoginUser> getUserInfoByPhoneNumber(@RequestParam("phoneNumber") String phoneNumber) {
        return R.ok(userService.getUserInfoByPhoneNumber(phoneNumber));
    }
}
