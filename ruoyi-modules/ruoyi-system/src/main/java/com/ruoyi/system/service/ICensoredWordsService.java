package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.CensoredWords;

/**
 * 敏感词管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface ICensoredWordsService 
{
    /**
     * 查询敏感词管理
     * 
     * @param id 敏感词管理主键
     * @return 敏感词管理
     */
    public CensoredWords selectCensoredWordsById(Long id);

    /**
     * 查询敏感词管理列表
     * 
     * @param censoredWords 敏感词管理
     * @return 敏感词管理集合
     */
    public List<CensoredWords> selectCensoredWordsList(CensoredWords censoredWords);

    /**
     * 新增敏感词管理
     * 
     * @param censoredWords 敏感词管理
     * @return 结果
     */
    public int insertCensoredWords(CensoredWords censoredWords);

    /**
     * 修改敏感词管理
     * 
     * @param censoredWords 敏感词管理
     * @return 结果
     */
    public int updateCensoredWords(CensoredWords censoredWords);

    /**
     * 批量删除敏感词管理
     * 
     * @param ids 需要删除的敏感词管理主键集合
     * @return 结果
     */
    public int deleteCensoredWordsByIds(Long[] ids);

    /**
     * 删除敏感词管理信息
     * 
     * @param id 敏感词管理主键
     * @return 结果
     */
    public int deleteCensoredWordsById(Long id);

    void addByExcel(Long id);
}
