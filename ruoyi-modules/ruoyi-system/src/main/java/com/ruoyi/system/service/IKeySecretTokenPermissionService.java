package com.ruoyi.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.KeySecretToken;
import com.ruoyi.system.domain.KeySecretTokenPermission;

import java.util.List;

/**
 * Token接口权限配置Service接口
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface IKeySecretTokenPermissionService extends IService<KeySecretTokenPermission>
{
    /**
     * 查询Token接口权限配置
     *
     * @param id Token接口权限配置主键
     * @return Token接口权限配置
     */
    public KeySecretTokenPermission selectKeySecretTokenPermissionById(Long id);

    /**
     * 查询Token接口权限配置列表
     *
     * @param keySecretTokenPermission Token接口权限配置
     * @return Token接口权限配置集合
     */
    public List<KeySecretTokenPermission> selectKeySecretTokenPermissionList(KeySecretTokenPermission keySecretTokenPermission);

    /**
     * 新增Token接口权限配置
     *
     * @param keySecretTokenPermission Token接口权限配置
     * @return 结果
     */
    public int insertKeySecretTokenPermission(KeySecretTokenPermission keySecretTokenPermission);

    /**
     * 修改Token接口权限配置
     *
     * @param keySecretTokenPermission Token接口权限配置
     * @return 结果
     */
    public int updateKeySecretTokenPermission(KeySecretTokenPermission keySecretTokenPermission);

    /**
     * 批量删除Token接口权限配置
     *
     * @param ids 需要删除的Token接口权限配置主键集合
     * @return 结果
     */
    public int deleteKeySecretTokenPermissionByIds(Long[] ids);

    /**
     * 删除Token接口权限配置信息
     *
     * @param id Token接口权限配置主键
     * @return 结果
     */
    public int deleteKeySecretTokenPermissionById(Long id);

    String refreshCache(KeySecretToken keySecretToken);
}
