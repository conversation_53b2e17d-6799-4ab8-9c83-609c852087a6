package com.ruoyi.system.controller;

import java.util.List;
import java.io.IOException;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.dto.KeySecretManageDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.system.domain.KeySecretManage;
import com.ruoyi.system.service.IKeySecretManageService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * key_secret管理Controller
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@RestController
@RequestMapping("/keySecretManage")
public class KeySecretManageController extends BaseController {
    @Resource
    private IKeySecretManageService keySecretManageService;

    /**
     * 查询key_secret管理列表
     */
    @GetMapping("/list")
    public TableDataInfo list(KeySecretManage keySecretManage) {
        startPage();
        List<KeySecretManage> list = keySecretManageService.selectKeySecretManageList(keySecretManage);
        return getDataTable(list);
    }

    /**
     * 导出key_secret管理列表
     */
    @Log(title = "key_secret管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KeySecretManage keySecretManage) {
        List<KeySecretManage> list = keySecretManageService.selectKeySecretManageList(keySecretManage);
        ExcelUtil<KeySecretManage> util = new ExcelUtil<KeySecretManage>(KeySecretManage.class);
        util.exportExcel(response, list, "key_secret管理数据");
    }

    /**
     * 获取key_secret管理详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(keySecretManageService.selectKeySecretManageById(id));
    }

    /**
     * 新增key_secret管理
     */
    @Log(title = "key_secret管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KeySecretManage keySecretManage) {
        return toAjax(keySecretManageService.insertKeySecretManage(keySecretManage));
    }

    /**
     * 修改key_secret管理
     */
    @Log(title = "key_secret管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KeySecretManage keySecretManage) {
        return toAjax(keySecretManageService.updateKeySecretManage(keySecretManage));
    }

    /**
     * 删除key_secret管理
     */
    @Log(title = "key_secret管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(keySecretManageService.deleteKeySecretManageByIds(ids));
    }

    /**
     * 校验key_secret
     */
    @Log(title = "校验apikey", businessType = BusinessType.INSERT)
    @GetMapping("/checkApiKey")
    public AjaxResult checkKeySecret(@RequestParam("apiKey") String apiKey) {
        return AjaxResult.success(keySecretManageService.checkKeySecret(apiKey));
    }

    @GetMapping("/setCacheApiKey")
    public AjaxResult setCacheApiKey() {
        return AjaxResult.success(keySecretManageService.setCacheApiKey());
    }

    @GetMapping("/createApiKeySecret")
    public AjaxResult createApiKeySecret() {
        return AjaxResult.success(keySecretManageService.createApiKeySecret());
    }

}
