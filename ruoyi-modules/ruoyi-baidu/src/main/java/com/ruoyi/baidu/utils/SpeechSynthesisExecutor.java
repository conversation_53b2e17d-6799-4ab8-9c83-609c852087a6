//package com.ruoyi.baidu.utils;
//
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.PostConstruct;
//import javax.annotation.PreDestroy;
//import java.util.concurrent.*;
//
///**
// * 单机模式控制并发请求数量
// * 语音合成执行器
// * 使用Semaphore控制并发请求数量，避免过多请求导致服务崩溃
// */
//@Component
//public class SpeechSynthesisExecutor {
//    private static final Logger log = LoggerFactory.getLogger(SpeechSynthesisExecutor.class);
//
//    // 默认最大并发数
//    private static final int DEFAULT_MAX_CONCURRENT_REQUESTS = 10;
//
//    // 控制并发请求的信号量
//    private final Semaphore semaphore;
//
//    // 执行异步任务的线程池
//    private final ExecutorService executorService;
//
//    public SpeechSynthesisExecutor() {
//        this(DEFAULT_MAX_CONCURRENT_REQUESTS);
//    }
//
//    public SpeechSynthesisExecutor(int maxConcurrentRequests) {
//        this.semaphore = new Semaphore(maxConcurrentRequests);
//        // 创建线程池，核心线程数与最大并发数相同
//        this.executorService = new ThreadPoolExecutor(
//            maxConcurrentRequests,
//            maxConcurrentRequests * 2,
//            60L,
//            TimeUnit.SECONDS,
//            new LinkedBlockingQueue<>(100),
//            r -> {
//                Thread t = new Thread(r, "speech-synthesis-worker");
//                t.setDaemon(true);
//                return t;
//            },
//            new ThreadPoolExecutor.CallerRunsPolicy()
//        );
//
//        log.info("语音合成执行器初始化完成，最大并发请求数: {}", maxConcurrentRequests);
//    }
//
//    @PostConstruct
//    public void init() {
//        log.info("语音合成执行器已启动");
//    }
//
//    @PreDestroy
//    public void shutdown() {
//        executorService.shutdown();
//        try {
//            if (!executorService.awaitTermination(10, TimeUnit.SECONDS)) {
//                executorService.shutdownNow();
//            }
//        } catch (InterruptedException e) {
//            executorService.shutdownNow();
//            Thread.currentThread().interrupt();
//        }
//        log.info("语音合成执行器已关闭");
//    }
//
//    /**
//     * 执行语音合成任务
//     * @param task 需要执行的任务
//     * @param <T> 返回值类型
//     * @return 任务执行结果的Future对象
//     */
//    public <T> CompletableFuture<T> execute(Callable<T> task) {
//        CompletableFuture<T> future = new CompletableFuture<>();
//        // 提交任务到线程池
//        executorService.submit(() -> {
//            try {
//                // 尝试获取信号量许可 tryAcquire 获取信号量，获取返回true 共享信号量-1 超时获取不到信号量 false
//                if (!semaphore.tryAcquire(15, TimeUnit.SECONDS)) {
//                    // completeExceptionally 提前结束任务处理方法 -> 超时获取不到信号量 抛出异常
//                    future.completeExceptionally(new TimeoutException("语音合成服务繁忙，请稍后再试"));
//                    return;
//                }
//                // 获取到信号量
//                try {
//                    // 执行任务
//                    T result = task.call();
//                    future.complete(result);
//                } catch (Exception e) {
//                    future.completeExceptionally(e);
//                } finally {
//                    // 释放信号量许可
//                    semaphore.release();
//                }
//            } catch (InterruptedException e) {
//                Thread.currentThread().interrupt();
//                future.completeExceptionally(e);
//            }
//        });
//
//        return future;
//    }
//
//    /**
//     * 同步执行语音合成任务（带超时控制）
//     * @param task 需要执行的任务
//     * @param timeout 超时时间
//     * @param unit 时间单位
//     * @param <T> 返回值类型
//     * @return 任务执行结果
//     * @throws Exception 执行过程中的异常
//     */
//    public <T> T executeSync(Callable<T> task, long timeout, TimeUnit unit) throws Exception {
//        return execute(task).get(timeout, unit);
//    }
//
//    /**
//     * 获取当前可用许可数
//     * @return 可用许可数
//     */
//    public int getAvailablePermits() {
//        return semaphore.availablePermits();
//    }
//
//    /**
//     * 获取当前等待获取许可的线程数
//     * @return 等待线程数
//     */
//    public int getQueueLength() {
//        return semaphore.getQueueLength();
//    }
//
//    /**
//     * 获取执行器状态信息
//     * @return 状态信息
//     */
//    public String getStatus() {
//        ThreadPoolExecutor tpe = (ThreadPoolExecutor) executorService;
//        return String.format(
//            "语音合成执行器状态：活动线程=%d，队列任务=%d，已完成任务=%d，可用许可=%d，等待线程=%d",
//            tpe.getActiveCount(),
//            tpe.getQueue().size(),
//            tpe.getCompletedTaskCount(),
//            semaphore.availablePermits(),
//            semaphore.getQueueLength()
//        );
//    }
//}
