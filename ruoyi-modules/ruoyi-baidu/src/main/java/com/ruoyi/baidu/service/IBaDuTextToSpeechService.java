package com.ruoyi.baidu.service;

import com.ruoyi.baidu.dto.DBSpeechSynthesisDto;
import com.ruoyi.baidu.dto.SpeechSynthesisDto;
import com.ruoyi.common.core.web.domain.AjaxResult;

import java.util.Map;

public interface IBaDuTextToSpeechService {

    public AjaxResult getBaDuApiToken();

    /**
     * 百度语音识别
     * @param option
     * @return
     */
    String BaiDuASR(Map<String, String> option);

    AjaxResult getBaDuASRToken();

    /**
     * 语音合成
     * @param speechSynthesisDto 数据
     * @return 音频数据
     */
    byte[] YRTTS(SpeechSynthesisDto speechSynthesisDto);

    /**
     * 豆包文本转语音
     *
     * @param dbSpeechSynthesisDto 语音合成参数
     * @return 音频数据的base64编码字符串
     */
    String DBTTS(DBSpeechSynthesisDto dbSpeechSynthesisDto);

    /**
     * 豆包文本转语音文件
     * @param dbSpeechSynthesisDto
     */
    Map<String, Object> DBtextToSpeechFile(DBSpeechSynthesisDto dbSpeechSynthesisDto);
}
