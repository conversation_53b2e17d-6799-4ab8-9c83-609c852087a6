package com.ruoyi.baidu.service;

import com.baidubce.appbuilder.base.exception.AppBuilderServerException;
import com.baidubce.appbuilder.model.knowledgebase.Document;
import org.json.JSONException;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface IBaiduApiService {
    String createData(String ak, String sk, String groupName);

    Boolean importDataFile(String ak, String sk, String datasetId, String[] urlArray);

    int importStatus(String ak, String sk, String datasetId);

    Boolean releaseDataSet(String ak, String sk, String datasetId);

    Boolean deldata(String ak, String sk, String datasetId);

    String modelFineTuningTask(String ak, String sk, Map<String, String> map);

    String fineTuningTaskDetails(String ak, String sk, String taskId);

    String newModelVersion(String ak, String sk, String modelSetId, String taskId);

    String creatdataSet(String secretkey, String name);

    String[] importFile(String secretkey, String datasetId, List<String> filePath, Boolean enhanced);

    boolean delDataSetFile(String secretkey, String datasetId, String fileId);

    String sendContent(String messages, String apiKey, String secretKey, String apiURL) throws JSONException, IOException;

    String knowledgeBase(String query, String appid, String secretkey) throws IOException, AppBuilderServerException;

    String[] putObjectSimple(List<String> filePathList, List<String> busiIdList, String ak, String sk, String domainName,String bosBucketName);

    boolean delbos(List<String> filePathList, List<String> busiIdList, String ak, String sk, boolean flag, String bosBucketName);

    String insertPromptOptimization(String ak, String sk, String jsonBody);

    com.alibaba.fastjson2.JSONObject getPromptInfo(String ak, String sk, String optimizationId);

    String newSession(String appid, String secretkey);

    String getfileId(String appid, String secretkey, String conversationId, File file) throws IOException, JSONException;

    String getAnswer(String appid, String secretkey, String conversationId, String query, String fileId);

    String creatdataSetNew(String secretkey, String name,String dtoDescription);

    String creatdataSetNew(String secretkey, String name,String dtoDescription,String PathPrefix);

    String[] importFileNew(String secretkey,String datasetId, List<String> filePath, Boolean enhanced,String contentFormat);

    Document[]  getDocuments(String secretkey, String knowledgeBaseId,String after,String before) throws IOException, AppBuilderServerException;
    boolean delKnowledgeBaseFile(String secretkey,String datasetId, String fileId);

    void delKnowledgeBase(String secretkey, String datasetId);

    Map<String,String> getImage(String query, String appid, String secretkey,String conversationId,String basePath) throws IOException, AppBuilderServerException;

    String getImageInfo(String appid, String secretkey, List<String> filePath, String query) throws Exception;
}
