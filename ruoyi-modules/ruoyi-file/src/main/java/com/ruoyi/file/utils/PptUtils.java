package com.ruoyi.file.utils;

import org.apache.poi.hslf.usermodel.HSLFSlide;
import org.apache.poi.hslf.usermodel.HSLFSlideShow;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.xslf.usermodel.XMLSlideShow;
import org.apache.poi.xslf.usermodel.XSLFRelation;
import org.apache.poi.xslf.usermodel.XSLFSlide;

import java.io.*;
import java.util.List;

public class PptUtils {
    /**
     * 检测PPT文件类型
     * @param file 传递ppt文件
     * @return 返回ppt文件实际类型，以及ppt文件是否可读等
     */
    public static String check_ppt(File file){
        // 前置文件检查
        if (file == null) return "null_file";
        if (!file.exists()) return "not_exist";
        if (!file.canRead()) return "unreadable";
        if (file.length() == 0) return "empty_file";

        try (InputStream fis = new BufferedInputStream(new FileInputStream(file))) {
            // 标记流以便重置
            fis.mark(1024); // 标记足够长度用于后续重置
            // 阶段1：检测PPT（OLE2格式）
            if (POIFSFileSystem.hasPOIFSHeader(fis)) {
                return "ppt";
            }
            // 重置流状态
            fis.reset();
            // 阶段2：检测PPTX（OOXML格式）
            try (OPCPackage pkg = OPCPackage.open(fis)) {
                // 精确验证是否为PPTX（避免误判其他ZIP文件）
                if (pkg.getRelationshipsByType(XSLFRelation.MAIN.getContentType()).size() > 0) {
                    return "pptx";
                }
                // 方式二：通过 SlideShow 对象判断
                XMLSlideShow slideShow = new XMLSlideShow(pkg);
                if (slideShow.getSlideMasters().size() > 0) {
                    return "pptx";
                }
            } catch (InvalidFormatException ife) {
                System.err.println("非OOXML格式文件: " + ife.getMessage());
            } catch (Exception e) {
                System.err.println("解析OOXML异常: " + e.getClass().getSimpleName());

            }
        } catch (FileNotFoundException fnfe) {
            System.err.println("文件未找到: " + fnfe.getMessage());
            return "file_not_found";
        } catch (IOException ioe) {
            System.err.println("IO异常: " + ioe.getMessage());
            return "io_error";
        }
        return "unknown";
    }

    public static int getPptSizeFromPpt(File pptFile) {
        int pptSize = 0;
        try (FileInputStream fis = new FileInputStream(pptFile);
             HSLFSlideShow ppt = new HSLFSlideShow(fis)) {
            // 遍历每一页
            List<HSLFSlide> slides = ppt.getSlides();
            pptSize = slides.size();
        } catch (FileNotFoundException e) {
            System.out.println("File not found: " + e.getMessage());
        } catch (IOException e) {
            System.out.println("IOException: " + e.getMessage());
        }
        return pptSize;
    }
    public static int getPptSizeFromPptx(File pptFile) {
        int pptSize = 0;
        try (FileInputStream fis = new FileInputStream(pptFile);
             XMLSlideShow ppt = new XMLSlideShow(fis)) {
            // 遍历每一页
            List<XSLFSlide> slides = ppt.getSlides();
            pptSize = slides.size();
        } catch (FileNotFoundException e) {
            System.out.println("File not found: " + e.getMessage());
        } catch (IOException e) {
            System.out.println("IOException: " + e.getMessage());
        }
        return pptSize;
    }

}
