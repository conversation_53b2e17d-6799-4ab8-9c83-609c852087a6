package com.ruoyi.create.service.impl;


import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.create.domain.SpeechdraftProblem;
import com.ruoyi.create.mapper.SpeechdraftProblemMapper;
import com.ruoyi.create.service.ISpeechdraftProblemService;
import org.springframework.stereotype.Service;

import java.util.List;
import javax.annotation.Resource;

/**
 * 讲演稿题目Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
@Service
public class SpeechdraftProblemServiceImpl implements ISpeechdraftProblemService
{
    @Resource
    private SpeechdraftProblemMapper speechdraftProblemMapper;

    /**
     * 查询讲演稿题目
     *
     * @param id 讲演稿题目主键
     * @return 讲演稿题目
     */
    @Override
    public SpeechdraftProblem selectSpeechdraftProblemById(Long id)
    {
        return speechdraftProblemMapper.selectSpeechdraftProblemById(id);
    }

    /**
     * 查询讲演稿题目列表
     *
     * @param speechdraftProblem 讲演稿题目
     * @return 讲演稿题目
     */
    @Override
    public List<SpeechdraftProblem> selectSpeechdraftProblemList(SpeechdraftProblem speechdraftProblem)
    {
        return speechdraftProblemMapper.selectSpeechdraftProblemList(speechdraftProblem);
    }

    /**
     * 新增讲演稿题目
     *
     * @param speechdraftProblem 讲演稿题目
     * @return 结果
     */
    @Override
    public int insertSpeechdraftProblem(SpeechdraftProblem speechdraftProblem)
    {
        speechdraftProblem.setCreateTime(DateUtils.getNowDate());
        return speechdraftProblemMapper.insertSpeechdraftProblem(speechdraftProblem);
    }

    /**
     * 修改讲演稿题目
     *
     * @param speechdraftProblem 讲演稿题目
     * @return 结果
     */
    @Override
    public int updateSpeechdraftProblem(SpeechdraftProblem speechdraftProblem)
    {
        return speechdraftProblemMapper.updateSpeechdraftProblem(speechdraftProblem);
    }

    /**
     * 批量删除讲演稿题目
     *
     * @param ids 需要删除的讲演稿题目主键
     * @return 结果
     */
    @Override
    public int deleteSpeechdraftProblemByIds(Long[] ids)
    {
        return speechdraftProblemMapper.deleteSpeechdraftProblemByIds(ids);
    }

    @Override
    public int deleteSpeechdraftProblemBypresentationId(Long presentationId) {

        return speechdraftProblemMapper.deleteSpeechdraftProblemBypresentationId(presentationId);
    }

    /**
     * 删除讲演稿题目信息
     *
     * @param id 讲演稿题目主键
     * @return 结果
     */
    @Override
    public int deleteSpeechdraftProblemById(Long id)
    {
        return speechdraftProblemMapper.deleteSpeechdraftProblemById(id);
    }

    @Override
    public int insertSpeechdraftProblemList(List<SpeechdraftProblem> speechdraftProblemArrayList) {
        return speechdraftProblemMapper.insertSpeechdraftProblemList(speechdraftProblemArrayList);
    }


}
