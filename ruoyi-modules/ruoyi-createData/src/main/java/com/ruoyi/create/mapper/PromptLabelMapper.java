package com.ruoyi.create.mapper;

import java.util.List;
import com.ruoyi.create.domain.PromptLabel;

/**
 * prompt模板标签Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
public interface PromptLabelMapper 
{
    /**
     * 查询prompt模板标签
     * 
     * @param id prompt模板标签主键
     * @return prompt模板标签
     */
    public PromptLabel selectPromptLabelById(Long id);

    /**
     * 查询prompt模板标签列表
     * 
     * @param promptLabel prompt模板标签
     * @return prompt模板标签集合
     */
    public List<PromptLabel> selectPromptLabelList(PromptLabel promptLabel);

    /**
     * 新增prompt模板标签
     * 
     * @param promptLabel prompt模板标签
     * @return 结果
     */
    public int insertPromptLabel(PromptLabel promptLabel);

    /**
     * 修改prompt模板标签
     * 
     * @param promptLabel prompt模板标签
     * @return 结果
     */
    public int updatePromptLabel(PromptLabel promptLabel);

    /**
     * 删除prompt模板标签
     * 
     * @param id prompt模板标签主键
     * @return 结果
     */
    public int deletePromptLabelById(Long id);

    /**
     * 批量删除prompt模板标签
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePromptLabelByIds(Long[] ids);
}
