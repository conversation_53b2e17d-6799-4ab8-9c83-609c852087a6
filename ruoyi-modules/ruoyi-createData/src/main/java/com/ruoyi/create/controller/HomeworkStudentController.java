package com.ruoyi.create.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.create.domain.ClassInfo;
import com.ruoyi.create.domain.HomeworkStudentDetail;
import com.ruoyi.create.service.IClassInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.HomeworkStudent;
import com.ruoyi.create.service.IHomeworkStudentService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 作业状态Controller
 *
 * <AUTHOR>
 * @date 2024-06-07
 */
@RestController
@RequestMapping("/student")
public class HomeworkStudentController extends BaseController
{
    @Autowired
    private IHomeworkStudentService homeworkStudentService;

    @Autowired
    private IClassInfoService classInfoService;

    /**
     * 查询作业状态列表
     */
    @RequiresPermissions("create:student:list")
    @GetMapping("/list")
    public TableDataInfo list(HomeworkStudent homeworkStudent)
    {
        startPage();
        List<HomeworkStudent> list = homeworkStudentService.selectHomeworkStudentList(homeworkStudent);
        return getDataTable(list);
    }

    /**
     * 批改作业状态列表
     */
    @RequiresPermissions("create:student:list")
    @GetMapping("/listCorrect")
    public TableDataInfo listCorrect(HomeworkStudent homeworkStudent)
    {
        if (homeworkStudent.getCommitStatus().equals("2")){
			// 查询的全部
            homeworkStudent.setCommitStatus(null);
        }
        startPage();
        List<HomeworkStudent> list = homeworkStudentService.selectCorrectHomeworkStudentList(homeworkStudent);
        return getDataTable(list);
    }


    /**
     * 导出作业状态列表
     */
    @RequiresPermissions("create:student:export")
    @Log(title = "作业状态", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HomeworkStudent homeworkStudent)
    {
        List<HomeworkStudent> list = homeworkStudentService.selectHomeworkStudentList(homeworkStudent);
        ExcelUtil<HomeworkStudent> util = new ExcelUtil<HomeworkStudent>(HomeworkStudent.class);
        util.exportExcel(response, list, "作业状态数据");
    }

    /**
     * 获取作业状态详细信息
     */
    @RequiresPermissions("create:student:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(homeworkStudentService.selectHomeworkStudentById(id));
    }

    /**
     * 新增作业状态
     */
    @RequiresPermissions("create:student:add")
    @Log(title = "作业状态", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HomeworkStudent homeworkStudent)
    {
        return toAjax(homeworkStudentService.insertHomeworkStudent(homeworkStudent));
    }

    /**
     * 修改作业状态
     */
    @RequiresPermissions("create:student:edit")
    @Log(title = "作业状态", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HomeworkStudent homeworkStudent)
    {
        return toAjax(homeworkStudentService.updateHomeworkStudent(homeworkStudent));
    }

    /**
     * 延长截止时间
     */
    //@RequiresPermissions("create:student:edit")
    @Log(title = "延长截止时间", businessType = BusinessType.UPDATE)
    @PutMapping("/addCutOffTime")
    public AjaxResult addCutOffTime(@RequestBody HomeworkStudent homeworkStudent)
    {
        return toAjax(homeworkStudentService.updateHomeworkStudentCutOffTime(homeworkStudent));
    }

    /**
     * 删除作业状态
     */
    @RequiresPermissions("create:student:remove")
    @Log(title = "作业状态", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(homeworkStudentService.deleteHomeworkStudentByIds(ids));
    }

    /**
     * 通过作业查询出专业和班级
     */
    @RequiresPermissions("create:student:listClass")
    @GetMapping("/listClass/{hmId}")
    public AjaxResult listClass(@PathVariable Long hmId)
    {
        List<ClassInfo> list = classInfoService.selectClassINfoByHmId(hmId);
        return success(list);
    }
}
