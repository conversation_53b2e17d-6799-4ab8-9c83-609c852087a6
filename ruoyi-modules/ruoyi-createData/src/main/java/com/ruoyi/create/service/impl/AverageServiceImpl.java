package com.ruoyi.create.service.impl;

import com.ruoyi.create.domain.Average;
import com.ruoyi.create.domain.MySelfPortrait;
import com.ruoyi.create.mapper.AverageMapper;
import com.ruoyi.create.mapper.MySelfPortraitMapper;
import com.ruoyi.create.service.AverageService;
import com.ruoyi.create.service.MySelfPortraitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
@Repository
public class AverageServiceImpl implements AverageService {

    @Resource
    private AverageMapper aMapper;

    @Override
    public Average getAById(String classId) {
        Average a =   aMapper.selectAById(classId);
        return a;
    }
}