package com.ruoyi.create.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.create.domain.HomeworkStudent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.ClassInfoMapper;
import com.ruoyi.create.domain.ClassInfo;
import com.ruoyi.create.service.IClassInfoService;

/**
 * 班级信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
@Service
public class ClassInfoServiceImpl extends ServiceImpl<ClassInfoMapper, ClassInfo> implements IClassInfoService
{
    @Autowired
    private ClassInfoMapper classInfoMapper;

    /**
     * 查询班级信息
     *
     * @param id 班级信息主键
     * @return 班级信息
     */
    @Override
    public ClassInfo selectClassInfoById(Long id)
    {
        return classInfoMapper.selectClassInfoById(id);
    }

    /**
     * 查询班级信息列表
     *
     * @param classInfo 班级信息
     * @return 班级信息
     */
    @Override
    public List<ClassInfo> selectClassInfoList(ClassInfo classInfo)
    {
        return classInfoMapper.selectClassInfoList(classInfo);
    }

    /**
     * 新增班级信息
     *
     * @param classInfo 班级信息
     * @return 结果
     */
    @Override
    public int insertClassInfo(ClassInfo classInfo)
    {
        classInfo.setCreateTime(DateUtils.getNowDate());
        return classInfoMapper.insertClassInfo(classInfo);
    }

    /**
     * 修改班级信息
     *
     * @param classInfo 班级信息
     * @return 结果
     */
    @Override
    public int updateClassInfo(ClassInfo classInfo)
    {
        classInfo.setUpdateTime(DateUtils.getNowDate());
        return classInfoMapper.updateClassInfo(classInfo);
    }

    /**
     * 批量删除班级信息
     *
     * @param ids 需要删除的班级信息主键
     * @return 结果
     */
    @Override
    public int deleteClassInfoByIds(Long[] ids)
    {
        return classInfoMapper.deleteClassInfoByIds(ids);
    }

    /**
     * 删除班级信息信息
     *
     * @param id 班级信息主键
     * @return 结果
     */
    @Override
    public int deleteClassInfoById(Long id)
    {
        return classInfoMapper.deleteClassInfoById(id);
    }

    @Override
    public List<ClassInfo> selectClassINfoByHmId(Long hmId) {
        return classInfoMapper.selectClassINfoByHmId(hmId);
    }
}
