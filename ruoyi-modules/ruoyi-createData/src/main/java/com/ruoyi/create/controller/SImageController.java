package com.ruoyi.create.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.SImage;
import com.ruoyi.create.service.ISImageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 系统图片Controller
 *
 * <AUTHOR>
 * @date 2024-07-05
 */
@RestController
@RequestMapping("/image")
public class SImageController extends BaseController
{
    @Autowired
    private ISImageService sImageService;

    /**
     * 查询系统图片列表
     */
    @RequiresPermissions("create:image:list")
    @GetMapping("/list")
    public TableDataInfo list(SImage sImage)
    {
        startPage();
        List<SImage> list = sImageService.selectSImageList(sImage);
        return getDataTable(list);
    }

    /**
     * 导出系统图片列表
     */
    @RequiresPermissions("create:image:export")
    @Log(title = "系统图片", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SImage sImage)
    {
        List<SImage> list = sImageService.selectSImageList(sImage);
        ExcelUtil<SImage> util = new ExcelUtil<SImage>(SImage.class);
        util.exportExcel(response, list, "系统图片数据");
    }

    /**
     * 获取系统图片详细信息
     */
    @RequiresPermissions("create:image:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sImageService.selectSImageById(id));
    }

    /**
     * 新增系统图片
     */
    @RequiresPermissions("create:image:add")
    @Log(title = "系统图片", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SImage sImage)
    {
        return toAjax(sImageService.insertSImage(sImage));
    }

    /**
     * 修改系统图片
     */
    @RequiresPermissions("create:image:edit")
    @Log(title = "系统图片", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SImage sImage)
    {
        return toAjax(sImageService.updateSImage(sImage));
    }

    /**
     * 删除系统图片
     */
    @RequiresPermissions("create:image:remove")
    @Log(title = "系统图片", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sImageService.deleteSImageByIds(ids));
    }

    /**
     * 查询数据集列表
     */
    @RequiresPermissions("create:dataSet:all")
    @PostMapping("/all")
    public AjaxResult selectDataSetAll()
    {
        List<SImage> list = sImageService.selectImageAll();
        return success(list);
    }
}
