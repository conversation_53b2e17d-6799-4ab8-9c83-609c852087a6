package com.ruoyi.create.service.impl;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.domain.SMessage;
import com.ruoyi.create.domain.SMessage1;
import com.ruoyi.create.domain.SMsgReply;
import com.ruoyi.create.domain.SMsgReply1;
import com.ruoyi.create.mapper.SMessageMapper;
import com.ruoyi.create.mapper.SMessageMapper1;
import com.ruoyi.create.mapper.SMsgReplyMapper;
import com.ruoyi.create.mapper.SMsgReplyMapper1;
import com.ruoyi.create.service.ISMsgReplyService;
import com.ruoyi.create.service.ISMsgReplyService1;
import com.ruoyi.create.utils.Snowflake;
import com.ruoyi.system.api.RemoteUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.api.RemoteFileService;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 留言与回复关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-05
 */
@Service
public class SMsgReplyServiceImpl1 implements ISMsgReplyService1
{
    @Resource
    private SMsgReplyMapper1 sMsgReplyMapper;

    @Resource
    private RemoteUserService userService;

    @Resource
    private SMessageMapper1 sMessageMapper;
    @Resource
    private RemoteFileService remoteFileService;


    /**
     * 查询留言与回复关联
     *
     * @param msgId 留言与回复关联主键
     * @return 留言与回复关联
     */
    @Override
    public SMsgReply1 selectSMsgReplyByMsgId(Long msgId)
    {
        return sMsgReplyMapper.selectSMsgReplyByMsgId(msgId);
    }

    /**
     * 查询留言与回复关联列表
     *
     * @param sMsgReply 留言与回复关联
     * @return 留言与回复关联
     */
    @Override
    public List<SMsgReply1> selectSMsgReplyList(SMsgReply1 sMsgReply)
    {
        return sMsgReplyMapper.selectSMsgReplyList(sMsgReply);
    }

    /**
     * 新增留言与回复关联
     *
     * @param sMsgReply 留言与回复关联
     * @return 结果
     */
    @Override
    public int insertSMsgReply(SMsgReply1 sMsgReply)
    {
        //当前回复是否为
        Snowflake snowflakereply = new Snowflake(1, 1);
        long replyId = snowflakereply.generateId();
        if(ObjectUtils.isNotEmpty(sMsgReply.getI())){
            remoteFileService.relationFile(sMsgReply.getI(),String.valueOf(replyId));
        }
        sMsgReply.setReplyId(replyId);
        sMsgReply.setReplyUserid(SecurityUtils.getUserId());
        AjaxResult ajaxResult =userService.getInfo(SecurityUtils.getUserId(), SecurityConstants.INNER);
       Map linkedHashMap = (LinkedHashMap) ajaxResult.get("data");
        sMsgReply.setReplyUserName(String.valueOf(linkedHashMap.get("nickName")));
        sMsgReply.setCreateTime(DateUtils.getNowDate());
        return sMsgReplyMapper.insertSMsgReply(sMsgReply);
    }

    /**
     * 修改留言与回复关联
     *
     * @param sMsgReply 留言与回复关联
     * @return 结果
     */
    @Override
    public int updateSMsgReply(SMsgReply1 sMsgReply)
    {
        return sMsgReplyMapper.updateSMsgReply(sMsgReply);
    }

    /**
     * 批量删除留言与回复关联
     *
     * @param msgIds 需要删除的留言与回复关联主键
     * @return 结果
     */
    @Override
    public int deleteSMsgReplyByMsgIds(Long[] msgIds)
    {
        return sMsgReplyMapper.deleteSMsgReplyByMsgIds(msgIds);
    }

    /**
     * 删除留言与回复关联信息
     *
     * @param msgId 留言与回复关联主键
     * @return 结果
     */
    @Override
    public int deleteSMsgReplyByMsgId(Long msgId)
    {
        return sMsgReplyMapper.deleteSMsgReplyByMsgId(msgId);
    }

    @Override
    public List<SMessage1>  selectMsgAndReplyAll(long msgId) {
        SMessage1 sMessage = new SMessage1();
        sMessage.setMsgId(msgId);
        // 获取权限
        long uid = SecurityUtils.getUserId();
        AjaxResult ajaxResult = userService.getInfo(SecurityUtils.getUserId(), SecurityConstants.INNER);
        List<Integer> userRoleid = (ArrayList) ajaxResult.get("roleIds");
        List<SMessage1> sMessageList = sMessageMapper.selectSMessageList(sMessage);
        for (SMessage1 itemsMessage : sMessageList) {
            List<SMsgReply1>  sMsgReplyList = sMsgReplyMapper.selectSMsgReplyAll(msgId);
            boolean isAdmin = userRoleid.contains(1) || userRoleid.contains(105);
            if (isAdmin || itemsMessage.getMsgUserId() == uid) {
                itemsMessage.setDeleteFlag(true);
                sMsgReplyList.forEach(itemSMsgReply -> itemSMsgReply.setDeleteFlag(true));
            } else {
                itemsMessage.setDeleteFlag(false);
                sMsgReplyList.stream()
                        .filter(s -> s.getReplyUserid() == uid)
                        .forEach(s -> s.setDeleteFlag(true));
            }
            itemsMessage.setsMsgReplyList1(sMsgReplyList);
        }
        return sMessageList;
    }

    @Override
    public int deleteSMsgReplyByReplyIds(Long[] replyIds) {
        return sMsgReplyMapper.deleteSMsgReplyByReplyIds(replyIds);
    }
}
