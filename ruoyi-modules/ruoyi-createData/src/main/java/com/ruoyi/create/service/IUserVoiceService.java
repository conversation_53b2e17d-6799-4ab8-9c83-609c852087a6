package com.ruoyi.create.service;

import com.ruoyi.create.Vo.UserVoiceVo;
import com.ruoyi.create.domain.UserVoice;

import java.util.List;
import java.util.Map;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2024-08-09
 */
public interface IUserVoiceService
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public UserVoice selectSUserVoiceById(Long id);

    UserVoice selectSUserVoiceById();
    /**
     * 查询【请填写功能名称】列表
     *
     * @param userVoice 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<UserVoice> selectSUserVoiceList(UserVoice userVoice);

    /**
     * 新增【请填写功能名称】
     *
     * @param userVoice 【请填写功能名称】
     * @return 结果
     */
    public int insertSUserVoice(UserVoice userVoice);

    /**
     * 修改【请填写功能名称】
     *
     * @param userVoice 【请填写功能名称】
     * @return 结果
     */
    public int updateSUserVoice(UserVoice userVoice);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteSUserVoiceByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteSUserVoiceById(Long id);

    /**
     *
     * @param userVoice
     * @return
     */
    int saveUserVoice(UserVoice userVoice);

    /**
     * 获取系统TTS选择
     * @return 选择的值
     */
    String getSystemTTSChoose();

    List<UserVoiceVo> list();
}
