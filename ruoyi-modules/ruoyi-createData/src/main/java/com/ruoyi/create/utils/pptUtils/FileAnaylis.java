package com.ruoyi.create.utils.pptUtils;


import org.apache.poi.POIXMLDocument;
import org.apache.poi.POIXMLTextExtractor;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;

import java.io.*;

public class FileAnaylis {
    //文件类型
    private String type;

    /**
     * 读取TXT文件内容或者文本内容并返回一个字符串
     * @param filePath TXT文件的路径
     * @return 文件的全部内容作为一个字符串
     */
    public static String readFileToString(String type,String filePath) {
        String result = "";
        try {
            InputStream is = new FileInputStream(new File(filePath));
            if ("txt".equals(type)) {
                StringBuilder contentBuilder = new StringBuilder();
                try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
                    String line;
                    while ((line = br.readLine()) != null) {
                        contentBuilder.append(line).append("\n"); // 将每行内容添加到StringBuilder中，并添加换行符以保持原有格式
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                    System.err.println("An error occurred while reading the file.");
                    result = null; // 或者根据实际情况处理异常，比如返回一个空字符串""
                }
                result = contentBuilder.toString(); // 将StringBuilder转换为String返回
            } else if ("doc".equals(type)) {
                HWPFDocument document = new HWPFDocument(is);
                WordExtractor extractor = new WordExtractor(document);
                result = extractor.getText();
            } else if ("docx".equals(type)) {
                OPCPackage opcPackage = POIXMLDocument.openPackage(filePath);
                POIXMLTextExtractor extractor = new XWPFWordExtractor(opcPackage);
                result = extractor.getText();
                extractor.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }
}
