package com.ruoyi.create.controller;

import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.create.domain.MySelfPortrait;
import com.ruoyi.create.service.MappingKnowledgeService;
import com.ruoyi.create.service.MySelfPortraitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

import static com.ruoyi.common.core.web.domain.AjaxResult.success;

@RestController
@RequestMapping("/mySelfPortrait")
public class MySelfPortraitController {

    @Autowired
    private MySelfPortraitService mySelfPortraitService;

    @GetMapping("/yucefenxi/{studentId}")
    public AjaxResult addYuceFenxi(@PathVariable String studentId) throws IOException {
        AjaxResult ajaxResult = success(mySelfPortraitService.analyzeStudentAsync(studentId));
        return ajaxResult;
    }
    @GetMapping("/{studentId}")
    public AjaxResult getMySelfPortraitById(@PathVariable String studentId) throws IOException {
        AjaxResult ajaxResult = success(mySelfPortraitService.getMySelfPortraitById(studentId));
        return ajaxResult;
    }

    @GetMapping("/generateAndInsertPortrait")
    public void generateAndInsertPortrait() {
        mySelfPortraitService.generateAndInsertPortrait();

    }
}