package com.ruoyi.create.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.domain.ClassInfo;
import com.ruoyi.create.domain.CollegeInfo;
import com.ruoyi.create.domain.MajorInfo;
import com.ruoyi.create.service.IClassInfoService;
import com.ruoyi.create.service.ICollegeInfoService;
import com.ruoyi.create.service.IMajorInfoService;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.UniversityMapper;
import com.ruoyi.create.domain.University;
import com.ruoyi.create.service.IUniversityService;

/**
 * 学校信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
@Service
public class UniversityServiceImpl extends ServiceImpl<UniversityMapper, University> implements IUniversityService
{
    @Autowired
    private UniversityMapper universityMapper;
    @Autowired
    private ICollegeInfoService collegeInfoService;
    @Autowired
    private IMajorInfoService majorInfoService;
    @Autowired
    private IClassInfoService classInfoService;

    /**
     * 查询学校信息
     *
     * @param id 学校信息主键
     * @return 学校信息
     */
    @Override
    public University selectUniversityById(Long id)
    {
        return universityMapper.selectUniversityById(id);
    }

    /**
     * 查询学校信息列表
     *
     * @param university 学校信息
     * @return 学校信息
     */
    @Override
    public List<University> selectUniversityList(University university)
    {
        List<University> universityList =  universityMapper.selectUniversityList(university);
        return universityList;
    }

    /**
     * 新增学校信息
     *
     * @param university 学校信息
     * @return 结果
     */
    @Override
    public int insertUniversity(University university)
    {
        university.setCreateTime(DateUtils.getNowDate());
        return universityMapper.insertUniversity(university);
    }

    /**
     * 修改学校信息
     *
     * @param university 学校信息
     * @return 结果
     */
    @Override
    public int updateUniversity(University university)
    {
        university.setUpdateTime(DateUtils.getNowDate());
        return universityMapper.updateUniversity(university);
    }

    /**
     * 批量删除学校信息
     *
     * @param ids 需要删除的学校信息主键
     * @return 结果
     */
    @Override
    public int deleteUniversityByIds(Long[] ids)
    {
        return universityMapper.deleteUniversityByIds(ids);
    }

    /**
     * 删除学校信息信息
     *
     * @param id 学校信息主键
     * @return 结果
     */
    @Override
    public int deleteUniversityById(Long id)
    {
        return universityMapper.deleteUniversityById(id);
    }

    @Override
    public List<University> selectUniversityListAll(Long roleId) {
        List<University>  universityList = new ArrayList<>();
        if (roleId==102){
            universityList = universityMapper.selectUniversityListallTeacher();
        }else{
            universityList = universityMapper.selectUniversityListAll();
        }

        // 使用Stream API进行优化处理
        universityList.stream()
                .map(university -> {
                    university.setChildren(
                            university.getChildren().stream()
                                    .filter(college -> college.getId() != null && college.getName() != null)
                                    .map(college -> {
                                        college.setChildren(
                                                college.getChildren().stream()
                                                        .filter(major -> major.getId() != null && major.getName() != null)
                                                        .map(major -> {
                                                            major.setChildren(
                                                                    major.getChildren().stream()
                                                                            .filter(clazz -> clazz.getId() != null && clazz.getName() != null)
                                                                            .collect(Collectors.toList())
                                                            );
                                                            return major;
                                                        })
                                                        .collect(Collectors.toList())
                                        );
                                        return college;
                                    })
                                    .collect(Collectors.toList())
                    );
                    return university;
                }).collect(Collectors.toList());
        return universityList;
    }

    @Override
    public University getUniversityInfo(University university) {
        University universityList = universityMapper.selectUniversityById(university.getId());
        CollegeInfo collegeInfo ;
        MajorInfo majorInfo;
        ClassInfo classInfo;
        if(university.getCollegeId() !=null){
            collegeInfo = collegeInfoService.selectCollegeInfoById(university.getCollegeId());
            universityList.setColleName(collegeInfo.getColleName());
        }
        if(university.getMajorId() !=null){
            majorInfo = majorInfoService.selectMajorInfoById(university.getMajorId());
            universityList.setMajorName(majorInfo.getMajorName());
        }
        if(university.getClassId() !=null){
            classInfo = classInfoService.selectClassInfoById(university.getClassId());
            universityList.setClassName(classInfo.getClassName());
        }
        return universityList;
    }

    @Override
    public List<University> getUniversityAllUseForHm() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser sysUser = loginUser.getSysUser();
        Long universityId = sysUser.getUniversityId();
        List<University>  universityList;
        if(universityId!=null){
            universityList = universityMapper.selectUniversityListByUniversityId(universityId);
        }else {
            universityList = universityMapper.selectUniversityListAll();
        }

        // 使用Stream API进行优化处理
        universityList.stream()
                .map(university -> {
                    university.setChildren(
                            university.getChildren().stream()
                                    .filter(college -> college.getId() != null && college.getName() != null)
                                    .map(college -> {
                                        college.setChildren(
                                                college.getChildren().stream()
                                                        .filter(major -> major.getId() != null && major.getName() != null)
                                                        .map(major -> {
                                                            major.setChildren(
                                                                    major.getChildren().stream()
                                                                            .filter(clazz -> clazz.getId() != null && clazz.getName() != null)
                                                                            .collect(Collectors.toList())
                                                            );
                                                            return major;
                                                        })
                                                        .collect(Collectors.toList())
                                        );
                                        return college;
                                    })
                                    .collect(Collectors.toList())
                    );
                    return university;
                }).collect(Collectors.toList());
        return universityList;
    }
    @Override
    public List<University> getUniversityUseForHm() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser sysUser = loginUser.getSysUser();
        Long universityId = sysUser.getUniversityId();
        List<University>  universityList;
        if(universityId!=null){
            universityList = universityMapper.selectUniversityListById(universityId);
        }else {
            universityList = universityMapper.selectListAll();
        }

        // 使用Stream API进行优化处理
        universityList.stream()
                .map(university -> {
                    university.setChildren(
                            university.getChildren().stream()
                                    .filter(college -> college.getId() != null && college.getName() != null)
                                    .map(college -> {
                                        college.setChildren(
                                                college.getChildren().stream()
                                                        .filter(major -> major.getId() != null && major.getName() != null)
                                                        .map(major -> {
                                                            major.setChildren(
                                                                    major.getChildren().stream()
                                                                            .filter(clazz -> clazz.getId() != null && clazz.getName() != null)
                                                                            .collect(Collectors.toList())
                                                            );
                                                            return major;
                                                        })
                                                        .collect(Collectors.toList())
                                        );
                                        return college;
                                    })
                                    .collect(Collectors.toList())
                    );
                    return university;
                }).collect(Collectors.toList());
        return universityList;
    }
}

