package com.ruoyi.create.mapper;

import java.util.List;
import com.ruoyi.create.domain.CareResource;

/**
 * 服务资源配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
public interface CareResourceMapper 
{
    /**
     * 查询服务资源配置
     * 
     * @param id 服务资源配置主键
     * @return 服务资源配置
     */
    public CareResource selectCareResourceById(Long id);

    /**
     * 查询服务资源配置列表
     * 
     * @param careResource 服务资源配置
     * @return 服务资源配置集合
     */
    public List<CareResource> selectCareResourceList(CareResource careResource);

    /**
     * 新增服务资源配置
     * 
     * @param careResource 服务资源配置
     * @return 结果
     */
    public int insertCareResource(CareResource careResource);

    /**
     * 修改服务资源配置
     * 
     * @param careResource 服务资源配置
     * @return 结果
     */
    public int updateCareResource(CareResource careResource);

    /**
     * 删除服务资源配置
     * 
     * @param id 服务资源配置主键
     * @return 结果
     */
    public int deleteCareResourceById(Long id);

    /**
     * 批量删除服务资源配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCareResourceByIds(Long[] ids);
}
