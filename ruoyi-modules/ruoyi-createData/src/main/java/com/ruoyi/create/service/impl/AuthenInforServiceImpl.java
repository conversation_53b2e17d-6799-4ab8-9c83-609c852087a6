package com.ruoyi.create.service.impl;

import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.create.domain.AuthenInfor;
import com.ruoyi.create.mapper.AuthenInforMapper;
import com.ruoyi.create.service.IAuthenInforService;
import org.apache.tomcat.util.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.crypto.Cipher;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 鉴权信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-14
 */
@Service
public class AuthenInforServiceImpl implements IAuthenInforService {
    @Resource
    private AuthenInforMapper authenInforMapper;

    @Autowired
    private RedisService redisService;

    private String privateKey = "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBALL2yjem0ENoZaz2AFPSMNATos67ehwIDqJCuBmAmIOd9p+iKYdsWAbDUJrKezu7hdJBXeJzT3beLhCtuzt0EqL7DFl8i5RgQygO9hceTyt87errhPOpojVDTPs9hDBzSTY33ouI55jPH2I+8EblKaynLs08eXmjTGXsDqM5XYyZAgMBAAECgYA2oYudbXjJ+wZ+xCHZdKKeAkCC50whXnxJICDe+BiWpRPyKyiORI6ikeD7P7BazaXOR1IHnLe3S5+4S7CKN6awQUpE+f4ayYMfRoSbH8d8ZeioWTxmP24tgYgaF9Z3wEnesc3auZIyOnqH9eo0fsvjgKiq1ptWUP5BL0oLjo7gYQJBAP8szJbAOBTCgDXcUcD1Y3eJ4UDsoWBZ0xzQD2NinseKvwoaB2KBd4OSUPkPhhlIoL9OBM9T4agPI4hFmtKEgfUCQQCziunE0VBFfVGOKvk8QIT6F08F340tJr48OIsdJnZhOVjoQHqI3c1lFRxSuSGFxtyaqfHruWcyveJScYwkr6WVAkBEuNn4l5gC70b8OnPCFdRN81I43AGyIz7Z+abLS1obv2An5k6q1tdLFfK8wNOKp6azHt3owFx7mGgnYSeLHqipAkAidgBGobJZlCMqOX9bHDsp0X1+cBkl2HDdGDFDaBWCtcIl2fJrAL+irjmgex4/EhtXqFTh3NU8/QtKrbard/c9AkBSHXD0A2aPqPN2w9JYTIsXPV3ZLGg6/oOyVJ40DMu4Y2Ndw5tr42lM1i/DHFiR78ZqTRK/kMN5bb1Sn3kze4Cu";


    /**
     * 查询鉴权信息
     *
     * @param id 鉴权信息主键
     * @return 鉴权信息
     */
    @Override
    public AuthenInfor selectAuthenInforById(Long id) {
        AuthenInfor authenInfor = authenInforMapper.selectAuthenInforById(id);
        return authenInfor;
    }

    /**
     * 查询鉴权信息列表
     *
     * @param authenInfor 鉴权信息
     * @return 鉴权信息
     */
    @Override
    public List<AuthenInfor> selectAuthenInforList(AuthenInfor authenInfor) {
        List<AuthenInfor> authenInforList = authenInforMapper.selectAuthenInforList(authenInfor);
        authenInforList.forEach(authenInfor1 -> {
            try {
                authenInfor1.setApiKey(originalString(decrypt(authenInfor1.getApiKey())));
                authenInfor1.setSecretKey(originalString(decrypt(authenInfor1.getSecretKey())));
                authenInfor1.setApiUrl(decrypt(authenInfor1.getApiUrl()));
                authenInfor1.setAk(originalString(decrypt(authenInfor1.getAk())));
                authenInfor1.setSk(originalString(decrypt(authenInfor1.getSk())));
                authenInfor1.setDomainName(originalString(decrypt(authenInfor1.getDomainName())));
                authenInfor1.setBosBucketName(originalString(decrypt(authenInfor1.getBosBucketName())));
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        return authenInforList;
    }

    /**
     * 新增鉴权信息
     *
     * @param authenInfor 鉴权信息
     * @return 结果
     */
    @Override
    public int insertAuthenInfor(AuthenInfor authenInfor) throws Exception {
        if (authenInforMapper.countMenuRouting(authenInfor.getMenuRouting()) > 0) {
            throw new Exception("菜单路由重复");
        }
        authenInfor.setCreateTime(DateUtils.getNowDate());
        int row = authenInforMapper.insertAuthenInfor(authenInfor);
        if (row > 0) {
            try {
                Map<String, String> map = new HashMap<>();
                map.put("apiKey", decrypt(authenInfor.getApiKey()));
                map.put("secretKey", decrypt(authenInfor.getSecretKey()));
                map.put("apiUrl", decrypt(authenInfor.getApiUrl()));
                map.put("ak", decrypt(authenInfor.getAk()));
                map.put("sk", decrypt(authenInfor.getSk()));
                map.put("domainName", decrypt(authenInfor.getDomainName()));
                map.put("bosBucketName", decrypt(authenInfor.getBosBucketName()));
                redisService.setCacheMap(authenInfor.getMenuRouting(), map);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return row;
    }

    /**
     * 修改鉴权信息
     *
     * @param authenInfor 鉴权信息
     * @return 结果
     */
    @Override
    public int updateAuthenInfor(AuthenInfor authenInfor) throws Exception {
        if (authenInforMapper.countUpdateMenuRouting(authenInfor) > 0) {
            throw new Exception("菜单路由重复");
        }
        redisService.deleteObject(authenInfor.getMenuRouting());
        authenInfor.setUpdateTime(DateUtils.getNowDate());
        int row = authenInforMapper.updateAuthenInfor(authenInfor);
        if (row > 0) {
            try {
                Map<String, String> map = new HashMap<>();
                map.put("apiKey", decrypt(authenInfor.getApiKey()));
                map.put("secretKey", decrypt(authenInfor.getSecretKey()));
                map.put("apiUrl", decrypt(authenInfor.getApiUrl()));
                map.put("ak", decrypt(authenInfor.getAk()));
                map.put("sk", decrypt(authenInfor.getSk()));
                map.put("domainName", decrypt(authenInfor.getDomainName()));
                map.put("bosBucketName", decrypt(authenInfor.getBosBucketName()));
                redisService.setCacheMap(authenInfor.getMenuRouting(), map);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return row;
    }

    /**
     * 批量删除鉴权信息
     *
     * @param ids 需要删除的鉴权信息主键
     * @return 结果
     */
    @Override
    public int deleteAuthenInforByIds(Long[] ids) {
        List<AuthenInfor> authenInforList = authenInforMapper.selectAuthenInforListByIds(ids);
        for (AuthenInfor authenInfor : authenInforList) {
            redisService.deleteObject(authenInfor.getMenuRouting());
        }
        return authenInforMapper.deleteAuthenInforByIds(ids);
    }

    /**
     * 删除鉴权信息信息
     *
     * @param id 鉴权信息主键
     * @return 结果
     */
    @Override
    public int deleteAuthenInforById(Long id) {
        AuthenInfor authenInfor = authenInforMapper.selectAuthenInforById(id);
        redisService.deleteObject(authenInfor.getMenuRouting());
        return authenInforMapper.deleteAuthenInforById(id);
    }

    @Override
    public String selectConfigByKey(String menuRouting, String key) {
        String configValue = Convert.toStr(redisService.getCacheMapValue(menuRouting,key));
        if (StringUtils.isNotEmpty(configValue))
        {
            return configValue;
        }
        AuthenInfor authenInfor1 = new AuthenInfor();
        authenInfor1.setMenuRouting(menuRouting);
        List<AuthenInfor> authenInforList = authenInforMapper.selectAuthenInforList(authenInfor1);
        if (authenInforList.size()>0)
        {
            AuthenInfor authenInfor = authenInforList.get(0);
            Map<String, String> map = new HashMap<>();
            try {
                map.put("apiKey", decrypt(authenInfor.getApiKey()));
                map.put("secretKey", decrypt(authenInfor.getSecretKey()));
                map.put("apiUrl", decrypt(authenInfor.getApiUrl()));
                map.put("ak", decrypt(authenInfor.getAk()));
                map.put("sk", decrypt(authenInfor.getSk()));
                map.put("domainName", decrypt(authenInfor.getDomainName()));
                map.put("bosBucketName", decrypt(authenInfor.getBosBucketName()));
                redisService.setCacheMap(authenInfor.getMenuRouting(), map);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return map.get(key);
        }
        return StringUtils.EMPTY;
    }

    @Override
    public List<AuthenInfor> selectAuthenInforAll() {
        return authenInforMapper.selectAuthenInforAll();
    }


    public static Map<Integer, String> genKeyPair() throws NoSuchAlgorithmException {
        Map<Integer, String> result = new HashMap<>();
        // KeyPairGenerator类用于生成公钥和私钥对，基于RSA算法生成对象
        KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance("RSA");
        // 初始化密钥对生成器，密钥大小为96-1024位
        keyPairGen.initialize(1024, new SecureRandom());
        // 生成一个密钥对，保存在keyPair中
        KeyPair keyPair = keyPairGen.generateKeyPair();
        RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();   // 得到私钥
        RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();  // 得到公钥
        String publicKeyString = new String(Base64.encodeBase64(publicKey.getEncoded()));
        // 得到私钥字符串
        String privateKeyString = new String(Base64.encodeBase64((privateKey.getEncoded())));
        // 将公钥和私钥保存到Map
        result.put(0, publicKeyString);  //0表示公钥
        result.put(1, privateKeyString);  //1表示私钥
        return result;
    }

    public String originalString(String subString) {

        // 提取字符串的第一位、第二位和最后一位字符
        String firstTwoChars = subString.substring(0, 2);
        char lastChar = subString.charAt(subString.length() - 1);

        // 计算需要替换的星号数量
        int asterisksCount = subString.length() - 3;

        // 生成星号字符串
        String asterisks = new String(new char[asterisksCount]).replace('\0', '*');

        // 拼接最终的字符串
        String resultString = firstTwoChars + asterisks + lastChar;

        // 输出结果
        return resultString;
    }

    public String decrypt(String str) throws Exception {
        //64位解码加密后的字符串
        byte[] inputByte = Base64.decodeBase64(str.getBytes("UTF-8"));
        //base64编码的私钥
        byte[] decoded = Base64.decodeBase64(privateKey);
        RSAPrivateKey priKey = (RSAPrivateKey) KeyFactory.getInstance("RSA").generatePrivate(new PKCS8EncodedKeySpec(decoded));
        //RSA解密
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, priKey);
        String outStr = new String(cipher.doFinal(inputByte));
        return outStr;
    }


}
