package com.ruoyi.create.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.KnowledgeSpeciality;
import com.ruoyi.create.service.IKnowledgeSpecialityService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 知识库专业配置Controller
 * 
 * <AUTHOR>
 * @date 2024-07-30
 */
@RestController
@RequestMapping("/speciality")
public class KnowledgeSpecialityController extends BaseController
{
    @Autowired
    private IKnowledgeSpecialityService knowledgeSpecialityService;

    /**
     * 查询知识库专业配置列表
     */
    @RequiresPermissions("create:speciality:list")
    @GetMapping("/list")
    public TableDataInfo list(KnowledgeSpeciality knowledgeSpeciality)
    {
        startPage();
        List<KnowledgeSpeciality> list = knowledgeSpecialityService.selectKnowledgeSpecialityList(knowledgeSpeciality);
        return getDataTable(list);
    }

    /**
     * 导出知识库专业配置列表
     */
    @RequiresPermissions("create:speciality:export")
    @Log(title = "知识库专业配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KnowledgeSpeciality knowledgeSpeciality)
    {
        List<KnowledgeSpeciality> list = knowledgeSpecialityService.selectKnowledgeSpecialityList(knowledgeSpeciality);
        ExcelUtil<KnowledgeSpeciality> util = new ExcelUtil<KnowledgeSpeciality>(KnowledgeSpeciality.class);
        util.exportExcel(response, list, "知识库专业配置数据");
    }

    /**
     * 获取知识库专业配置详细信息
     */
    @RequiresPermissions("create:speciality:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(knowledgeSpecialityService.selectKnowledgeSpecialityById(id));
    }

    /**
     * 新增知识库专业配置
     */
    @RequiresPermissions("create:speciality:add")
    @Log(title = "知识库专业配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KnowledgeSpeciality knowledgeSpeciality)
    {
        return toAjax(knowledgeSpecialityService.insertKnowledgeSpeciality(knowledgeSpeciality));
    }

    /**
     * 修改知识库专业配置
     */
    @RequiresPermissions("create:speciality:edit")
    @Log(title = "知识库专业配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KnowledgeSpeciality knowledgeSpeciality)
    {
        return toAjax(knowledgeSpecialityService.updateKnowledgeSpeciality(knowledgeSpeciality));
    }

    /**
     * 删除知识库专业配置
     */
    @RequiresPermissions("create:speciality:remove")
    @Log(title = "知识库专业配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(knowledgeSpecialityService.deleteKnowledgeSpecialityByIds(ids));
    }

    /**
     * 根据专业id获取知识库配置
     */
    @GetMapping("/getKnowledge/{id}")
    public KnowledgeSpeciality getKnowledge(@PathVariable Long id)
    {
        return knowledgeSpecialityService.selectKnowledgeSpecialityByMajorInfoId(id);
    }
}
