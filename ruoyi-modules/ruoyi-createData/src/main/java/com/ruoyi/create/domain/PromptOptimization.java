package com.ruoyi.create.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * prompt模板优化对象 s_prompt_optimization
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@TableName("s_prompt_optimization")
public class PromptOptimization extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 原始prompt内容 */
    @Excel(name = "原始prompt内容")
    private String content;

    /** 模型服务名称 */
    @Excel(name = "模型服务名称")
    private String serviceName;

    /** 质量优化参数 取值范围：true,false */
    @Excel(name = "质量优化参数 取值范围：true,false")
    private String qualityOptFlag;

    /** 缩短提示词参数 取值范围：true,false */
    @Excel(name = "缩短提示词参数 取值范围：true,false")
    private String shortPromptFlag;

    /** 迭代轮次 取值范围：1，2 */
    @Excel(name = "迭代轮次 取值范围：1，2")
    private Long iterationRound;

    /** 思维链条参数 取值范围：true,false */
    @Excel(name = "思维链条参数 取值范围：true,false")
    private String thoughtChainFlag;

    /** prompt优化任务id */
    @Excel(name = "prompt优化任务id")
    private String optimizationId;

    /** 优化后prompt内容 */
    @Excel(name = "优化后prompt内容")
    private String optimizeContent;

    /** 状态,1：优化中2：优化完成3：优化失败 */
    @Excel(name = "状态,1：优化中2：优化完成3：优化失败")
    private Long processStatus;

    /** 推理结果_优化前 */
    @Excel(name = "推理结果_优化前")
    private String inferenceBefore;

    /** 推理结果_优化后 */
    @Excel(name = "推理结果_优化后")
    private String inferenceAfter;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setContent(String content)
    {
        this.content = content;
    }

    public String getContent()
    {
        return content;
    }
    public void setServiceName(String serviceName)
    {
        this.serviceName = serviceName;
    }

    public String getServiceName()
    {
        return serviceName;
    }
    public void setQualityOptFlag(String qualityOptFlag)
    {
        this.qualityOptFlag = qualityOptFlag;
    }

    public String getQualityOptFlag()
    {
        return qualityOptFlag;
    }
    public void setShortPromptFlag(String shortPromptFlag)
    {
        this.shortPromptFlag = shortPromptFlag;
    }

    public String getShortPromptFlag()
    {
        return shortPromptFlag;
    }
    public void setIterationRound(Long iterationRound)
    {
        this.iterationRound = iterationRound;
    }

    public Long getIterationRound()
    {
        return iterationRound;
    }
    public void setThoughtChainFlag(String thoughtChainFlag)
    {
        this.thoughtChainFlag = thoughtChainFlag;
    }

    public String getThoughtChainFlag()
    {
        return thoughtChainFlag;
    }
    public void setOptimizationId(String optimizationId)
    {
        this.optimizationId = optimizationId;
    }

    public String getOptimizationId()
    {
        return optimizationId;
    }
    public void setOptimizeContent(String optimizeContent)
    {
        this.optimizeContent = optimizeContent;
    }

    public String getOptimizeContent()
    {
        return optimizeContent;
    }
    public void setProcessStatus(Long processStatus)
    {
        this.processStatus = processStatus;
    }

    public Long getProcessStatus()
    {
        return processStatus;
    }
    public void setInferenceBefore(String inferenceBefore)
    {
        this.inferenceBefore = inferenceBefore;
    }

    public String getInferenceBefore()
    {
        return inferenceBefore;
    }
    public void setInferenceAfter(String inferenceAfter)
    {
        this.inferenceAfter = inferenceAfter;
    }

    public String getInferenceAfter()
    {
        return inferenceAfter;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("content", getContent())
            .append("serviceName", getServiceName())
            .append("qualityOptFlag", getQualityOptFlag())
            .append("shortPromptFlag", getShortPromptFlag())
            .append("iterationRound", getIterationRound())
            .append("thoughtChainFlag", getThoughtChainFlag())
            .append("optimizationId", getOptimizationId())
            .append("optimizeContent", getOptimizeContent())
            .append("processStatus", getProcessStatus())
            .append("inferenceBefore", getInferenceBefore())
            .append("inferenceAfter", getInferenceAfter())
            .toString();
    }
}
