package com.ruoyi.create.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.STextbookCountMapper;
import com.ruoyi.create.domain.TextbookCount;
import com.ruoyi.create.service.ISTextbookCountService;

/**
 * 文献整理- 教材数量目标Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-31
 */
@Service
public class STextbookCountServiceImpl implements ISTextbookCountService 
{
    @Autowired
    private STextbookCountMapper sTextbookCountMapper;

    /**
     * 查询文献整理- 教材数量目标
     * 
     * @param id 文献整理- 教材数量目标主键
     * @return 文献整理- 教材数量目标
     */
    @Override
    public TextbookCount selectSTextbookCountById(Long id)
    {
        return sTextbookCountMapper.selectSTextbookCountById(id);
    }

    /**
     * 查询文献整理- 教材数量目标列表
     * 
     * @param sTextbookCount 文献整理- 教材数量目标
     * @return 文献整理- 教材数量目标
     */
    @Override
    public List<TextbookCount> selectSTextbookCountList(TextbookCount sTextbookCount)
    {
        return sTextbookCountMapper.selectSTextbookCountList(sTextbookCount);
    }

    /**
     * 新增文献整理- 教材数量目标
     * 
     * @param sTextbookCount 文献整理- 教材数量目标
     * @return 结果
     */
    @Override
    public int insertSTextbookCount(TextbookCount sTextbookCount)
    {
        sTextbookCount.setCreateTime(DateUtils.getNowDate());
        return sTextbookCountMapper.insertSTextbookCount(sTextbookCount);
    }

    /**
     * 修改文献整理- 教材数量目标
     * 
     * @param sTextbookCount 文献整理- 教材数量目标
     * @return 结果
     */
    @Override
    public int updateSTextbookCount(TextbookCount sTextbookCount)
    {
        sTextbookCount.setUpdateTime(DateUtils.getNowDate());
        return sTextbookCountMapper.updateSTextbookCount(sTextbookCount);
    }

    /**
     * 批量删除文献整理- 教材数量目标
     * 
     * @param ids 需要删除的文献整理- 教材数量目标主键
     * @return 结果
     */
    @Override
    public int deleteSTextbookCountByIds(Long[] ids)
    {
        return sTextbookCountMapper.deleteSTextbookCountByIds(ids);
    }

    /**
     * 删除文献整理- 教材数量目标信息
     * 
     * @param id 文献整理- 教材数量目标主键
     * @return 结果
     */
    @Override
    public int deleteSTextbookCountById(Long id)
    {
        return sTextbookCountMapper.deleteSTextbookCountById(id);
    }
}
