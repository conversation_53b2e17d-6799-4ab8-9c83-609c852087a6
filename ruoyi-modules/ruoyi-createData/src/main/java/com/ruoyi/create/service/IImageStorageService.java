package com.ruoyi.create.service;



import com.ruoyi.create.domain.ImageStorage;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 图片存储Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-06
 */
public interface IImageStorageService 
{
    /**
     * 查询图片存储
     * 
     * @param id 图片存储主键
     * @return 图片存储
     */
    public ImageStorage selectImageStorageById(Long id);

    /**
     * 查询图片存储列表
     * 
     * @param imageStorage 图片存储
     * @return 图片存储集合
     */
    public List<ImageStorage> selectImageStorageList(ImageStorage imageStorage);

    /**
     * 新增图片存储
     *
     * @return 结果
     */
    public int insertImageStorage(MultipartFile file, Long dataId,String imageNumber) throws Exception;

    /**
     * 修改图片存储
     * 
     * @param imageStorage 图片存储
     * @return 结果
     */
    public int updateImageStorage(ImageStorage imageStorage);

    /**
     * 批量删除图片存储
     * 
     * @param ids 需要删除的图片存储主键集合
     * @return 结果
     */
    public int deleteImageStorageByIds(Long[] ids);

    /**
     * 删除图片存储信息
     * 
     * @param id 图片存储主键
     * @return 结果
     */
    public int deleteImageStorageById(Long id);
}
