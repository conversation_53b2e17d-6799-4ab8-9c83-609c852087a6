package com.ruoyi.create.service;

import com.ruoyi.create.domain.CircularBannerConfig;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 学校轮播图配置Service接口
 *
 * <AUTHOR>
 * @date 2024-09-04
 */
public interface ICircularBannerConfigService {

    /**
     * 查询学校轮播图配置列表
     *
     * @param circularBannerConfig 学校轮播图配置
     * @return 学校轮播图配置集合
     */
    public List<CircularBannerConfig> selectCircularBannerConfigList(CircularBannerConfig circularBannerConfig);

    /**
     * 上传学校轮播图配置列表
     *
     * @param file 学校轮播图
     * @return 结果
     */
    public CircularBannerConfig uploadCircularBannerConfig(MultipartFile file);

    /**
     * 提交学校轮播图配置
     *
     * @return 结果
     */
    public int updateCircularBannerConfigByFileName(CircularBannerConfig circularBannerConfig);



    /**
     * 获取学校轮播图配置
     *
     * @param id 学校轮播图配置主键
     * @return 学校轮播图配置
     */
    public CircularBannerConfig selectCircularBannerConfigById(Long id);



    /**
     * 修改学校轮播图配置
     *
     * @param circularBannerConfig 学校轮播图配置
     * @return 结果
     */
    public int updateCircularBannerConfig(CircularBannerConfig circularBannerConfig);

    /**
     * 删除学校轮播图配置信息
     *
     * @param id 学校轮播图配置主键
     * @return 结果
     */
    public int deleteCircularBannerConfigById(Long id);

    /**
     * 获取所有图片的路径
     *
     * @return 结果
     */
    public List<String> selectCircularBannerConfigListAll();

    /**
     * 获得登录人所在学校名称
     * @param username 登录人名称
     * @return 结果
     */
    public String selectUniversity(String username);
}
