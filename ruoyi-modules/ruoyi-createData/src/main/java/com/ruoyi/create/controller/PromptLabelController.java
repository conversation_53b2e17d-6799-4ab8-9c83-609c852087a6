package com.ruoyi.create.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.PromptLabel;
import com.ruoyi.create.service.IPromptLabelService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * prompt模板标签Controller
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
@RestController
@RequestMapping("/label")
public class PromptLabelController extends BaseController
{
    @Autowired
    private IPromptLabelService promptLabelService;

    /**
     * 查询prompt模板标签列表
     */
    @RequiresPermissions("create:label:list")
    @GetMapping("/list")
    public TableDataInfo list(PromptLabel promptLabel)
    {
        startPage();
        List<PromptLabel> list = promptLabelService.selectPromptLabelList(promptLabel);
        return getDataTable(list);
    }

    /**
     * 导出prompt模板标签列表
     */
    @RequiresPermissions("create:label:export")
    @Log(title = "prompt模板标签", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PromptLabel promptLabel)
    {
        List<PromptLabel> list = promptLabelService.selectPromptLabelList(promptLabel);
        ExcelUtil<PromptLabel> util = new ExcelUtil<PromptLabel>(PromptLabel.class);
        util.exportExcel(response, list, "prompt模板标签数据");
    }

    /**
     * 获取prompt模板标签详细信息
     */
    @RequiresPermissions("create:label:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(promptLabelService.selectPromptLabelById(id));
    }

    /**
     * 新增prompt模板标签
     */
    @RequiresPermissions("create:label:add")
    @Log(title = "prompt模板标签", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PromptLabel promptLabel)
    {
        return toAjax(promptLabelService.insertPromptLabel(promptLabel));
    }

    /**
     * 修改prompt模板标签
     */
    @RequiresPermissions("create:label:edit")
    @Log(title = "prompt模板标签", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PromptLabel promptLabel)
    {
        return toAjax(promptLabelService.updatePromptLabel(promptLabel));
    }

    /**
     * 删除prompt模板标签
     */
    @RequiresPermissions("create:label:remove")
    @Log(title = "prompt模板标签", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(promptLabelService.deletePromptLabelByIds(ids));
    }
}
