package com.ruoyi.create.service;

import com.ruoyi.create.Vo.DataDapingVo;
import com.ruoyi.create.domain.DataDaping.*;

import java.util.List;

public interface DataDapingService {


    DataDapingVo getDataDapingVo();

//    // 1. 智慧学堂统计（单条最新）
//    SWisdomStatistics getLatestWisdomStatistics();
//
//    // 2. 平台实时数据（单条最新）
//    SPlatformReal getLatestPlatformReal();
//
//    // 3. 专业知识点热度排行（多条，按rank升序）
//    List<SKnowledgeRanking> getLatestKnowledgeRankingList();
//
//    // 4. 每日趋势（多条，周一到周日）
//    List<SDailyTrend> getLatestDailyTrendList();
//
//    // 5. 实践实训（多条，不同场景）
//    List<SPracticalTraining> getLatestPracticalTrainingList();
//
//    // 6. 知识库课程数量（多条，不同学科）1
//    List<SCourseKnowledge> getLatestCourseKnowledgeList();
//
//    // 7. 学生活跃度排行（多条，按rank升序）
//    List<SStudentRanking> getLatestStudentRankingList();
//
//    // 8. 学生专业课程知识点掌握数量（多条）
//    List<SStudentAcquired> getLatestStudentAcquiredList();
//
//    // 9. 学生作业周完成情况（多条，周一到周日）
//    List<SWeeklyCompletion> getLatestWeeklyCompletionList();
}
