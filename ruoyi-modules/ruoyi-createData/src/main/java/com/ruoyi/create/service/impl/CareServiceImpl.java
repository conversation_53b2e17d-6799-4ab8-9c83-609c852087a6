package com.ruoyi.create.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.CareMapper;
import com.ruoyi.create.domain.Care;
import com.ruoyi.create.service.ICareService;

/**
 * 服务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
@Service
public class CareServiceImpl implements ICareService 
{
    @Autowired
    private CareMapper careMapper;

    /**
     * 查询服务
     * 
     * @param id 服务主键
     * @return 服务
     */
    @Override
    public Care selectCareById(Long id)
    {
        return careMapper.selectCareById(id);
    }

    /**
     * 查询服务列表
     * 
     * @param care 服务
     * @return 服务
     */
    @Override
    public List<Care> selectCareList(Care care)
    {
        return careMapper.selectCareList(care);
    }

    /**
     * 新增服务
     * 
     * @param care 服务
     * @return 结果
     */
    @Override
    public int insertCare(Care care)
    {
        care.setCreateTime(DateUtils.getNowDate());
        return careMapper.insertCare(care);
    }

    /**
     * 修改服务
     * 
     * @param care 服务
     * @return 结果
     */
    @Override
    public int updateCare(Care care)
    {
        care.setUpdateTime(DateUtils.getNowDate());
        return careMapper.updateCare(care);
    }

    /**
     * 批量删除服务
     * 
     * @param ids 需要删除的服务主键
     * @return 结果
     */
    @Override
    public int deleteCareByIds(Long[] ids)
    {
        return careMapper.deleteCareByIds(ids);
    }

    /**
     * 删除服务信息
     * 
     * @param id 服务主键
     * @return 结果
     */
    @Override
    public int deleteCareById(Long id)
    {
        return careMapper.deleteCareById(id);
    }
}
