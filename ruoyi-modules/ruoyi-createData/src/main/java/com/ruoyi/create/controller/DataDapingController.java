package com.ruoyi.create.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.create.Vo.DataDapingVo;
import com.ruoyi.create.service.DataDapingService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 大屏数据控制器
 */
@RestController
@RequestMapping("/dataDaping")
public class DataDapingController {

    @Autowired
    private DataDapingService dataDapingService;

    /**
     * 获取大屏全部统计数据
     */
    @GetMapping("/getDataDapingVo")
    public DataDapingVo getDataDapingVo() {
        DataDapingVo vo = dataDapingService.getDataDapingVo();
        System.out.println(vo);
        return vo;
    }
}
