package com.ruoyi.create.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.create.domain.CourseTopicsReplies;
import com.ruoyi.create.domain.SMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.TopicsReply;
import com.ruoyi.create.service.ITopicsReplyService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 课程留言与回复关联Controller
 * 
 * <AUTHOR>
 * @date 2024-12-04
 */
@RestController
@RequestMapping("/topReply")
public class TopicsReplyController extends BaseController
{
    @Autowired
    private ITopicsReplyService topicsReplyService;

    /**
     * 查询课程留言与回复关联列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TopicsReply topicsReply)
    {
        startPage();
        List<TopicsReply> list = topicsReplyService.selectTopicsReplyList(topicsReply);
        return getDataTable(list);
    }

    /**
     * 导出课程留言与回复关联列表
     */
    @Log(title = "课程留言与回复关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TopicsReply topicsReply)
    {
        List<TopicsReply> list = topicsReplyService.selectTopicsReplyList(topicsReply);
        ExcelUtil<TopicsReply> util = new ExcelUtil<TopicsReply>(TopicsReply.class);
        util.exportExcel(response, list, "课程留言与回复关联数据");
    }

    /**
     * 获取课程留言与回复关联详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(topicsReplyService.selectTopicsReplyById(id));
    }

    /**
     * 新增课程留言与回复关联
     */
    @Log(title = "课程留言与回复关联", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TopicsReply topicsReply)
    {
        return toAjax(topicsReplyService.insertTopicsReply(topicsReply));
    }

    /**
     * 修改课程留言与回复关联
     */
    @Log(title = "课程留言与回复关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TopicsReply topicsReply)
    {
        return toAjax(topicsReplyService.updateTopicsReply(topicsReply));
    }

    /**
     * 查询此留言的全部回复信息
     */
    @GetMapping("/all/{msgId}")
    public AjaxResult selectTopicsReplyAll(@PathVariable("msgId") Long msgId)
    {
        List<CourseTopicsReplies> list = topicsReplyService.selectTopicsReplyAll(msgId);
        return success(list);
    }

    /**
     * 删除课程留言与回复关联
     */
    @Log(title = "课程留言与回复关联", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(topicsReplyService.deleteTopicsReplyByIds(ids));
    }

    /**
     * 删除留言与回复关联
     */
    @Log(title = "留言与回复关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/removeReply/{replyIds}")
    public AjaxResult removeReplyIds(@PathVariable Long[] replyIds)
    {
        return toAjax(topicsReplyService.deleteTopicsReplylyIds(replyIds));
    }
}
