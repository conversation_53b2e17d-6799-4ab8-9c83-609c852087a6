package com.ruoyi.create.mapper;

import java.util.List;
import com.ruoyi.create.domain.CourseKikeStep;

/**
 * 课程讨论话题回复点赞Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface CourseKikeStepMapper 
{
    /**
     * 查询课程讨论话题回复点赞
     * 
     * @param id 课程讨论话题回复点赞主键
     * @return 课程讨论话题回复点赞
     */
    public CourseKikeStep selectCourseKikeStepById(Long id);

    /**
     * 查询课程讨论话题回复点赞列表
     * 
     * @param courseKikeStep 课程讨论话题回复点赞
     * @return 课程讨论话题回复点赞集合
     */
    public List<CourseKikeStep> selectCourseKikeStepList(CourseKikeStep courseKikeStep);

    /**
     * 新增课程讨论话题回复点赞
     * 
     * @param courseKikeStep 课程讨论话题回复点赞
     * @return 结果
     */
    public int insertCourseKikeStep(CourseKikeStep courseKikeStep);

    /**
     * 修改课程讨论话题回复点赞
     * 
     * @param courseKikeStep 课程讨论话题回复点赞
     * @return 结果
     */
    public int updateCourseKikeStep(CourseKikeStep courseKikeStep);

    /**
     * 删除课程讨论话题回复点赞
     * 
     * @param id 课程讨论话题回复点赞主键
     * @return 结果
     */
    public int deleteCourseKikeStepById(Long id);

    /**
     * 批量删除课程讨论话题回复点赞
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCourseKikeStepByIds(Long[] ids);

    int deleteCourseKikeStep(CourseKikeStep courseKikeStep);

    int countCourseKike(Long msgId);

    int countCourseStep(Long msgId);

    String selectCourseKikeStepByUserName(CourseKikeStep courseKikeStep);
}
