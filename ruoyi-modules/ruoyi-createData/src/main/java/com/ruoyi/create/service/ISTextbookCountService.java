package com.ruoyi.create.service;

import java.util.List;
import com.ruoyi.create.domain.TextbookCount;

/**
 * 文献整理- 教材数量目标Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-31
 */
public interface ISTextbookCountService 
{
    /**
     * 查询文献整理- 教材数量目标
     * 
     * @param id 文献整理- 教材数量目标主键
     * @return 文献整理- 教材数量目标
     */
    public TextbookCount selectSTextbookCountById(Long id);

    /**
     * 查询文献整理- 教材数量目标列表
     * 
     * @param sTextbookCount 文献整理- 教材数量目标
     * @return 文献整理- 教材数量目标集合
     */
    public List<TextbookCount> selectSTextbookCountList(TextbookCount sTextbookCount);

    /**
     * 新增文献整理- 教材数量目标
     * 
     * @param sTextbookCount 文献整理- 教材数量目标
     * @return 结果
     */
    public int insertSTextbookCount(TextbookCount sTextbookCount);

    /**
     * 修改文献整理- 教材数量目标
     * 
     * @param sTextbookCount 文献整理- 教材数量目标
     * @return 结果
     */
    public int updateSTextbookCount(TextbookCount sTextbookCount);

    /**
     * 批量删除文献整理- 教材数量目标
     * 
     * @param ids 需要删除的文献整理- 教材数量目标主键集合
     * @return 结果
     */
    public int deleteSTextbookCountByIds(Long[] ids);

    /**
     * 删除文献整理- 教材数量目标信息
     * 
     * @param id 文献整理- 教材数量目标主键
     * @return 结果
     */
    public int deleteSTextbookCountById(Long id);
}
