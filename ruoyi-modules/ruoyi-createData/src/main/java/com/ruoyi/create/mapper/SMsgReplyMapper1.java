package com.ruoyi.create.mapper;

import com.ruoyi.create.domain.SMsgReply;
import com.ruoyi.create.domain.SMsgReply1;
import com.ruoyi.system.api.domain.SysUser;

import java.util.List;

/**
 * 留言与回复关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-05
 */
public interface SMsgReplyMapper1
{
    /**
     * 查询留言与回复关联
     * 
     * @param msgId 留言与回复关联主键
     * @return 留言与回复关联
     */
    public SMsgReply1 selectSMsgReplyByMsgId(Long msgId);

    /**
     * 查询留言与回复关联列表
     * 
     * @param sMsgReply 留言与回复关联
     * @return 留言与回复关联集合
     */
    public List<SMsgReply1> selectSMsgReplyList(SMsgReply1 sMsgReply);

    /**
     * 新增留言与回复关联
     * 
     * @param sMsgReply 留言与回复关联
     * @return 结果
     */
    public int insertSMsgReply(SMsgReply1 sMsgReply);

    /**
     * 修改留言与回复关联
     * 
     * @param sMsgReply 留言与回复关联
     * @return 结果
     */
    public int updateSMsgReply(SMsgReply1 sMsgReply);

    /**
     * 删除留言与回复关联
     * 
     * @param msgId 留言与回复关联主键
     * @return 结果
     */
    public int deleteSMsgReplyByMsgId(Long msgId);

    /**
     * 批量删除留言与回复关联
     * 
     * @param msgIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSMsgReplyByMsgIds(Long[] msgIds);

    List<SMsgReply1> selectSMsgReplyPortion(Long msgId);

    List<SMsgReply1>  selectSMsgReplyAll(Long msgId);

    SysUser selectUserById(Long userId);

    int deleteSMsgReplyByReplyIds(Long[] replyIds);
}
