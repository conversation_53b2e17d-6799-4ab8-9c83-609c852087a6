package com.ruoyi.create.service;

import java.util.List;
import com.ruoyi.create.domain.ClassStudentCourseReport;

/**
 * 课程班级信息统计Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-10
 */
public interface IClassStudentCourseReportService 
{
    /**
     * 查询课程班级信息统计
     * 
     * @param id 课程班级信息统计主键
     * @return 课程班级信息统计
     */
    public ClassStudentCourseReport selectClassStudentCourseReportById(Long id);

    /**
     * 查询课程班级信息统计列表
     * 
     * @param classStudentCourseReport 课程班级信息统计
     * @return 课程班级信息统计集合
     */
    public List<ClassStudentCourseReport> selectClassStudentCourseReportList(ClassStudentCourseReport classStudentCourseReport);

    /**
     * 新增课程班级信息统计
     * 
     * @param classStudentCourseReport 课程班级信息统计
     * @return 结果
     */
    public int insertClassStudentCourseReport(ClassStudentCourseReport classStudentCourseReport);

    /**
     * 修改课程班级信息统计
     * 
     * @param classStudentCourseReport 课程班级信息统计
     * @return 结果
     */
    public int updateClassStudentCourseReport(ClassStudentCourseReport classStudentCourseReport);

    /**
     * 批量删除课程班级信息统计
     * 
     * @param ids 需要删除的课程班级信息统计主键集合
     * @return 结果
     */
    public int deleteClassStudentCourseReportByIds(Long[] ids);

    /**
     * 删除课程班级信息统计信息
     * 
     * @param id 课程班级信息统计主键
     * @return 结果
     */
    public int deleteClassStudentCourseReportById(Long id);
	
	/** 创建单个学生单门课程分析  */
	ClassStudentCourseReport createSingleCourseAnalysis(ClassStudentCourseReport courseReport);

	/** 获取单个学生单门课程分析*/
	ClassStudentCourseReport getSingleCourseAnalysis(ClassStudentCourseReport courseReport);

	/**
	 * @description: 创建班级内全部学生课程分析数据
	 * @author: zhaoTianQi
	 * @date: 2024/12/10 14:46
	 * @param: courseReport
	 * @return: AjaxResult
	 **/
	void createClassStudentCourseAnalysis(ClassStudentCourseReport courseReport);

	/**
	 * @description: 创建单个班级课程分析数据
	 * @author: zhaoTianQi 
	 * @date:  15:34
	 * @param: courseReport
	 * @return: ClassStudentCourseReport
	 **/
	ClassStudentCourseReport createClassCourseAnalysis(ClassStudentCourseReport courseReport);

	/**
	 * @description: 获取班级课程分析数据
	 * @author: zhaoTianQi 
	 * @date: 2024/12/11 10:23
	 * @param: courseReport
	 * @return: String
	 **/
	ClassStudentCourseReport getClassCourseAnalysis(ClassStudentCourseReport courseReport);

	/**
	 * @description: 创建所有的班级课程分析数据
	 * @author: zhaoTianQi
	 * @date: 10:36
	 * @param:
	 * @return: void
	 **/
	void createAllClassCourseAnalysis();

	/**
	 * @description: 处理所有的课程分析 学生 和 班级
	 * @author: zhaoTianQi
	 * @date: 2024/12/11 12:36
	 * @param:
	 * @return: AjaxResult
	 **/
	void dealAllCourseAnalysis();
}
