package com.ruoyi.create.mapper;

import com.ruoyi.create.domain.LiteratrueData;

import java.util.List;

/**
 * 文献整理-原始数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-18
 */
public interface LiteratrueDataMapper 
{
    /**
     * 查询文献整理-原始数据
     * 
     * @param id 文献整理-原始数据主键
     * @return 文献整理-原始数据
     */
    public LiteratrueData selectLiteratrueDataById(Long id);

    /**
     * 查询文献整理-原始数据列表
     * 
     * @param literatrueData 文献整理-原始数据
     * @return 文献整理-原始数据集合
     */
    public List<LiteratrueData> selectLiteratrueDataList(LiteratrueData literatrueData);

    /**
     * 新增文献整理-原始数据
     * 
     * @param literatrueData 文献整理-原始数据
     * @return 结果
     */
    public int insertLiteratrueData(LiteratrueData literatrueData);

    /**
     * 修改文献整理-原始数据
     * 
     * @param literatrueData 文献整理-原始数据
     * @return 结果
     */
    public int updateLiteratrueData(LiteratrueData literatrueData);

    /**
     * 删除文献整理-原始数据
     * 
     * @param id 文献整理-原始数据主键
     * @return 结果
     */
    public int deleteLiteratrueDataById(Long id);

    /**
     * 批量删除文献整理-原始数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLiteratrueDataByIds(Long[] ids);

    public int  insertLiteratrueDataList(List<LiteratrueData> literatrueData);

    Long selectMaxTitleId();

    List<LiteratrueData> selectClickLiteratrueData(LiteratrueData literatrueData);
}
