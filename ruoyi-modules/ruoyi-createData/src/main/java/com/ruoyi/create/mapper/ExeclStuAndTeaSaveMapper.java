package com.ruoyi.create.mapper;

import com.ruoyi.create.domain.SysDept;
import com.ruoyi.create.domain.execl.*;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.create.domain.execl.Class;

import java.util.List;

public interface ExeclStuAndTeaSaveMapper {

    public List<Coll> selectCollByName(@Param("collName") String collName,@Param("univerId") Long univerId);
    public List<Long> selectCollIdByName(@Param("collName") String collName,@Param("univerId") Long univerId);
    public List<Long> selectMajIdByName(@Param("majName") String majName,@Param("univerId") Long univerId);
    public List<Long> selectClaIdByName(@Param("claName") String claName,@Param("univerId") Long univerId);

    public Student selectStuById(Long studentId);

    public int insertCla(Class c);
    public int insertMajor(Major m);
    public int insertColl(Coll coll);


    public int insertOrUpdateBatchS(@Param("list") List<Student> list);
    public int insertOrUpdateBatchT(@Param("list") List<Teacher> list);

    public int insertOrUpdateUS(Student student);
    public int insertOrUpdateBatchUS(@Param("list") List<Student> list);
    public int insertOrUpdateUT(Teacher teacher);
    public int insertOrUpdateBatchUT(@Param("list") List<Teacher> list);

    public String selectUserIdByUserName(String userName);
    public int updateAuthStatusByUserId(User user);

    public int insertUserRoleBatchS(@Param("list") List<Student> list);
    public int insertUserRoleBatchSP(@Param("list") List<Student> list);
    public int insertUserRoleBatchT(@Param("list") List<Teacher> list);

    public int insertUserRoleS(Long userId);
    public int insertUserRoleSP(Long userId);
    public int insertUserRoleT(Long userId);



    public Student selectStuById2(String studentId);

    public Teacher selectTeaById2(String teacherId);

    public int deleteByDeFalg();

    public String selectUniverNameById(Long id);

    public SysDept selectDeptByNameAndPId(SysDept sysDept);
    public SysDept selectDeptInit(SysDept sysDept);
    public int insertOrUpdateDept(SysDept sysDept);

}
