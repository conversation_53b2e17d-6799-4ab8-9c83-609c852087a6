package com.ruoyi.create.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.Lesson;
import com.ruoyi.create.service.ILessonService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 课程信息Controller
 *
 * <AUTHOR>
 * @date 2024-06-20
 */
@RestController
@RequestMapping("/lesson")
@Api(tags = "课程信息")
public class LessonController extends BaseController
{
    @Autowired
    private ILessonService lessonService;

    /**
     * 查询课程信息列表
     */
    @RequiresPermissions("create:lesson:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询课程信息列表")
    public TableDataInfo list(Lesson lesson)
    {
        startPage();
        List<Lesson> list = lessonService.selectLessonList(lesson);
        return getDataTable(list);
    }

    /**
     * 导出课程信息列表
     */
    @RequiresPermissions("create:lesson:export")
    @Log(title = "课程信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Lesson lesson)
    {
        List<Lesson> list = lessonService.selectLessonList(lesson);
        ExcelUtil<Lesson> util = new ExcelUtil<Lesson>(Lesson.class);
        util.exportExcel(response, list, "课程信息数据");
    }

    /**
     * 获取课程信息详细信息
     */
    @RequiresPermissions("create:lesson:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(lessonService.selectLessonById(id));
    }

    /**
     * 新增课程信息
     */
    @RequiresPermissions("create:lesson:add")
    @Log(title = "课程信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Lesson lesson)
    {
        return toAjax(lessonService.insertLesson(lesson));
    }

    /**
     * 修改课程信息
     */
    @RequiresPermissions("create:lesson:edit")
    @Log(title = "课程信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Lesson lesson)
    {
        return toAjax(lessonService.updateLesson(lesson));
    }

    /**
     * 删除课程信息
     */
    @RequiresPermissions("create:lesson:remove")
    @Log(title = "课程信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(lessonService.deleteLessonByIds(ids));
    }
}
