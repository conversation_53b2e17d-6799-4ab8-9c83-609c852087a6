package com.ruoyi.create.service.impl;

import java.util.ArrayList;
import java.util.List;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.domain.*;
import com.ruoyi.create.mapper.CourseKikeStepMapper;
import com.ruoyi.create.mapper.TopicsReplyMapper;
import com.ruoyi.create.utils.Snowflake;
import com.ruoyi.system.api.RemoteUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.CourseTopicsRepliesMapper;
import com.ruoyi.create.service.ICourseTopicsRepliesService;

/**
 * 课程讨论话题回复Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-04
 */
@Service
public class CourseTopicsRepliesServiceImpl implements ICourseTopicsRepliesService 
{
    @Autowired
    private CourseTopicsRepliesMapper courseTopicsRepliesMapper;

    @Autowired
    private RemoteUserService userService;

    @Autowired
    private TopicsReplyMapper topicsReplyMapper;

    @Autowired
    private CourseKikeStepMapper courseKikeStepMapper;

    /**
     * 查询课程讨论话题回复
     * 
     * @param id 课程讨论话题回复主键
     * @return 课程讨论话题回复
     */
    @Override
    public CourseTopicsReplies selectCourseTopicsRepliesById(Long id)
    {
        return courseTopicsRepliesMapper.selectCourseTopicsRepliesById(id);
    }

    /**
     * 查询课程讨论话题回复列表
     * 
     * @param courseTopicsReplies 课程讨论话题回复
     * @return 课程讨论话题回复
     */
    @Override
    public List<CourseTopicsReplies> selectCourseTopicsRepliesList(CourseTopicsReplies courseTopicsReplies)
    {
        List<CourseTopicsReplies> courseTopicsRepliesList = courseTopicsRepliesMapper.selectCourseTopicsRepliesList(courseTopicsReplies);
        long uid = SecurityUtils.getUserId();
        AjaxResult ajaxResult = userService.getInfo(SecurityUtils.getUserId(), SecurityConstants.INNER);
        List<Integer> userRoleid = (ArrayList) ajaxResult.get("roleIds");

        for (CourseTopicsReplies topicsReplies : courseTopicsRepliesList) {
            List<TopicsReply> topicsReplyList = topicsReplyMapper.selectTopicsReply(topicsReplies.getMsgId());
            int count = topicsReplyMapper.countTopicsReply(topicsReplies.getMsgId());
            boolean isAdmin = userRoleid.contains(1) || userRoleid.contains(105);
            if (isAdmin || topicsReplies.getMsgUserId() == uid) {
                topicsReplies.setDeleteFlag(true);
                topicsReplyList.forEach(itemSMsgReply -> itemSMsgReply.setDeleteFlag(true));
            } else {
                topicsReplies.setDeleteFlag(false);
                topicsReplyList.stream()
                        .filter(s -> s.getReplyUserid() == uid)
                        .forEach(s -> s.setDeleteFlag(true));
            }
            //点赞数量
            int coutKike =  courseKikeStepMapper.countCourseKike(topicsReplies.getMsgId());
            //点踩数量
            int coutStep = courseKikeStepMapper.countCourseStep(topicsReplies.getMsgId());
            //当前用户赞踩
            CourseKikeStep courseKikeStep = new CourseKikeStep();
            courseKikeStep.setMsgId(topicsReplies.getMsgId());
            courseKikeStep.setCreateBy(SecurityUtils.getUsername());
            String  kikeStep=  courseKikeStepMapper.selectCourseKikeStepByUserName(courseKikeStep);
            topicsReplies.setTopicsReplyList(topicsReplyList);
            topicsReplies.setCount(count);
            topicsReplies.setCoutKike(coutKike);
            topicsReplies.setCoutStep(coutStep);
            if (kikeStep!=null){
                topicsReplies.setKikeStep(kikeStep);
            }
        }
        return courseTopicsRepliesList;
    }

    /**
     * 新增课程讨论话题回复
     * 
     * @param courseTopicsReplies 课程讨论话题回复
     * @return 结果
     */
    @Override
    public int insertCourseTopicsReplies(CourseTopicsReplies courseTopicsReplies)
    {
        Snowflake snowflake = new Snowflake(1, 1);
        long id = snowflake.generateId();
        courseTopicsReplies.setMsgId(id);
        courseTopicsReplies.setCreateTime(DateUtils.getNowDate());
        courseTopicsReplies.setMsgUserId(SecurityUtils.getUserId());
        return courseTopicsRepliesMapper.insertCourseTopicsReplies(courseTopicsReplies);
    }

    /**
     * 修改课程讨论话题回复
     * 
     * @param courseTopicsReplies 课程讨论话题回复
     * @return 结果
     */
    @Override
    public int updateCourseTopicsReplies(CourseTopicsReplies courseTopicsReplies)
    {
        return courseTopicsRepliesMapper.updateCourseTopicsReplies(courseTopicsReplies);
    }

    /**
     * 批量删除课程讨论话题回复
     * 
     * @param msgIds 需要删除的课程讨论话题回复主键
     * @return 结果
     */
    @Override
    public int deleteCourseTopicsRepliesByIds(Long[] msgIds)
    {
        topicsReplyMapper.deleteTopicsReplyByIds(msgIds);
        return courseTopicsRepliesMapper.deleteCourseTopicsRepliesByIds(msgIds);
    }

    /**
     * 删除课程讨论话题回复信息
     * 
     * @param id 课程讨论话题回复主键
     * @return 结果
     */
    @Override
    public int deleteCourseTopicsRepliesById(Long id)
    {
        return courseTopicsRepliesMapper.deleteCourseTopicsRepliesById(id);
    }
}
