package com.ruoyi.create.service;

import java.util.List;

import com.ruoyi.create.domain.CourseTopics;

/**
 * 课程讨论话题Service接口
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
public interface ICourseTopicsService {
    /**
     * 查询课程讨论话题
     *
     * @param id 课程讨论话题主键
     * @return 课程讨论话题
     */
    public CourseTopics selectCourseTopicsById(Long id);

    /**
     * 查询课程讨论话题列表
     *
     * @param courseTopics 课程讨论话题
     * @return 课程讨论话题集合
     */
    public List<CourseTopics> selectCourseTopicsList(CourseTopics courseTopics);

    /**
     * 新增课程讨论话题
     *
     * @param courseTopics 课程讨论话题
     * @return 结果
     */
    public int insertCourseTopics(CourseTopics courseTopics);

    /**
     * 修改课程讨论话题
     *
     * @param courseTopics 课程讨论话题
     * @return 结果
     */
    public int updateCourseTopics(CourseTopics courseTopics);

    /**
     * 批量删除课程讨论话题
     *
     * @param ids 需要删除的课程讨论话题主键集合
     * @return 结果
     */
    public int deleteCourseTopicsByIds(Long[] ids);

    /**
     * 删除课程讨论话题信息
     *
     * @param id 课程讨论话题主键
     * @return 结果
     */
    public int deleteCourseTopicsById(Long id);

    /**
     * 查询课程讨论话题列表 多条件查询
     */
    List<CourseTopics> selectCourseTopicsListCondition(CourseTopics courseTopics);

    /**
     * 批量删除课程讨论话题信息，并且删除回复信息
     */
    int deleteCourseTopicsAndReplaceByIds(Long[] ids);
}
