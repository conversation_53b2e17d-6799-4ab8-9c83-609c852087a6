package com.ruoyi.create.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.KnowledgeRoutingMapper;
import com.ruoyi.create.domain.KnowledgeRouting;
import com.ruoyi.create.service.IKnowledgeRoutingService;

/**
 * 知识库路由配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-30
 */
@Service
public class KnowledgeRoutingServiceImpl implements IKnowledgeRoutingService 
{
    @Autowired
    private KnowledgeRoutingMapper knowledgeRoutingMapper;

    /**
     * 查询知识库路由配置
     * 
     * @param id 知识库路由配置主键
     * @return 知识库路由配置
     */
    @Override
    public KnowledgeRouting selectKnowledgeRoutingById(Long id)
    {
        return knowledgeRoutingMapper.selectKnowledgeRoutingById(id);
    }

    /**
     * 查询知识库路由配置列表
     * 
     * @param knowledgeRouting 知识库路由配置
     * @return 知识库路由配置
     */
    @Override
    public List<KnowledgeRouting> selectKnowledgeRoutingList(KnowledgeRouting knowledgeRouting)
    {
        return knowledgeRoutingMapper.selectKnowledgeRoutingList(knowledgeRouting);
    }

    /**
     * 新增知识库路由配置
     * 
     * @param knowledgeRouting 知识库路由配置
     * @return 结果
     */
    @Override
    public int insertKnowledgeRouting(KnowledgeRouting knowledgeRouting)
    {
        knowledgeRouting.setCreateTime(DateUtils.getNowDate());
        return knowledgeRoutingMapper.insertKnowledgeRouting(knowledgeRouting);
    }

    /**
     * 修改知识库路由配置
     * 
     * @param knowledgeRouting 知识库路由配置
     * @return 结果
     */
    @Override
    public int updateKnowledgeRouting(KnowledgeRouting knowledgeRouting)
    {
        knowledgeRouting.setUpdateTime(DateUtils.getNowDate());
        return knowledgeRoutingMapper.updateKnowledgeRouting(knowledgeRouting);
    }

    /**
     * 批量删除知识库路由配置
     * 
     * @param ids 需要删除的知识库路由配置主键
     * @return 结果
     */
    @Override
    public int deleteKnowledgeRoutingByIds(Long[] ids)
    {
        return knowledgeRoutingMapper.deleteKnowledgeRoutingByIds(ids);
    }

    /**
     * 删除知识库路由配置信息
     * 
     * @param id 知识库路由配置主键
     * @return 结果
     */
    @Override
    public int deleteKnowledgeRoutingById(Long id)
    {
        return knowledgeRoutingMapper.deleteKnowledgeRoutingById(id);
    }


    @Override
    public List<KnowledgeRouting> selectKnowledgeByMenuRouting(String menuRouting)
    {
        return knowledgeRoutingMapper.selectKnowledgeByMenuRouting(menuRouting);
    }
}
