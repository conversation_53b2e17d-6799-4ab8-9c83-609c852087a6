package com.ruoyi.create.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.domain.*;
import com.ruoyi.create.mapper.CircularBannerConfigMapper;
import com.ruoyi.create.service.ICircularBannerConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.nio.file.*;
import java.nio.file.Path;
import java.util.*;

/**
 * 学校轮播图配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-04
 */
@Service
public class CircularBannerConfigServiceImpl implements ICircularBannerConfigService {

    @Value("${logo.path.file-path-win}")
    String localFilePathWin;// D:/ruoyi/uploadDataPath

    @Value("${logo.path.file-path-linux}")
    String localFilePathLinux;// /home/<USER>/uploadDataPath

    @Autowired
    private CircularBannerConfigMapper circularBannerConfigMapper;

    /**
     * 查询学校轮播图配置列表
     *
     * @param circularBannerConfig 学校轮播图配置
     * @return 学校轮播图配置
     */
    @Override
    public List<CircularBannerConfig> selectCircularBannerConfigList(CircularBannerConfig circularBannerConfig)
    {
        List<CircularBannerConfig> circularBannerConfigList = circularBannerConfigMapper.selectCircularBannerConfigList(circularBannerConfig);

        return circularBannerConfigList;
    }

    /**
     * 获取所有图片的路径
     *
     * @return 结果
     */
    public List<String> selectCircularBannerConfigListAll()
    {
        String univerName = circularBannerConfigMapper.selectUniversity(SecurityUtils.getUsername());
        List<CircularBannerConfig> circularBannerConfigListAll = circularBannerConfigMapper.selectCircularBannerConfigListAll(univerName);
        List<String> fileNameList = new ArrayList<>();
        List<String> filePathList = new ArrayList<>();
        if(circularBannerConfigListAll.size()==0){
            circularBannerConfigListAll = circularBannerConfigMapper.selectCircularBannerConfigBySchool("山东财经大学");
        }
        for (CircularBannerConfig circularBannerConfig : circularBannerConfigListAll) {
            fileNameList.add(circularBannerConfig.getFileName());
        }
        for (String s : fileNameList) {
            filePathList.add("/lbt/"+s);
        }
        return filePathList;
    }

    /**
     * 获得登录人所在学校名称
     * @param username 登录人名称
     * @return 结果
     */
    @Override
    public String selectUniversity(String username) {
        return circularBannerConfigMapper.selectUniversity(username);
    }

    /**
     * 上传校轮播图至服务器
     *@param file 轮播图图片
     * @return 结果
     */
    @Override
    public CircularBannerConfig uploadCircularBannerConfig(MultipartFile file) {
        try {
            String univerName = circularBannerConfigMapper.selectUniversity(SecurityUtils.getUsername());
            CircularBannerConfig circularBannerConfig = new CircularBannerConfig();
            long uniqueID = System.currentTimeMillis();
            // 获取上传的文件名 photo1.jpg
            String fileName = file.getOriginalFilename();
            fileName = uniqueID+"-"+fileName;

            String os = System.getProperty("os.name").toLowerCase();
            String speechdraftpath = "";
            if (os.contains("win")) {
                speechdraftpath = localFilePathWin;
            } else if (os.contains("nix") || os.contains("nux") || os.contains("mac")) {
                speechdraftpath = localFilePathLinux;
            } else {
                throw new UnsupportedOperationException("Unsupported operating system: " + os);
            }
            String filePath = speechdraftpath+"lbt";
            // 检查并创建目录
            Path uploadPath = Paths.get(filePath);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }
            // 保存文件到指定路径
            Path filePath1 = uploadPath.resolve(fileName);
            Files.write(filePath1, file.getBytes());
            circularBannerConfig.setFileName(fileName);
            circularBannerConfig.setFileId(String.valueOf(uniqueID));
            circularBannerConfig.setSchool(univerName);
            return circularBannerConfig;
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 提交校轮播图配置
     *
     * @return 结果
     */
    @Override
    public int updateCircularBannerConfigByFileName(CircularBannerConfig circularBannerConfig) {
        List<CircularBannerConfig> circularBannerConfigList = circularBannerConfigMapper.selectCircularBannerConfigByFileName(circularBannerConfig.getFileName());
        if(circularBannerConfigList.size()==0){
            String ssUrl="";
            String os = System.getProperty("os.name").toLowerCase();
            if (os.contains("win")) {
                ssUrl=localFilePathWin;
            } else if (os.contains("nix") || os.contains("nux") || os.contains("mac")) {
                ssUrl=localFilePathLinux;
            } else {
                throw new UnsupportedOperationException("Unsupported operating system: " + os);
            }
            circularBannerConfig.setImageUrl(ssUrl+"lbt");
            circularBannerConfig.setCreateTime(DateUtils.getNowDate());
            circularBannerConfig.setCreateBy(SecurityUtils.getUsername());
        }
        return circularBannerConfigMapper.insertCircularBannerConfig(circularBannerConfig);
    }


    /**
     * 查询知学校轮播图配置
     *
     * @param id 学校轮播图配置主键
     * @return 学校轮播图配置
     */
    @Override
    public CircularBannerConfig selectCircularBannerConfigById(Long id)
    {
        return circularBannerConfigMapper.selectCircularBannerConfigById(id);
    }

    /**
     * 修改学校轮播图配置
     *
     * @param circularBannerConfig 学校轮播图配置
     * @return 结果
     */
    @Override
    public int updateCircularBannerConfig(CircularBannerConfig circularBannerConfig)
    {
        List<CircularBannerConfig> circularBannerConfigList = circularBannerConfigMapper.selectCircularBannerConfigByFileName(circularBannerConfig.getFileName());
        if(circularBannerConfigList.size()>0){
            circularBannerConfig.setUpdateTime(DateUtils.getNowDate());
            circularBannerConfig.setUpdateBy(SecurityUtils.getUsername());
        }
        return circularBannerConfigMapper.updateCircularBannerConfig(circularBannerConfig);
    }

    /**
     * 删除学校轮播图配置信息
     *
     * @param id 学校轮播图配置主键
     * @return 结果
     */
    @Override
    public int deleteCircularBannerConfigById(Long id)
    {
        CircularBannerConfig circularBannerConfig = circularBannerConfigMapper.selectCircularBannerConfigById(id);
        String ssUrl="";
        String os = System.getProperty("os.name").toLowerCase();
        if (os.contains("win")) {
            ssUrl=localFilePathWin;
        } else if (os.contains("nix") || os.contains("nux") || os.contains("mac")) {
            ssUrl=localFilePathLinux;
        } else {
            throw new UnsupportedOperationException("Unsupported operating system: " + os);
        }
        File file = new File(ssUrl+"lbt/"+circularBannerConfig.getFileName());
        // 检查文件是否存在
        if (file.exists()) {
            // 尝试删除文件
            file.delete();
        }
        return circularBannerConfigMapper.deleteCircularBannerConfigById(id);
    }

    /**
     * 根据图片路径将图片转换成字节
     * @param filePath
     * @return
     * @throws IOException
     */
    public String  processing(String filePath) throws IOException {
        File file = new File(filePath); // 图片的实际路径
        BufferedImage image = ImageIO.read(file);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, getFileExtension(filePath), baos);
        baos.flush();
        byte[] imageBytes = baos.toByteArray();
        baos.close();
        return Base64.getEncoder().encodeToString(imageBytes);
    }

    /**
     * 根据图片名称或路径获取图片后缀
     * @param fileName
     * @return
     */
    public String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return ""; // 如果文件名为空，返回空字符串
        }
        int dotIndex = fileName.lastIndexOf('.'); // 查找最后一个'.'的位置
        if (dotIndex == -1 || dotIndex == fileName.length() - 1) {
            return ""; // 如果没有找到'.'或者'.'在字符串末尾，说明没有扩展名
        }
        return fileName.substring(dotIndex + 1); // 返回'.'之后的部分，即文件扩展名
    }

}


