package com.ruoyi.create.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.utils.Snowflake;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.CourseCaseMapper;
import com.ruoyi.create.domain.CourseCase;
import com.ruoyi.create.service.ICourseCaseService;

import javax.annotation.Resource;

/**
 * 课程案例Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-27
 */
@Service
public class CourseCaseServiceImpl implements ICourseCaseService 
{
    @Autowired
    private CourseCaseMapper courseCaseMapper;

    @Resource
    private RemoteFileService remoteFileService;

    @Resource
    private RemoteUserService remoteUserService;

    /**
     * 查询课程案例
     * 
     * @param id 课程案例主键
     * @return 课程案例
     */
    @Override
    public CourseCase selectCourseCaseById(Long id)
    {
        return courseCaseMapper.selectCourseCaseById(id);
    }

    /**
     * 查询课程案例列表
     * 
     * @param courseCase 课程案例
     * @return 课程案例
     */
    @Override
    public List<CourseCase> selectCourseCaseList(CourseCase courseCase)
    {
        R<LoginUser> userAndRole = remoteUserService.getUserAndRole(SecurityUtils.getUserId(), SecurityConstants.INNER);
        LoginUser data2 = userAndRole.getData();
        String jobId = data2.getSysUser().getJobId();
        courseCase.setTeacherId(jobId);
        return courseCaseMapper.selectCourseCaseList(courseCase);
    }

    /**
     * 新增课程案例
     * 
     * @param courseCase 课程案例
     * @return 结果
     */
    @Override
    public int insertCourseCase(CourseCase courseCase)
    {
        Snowflake snowflake = new Snowflake(1, 1);
        long id = snowflake.generateId();
        if(ObjectUtils.isNotEmpty(courseCase.getFileIds())){
            remoteFileService.relationFile(courseCase.getFileIds(),String.valueOf(id));
        }
        R<LoginUser> userAndRole = remoteUserService.getUserAndRole(SecurityUtils.getUserId(), SecurityConstants.INNER);
        LoginUser data2 = userAndRole.getData();
        String jobId = data2.getSysUser().getJobId();
        courseCase.setId(id);
        courseCase.setTeacherId(jobId);
        courseCase.setCreateBy(SecurityUtils.getUsername());
        courseCase.setCreateTime(DateUtils.getNowDate());
        return courseCaseMapper.insertCourseCase(courseCase);
    }

    /**
     * 修改课程案例
     * 
     * @param courseCase 课程案例
     * @return 结果
     */
    @Override
    public int updateCourseCase(CourseCase courseCase)
    {
        courseCase.setUpdateTime(DateUtils.getNowDate());
        return courseCaseMapper.updateCourseCase(courseCase);
    }

    /**
     * 批量删除课程案例
     * 
     * @param ids 需要删除的课程案例主键
     * @return 结果
     */
    @Override
    public int deleteCourseCaseByIds(Long[] ids)
    {
        remoteFileService.deleteFile(ids[0].toString());
        return courseCaseMapper.deleteCourseCaseByIds(ids);
    }

    /**
     * 删除课程案例信息
     * 
     * @param id 课程案例主键
     * @return 结果
     */
    @Override
    public int deleteCourseCaseById(Long id)
    {
        return courseCaseMapper.deleteCourseCaseById(id);
    }

    @Override
    public List<CourseCase> selectStudentCourseCaseList(CourseCase courseCase) {
        return courseCaseMapper.selectCourseCaseList(courseCase);
    }
}
