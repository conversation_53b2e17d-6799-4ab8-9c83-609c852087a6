package com.ruoyi.create.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.domain.SImage;
import com.ruoyi.create.mapper.SImageMapper;
import com.ruoyi.create.service.ISImageService;
import com.ruoyi.create.utils.UserUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.List;

/**
 * 系统图片Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-05
 */
@Service
public class SImageServiceImpl implements ISImageService
{
    private Logger logger = LoggerFactory.getLogger(SImageServiceImpl.class);

    @Autowired
    private SImageMapper sImageMapper;

    @Autowired
    private UserUtils userUtils;

    /**
     * 查询系统图片
     * 
     * @param id 系统图片主键
     * @return 系统图片
     */
    @Override
    public SImage selectSImageById(Long id)
    {
        return sImageMapper.selectSImageById(id);
    }

    /**
     * 查询系统图片列表
     * 
     * @param sImage 系统图片
     * @return 系统图片
     */
    @Override
    public List<SImage> selectSImageList(SImage sImage)
    {
        return sImageMapper.selectSImageList(sImage);
    }

    /**
     * 新增系统图片
     * 
     * @param sImage 系统图片
     * @return 结果
     */
    @Override
    public int insertSImage(SImage sImage)
    {
        sImage.setCreateTime(DateUtils.getNowDate());
        sImage.setCreateBy(userUtils.getNickName(SecurityUtils.getUserId()));
        sImage.setImageFlag("3");
        return sImageMapper.insertSImage(sImage);
    }

    /**
     * 修改系统图片
     * 
     * @param sImage 系统图片
     * @return 结果
     */
    @Override
    public int updateSImage(SImage sImage)
    {
        sImage.setUpdateTime(DateUtils.getNowDate());
        return sImageMapper.updateSImage(sImage);
    }

    /**
     * 批量删除系统图片
     * 
     * @param ids 需要删除的系统图片主键
     * @return 结果
     */
    @Override
    public int deleteSImageByIds(Long[] ids)
    {

        return sImageMapper.deleteSImageByIds(ids);
    }

    /**
     * 删除系统图片信息
     * 
     * @param id 系统图片主键
     * @return 结果
     */
    @Override
    public int deleteSImageById(Long id)
    {
        return sImageMapper.deleteSImageById(id);
    }

    @Override
    public List<SImage> selectImageAll() {
        return sImageMapper.selectImageAll();
    }

    /**
     * 删除文件
     * @param filePath
     */
    public boolean deleteFileAndEmptyParentIfExists(String filePath) {
        File file = new File(filePath);

        // 检查文件是否存在
        if (!file.exists()) {
            logger.warn("文件不存在，无法删除");
            return false;
        }

        // 尝试删除文件
        if (!file.delete()) {
            logger.error("文件删除失败，可能是文件正在被使用或者权限不足");
            return false;
        }
        logger.info("文件已成功删除");

        // 删除文件后检查父目录是否为空
        File parentDir = file.getParentFile();
        if (parentDir != null && isDirectoryEmpty(parentDir) && deleteDirectoryIfEmpty(parentDir)) {
            logger.info("空目录 {} 已成功删除", parentDir.getAbsolutePath());

            return true;
        }
        return true; // 如果没有空目录需要删除，或删除操作已成功，返回true
    }

    private boolean isDirectoryEmpty(File directory) {
        String[] contents = directory.list();
        return contents == null || contents.length == 0;
    }

    private boolean deleteDirectoryIfEmpty(File directory) {
        // 确保是目录且为空
        if (directory.isDirectory() && isDirectoryEmpty(directory)) {
            return directory.delete();
        }
        return false;
    }
}
