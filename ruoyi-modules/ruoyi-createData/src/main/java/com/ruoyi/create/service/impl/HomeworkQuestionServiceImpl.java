package com.ruoyi.create.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.create.domain.HomeworkQuestion;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.HomeworkQuestionMapper;
import com.ruoyi.create.service.IHomeworkQuestionService;

/**
 * 作业题目Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-07
 */
@Service
public class HomeworkQuestionServiceImpl extends ServiceImpl<HomeworkQuestionMapper, HomeworkQuestion> implements IHomeworkQuestionService
{
    @Autowired
    private HomeworkQuestionMapper homeworkQuestionMapper;

    /**
     * 查询作业题目
     *
     * @param id 作业题目主键
     * @return 作业题目
     */
    @Override
    public HomeworkQuestion selectHomeworkQuestionById(Long id)
    {
        return homeworkQuestionMapper.selectHomeworkQuestionById(id);
    }

    /**
     * 查询作业题目列表
     *
     * @param homeworkQuestion 作业题目
     * @return 作业题目
     */
    @Override
    public List<HomeworkQuestion> selectHomeworkQuestionList(HomeworkQuestion homeworkQuestion)
    {
        return homeworkQuestionMapper.selectHomeworkQuestionList(homeworkQuestion);
    }

    /**
     * 新增作业题目
     *
     * @param homeworkQuestion 作业题目
     * @return 结果
     */
    @Override
    public int insertHomeworkQuestion(HomeworkQuestion homeworkQuestion)
    {
        return homeworkQuestionMapper.insertHomeworkQuestion(homeworkQuestion);
    }

    /**
     * 修改作业题目
     *
     * @param homeworkQuestion 作业题目
     * @return 结果
     */
    @Override
    public int updateHomeworkQuestion(HomeworkQuestion homeworkQuestion)
    {
        return homeworkQuestionMapper.updateHomeworkQuestion(homeworkQuestion);
    }

    /**
     * 批量删除作业题目
     *
     * @param ids 需要删除的作业题目主键
     * @return 结果
     */
    @Override
    public int deleteHomeworkQuestionByIds(Long[] ids)
    {
        return homeworkQuestionMapper.deleteHomeworkQuestionByIds(ids);
    }

    /**
     * 删除作业题目信息
     *
     * @param id 作业题目主键
     * @return 结果
     */
    @Override
    public int deleteHomeworkQuestionById(Long id)
    {
        return homeworkQuestionMapper.deleteHomeworkQuestionById(id);
    }
}
