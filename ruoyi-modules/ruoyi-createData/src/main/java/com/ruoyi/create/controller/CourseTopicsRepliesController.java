package com.ruoyi.create.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.CourseTopicsReplies;
import com.ruoyi.create.service.ICourseTopicsRepliesService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 课程讨论话题回复Controller
 * 
 * <AUTHOR>
 * @date 2024-12-04
 */
@RestController
@RequestMapping("/replies")
public class CourseTopicsRepliesController extends BaseController
{
    @Autowired
    private ICourseTopicsRepliesService courseTopicsRepliesService;

    /**
     * 查询课程讨论话题回复列表
     */
    @GetMapping("/list")
    public TableDataInfo list(CourseTopicsReplies courseTopicsReplies)
    {
        startPage();
        List<CourseTopicsReplies> list = courseTopicsRepliesService.selectCourseTopicsRepliesList(courseTopicsReplies);
        return getDataTable(list);
    }

    /**
     * 导出课程讨论话题回复列表
     */
    @Log(title = "课程讨论话题回复", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CourseTopicsReplies courseTopicsReplies)
    {
        List<CourseTopicsReplies> list = courseTopicsRepliesService.selectCourseTopicsRepliesList(courseTopicsReplies);
        ExcelUtil<CourseTopicsReplies> util = new ExcelUtil<CourseTopicsReplies>(CourseTopicsReplies.class);
        util.exportExcel(response, list, "课程讨论话题回复数据");
    }

    /**
     * 获取课程讨论话题回复详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(courseTopicsRepliesService.selectCourseTopicsRepliesById(id));
    }

    /**
     * 新增课程讨论话题回复
     */
    @Log(title = "课程讨论话题回复", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CourseTopicsReplies courseTopicsReplies)
    {
        return toAjax(courseTopicsRepliesService.insertCourseTopicsReplies(courseTopicsReplies));
    }

    /**
     * 修改课程讨论话题回复
     */
    @Log(title = "课程讨论话题回复", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CourseTopicsReplies courseTopicsReplies)
    {
        return toAjax(courseTopicsRepliesService.updateCourseTopicsReplies(courseTopicsReplies));
    }

    /**
     * 删除课程讨论话题回复
     */
    @Log(title = "课程讨论话题回复", businessType = BusinessType.DELETE)
	@DeleteMapping("/{msgIds}")
    public AjaxResult remove(@PathVariable Long[] msgIds)
    {
        return toAjax(courseTopicsRepliesService.deleteCourseTopicsRepliesByIds(msgIds));
    }
}
