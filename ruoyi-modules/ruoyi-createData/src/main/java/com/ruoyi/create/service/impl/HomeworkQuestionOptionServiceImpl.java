package com.ruoyi.create.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.create.domain.HomeworkQuestion;
import com.ruoyi.create.domain.HomeworkQuestionOption;
import com.ruoyi.create.mapper.HomeworkQuestionMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.HomeworkQuestionOptionMapper;
import com.ruoyi.create.service.IHomeworkQuestionOptionService;

/**
 * 作业题目选项Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-07
 */
@Service
public class HomeworkQuestionOptionServiceImpl extends ServiceImpl<HomeworkQuestionOptionMapper, HomeworkQuestionOption> implements IHomeworkQuestionOptionService
{
    @Autowired
    private HomeworkQuestionOptionMapper homeworkQuestionOptionMapper;

    /**
     * 查询作业题目选项
     *
     * @param id 作业题目选项主键
     * @return 作业题目选项
     */
    @Override
    public HomeworkQuestionOption selectHomeworkQuestionOptionById(Long id)
    {
        return homeworkQuestionOptionMapper.selectHomeworkQuestionOptionById(id);
    }

    /**
     * 查询作业题目选项列表
     *
     * @param homeworkQuestionOption 作业题目选项
     * @return 作业题目选项
     */
    @Override
    public List<HomeworkQuestionOption> selectHomeworkQuestionOptionList(HomeworkQuestionOption homeworkQuestionOption)
    {
        return homeworkQuestionOptionMapper.selectHomeworkQuestionOptionList(homeworkQuestionOption);
    }

    /**
     * 新增作业题目选项
     *
     * @param homeworkQuestionOption 作业题目选项
     * @return 结果
     */
    @Override
    public int insertHomeworkQuestionOption(HomeworkQuestionOption homeworkQuestionOption)
    {
        return homeworkQuestionOptionMapper.insertHomeworkQuestionOption(homeworkQuestionOption);
    }

    /**
     * 修改作业题目选项
     *
     * @param homeworkQuestionOption 作业题目选项
     * @return 结果
     */
    @Override
    public int updateHomeworkQuestionOption(HomeworkQuestionOption homeworkQuestionOption)
    {
        return homeworkQuestionOptionMapper.updateHomeworkQuestionOption(homeworkQuestionOption);
    }

    /**
     * 批量删除作业题目选项
     *
     * @param ids 需要删除的作业题目选项主键
     * @return 结果
     */
    @Override
    public int deleteHomeworkQuestionOptionByIds(Long[] ids)
    {
        return homeworkQuestionOptionMapper.deleteHomeworkQuestionOptionByIds(ids);
    }

    /**
     * 删除作业题目选项信息
     *
     * @param id 作业题目选项主键
     * @return 结果
     */
    @Override
    public int deleteHomeworkQuestionOptionById(Long id)
    {
        return homeworkQuestionOptionMapper.deleteHomeworkQuestionOptionById(id);
    }
}
