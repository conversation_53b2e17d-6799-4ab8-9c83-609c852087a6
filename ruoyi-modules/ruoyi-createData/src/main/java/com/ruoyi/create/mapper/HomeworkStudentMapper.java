package com.ruoyi.create.mapper;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.create.domain.HomeworkStudent;
import org.apache.ibatis.annotations.MapKey;

/**
 * 作业状态Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-07
 */
public interface HomeworkStudentMapper extends BaseMapper<HomeworkStudent>
{
    /**
     * 查询作业状态
     *
     * @param id 作业状态主键
     * @return 作业状态
     */
    public HomeworkStudent selectHomeworkStudentById(Long id);

    /**
     * 查询作业状态列表
     *
     * @param homeworkStudent 作业状态
     * @return 作业状态集合
     */
    public List<HomeworkStudent> selectHomeworkStudentList(HomeworkStudent homeworkStudent);

	/**
	 * 查询作业状态列表数量
	 *
	 * @param homeworkStudent 作业状态
	 * @return 作业状态集合
	 */
	public String selectHomeworkStudentListCount(HomeworkStudent homeworkStudent);
	
    /**
     * 新增作业状态
     *
     * @param homeworkStudent 作业状态
     * @return 结果
     */
    public int insertHomeworkStudent(HomeworkStudent homeworkStudent);

    /**
     * 修改作业状态
     *
     * @param homeworkStudent 作业状态
     * @return 结果
     */
    public int updateHomeworkStudent(HomeworkStudent homeworkStudent);

    /**
     * 删除作业状态
     *
     * @param id 作业状态主键
     * @return 结果
     */
    public int deleteHomeworkStudentById(Long id);

    /**
     * 批量删除作业状态
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHomeworkStudentByIds(Long[] ids);

    List<HomeworkStudent> selectCorrectHomeworkStudentList(HomeworkStudent homeworkStudent);

    int updateHomeworkStudentByHmId(HomeworkStudent homeworkStudent);

	List<HomeworkStudent> selectClaByHId(Long hmId);

	void updateHomeworkToClearCommitTime(HomeworkStudent homeworkStudent);

	@MapKey("id")
	Map<Long, Map<String, String>> selectClassByHId(Long classId);
}
