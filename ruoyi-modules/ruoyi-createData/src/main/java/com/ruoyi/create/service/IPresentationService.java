package com.ruoyi.create.service;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import com.baidubce.appbuilder.base.exception.AppBuilderServerException;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.create.domain.KnowledgeBaseFile;
import com.ruoyi.create.domain.Presentation;
import com.ruoyi.create.dto.UserDto;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 老师课程Service接口
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
public interface IPresentationService
{
    /**
     * 查询老师课程
     *
     * @param id 老师课程主键
     * @return 老师课程
     */
    public Presentation selectPresentationById(Long id);

    /**
     * 查询老师课程列表
     *
     * @param presentation 老师课程
     * @return 老师课程集合
     */
    public List<Presentation> selectPresentationList(Presentation presentation);
    public List<Presentation> selectPresentationListGroupByCourse(Presentation presentation);
    /**
     * 新增老师课程
     *
     * @param presentation 老师课程
     * @return 结果
     */
    public int insertPresentation(Presentation presentation) throws IOException, AppBuilderServerException;

    /**
     * 修改老师课程
     *
     * @param presentation 老师课程
     * @return 结果
     */
    public int updatePresentation(Presentation presentation) throws IOException, AppBuilderServerException;

    /**
     * 批量删除老师课程
     *
     * @param ids 需要删除的老师课程主键集合
     * @return 结果
     */
    public int deletePresentationByIds(Long[] ids);

    /**
     * 删除老师课程信息
     *
     * @param id 老师课程主键
     * @return 结果
     */
    public int deletePresentationById(Long id);

    public int selectUByT(Long id);
    public int selectCByT(Long id);
    public UserDto selectUserByUserName(String username);
    public List<String> selectUserRoleKeyByUserName(String username);



//    Map<String, Object> uploadPPt(MultipartFile file,String modeltype);
//    Map<String, Object> uploadPPtOld(MultipartFile file);

//    Map<String, Object> uploadSpeechDraftOld(MultipartFile file);


    String selectNameById(String id);
    void getTxt(HttpServletResponse response, Presentation speechdraftpath);
    void getTxt2(HttpServletResponse response, String speechdraftpath);

    Boolean handleSubmit(Presentation presentation);

//    void getTxtDemo(HttpServletResponse response);
//
    void download(HttpServletRequest request, HttpServletResponse response,Long fileId,int remark);

    void saveConsonant(String path,String presentationId) throws IOException, AppBuilderServerException;

    public List<KnowledgeBaseFile> selectTxtIdByCourse(KnowledgeBaseFile knowledgeBaseFile);

    public List<String> selectChapterById(Long id);
}
