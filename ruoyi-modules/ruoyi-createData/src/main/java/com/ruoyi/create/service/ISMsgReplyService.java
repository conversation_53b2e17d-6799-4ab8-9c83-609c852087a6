package com.ruoyi.create.service;

import com.ruoyi.create.domain.SMessage;
import com.ruoyi.create.domain.SMsgReply;

import java.util.List;

/**
 * 留言与回复关联Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-05
 */
public interface ISMsgReplyService 
{
    /**
     * 查询留言与回复关联
     * 
     * @param msgId 留言与回复关联主键
     * @return 留言与回复关联
     */
    public SMsgReply selectSMsgReplyByMsgId(Long msgId);

    /**
     * 查询留言与回复关联列表
     * 
     * @param sMsgReply 留言与回复关联
     * @return 留言与回复关联集合
     */
    public List<SMsgReply> selectSMsgReplyList(SMsgReply sMsgReply);

    /**
     * 新增留言与回复关联
     * 
     * @param sMsgReply 留言与回复关联
     * @return 结果
     */
    public int insertSMsgReply(SMsgReply sMsgReply);

    /**
     * 修改留言与回复关联
     * 
     * @param sMsgReply 留言与回复关联
     * @return 结果
     */
    public int updateSMsgReply(SMsgReply sMsgReply);

    /**
     * 批量删除留言与回复关联
     * 
     * @param msgIds 需要删除的留言与回复关联主键集合
     * @return 结果
     */
    public int deleteSMsgReplyByMsgIds(Long[] msgIds);

    /**
     * 删除留言与回复关联信息
     * 
     * @param msgId 留言与回复关联主键
     * @return 结果
     */
    public int deleteSMsgReplyByMsgId(Long msgId);

    List<SMessage>  selectMsgAndReplyAll(long msgId);

    int deleteSMsgReplyByReplyIds(Long[] replyIds);
}
