package com.ruoyi.create.service;

import java.util.List;
import com.ruoyi.create.domain.StudentPromotion;
import com.ruoyi.create.domain.StudentPromotionVO;

/**
 * 提升课程推送Service接口
 *
 * <AUTHOR>
 * @date 2024-08-13
 */
public interface IStudentPromotionService
{
    /**
     * 查询提升课程推送
     *
     * @param id 提升课程推送主键
     * @return 提升课程推送
     */
    public StudentPromotion selectStudentPromotionById(String id);

    /**
     * 查询提升课程推送列表
     *
     * @param studentPromotion 提升课程推送
     * @return 提升课程推送集合
     */
    public List<StudentPromotion> selectStudentPromotionList(StudentPromotion studentPromotion);

    /**
     * 新增提升课程推送
     *
     * @param studentPromotion 提升课程推送
     * @return 结果
     */
    public int insertOrUpdateStudentPromotion(StudentPromotion studentPromotion);

    /**
     * 修改提升课程推送
     *
     * @param studentPromotion 提升课程推送
     * @return 结果
     */
    public int updateStudentPromotion(StudentPromotion studentPromotion);

    /**
     * 批量删除提升课程推送
     *
     * @param ids 需要删除的提升课程推送主键集合
     * @return 结果
     */
    public int deleteStudentPromotionByIds(Long[] ids);

    /**
     * 删除提升课程推送信息
     *
     * @param id 提升课程推送主键
     * @return 结果
     */
    public int deleteStudentPromotionById(Long id);

    /**
     * 根据学生id查询学生提升课程
     *
     * @return
     */
    List<StudentPromotionVO> selectStudentPromotionByStudentId(StudentPromotionVO studentPromotion);

}
