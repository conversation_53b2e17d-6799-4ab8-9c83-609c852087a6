package com.ruoyi.create.mapper;

import java.util.List;

import com.ruoyi.create.domain.*;
import org.apache.ibatis.annotations.Param;

/**
 * 文献整理- 教材原始数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-31
 */
public interface STextbookDataMapper 
{
    /**
     * 查询文献整理- 教材原始数据
     * 
     * @param id 文献整理- 教材原始数据主键
     * @return 文献整理- 教材原始数据
     */
    public TextbookData selectSTextbookDataById(Long id);

    /**
     * 查询文献整理- 教材原始数据列表
     * 
     * @param sTextbookData 文献整理- 教材原始数据
     * @return 文献整理- 教材原始数据集合
     */
    public List<TextbookData> selectSTextbookDataList(TextbookData sTextbookData);

    /**
     * 新增文献整理- 教材原始数据
     * 
     * @param sTextbookData 文献整理- 教材原始数据
     * @return 结果
     */
    public int insertSTextbookData(TextbookData sTextbookData);

    /**
     * 修改文献整理- 教材原始数据
     * 
     * @param sTextbookData 文献整理- 教材原始数据
     * @return 结果
     */
    public int updateSTextbookData(TextbookData sTextbookData);

    /**
     * 删除文献整理- 教材原始数据
     * 
     * @param id 文献整理- 教材原始数据主键
     * @return 结果
     */
    public int deleteSTextbookDataById(Long id);

    /**
     * 批量删除文献整理- 教材原始数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSTextbookDataByIds(Long[] ids);

    int insertSTextbookDataList(List <TextbookData> sTextbookData);

    List<TextbookData> selectTextbookDataKnowledgeGraph(TextbookData sTextbookData);

    List<TextbookData> selectByCourseName(String[] keyArry);

    public List<MindMapping> selectMindMappingList();

    List<ThinkingKeyword> getThinkingKeyword(@Param("id") Long id);

    int selectCountSubject();

    int selectCountCourse();

    int selectCountKnowledge();

    List<ProductSales> selectProductSalesList();
}
