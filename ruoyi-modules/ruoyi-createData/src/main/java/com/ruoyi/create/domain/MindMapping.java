package com.ruoyi.create.domain;

import com.ruoyi.common.core.annotation.Excel;

public class MindMapping {
    private static final long serialVersionUID = 1L;

    /** 学科门类 */
    @Excel(name = "学科门类")
    private String academicDiscipline;

    /** 一级学科 */
    @Excel(name = "一级学科")
    private String colleName;

    /** 二级学科 */
    @Excel(name = "二级学科")
    private String majorName;

    /** 课程 */
    @Excel(name = "课程")
    private String courseName;

    /**
     * 教材
     */
    private String fileName;

    /** id */
    private Long id;


    public void setAcademicDiscipline(String academicDiscipline)
    {
        this.academicDiscipline = academicDiscipline;
    }

    public String getAcademicDiscipline()
    {
        return academicDiscipline;
    }
    public void setColleName(String colleName)
    {
        this.colleName = colleName;
    }

    public String getColleName()
    {
        return colleName;
    }
    public void setMajorName(String majorName)
    {
        this.majorName = majorName;
    }

    public String getMajorName()
    {
        return majorName;
    }
    public void setCourseName(String courseName)
    {
        this.courseName = courseName;
    }

    public String getCourseName()
    {
        return courseName;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}
