package com.ruoyi.create.controller;

import com.ruoyi.create.service.IMajorInfoService;
import com.ruoyi.create.service.MappingKnowledgeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/mappingKnowledge")
public class MappingKnowledgeController {

    @Autowired
    private MappingKnowledgeService mappingKnowledgeService;
    @PostMapping("/CountMappingKnowledge")
    public void CountMappingKnowledge() {
        mappingKnowledgeService.CountMappingKnowledge();

    }

}
