package com.ruoyi.create.controller;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.github.pagehelper.PageInfo;
import com.ruoyi.common.core.constant.HttpStatus;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.domain.CircularBannerConfig;
import com.ruoyi.create.mapper.CircularBannerConfigMapper;
import com.ruoyi.create.service.ICircularBannerConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 学校轮播图配置Controller
 *
 * <AUTHOR>
 * @date 2024-09-04
 */
@RestController
@RequestMapping("/circularbannerconfig")
public class CircularBannerConfigController extends BaseController
{

    @Value("${logo.path.file-path-win}")
    String localFilePathWin;// D:/ruoyi/uploadDataPath

    @Value("${logo.path.file-path-linux}")
    String localFilePathLinux;// /home/<USER>/uploadDataPath

    @Autowired
    private ICircularBannerConfigService circularBannerConfigService;

    @Autowired
    private CircularBannerConfigMapper circularBannerConfigMapper;

    /**
     * 查询学校轮播图配置列表
     */
    @GetMapping("/list")
    public TableDataInfo list(CircularBannerConfig circularBannerConfig)
    {
        String univerName = circularBannerConfigMapper.selectUniversity(SecurityUtils.getUsername());
        circularBannerConfig.setSchool(univerName);
        startPage();
        List<CircularBannerConfig> list = circularBannerConfigService.selectCircularBannerConfigList(circularBannerConfig);

        List<CircularBannerConfig> circularBannerConfigList = circularBannerConfigMapper.selectCircularBannerConfigList(circularBannerConfig);
        List<CircularBannerConfig> cList = new ArrayList<CircularBannerConfig>();
        for (int i=0;i<circularBannerConfigList.size();i++) {

            CircularBannerConfig c = circularBannerConfigList.get(i);
            String ssUrl="";
            String imagename=c.getFileName();
            String os = System.getProperty("os.name").toLowerCase();
            if (os.contains("win")) {
                ssUrl=localFilePathWin+"lbt";
            } else if (os.contains("nix") || os.contains("nux") || os.contains("mac")) {
                ssUrl=localFilePathLinux+"lbt";
            } else {
                throw new UnsupportedOperationException("Unsupported operating system: " + os);
            }
            // 使用 Path 方式安全获取最后一级目录名，兼容 Windows/Linux
            Path path = Paths.get(ssUrl);
            String lastDirectoryName = path.getFileName().toString();

// 构建图片 URL（作为前端访问路径，保留 / 分隔）
            String imageUrl = lastDirectoryName + "/" + imagename;
            List<Map<String, String>> presentationFileList = new ArrayList<>();
            // 创建第一个文件的Map
            Map<String, String> file1 = new HashMap<>();
            file1.put("name", imagename != null ? imagename : "");
            file1.put("url", imageUrl != null ? imageUrl : "");
            presentationFileList.add(file1);
            cList.add(
                    CircularBannerConfig.builder().id(c.getId())
                            .fileName(c.getFileName())
                            .fileId(c.getFileId())
                            .title(c.getTitle())
                            .imageUrl(c.getImageUrl())
                            .description(c.getDescription())
                            .school(c.getSchool())
                            .createBy(c.getCreateBy())
                            .createTime(c.getCreateTime())
                            .updateBy(c.getUpdateBy())
                            .updateTime(c.getUpdateTime())
                            .presentationFileList(presentationFileList)
                            .build()
            );
        }
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setRows(cList);
        rspData.setMsg("查询成功");
        rspData.setTotal(new PageInfo(cList).getTotal());
        return rspData;
    }

    /**
     * 上传学校轮播图配置
     */
    @PostMapping("/upload")
    public @ResponseBody CircularBannerConfig upload(@RequestParam("file") MultipartFile file) {
        return circularBannerConfigService.uploadCircularBannerConfig(file);
    }

    /**
     * 提交学校轮播图配置
     */
    @Log(title = "上传学校轮播图配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CircularBannerConfig circularBannerConfig){
        return toAjax(circularBannerConfigService.updateCircularBannerConfigByFileName(circularBannerConfig));
    }

    /**
     * 获取所有图片的路径
     */
    @GetMapping("/listAll")
    public List<String> listAll()
    {
        return circularBannerConfigService.selectCircularBannerConfigListAll();
    }

    /**
     * 获取学校轮播图配置详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(circularBannerConfigService.selectCircularBannerConfigById(id));
    }

    /**
     * 修改学校轮播图配置
     */
    @Log(title = "学校轮播图配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CircularBannerConfig circularBannerConfig)
    {
        return toAjax(circularBannerConfigService.updateCircularBannerConfig(circularBannerConfig));
    }

    /**
     * 删除学校轮播图配置
     */
    @Log(title = "学校轮播图配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id)
    {
        return toAjax(circularBannerConfigService.deleteCircularBannerConfigById(id));
    }

    /**
     * 导出学校轮播图配置列表
     */
    @Log(title = "学校轮播图配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CircularBannerConfig circularBannerConfig)
    {
        List<CircularBannerConfig> list = circularBannerConfigService.selectCircularBannerConfigList(circularBannerConfig);
        ExcelUtil<CircularBannerConfig> util = new ExcelUtil<CircularBannerConfig>(CircularBannerConfig.class);
        util.exportExcel(response, list, "学校轮播图配置数据");
    }

    /**
     *  获得登录人所在学校名称
     */
    @GetMapping("/selUniverName")
    public AjaxResult getUniverName()
    {
        return success(circularBannerConfigService.selectUniversity(SecurityUtils.getUsername()));
    }
}
