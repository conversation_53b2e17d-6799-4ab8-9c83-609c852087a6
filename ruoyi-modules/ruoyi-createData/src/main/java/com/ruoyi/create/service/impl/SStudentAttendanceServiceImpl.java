package com.ruoyi.create.service.impl;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.create.domain.SStudentAttendance;
import com.ruoyi.create.mapper.SStudentAttendanceMapper;
import com.ruoyi.create.service.ISStudentAttendanceService;
import com.ruoyi.system.api.RemoteStudentInfoService;
import com.ruoyi.system.api.domain.StudentInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 学生签到历史Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
@Service
public class SStudentAttendanceServiceImpl implements ISStudentAttendanceService
{
    @Resource
    private SStudentAttendanceMapper sStudentAttendanceMapper;
    @Resource
    private RemoteStudentInfoService studentInfoService;

    /**
     * 查询学生签到历史
     *
     * @param id 学生签到历史主键
     * @return 学生签到历史
     */
    @Override
    public SStudentAttendance selectSStudentAttendanceById(Long id)
    {
        return sStudentAttendanceMapper.selectSStudentAttendanceById(id);
    }

    /**
     * 查询学生签到历史列表
     *
     * @param sStudentAttendance 学生签到历史
     * @return 学生签到历史
     */
    @Override
    public List<SStudentAttendance> selectSStudentAttendanceList(SStudentAttendance sStudentAttendance)
    {
        List<SStudentAttendance> attendanceList = sStudentAttendanceMapper.selectSStudentAttendanceList(sStudentAttendance);
        // 查询班级学生列表
        StudentInfo studentInfo = new StudentInfo();
        studentInfo.setClassId(sStudentAttendance.getClassId());
        List<StudentInfo> studentInfoList = studentInfoService.getList(studentInfo, SecurityConstants.INNER);
        Map<Long, String> studentIdMap = studentInfoList.stream().collect(Collectors.toMap(StudentInfo::getId, StudentInfo::getStudentId));
        Map<Long, String> studentNameMap = studentInfoList.stream().collect(Collectors.toMap(StudentInfo::getId, StudentInfo::getStudentName));
        return attendanceList.stream().peek(item -> {
            item.setStudentNo(studentIdMap.get(item.getStudentId()));
            item.setStudentName(studentNameMap.get(item.getStudentId()));
        }).collect(Collectors.toList());
    }

    /**
     * 新增学生签到历史
     *
     * @param sStudentAttendance 学生签到历史
     * @return 结果
     */
    @Override
    public int insertSStudentAttendance(SStudentAttendance sStudentAttendance)
    {
        sStudentAttendance.setCreateTime(DateUtils.getNowDate());
        return sStudentAttendanceMapper.insertSStudentAttendance(sStudentAttendance);
    }

    /**
     * 修改学生签到历史
     *
     * @param sStudentAttendance 学生签到历史
     * @return 结果
     */
    @Override
    public int updateSStudentAttendance(SStudentAttendance sStudentAttendance)
    {
        sStudentAttendance.setUpdateTime(DateUtils.getNowDate());
        return sStudentAttendanceMapper.updateSStudentAttendance(sStudentAttendance);
    }

    /**
     * 批量删除学生签到历史
     *
     * @param ids 需要删除的学生签到历史主键
     * @return 结果
     */
    @Override
    public int deleteSStudentAttendanceByIds(Long[] ids)
    {
        return sStudentAttendanceMapper.deleteSStudentAttendanceByIds(ids);
    }

    /**
     * 删除学生签到历史信息
     *
     * @param id 学生签到历史主键
     * @return 结果
     */
    @Override
    public int deleteSStudentAttendanceById(Long id)
    {
        return sStudentAttendanceMapper.deleteSStudentAttendanceById(id);
    }
}
