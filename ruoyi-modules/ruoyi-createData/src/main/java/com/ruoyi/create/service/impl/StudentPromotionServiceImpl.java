package com.ruoyi.create.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.domain.StudentPromotionVO;
import com.ruoyi.create.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.StudentPromotionMapper;
import com.ruoyi.create.domain.StudentPromotion;
import com.ruoyi.create.service.IStudentPromotionService;

/**
 * 提升课程推送Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-13
 */
@Service
public class StudentPromotionServiceImpl implements IStudentPromotionService
{
    @Autowired
    private StudentPromotionMapper studentPromotionMapper;

    @Autowired
    private UserUtils userUtils;

    /**
     * 查询提升课程推送
     *
     * @param id 提升课程推送主键
     * @return 提升课程推送
     */
    @Override
    public StudentPromotion selectStudentPromotionById(String id)
    {
        return studentPromotionMapper.selectStudentPromotionById(id);
    }

    /**
     * 查询提升课程推送列表
     *
     * @param studentPromotion 提升课程推送
     * @return 提升课程推送
     */
    @Override
    public List<StudentPromotion> selectStudentPromotionList(StudentPromotion studentPromotion)
    {
        return studentPromotionMapper.selectStudentPromotionList(studentPromotion);
    }

    /**
     * 新增提升课程推送
     *
     * @param studentPromotion 提升课程推送
     * @return 结果
     */
    @Override
    public int insertOrUpdateStudentPromotion(StudentPromotion studentPromotion)
    {
        String studentId = userUtils.getSysUser(SecurityUtils.getUserId()).getStudentId();
        StudentPromotion student = studentPromotionMapper.selectStudentPromotionById(studentId);
        if(Objects.isNull(student)){
            studentPromotion.setCreateTime(DateUtils.getNowDate());
            studentPromotion.setStudentId(Long.valueOf(studentId));
            return studentPromotionMapper.insertStudentPromotion(studentPromotion);
        }else {
            studentPromotion.setUpdateTime(DateUtils.getNowDate());
            return studentPromotionMapper.updateStudentPromotion(studentPromotion);
        }
    }

    /**
     * 修改提升课程推送
     *
     * @param studentPromotion 提升课程推送
     * @return 结果
     */
    @Override
    public int updateStudentPromotion(StudentPromotion studentPromotion)
    {
        String studentId = userUtils.getSysUser(SecurityUtils.getUserId()).getStudentId();
        StudentPromotion student = studentPromotionMapper.selectStudentPromotionById(studentId);
        studentPromotion.setUpdateTime(DateUtils.getNowDate());
        return studentPromotionMapper.updateStudentPromotion(studentPromotion);
    }

    /**
     * 批量删除提升课程推送
     *
     * @param ids 需要删除的提升课程推送主键
     * @return 结果
     */
    @Override
    public int deleteStudentPromotionByIds(Long[] ids)
    {
        return studentPromotionMapper.deleteStudentPromotionByIds(ids);
    }

    /**
     * 删除提升课程推送信息
     *
     * @param id 提升课程推送主键
     * @return 结果
     */
    @Override
    public int deleteStudentPromotionById(Long id)
    {
        return studentPromotionMapper.deleteStudentPromotionById(id);
    }

    /**
     * @return
     */
    @Override
    public List<StudentPromotionVO> selectStudentPromotionByStudentId(StudentPromotionVO studentPromotion) {
        List<StudentPromotionVO> studentPromotionList = new ArrayList<>();
        List<StudentPromotionVO> studentPromotionVOS = studentPromotionMapper.selectStudentPromotionByStudentId(studentPromotion);
        for (StudentPromotionVO vo : studentPromotionVOS) {
            vo.setProgress(Objects.isNull(vo.getProgress()) ? "-" : vo.getProgress() + "%");
            studentPromotionList.add(vo);
        }
        return studentPromotionList;
    }

}
