package com.ruoyi.create.service;

import java.util.List;

import com.ruoyi.create.domain.CourseTopicsReplies;
import com.ruoyi.create.domain.TopicsReply;

/**
 * 课程留言与回复关联Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-04
 */
public interface ITopicsReplyService 
{
    /**
     * 查询课程留言与回复关联
     * 
     * @param id 课程留言与回复关联主键
     * @return 课程留言与回复关联
     */
    public TopicsReply selectTopicsReplyById(Long id);

    /**
     * 查询课程留言与回复关联列表
     * 
     * @param topicsReply 课程留言与回复关联
     * @return 课程留言与回复关联集合
     */
    public List<TopicsReply> selectTopicsReplyList(TopicsReply topicsReply);

    /**
     * 新增课程留言与回复关联
     * 
     * @param topicsReply 课程留言与回复关联
     * @return 结果
     */
    public int insertTopicsReply(TopicsReply topicsReply);

    /**
     * 修改课程留言与回复关联
     * 
     * @param topicsReply 课程留言与回复关联
     * @return 结果
     */
    public int updateTopicsReply(TopicsReply topicsReply);

    /**
     * 批量删除课程留言与回复关联
     * 
     * @param ids 需要删除的课程留言与回复关联主键集合
     * @return 结果
     */
    public int deleteTopicsReplyByIds(Long[] ids);

    /**
     * 删除课程留言与回复关联信息
     * 
     * @param id 课程留言与回复关联主键
     * @return 结果
     */
    public int deleteTopicsReplyById(Long id);

    List<CourseTopicsReplies> selectTopicsReplyAll(Long msgId);

    int deleteTopicsReplylyIds(Long[] replyIds);
}
