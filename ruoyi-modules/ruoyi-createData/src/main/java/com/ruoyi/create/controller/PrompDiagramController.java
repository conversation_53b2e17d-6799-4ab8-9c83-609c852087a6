package com.ruoyi.create.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.PrompDiagram;
import com.ruoyi.create.service.IPrompDiagramService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * prompt模板文生图相关推理参数Controller
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
@RestController
@RequestMapping("/diagram")
public class PrompDiagramController extends BaseController
{
    @Autowired
    private IPrompDiagramService prompDiagramService;

    /**
     * 查询prompt模板文生图相关推理参数列表
     */
    @RequiresPermissions("create:diagram:list")
    @GetMapping("/list")
    public TableDataInfo list(PrompDiagram prompDiagram)
    {
        startPage();
        List<PrompDiagram> list = prompDiagramService.selectPrompDiagramList(prompDiagram);
        return getDataTable(list);
    }

    /**
     * 导出prompt模板文生图相关推理参数列表
     */
    @RequiresPermissions("create:diagram:export")
    @Log(title = "prompt模板文生图相关推理参数", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PrompDiagram prompDiagram)
    {
        List<PrompDiagram> list = prompDiagramService.selectPrompDiagramList(prompDiagram);
        ExcelUtil<PrompDiagram> util = new ExcelUtil<PrompDiagram>(PrompDiagram.class);
        util.exportExcel(response, list, "prompt模板文生图相关推理参数数据");
    }

    /**
     * 获取prompt模板文生图相关推理参数详细信息
     */
    @RequiresPermissions("create:diagram:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(prompDiagramService.selectPrompDiagramById(id));
    }

    /**
     * 新增prompt模板文生图相关推理参数
     */
    @RequiresPermissions("create:diagram:add")
    @Log(title = "prompt模板文生图相关推理参数", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PrompDiagram prompDiagram)
    {
        return toAjax(prompDiagramService.insertPrompDiagram(prompDiagram));
    }

    /**
     * 修改prompt模板文生图相关推理参数
     */
    @RequiresPermissions("create:diagram:edit")
    @Log(title = "prompt模板文生图相关推理参数", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PrompDiagram prompDiagram)
    {
        return toAjax(prompDiagramService.updatePrompDiagram(prompDiagram));
    }

    /**
     * 删除prompt模板文生图相关推理参数
     */
    @RequiresPermissions("create:diagram:remove")
    @Log(title = "prompt模板文生图相关推理参数", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(prompDiagramService.deletePrompDiagramByIds(ids));
    }
}
