package com.ruoyi.create.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.create.domain.HomeworkStudent;

/**
 * 作业状态Service接口
 *
 * <AUTHOR>
 * @date 2024-06-07
 */
public interface IHomeworkStudentService extends IService<HomeworkStudent>
{
    /**
     * 查询作业状态
     *
     * @param id 作业状态主键
     * @return 作业状态
     */
    public HomeworkStudent selectHomeworkStudentById(Long id);

    /**
     * 查询作业状态列表
     *
     * @param homeworkStudent 作业状态
     * @return 作业状态集合
     */
    public List<HomeworkStudent> selectHomeworkStudentList(HomeworkStudent homeworkStudent);

    /**
     * 新增作业状态
     *
     * @param homeworkStudent 作业状态
     * @return 结果
     */
    public int insertHomeworkStudent(HomeworkStudent homeworkStudent);

    /**
     * 修改作业状态
     *
     * @param homeworkStudent 作业状态
     * @return 结果
     */
    public int updateHomeworkStudent(HomeworkStudent homeworkStudent);
    public int updateHomeworkStudentCutOffTime(HomeworkStudent homeworkStudent);

    /**
     * 批量删除作业状态
     *
     * @param ids 需要删除的作业状态主键集合
     * @return 结果
     */
    public int deleteHomeworkStudentByIds(Long[] ids);

    /**
     * 删除作业状态信息
     *
     * @param id 作业状态主键
     * @return 结果
     */
    public int deleteHomeworkStudentById(Long id);

    List<HomeworkStudent> selectCorrectHomeworkStudentList(HomeworkStudent homeworkStudent);

    AjaxResult selectClaByHId(Long hmId);
}
