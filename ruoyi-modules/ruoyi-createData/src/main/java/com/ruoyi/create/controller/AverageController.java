package com.ruoyi.create.controller;

import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.create.service.AverageService;
import com.ruoyi.create.service.MySelfPortraitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.ruoyi.common.core.web.domain.AjaxResult.success;

@RestController
@RequestMapping("/Average")
public class AverageController {

    @Autowired
    private AverageService as;

    @GetMapping("/{classId}")
    public AjaxResult getAById(@PathVariable String classId) {
        AjaxResult ajaxResult = success(as.getAById(classId));
        return ajaxResult;
    }
}