package com.ruoyi.create.service.impl;

import cn.hutool.json.JSONException;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.create.domain.MySelfPortrait;

import com.ruoyi.create.domain.StudentEvaluation;
import com.ruoyi.create.dto.MessagesDto;
import com.ruoyi.create.mapper.MySelfPortraitDtoMapper;
import com.ruoyi.create.mapper.MySelfPortraitMapper;
import com.ruoyi.create.mapper.StudentEvaluationMapper;
import com.ruoyi.create.mapper.StudentMapper;
import com.ruoyi.create.service.MySelfPortraitService;
import com.ruoyi.system.api.ConfigService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.List;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Slf4j
@Service
@Repository
public class MySelfPortraitServiceImpl implements MySelfPortraitService {

    @Resource
    private MySelfPortraitMapper mySelfPortraitMapper;

    @Resource
    private MySelfPortraitDtoMapper mySelfPortraitDtoMapper;
    @Resource
    private StudentEvaluationMapper studentEvaluationMapper;
    @Resource
    private StudentMapper studentMapper;
    @Resource
    private ConfigService configService;
    private String resultString;
    // 创建Gson实例用于JSON序列化和反序列化
    private static final Gson GSON = new GsonBuilder().create();
    // 创建日志记录器
    private static final Logger logger = LoggerFactory.getLogger(MySelfPortraitServiceImpl.class);
    // 定义API URL和授权头常量
    private static final String API_URL = "https://qianfan.baidubce.com/v2/chat/completions";
    private static final String AUTHORIZATION_HEADER = "Bearer bce-v3/ALTAK-dyQhjZkw5RjqzaBOcJ5lc/04a5392ac58e7e75a61e7ce5a4a92a62613b075c";
    // 创建OkHttpClient实例用于HTTP请求，并设置超时时间
    private static final OkHttpClient HTTP_CLIENT = new OkHttpClient.Builder()
            .connectTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
            .readTimeout(60, java.util.concurrent.TimeUnit.SECONDS)
            .writeTimeout(60, java.util.concurrent.TimeUnit.SECONDS)
            .build();
    @Autowired
    private CourseCaseServiceImpl courseCaseServiceImpl;


    // 1. 查询学生画像信息
    @Override
    public MySelfPortrait getMySelfPortraitById(String studentId) {
        MySelfPortrait mySelfPortrait = mySelfPortraitMapper.selectById(studentId);
        logger.debug("mySelfPortrait:{}", mySelfPortrait);
        return mySelfPortrait;
    }
    // 2. 分析预测
    @Transactional
    @Override
    public String analyzeStudentAsync(String studentId) throws IOException {
        logger.info("🚀 事务是否生效: {}", TransactionAspectSupport.currentTransactionStatus().isRollbackOnly());
        // 获取到分析数据对象
        logger.debug("Fetching student data for ID: {}", studentId);
        MySelfPortrait mydata = mySelfPortraitMapper.selectById(studentId);
        // 创建评估记录对象
        StudentEvaluation studentEvaluation = new StudentEvaluation();
        logger.debug("Fetched student data: {}", mydata);
        String sme = configService.getConfigKey2("Student_YuceTom", SecurityConstants.INNER).get("msg").toString();
        // 拼接字符串:"请帮一名学生分析一下现状,并给出分析预测,学生的情况如下:......"
        String studentMessage = sme + myselfString(mydata);
        logger.debug("studentMessage: {}", studentMessage);
        // 创建请求列表
        String sss = apiutil(studentMessage);//调用大模型
        // 构建最终的内容字符
        studentEvaluation.setStudentId(studentId);//id
        studentEvaluation.setEvaluationResult(sss);//分析内容
        studentEvaluation.setEvaluationState("执行完毕");//执行状态
        studentEvaluation.setRelation(mydata.getSemester().toString());//学期
        studentEvaluation.setEvaluationTime(DateUtils.getNowDate());
        //将预测结果加入评估表
        studentEvaluationMapper.insertStudentEvaluation(studentEvaluation);
        // 更新画像表中的最新的预测分析结果
        mydata.setYuceFenxi(sss);
        mydata.setStatisticsTime(LocalDateTime.now());
        mySelfPortraitMapper.addOrUpdateYuceFenxi(mydata);
        resultString = sss;
        logger.info("输出更新过的对象：{}", mydata);
        // 新增评估记录表中的结果记
        return "王怀煜：预测分析第2版_分析结果为：" + resultString;
    }
    //3. 定时任务
    @Override
    public void generateAndInsertPortrait() {
        List<String> studentIds = studentMapper.getAllStudentIds();
        int count=0;
        if (studentIds == null || studentIds.isEmpty()) {
            System.out.println("学生表为空，暂无可处理学生");
            return;
        }
        for (String studentId : studentIds) {
            // 构造画像实体
            MySelfPortrait entity = mySelfPortraitMapper.selectById(studentId);
            if (entity == null) {
                entity = new MySelfPortrait();
                entity.setStudentId(studentId);
                mySelfPortraitMapper.insert(entity);
            } else {
                //作业质量( s_student_problem_sub ✅)
                Double zuoye0 = mySelfPortraitDtoMapper.selectCorrectRateByStudentId(studentId);
                Integer zuoye1 = null;
                String zuoyeString = null;
                if (zuoye0 != null) {
                    zuoye1 = (int) Math.round(zuoye0 * 100);
                    entity.setLaHomework(zuoye1);
                    zuoyeString = "作业质量得分：" + zuoye1 + ",";
                    System.out.println("！！！！！！！！！！！该生作业评分为：" + zuoye1);
                } else {
                    entity.setLaHomework(0);
                }

                //掌握( s_student_study_record ✅)
                Double zhangwo0 = mySelfPortraitDtoMapper.selectMaxProgressByStudentId(studentId);
                Integer zhangwo1 = null;
                String zhangwoString = null;
                if (zhangwo0 != null) {
                    zhangwo1 = (int) Math.round(zhangwo0);
                    zhangwoString = "知识掌握得分：" + zhangwo1 + ",";
                    System.out.println("！！！！！！！！！！！该生知识掌握评分为：" + zhangwo1);
                    //赋值
                    entity.setLaMastery(zhangwo1);
                } else {
                    entity.setLaMastery(0);
                }
                //兴趣( s_student_attendance ✅)
                String xingquString = null;
                Integer xingqu0 = mySelfPortraitDtoMapper.selectOnTimeRateByStudentId(studentId);
                if (xingqu0 != null) {
                    xingquString = "兴趣得分：" + xingqu0 + ",";
                    System.out.println("！！！！！！！！！！！该生兴趣评分为：" + xingqu0);
                    //赋值
                    entity.setLaInterest(xingqu0);
                } else {
                    entity.setLaInterest(0);
                }
                //评价( s_homework_student ✅)
                List<String> list = mySelfPortraitDtoMapper.selectRemarksByStudentId(studentId);
                String pingjiaString = null;
                if (list.size() > 0) {
                    StringBuilder stringBuilder = new StringBuilder();
                    for (String remark : list) {
                        if (remark != null) {
                            stringBuilder.append(remark);
                        }
                    }
                    if (stringBuilder.length() > 0) {
                        String pingjia0 = "请根据该生的教师评语给该生打分/百分制,简洁回答（别说废话）格式如：分数：98" + stringBuilder;
                        String pingjia1 = apiutil(pingjia0).replace("分数：", " ").trim();
                        entity.setLaEvaluation(Integer.parseInt(pingjia1));
                    } else {
                        entity.setLaEvaluation(0);  // 没有评价的情况
                    }

                } else {
                    entity.setLaEvaluation(0);//没有记录的情况
                    System.out.println("没有获取到学生id对应用户id的评价列表！！！");
                    System.out.println("该生评分默认为" + entity.getLaEvaluation());
                }
                //(由大模型进行数据填充 ✅)
                // 查询是否存在
                String sme1 = "请根据已有的数据," + zuoyeString + zhangwoString + xingquString + pingjiaString + "等四个真实得分，对一名学生的其它表现打分,注意：只要有两个以上真实得分，就要对其余字段全部主观评分，不允许为”0/null" +
                        "需要打分的字段如下（请返回便于分割字符串装进map的结果,格式如：MySelfPortrait(该学生学号=20180234431,成绩得分= 98,...):";
                String studentMessage = sme1 + myselfString(entity);
                logger.debug("studentMessage: {}", studentMessage);
                String content1 = apiutil(studentMessage);//调用大模型
                System.out.println(content1);//输出样式
                //获取map打分结果
                Map<String, String> map = extractKeyValuePairs(content1);
                entity.setStudentId((String) map.get("该学生学号"));
// 检查字符串是否为空或为"null"，为这些情况时设为默认值0
                entity.setLaScore(parseIntWithDefault(map.get("成绩得分")));
                entity.setLaPerformance(parseIntWithDefault(map.get("表现得分")));
                entity.setLaTalent(parseIntWithDefault(map.get("天赋得分")));
                entity.setLbMorality(parseIntWithDefault(map.get("道德素养得分")));
                entity.setLbAesthetics(parseIntWithDefault(map.get("审美得分")));
                entity.setLbHealth(parseIntWithDefault(map.get("健康状况得分")));
                entity.setLbCooperation(parseIntWithDefault(map.get("合作能力得分")));
                entity.setLbLearning(parseIntWithDefault(map.get("学习能力得分")));

// 课程得分赋值(暂无数据)
                entity.setYuExperiment(parseFloatWithDefault(map.get("实验课程完成度")));
                entity.setYuElective(parseFloatWithDefault(map.get("选修课程完成度")));
                entity.setYuGeneral(parseFloatWithDefault(map.get("通识课程完成度")));
                entity.setYuCore(parseFloatWithDefault(map.get("核心课程完成度")));
                entity.setYuFoundation(parseFloatWithDefault(map.get("基础课程完成度")));
                // 学分情况(暂无数据)
                entity.setXuTotalCredits(0.0F);
                entity.setXuCompletedCredits(0.0F);
                // 创建时间

                entity.setStatisticsTime(LocalDateTime.now());
                // 学期年月
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
                String semester = LocalDateTime.now().format(formatter);
                entity.setSemester(semester);
                mySelfPortraitMapper.updateByStudentId(entity);
            }
            count++;
        }
        System.out.println("赋值了"+count+"次对象！！！");
        System.out.println("✅ 所有学生画像已处理，共计 " + studentIds.size() + " 条");
        }



    // 1.大模型调用
    private static String apiutil (String message){
        String s = new String();
        List<MessagesDto> messagesList = new ArrayList<>();
        MessagesDto messages = new MessagesDto();
        messages.setRole("user");
        messages.setContent(message);
        messagesList.add(messages);

        MediaType mediaType = MediaType.parse("application/json");
        Gson gson = new Gson();
        RequestBody body = RequestBody.create(mediaType, "{\"model\":\"deepseek-r1\"," +
                "\"messages\":" + gson.toJson(messagesList) + "," +
                "\"stream\":true,\"disable_search\":false,\"enable_citation\":false}");
        Request request = new Request.Builder()
                .url(API_URL)
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", AUTHORIZATION_HEADER)
                .build();

        // 使用 try-with-resources 确保资源自动关闭，此处使用 HTTP_CLIENT 发起请求并接收响应
        try (Response response = HTTP_CLIENT.newCall(request).execute()) {
            // 检查响应是否成功
            if (!response.isSuccessful()) {
                logger.error("Request to external API failed with status code: {}", response.code());
                // 修正：直接使用 response 对象而非其 toString 方法，以避免潜在的混淆
                throw new IOException("Unexpected code " + response.code());
            }

            // 使用 try-with-resources 确保 BufferedReader 自动关闭
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().byteStream(), StandardCharsets.UTF_8))) {
                String line;
                StringBuilder contentBuilder = new StringBuilder();

                // 循环读取每一行数据
                while ((line = reader.readLine()) != null) {
                    // 由于 readLine 已保证 line 不为 null，这里检查空或仅含空白字符的情况即可
                    if (!line.trim().isEmpty()) {
                        // 移除前缀 "data: "
                        String jsonString = line.replaceFirst("^data: ", "").trim();

                        // 检查是否为结束标记 "[DONE]"
                        if ("[DONE]".equals(jsonString)) {
                            break;
                        }

                        try {
                            JSONObject jsonObject = new JSONObject(jsonString);

                            // 检查 choices 数组是否存在且非空
                            if (!jsonObject.has("choices") || jsonObject.getJSONArray("choices").length() == 0) {
                                logger.warn("Invalid JSON structure, missing 'choices' array or empty array in line: {}", line);
                                continue;
                            }

                            JSONArray choicesArray = jsonObject.getJSONArray("choices");
                            JSONObject firstChoice = choicesArray.getJSONObject(0);

                            // 检查 delta 对象是否存在
                            if (!firstChoice.has("delta")) {
                                logger.warn("Invalid JSON structure, missing 'delta' object in line: {}", line);
                                continue;
                            }

                            JSONObject deltaObject = firstChoice.getJSONObject("delta");

                            // 获取 content 字段的值，考虑到可能为空字符串或"null"
                            String contentValue = deltaObject.optString("content", "");

                            // 如果 contentValue 非空且不是 "null" 的字符串形式，则追加到内容构建器中
                            if (!contentValue.isEmpty() && !contentValue.equalsIgnoreCase("null")) {
                                contentBuilder.append(contentValue);
                            }
                        } catch (JSONException e) {
                            logger.error("Failed to parse JSON string: {}. Error: {}", jsonString, e.getMessage(), e);
                        }
                    }
                }

                // 构建最终的内容字符串
                s = contentBuilder.toString();
            } catch (Exception e) {
                logger.error("❌ 事务异常，回滚! studentId={}, 错误={}", e.getMessage(), e);
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }


        return s;
    }

    // 2.封装对象转字符串
    private static String myselfString (MySelfPortrait mySelfPortrait){
        String mydataString = mySelfPortrait.toString()
                .replace("studentId", "该学生学号")
                .replace("laScore", "成绩得分")
                .replace("laHomework", "作业得分")
                .replace("laMastery", "掌握度得分")
                .replace("laPerformance", "表现得分")
                .replace("laInterest", "兴趣得分")
                .replace("laTalent", "天赋得分")
                .replace("laEvaluation", "评价得分")
                .replace("lbMorality", "道德素养得分")
                .replace("lbAesthetics", "审美得分")
                .replace("lbHealth", "健康状况得分")
                .replace("lbCooperation", "合作能力得分")
                .replace("lbLearning", "学习能力得分")
                .replace("yuExperiment", "实验课程完成度")
                .replace("yuElective", "选修课程完成度")
                .replace("yuGeneral", "通识课程完成度")
                .replace("yuCore", "核心课程完成度")
                .replace("yuFoundation", "基础课程完成度")
                .replace("xuTotalCredits", "总学分")
                .replace("xuCompletedCredits", "已完成学分");
        return mydataString;
    }

    // 3.将返回打分结果装进map
    private static Map<String, String> extractKeyValuePairs (String input){
        Map<String, String> result = new LinkedHashMap<>();
        //提取括号内的内容
        Pattern bracketPattern = Pattern.compile("MySelfPortrait\\((.*?)\\)");
        Matcher matcher = bracketPattern.matcher(input);
        if (matcher.find()) {
            String inside = matcher.group(1); // 括号内内容
            // 拆分成 key=value 对
            String[] pairs = inside.split(",(?=[^=]+=)"); // 逗号分割（但确保不是值里的逗号）

            for (String pair : pairs) {
                String[] kv = pair.split("=", 2); // 最多分成两部分
                if (kv.length == 2) {
                    result.put(kv[0].trim(), kv[1].trim());
                }
            }
        }
        return result;
    }

    // 4.帮助函数：处理空字符串、"null" 或 null，默认值为0
    private int parseIntWithDefault(String value) {
        if (value == null || value.trim().isEmpty() || "null".equals(value.trim())) {
            return 0; // 默认值为0
        }
        return Integer.parseInt(value);
    }

    // 5.帮助函数：处理空字符串、"null" 或 null，默认值为0.0f
    private float parseFloatWithDefault(String value) {
        if (value == null || value.trim().isEmpty() || "null".equals(value.trim())) {
            return 0.0f; // 默认值为0.0
        }
        return Float.parseFloat(value);
    }

}


