package com.ruoyi.create.domain;

import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 作业状态对象 s_homework_student
 *
 * <AUTHOR>
 * @date 2024-06-07
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName( value = "s_homework_student")
public class HomeworkStudent extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 作业id */
    @Excel(name = "作业id")
    private Long hmId;

    /** 作业id */
    @Excel(name = "作业名称")
    @TableField(exist = false)
    private String hmName;

    /** 课程名称 */
    @Excel(name = "课程名称")
    @TableField(exist = false)
    private String lessonName;

    /** 用户id */
    @Excel(name = "用户id")
    private Long userId;

    /** 用户id */
    @Excel(name = "用户名称")
    @TableField(exist = false)
    private String nickName;

	
	@Excel(name ="学校id")
	@TableField("univer_id")
	private Long univerId;
	
    /** 学院id */
    @Excel(name = "学院id")
    private Long collegeId;

    /** 专业id */
    @Excel(name = "专业id")
    private Long majorId;

    /** 专业id */
    @Excel(name = "专业名称")
    private String majorName;

    /** 班级id */
    @Excel(name = "班级id")
    private Long classId;

    /** 班级id */
    @Excel(name = "班级名称")
    @TableField(exist = false)
    private String className;

    /** 作业提交状态 0-未提交  1-提交 3-过期 */
    @Excel(name = "作业提交状态 0-未提交  1-提交 3-过期")
    private String commitStatus;

    /** 提交时间  格式化年月日时分秒*/ 
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "提交时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date commitTime;

    /**
     * 截至时间
     **/
    @TableField("cutoff_time")
    @Excel(name = "截至时间")
    private String cutoffTime;
	
	/**
	 * 开始时间
	 **/
	@TableField("start_time")
	@Excel(name = "开始时间")
	private String startTime;

	/**
     * 截止时间标签
     **/
	@TableField(exist = false)
	private Boolean cutoffLabel;

    /** 作业批改状态 0-未批改 1-已批改 */
    @Excel(name = "作业批改状态 0-未批改 1-已批改")
    private String correctStatus;

    /** 批改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "批改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date correctTime;

    /** 作业批改评语 */
    @Excel(name = "评语")
    private String remark;

    @TableField(exist =  false)
    @Excel(name = "作业发布时间")
    private String publishTime;

	/** 创建者 */
	@TableField("create_by")
	private String createBy;

	/** 创建时间 */
	@TableField("create_time")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;

	/** 更新者 */
	@TableField("update_by")
	private String updateBy;

	/** 更新时间 */
	@TableField("update_time")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;
	
	
	
	/**学院名称*/
	@TableField(exist = false)
	private String colleName;
	/**学校名称*/
	@TableField(exist = false)
	private String univerName;

	
	
	


}
