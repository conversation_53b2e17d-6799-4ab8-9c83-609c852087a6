package com.ruoyi.create.service;

import java.util.List;
import com.ruoyi.create.domain.KnowledgeSpeciality;

/**
 * 知识库专业配置Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-30
 */
public interface IKnowledgeSpecialityService 
{
    /**
     * 查询知识库专业配置
     * 
     * @param id 知识库专业配置主键
     * @return 知识库专业配置
     */
    public KnowledgeSpeciality selectKnowledgeSpecialityById(Long id);

    /**
     * 查询知识库专业配置列表
     * 
     * @param knowledgeSpeciality 知识库专业配置
     * @return 知识库专业配置集合
     */
    public List<KnowledgeSpeciality> selectKnowledgeSpecialityList(KnowledgeSpeciality knowledgeSpeciality);

    /**
     * 新增知识库专业配置
     * 
     * @param knowledgeSpeciality 知识库专业配置
     * @return 结果
     */
    public int insertKnowledgeSpeciality(KnowledgeSpeciality knowledgeSpeciality);

    /**
     * 修改知识库专业配置
     * 
     * @param knowledgeSpeciality 知识库专业配置
     * @return 结果
     */
    public int updateKnowledgeSpeciality(KnowledgeSpeciality knowledgeSpeciality);

    /**
     * 批量删除知识库专业配置
     * 
     * @param ids 需要删除的知识库专业配置主键集合
     * @return 结果
     */
    public int deleteKnowledgeSpecialityByIds(Long[] ids);

    /**
     * 删除知识库专业配置信息
     * 
     * @param id 知识库专业配置主键
     * @return 结果
     */
    public int deleteKnowledgeSpecialityById(Long id);

    /**
     * 根据专业d查询知识库配置
     * @param majorId
     * @return
     */
    public KnowledgeSpeciality selectKnowledgeSpecialityByMajorInfoId(Long majorId);

}
