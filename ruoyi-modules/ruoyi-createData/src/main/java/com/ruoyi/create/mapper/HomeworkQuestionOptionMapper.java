package com.ruoyi.create.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.create.domain.HomeworkQuestionOption;

import java.util.List;

/**
 * 作业题目选项Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-07
 */
public interface HomeworkQuestionOptionMapper extends BaseMapper<HomeworkQuestionOption>
{
    /**
     * 查询作业题目选项
     *
     * @param id 作业题目选项主键
     * @return 作业题目选项
     */
    public HomeworkQuestionOption selectHomeworkQuestionOptionById(Long id);

    /**
     * 查询作业题目选项列表
     *
     * @param homeworkQuestionOption 作业题目选项
     * @return 作业题目选项集合
     */
    public List<HomeworkQuestionOption> selectHomeworkQuestionOptionList(HomeworkQuestionOption homeworkQuestionOption);

    /**
     * 新增作业题目选项
     *
     * @param homeworkQuestionOption 作业题目选项
     * @return 结果
     */
    public int insertHomeworkQuestionOption(HomeworkQuestionOption homeworkQuestionOption);

    /**
     * 修改作业题目选项
     *
     * @param homeworkQuestionOption 作业题目选项
     * @return 结果
     */
    public int updateHomeworkQuestionOption(HomeworkQuestionOption homeworkQuestionOption);

    /**
     * 删除作业题目选项
     *
     * @param id 作业题目选项主键
     * @return 结果
     */
    public int deleteHomeworkQuestionOptionById(Long id);

    /**
     * 批量删除作业题目选项
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHomeworkQuestionOptionByIds(Long[] ids);
}
