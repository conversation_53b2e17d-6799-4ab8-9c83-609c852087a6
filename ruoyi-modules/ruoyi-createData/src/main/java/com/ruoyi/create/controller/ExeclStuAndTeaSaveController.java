package com.ruoyi.create.controller;


import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.create.domain.execl.Execl;
import com.ruoyi.create.domain.execl.Student;
import com.ruoyi.create.domain.execl.Teacher;
import com.ruoyi.create.mapper.ExeclStuAndTeaSaveMapper;
import com.ruoyi.create.service.IExeclStuAndTeaSaveService;
import com.ruoyi.create.utils.Snowflake;
import com.ruoyi.system.api.RemoteFileService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 老师课程Controller
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
@Slf4j
@RestController
@RequestMapping("/execlStuTea")
public class ExeclStuAndTeaSaveController extends BaseController {

    @Resource
    IExeclStuAndTeaSaveService execlStuAndTeaSaveService;

    @Resource
    ExeclStuAndTeaSaveMapper execlStuAndTeaSaveMapper;

    @Resource
    private RemoteFileService remoteFileService;

    @PostMapping("/save")
    public AjaxResult save(@RequestBody Execl execl){
//        System.out.println("==========================");
//        System.out.println(execl);
//        System.out.println("==========================");
        Snowflake snowflake = new Snowflake(1, 1);
        long id = snowflake.generateId();
        if(ObjectUtils.isNotEmpty(execl.getFileId())){
            Long[] fileIds=new Long[]{execl.getFileId()};
            remoteFileService.relationFile(fileIds,String.valueOf(id));
        }
        execlStuAndTeaSaveService.ececlDataSave(execl);
        execlStuAndTeaSaveService.delExeclPath(execl);
        return success();
    }

    @GetMapping("/delFalg")
    public AjaxResult deleteByDeFalg(){
        return success(execlStuAndTeaSaveMapper.deleteByDeFalg());
    }


    @GetMapping("/getCollByStuId")
    public AjaxResult getCollByStuId(String studentId){
        Student student = execlStuAndTeaSaveMapper.selectStuById2(studentId);
        if (student == null){
            return error("未查询到该生对应的院系信息");
        }
        List<Long> list=new ArrayList<>();
        list.add(Long.valueOf(student.getUniverId()));
        list.add(student.getColleId());
        list.add(student.getMajorId());
        if (student.getClassId() !=null){
            list.add(student.getClassId());
        }
        return success(list);
    }
    @GetMapping("/getCollByTeaId")
    public AjaxResult getCollByTeaId(String teacherId){
        Teacher teacher = execlStuAndTeaSaveMapper.selectTeaById2(teacherId);
        if (teacher == null){
            return error("未查询到该老师对应的院系信息");
        }
        List<Long> list=new ArrayList<>();
        list.add(Long.valueOf(teacher.getUniverId()));
        list.add(teacher.getColleId());
        return success(list);
    }

}
