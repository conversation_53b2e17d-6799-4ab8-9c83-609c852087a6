package com.ruoyi.create.controller;

import java.util.List;
import java.io.IOException;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.create.domain.CourseManagement;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.ClassStudentCourseReport;
import com.ruoyi.create.service.IClassStudentCourseReportService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 课程班级信息统计Controller
 *
 * <AUTHOR>
 * @date 2024-12-10
 */
@RestController
@RequestMapping("/classStudentCourseReport")
public class ClassStudentCourseReportController extends BaseController {
	@Resource
	private IClassStudentCourseReportService classStudentCourseReportService;

	/**
	 * 查询课程班级信息统计列表
	 */
	@GetMapping("/list")
	public TableDataInfo list(ClassStudentCourseReport classStudentCourseReport) {
		startPage();
		List<ClassStudentCourseReport> list = classStudentCourseReportService.selectClassStudentCourseReportList(classStudentCourseReport);
		return getDataTable(list);
	}

	/**
	 * 导出课程班级信息统计列表
	 */
	@Log(title = "课程班级信息统计", businessType = BusinessType.EXPORT)
	@PostMapping("/export")
	public void export(HttpServletResponse response, ClassStudentCourseReport classStudentCourseReport) {
		List<ClassStudentCourseReport> list = classStudentCourseReportService.selectClassStudentCourseReportList(classStudentCourseReport);
		ExcelUtil<ClassStudentCourseReport> util = new ExcelUtil<ClassStudentCourseReport>(ClassStudentCourseReport.class);
		util.exportExcel(response, list, "课程班级信息统计数据");
	}

	/**
	 * 获取课程班级信息统计详细信息
	 */
	@GetMapping(value = "/{id}")
	public AjaxResult getInfo(@PathVariable("id") Long id) {
		return success(classStudentCourseReportService.selectClassStudentCourseReportById(id));
	}

	/**
	 * 新增课程班级信息统计
	 */
	@Log(title = "课程班级信息统计", businessType = BusinessType.INSERT)
	@PostMapping
	public AjaxResult add(@RequestBody ClassStudentCourseReport classStudentCourseReport) {
		return toAjax(classStudentCourseReportService.insertClassStudentCourseReport(classStudentCourseReport));
	}

	/**
	 * 修改课程班级信息统计
	 */
	@Log(title = "课程班级信息统计", businessType = BusinessType.UPDATE)
	@PutMapping
	public AjaxResult edit(@RequestBody ClassStudentCourseReport classStudentCourseReport) {
		return toAjax(classStudentCourseReportService.updateClassStudentCourseReport(classStudentCourseReport));
	}

	/**
	 * 删除课程班级信息统计
	 */
	@Log(title = "课程班级信息统计", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
	public AjaxResult remove(@PathVariable Long[] ids) {
		return toAjax(classStudentCourseReportService.deleteClassStudentCourseReportByIds(ids));
	}


	/**
	 * @description: 创建单个学生单门课程分析
	 * @author: zhaoTianQi
	 * @date: 2024/12/10 14:05
	 * @param: courseManagement
	 * @return: AjaxResult
	 **/
	@GetMapping("/createSingleCourseAnalysis")
	public AjaxResult createSingleCourseAnalysis(ClassStudentCourseReport courseReport) {
		return AjaxResult.success(classStudentCourseReportService.createSingleCourseAnalysis(courseReport));
	}

	/**
	 * @description: 获取单个学生单门课程分析
	 * @author: zhaoTianQi
	 * @date: 2024/12/10 14:22
	 * @param: courseReport
	 * @return: AjaxResult
	 **/
	@GetMapping("/getSingleCourseAnalysis")
	public AjaxResult singleCourseAnalysis(ClassStudentCourseReport courseReport) {
		return AjaxResult.success(classStudentCourseReportService.getSingleCourseAnalysis(courseReport));
	}

	/**
	 * @description: 创建班级内的全部学生课程分析数据
	 * @author: zhaoTianQi
	 * @date: 2024/12/10 14:46
	 * @param: courseReport
	 * @return: AjaxResult
	 **/
	@GetMapping("/createClassStudentCourseAnalysis")
	public AjaxResult createClassStudentCourseAnalysis(ClassStudentCourseReport courseReport) {
		classStudentCourseReportService.createClassStudentCourseAnalysis(courseReport);
		return AjaxResult.success();
	}


	/**
	 * @description: 创建单个班级课程分析数据
	 * @author: zhaoTianQi
	 * @date: 2024/12/10 15:33
	 * @param: courseReport
	 * @return: AjaxResult
	 **/
	@GetMapping("/createClassCourseAnalysis")
	public AjaxResult createClassCourseAnalysis(ClassStudentCourseReport courseReport) {
		return AjaxResult.success(classStudentCourseReportService.createClassCourseAnalysis(courseReport));
	}

	/**
	 * @description: 创建所有的班级课程分析数据
	 * @author: zhaoTianQi
	 * @date: 2024/12/11 10:36
	 * @param:
	 * @return: AjaxResult
	 **/
	@GetMapping("/createAllClassCourseAnalysis")
	public AjaxResult createAllClassCourseAnalysis() {
		classStudentCourseReportService.createAllClassCourseAnalysis();
		return AjaxResult.success();
	}

	/**
	 * @description: 获取班级课程分析数据
	 * @author: zhaoTianQi
	 * @date: 2024/12/11 10:22
	 * @param: courseReport
	 * @return: AjaxResult
	 **/
	@GetMapping("/getClassCourseAnalysis")
	public AjaxResult getClassCourseAnalysis(ClassStudentCourseReport courseReport) {
		return AjaxResult.success(classStudentCourseReportService.getClassCourseAnalysis(courseReport));
	}


	/**
	 * @description: 处理所有的课程分析 学生 和 班级
	 * @author: zhaoTianQi 
	 * @date: 2024/12/11 12:36
	 * @param: 
	 * @return: AjaxResult
	 **/
	@GetMapping("/dealAllCourseAnalysis")
	public AjaxResult dealAllCourseAnalysis() {
		classStudentCourseReportService.dealAllCourseAnalysis();
		return AjaxResult.success();
	}
}
