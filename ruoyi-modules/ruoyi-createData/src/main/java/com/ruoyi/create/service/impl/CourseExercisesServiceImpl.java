package com.ruoyi.create.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.utils.Snowflake;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.CourseExercisesMapper;
import com.ruoyi.create.domain.CourseExercises;
import com.ruoyi.create.service.ICourseExercisesService;

import javax.annotation.Resource;

/**
 * 课程习题Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-27
 */
@Service
public class CourseExercisesServiceImpl implements ICourseExercisesService 
{
    @Autowired
    private CourseExercisesMapper courseExercisesMapper;

    @Resource
    private RemoteFileService remoteFileService;

    @Resource
    private RemoteUserService remoteUserService;

    /**
     * 查询课程习题
     * 
     * @param id 课程习题主键
     * @return 课程习题
     */
    @Override
    public CourseExercises selectCourseExercisesById(Long id)
    {
        return courseExercisesMapper.selectCourseExercisesById(id);
    }

    /**
     * 查询课程习题列表
     * 
     * @param courseExercises 课程习题
     * @return 课程习题
     */
    @Override
    public List<CourseExercises> selectCourseExercisesList(CourseExercises courseExercises)
    {
        R<LoginUser> userAndRole = remoteUserService.getUserAndRole(SecurityUtils.getUserId(), SecurityConstants.INNER);
        LoginUser data2 = userAndRole.getData();
        String jobId = data2.getSysUser().getJobId();
        courseExercises.setTeacherId(jobId);
        return courseExercisesMapper.selectCourseExercisesList(courseExercises);
    }

    /**
     * 新增课程习题
     * 
     * @param courseExercises 课程习题
     * @return 结果
     */
    @Override
    public int insertCourseExercises(CourseExercises courseExercises)
    {
        Snowflake snowflake = new Snowflake(1, 1);
        long id = snowflake.generateId();
        if(ObjectUtils.isNotEmpty(courseExercises.getFileIds())){
            remoteFileService.relationFile(courseExercises.getFileIds(),String.valueOf(id));
        }
        R<LoginUser> userAndRole = remoteUserService.getUserAndRole(SecurityUtils.getUserId(), SecurityConstants.INNER);
        LoginUser data2 = userAndRole.getData();
        String jobId = data2.getSysUser().getJobId();
        courseExercises.setId(id);
        courseExercises.setTeacherId(jobId);
        courseExercises.setCreateBy(SecurityUtils.getUsername());
        courseExercises.setCreateTime(DateUtils.getNowDate());
        return courseExercisesMapper.insertCourseExercises(courseExercises);
    }

    /**
     * 修改课程习题
     * 
     * @param courseExercises 课程习题
     * @return 结果
     */
    @Override
    public int updateCourseExercises(CourseExercises courseExercises)
    {
        courseExercises.setUpdateTime(DateUtils.getNowDate());
        return courseExercisesMapper.updateCourseExercises(courseExercises);
    }

    /**
     * 批量删除课程习题
     * 
     * @param ids 需要删除的课程习题主键
     * @return 结果
     */
    @Override
    public int deleteCourseExercisesByIds(Long[] ids)
    {
        remoteFileService.deleteFile(ids[0].toString());
        return courseExercisesMapper.deleteCourseExercisesByIds(ids);
    }

    /**
     * 删除课程习题信息
     * 
     * @param id 课程习题主键
     * @return 结果
     */
    @Override
    public int deleteCourseExercisesById(Long id)
    {
        return courseExercisesMapper.deleteCourseExercisesById(id);
    }

    @Override
    public List<CourseExercises> selectStudentCourseExercisesList(CourseExercises courseExercises) {
        return courseExercisesMapper.selectCourseExercisesList(courseExercises);
    }
}
