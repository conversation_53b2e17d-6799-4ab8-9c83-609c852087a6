package com.ruoyi.create.service;

import java.util.List;
import com.ruoyi.create.domain.ExamineTxt;

/**
 * 课件审核建议Service接口
 *
 * <AUTHOR>
 * @date 2024-09-19
 */
public interface IExamineTxtService
{
    /**
     * 查询课件审核建议
     *
     * @param id 课件审核建议主键
     * @return 课件审核建议
     */
    public ExamineTxt selectExamineTxtById(Long id);

    public String selectSuggestionByPId(Long id);

    /**
     * 查询课件审核建议列表
     *
     * @param examineTxt 课件审核建议
     * @return 课件审核建议集合
     */
    public List<ExamineTxt> selectExamineTxtList(ExamineTxt examineTxt);

    /**
     * 新增课件审核建议
     *
     * @param examineTxt 课件审核建议
     * @return 结果
     */
    public int insertExamineTxt(ExamineTxt examineTxt);

    /**
     * 修改课件审核建议
     *
     * @param examineTxt 课件审核建议
     * @return 结果
     */
    public int updateExamineTxt(ExamineTxt examineTxt);

    /**
     * 批量删除课件审核建议
     *
     * @param ids 需要删除的课件审核建议主键集合
     * @return 结果
     */
    public int deleteExamineTxtByIds(Long[] ids);

    /**
     * 删除课件审核建议信息
     *
     * @param id 课件审核建议主键
     * @return 结果
     */
    public int deleteExamineTxtById(Long id);
    public int deleteExamineTxtByPId(Long id);
}
