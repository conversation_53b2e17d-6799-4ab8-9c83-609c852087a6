package com.ruoyi.create.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.TextbookKeywordData;
import com.ruoyi.create.service.ISTextbookKeywordDataService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 知识图谱-教材关键词数据Controller
 * 
 * <AUTHOR>
 * @date 2024-08-09
 */
@RestController
@RequestMapping("/tbkeyworddata")
public class STextbookKeywordDataController extends BaseController
{
    @Autowired
    private ISTextbookKeywordDataService sTextbookKeywordDataService;

    /**
     * 查询知识图谱-教材关键词数据列表
     */
    @RequiresPermissions("create:tbkeyworddata:list")
    @GetMapping("/list")
    public TableDataInfo list(TextbookKeywordData sTextbookKeywordData)
    {
        startPage();
        List<TextbookKeywordData> list = sTextbookKeywordDataService.selectSTextbookKeywordDataList(sTextbookKeywordData);
        return getDataTable(list);
    }

    /**
     * 导出知识图谱-教材关键词数据列表
     */
    @RequiresPermissions("create:tbkeyworddata:export")
    @Log(title = "知识图谱-教材关键词数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TextbookKeywordData sTextbookKeywordData)
    {
        List<TextbookKeywordData> list = sTextbookKeywordDataService.selectSTextbookKeywordDataList(sTextbookKeywordData);
        ExcelUtil<TextbookKeywordData> util = new ExcelUtil<TextbookKeywordData>(TextbookKeywordData.class);
        util.exportExcel(response, list, "知识图谱-教材关键词数据数据");
    }

    /**
     * 获取知识图谱-教材关键词数据详细信息
     */
    @RequiresPermissions("create:tbkeyworddata:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sTextbookKeywordDataService.selectSTextbookKeywordDataById(id));
    }

    /**
     * 新增知识图谱-教材关键词数据
     */
    @RequiresPermissions("create:tbkeyworddata:add")
    @Log(title = "知识图谱-教材关键词数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TextbookKeywordData sTextbookKeywordData)
    {
        return toAjax(sTextbookKeywordDataService.insertSTextbookKeywordData(sTextbookKeywordData));
    }

    /**
     * 修改知识图谱-教材关键词数据
     */
    @RequiresPermissions("create:tbkeyworddata:edit")
    @Log(title = "知识图谱-教材关键词数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TextbookKeywordData sTextbookKeywordData)
    {
        return toAjax(sTextbookKeywordDataService.updateSTextbookKeywordData(sTextbookKeywordData));
    }

    /**
     * 删除知识图谱-教材关键词数据
     */
    @RequiresPermissions("create:tbkeyworddata:remove")
    @Log(title = "知识图谱-教材关键词数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sTextbookKeywordDataService.deleteSTextbookKeywordDataByIds(ids));
    }
}
