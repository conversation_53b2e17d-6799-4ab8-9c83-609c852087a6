package com.ruoyi.create.service.impl;

import com.ruoyi.create.domain.StudentCourse;
import com.ruoyi.create.service.IStudentCoursesService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> @create 2024-11-06-15:04
 */
@Service
public class StudentCoursesServiceImpl implements IStudentCoursesService {

    @Override
    public List<StudentCourse> selectStudentCoursesList(StudentCourse studentCourse) {
        return null;
    }

    @Override
    public int insertStudentCourses(StudentCourse studentCourse) {
        return 0;
    }
}
