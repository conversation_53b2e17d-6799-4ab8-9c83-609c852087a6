package com.ruoyi.create.service;

import java.util.List;

import com.ruoyi.create.domain.StudentEvaluation;


/**
 * 学生测评Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-19
 */
public interface StudentEvaluationService
{
    /**
     * 查询学生测评
     * 
     * @param id 学生测评主键
     * @return 学生测评
     */
    public StudentEvaluation selectStudentEvaluationById(Long id);

    /**
     * 查询学生测评列表
     * 
     * @param 学生测评
     * @return 学生测评集合
     */
    public List<StudentEvaluation> selectStudentEvaluationList(String studentId);

    /**
     * 新增学生测评
     * 
     * @param sStudentEvaluation 学生测评
     * @return 结果
     */
    public int insertStudentEvaluation(StudentEvaluation sStudentEvaluation);

    /**
     * 修改学生测评
     * 
     * @param sStudentEvaluation 学生测评
     * @return 结果
     */
    public int updateStudentEvaluation(StudentEvaluation sStudentEvaluation);

    /**
     * 批量删除学生测评
     * 
     * @param ids 需要删除的学生测评主键集合
     * @return 结果
     */
    public int deleteStudentEvaluationByIds(Long[] ids);

    /**
     * 删除学生测评信息
     * 
     * @param id 学生测评主键
     * @return 结果
     */
    public int deleteStudentEvaluationById(String id);
}
