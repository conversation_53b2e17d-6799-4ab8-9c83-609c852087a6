package com.ruoyi.create.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.create.domain.HomeworkQuestion;

import java.util.List;

/**
 * 作业题目Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-07
 */
public interface HomeworkQuestionMapper extends BaseMapper<HomeworkQuestion>
{
    /**
     * 查询作业题目
     *
     * @param id 作业题目主键
     * @return 作业题目
     */
    public HomeworkQuestion selectHomeworkQuestionById(Long id);

    /**
     * 查询作业题目列表
     *
     * @param homeworkQuestion 作业题目
     * @return 作业题目集合
     */
    public List<HomeworkQuestion> selectHomeworkQuestionList(HomeworkQuestion homeworkQuestion);

    /**
     * 新增作业题目
     *
     * @param homeworkQuestion 作业题目
     * @return 结果
     */
    public int insertHomeworkQuestion(HomeworkQuestion homeworkQuestion);

    /**
     * 修改作业题目
     *
     * @param homeworkQuestion 作业题目
     * @return 结果
     */
    public int updateHomeworkQuestion(HomeworkQuestion homeworkQuestion);

    /**
     * 删除作业题目
     *
     * @param id 作业题目主键
     * @return 结果
     */
    public int deleteHomeworkQuestionById(Long id);

    /**
     * 批量删除作业题目
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHomeworkQuestionByIds(Long[] ids);
}
