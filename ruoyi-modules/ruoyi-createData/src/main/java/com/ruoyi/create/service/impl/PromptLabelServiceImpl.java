package com.ruoyi.create.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.PromptLabelMapper;
import com.ruoyi.create.domain.PromptLabel;
import com.ruoyi.create.service.IPromptLabelService;

/**
 * prompt模板标签Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
@Service
public class PromptLabelServiceImpl implements IPromptLabelService 
{
    @Autowired
    private PromptLabelMapper promptLabelMapper;

    /**
     * 查询prompt模板标签
     * 
     * @param id prompt模板标签主键
     * @return prompt模板标签
     */
    @Override
    public PromptLabel selectPromptLabelById(Long id)
    {
        return promptLabelMapper.selectPromptLabelById(id);
    }

    /**
     * 查询prompt模板标签列表
     * 
     * @param promptLabel prompt模板标签
     * @return prompt模板标签
     */
    @Override
    public List<PromptLabel> selectPromptLabelList(PromptLabel promptLabel)
    {
        return promptLabelMapper.selectPromptLabelList(promptLabel);
    }

    /**
     * 新增prompt模板标签
     * 
     * @param promptLabel prompt模板标签
     * @return 结果
     */
    @Override
    public int insertPromptLabel(PromptLabel promptLabel)
    {
        return promptLabelMapper.insertPromptLabel(promptLabel);
    }

    /**
     * 修改prompt模板标签
     * 
     * @param promptLabel prompt模板标签
     * @return 结果
     */
    @Override
    public int updatePromptLabel(PromptLabel promptLabel)
    {
        return promptLabelMapper.updatePromptLabel(promptLabel);
    }

    /**
     * 批量删除prompt模板标签
     * 
     * @param ids 需要删除的prompt模板标签主键
     * @return 结果
     */
    @Override
    public int deletePromptLabelByIds(Long[] ids)
    {
        return promptLabelMapper.deletePromptLabelByIds(ids);
    }

    /**
     * 删除prompt模板标签信息
     * 
     * @param id prompt模板标签主键
     * @return 结果
     */
    @Override
    public int deletePromptLabelById(Long id)
    {
        return promptLabelMapper.deletePromptLabelById(id);
    }
}
