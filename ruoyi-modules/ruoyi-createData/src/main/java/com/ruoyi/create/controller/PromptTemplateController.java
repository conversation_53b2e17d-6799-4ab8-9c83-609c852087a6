package com.ruoyi.create.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.PromptTemplate;
import com.ruoyi.create.service.IPromptTemplateService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * prompt模板Controller
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
@RestController
@RequestMapping("/template")
public class PromptTemplateController extends BaseController
{
    @Autowired
    private IPromptTemplateService promptTemplateService;

    /**
     * 查询prompt模板列表
     */
    @RequiresPermissions("create:template:list")
    @GetMapping("/list")
    public TableDataInfo list(PromptTemplate promptTemplate)
    {
        startPage();
        List<PromptTemplate> list = promptTemplateService.selectPromptTemplateList(promptTemplate);
        return getDataTable(list);
    }

    /**
     * 导出prompt模板列表
     */
    @RequiresPermissions("create:template:export")
    @Log(title = "prompt模板", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PromptTemplate promptTemplate)
    {
        List<PromptTemplate> list = promptTemplateService.selectPromptTemplateList(promptTemplate);
        ExcelUtil<PromptTemplate> util = new ExcelUtil<PromptTemplate>(PromptTemplate.class);
        util.exportExcel(response, list, "prompt模板数据");
    }

    /**
     * 获取prompt模板详细信息
     */
    @RequiresPermissions("create:template:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(promptTemplateService.selectPromptTemplateById(id));
    }

    /**
     * 新增prompt模板
     */
    @RequiresPermissions("create:template:add")
    @Log(title = "prompt模板", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PromptTemplate promptTemplate)
    {
        return toAjax(promptTemplateService.insertPromptTemplate(promptTemplate));
    }

    /**
     * 修改prompt模板
     */
    @RequiresPermissions("create:template:edit")
    @Log(title = "prompt模板", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PromptTemplate promptTemplate)
    {
        return toAjax(promptTemplateService.updatePromptTemplate(promptTemplate));
    }

    /**
     * 删除prompt模板
     */
    @RequiresPermissions("create:template:remove")
    @Log(title = "prompt模板", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(promptTemplateService.deletePromptTemplateByIds(ids));
    }

    /**
     * 获取全部prompt
     * @return
     */
    @GetMapping("/all")
    public AjaxResult selectPromptAll()
    {
        return success(promptTemplateService.selectPromptAll());
    }

    @GetMapping(value = "/prompt/{id}")
    public PromptTemplate getPromptInfo(@PathVariable("id") Long id)
    {
        return promptTemplateService.selectPromptTemplateById(id);
    }

}
