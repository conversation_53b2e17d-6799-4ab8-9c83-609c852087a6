package com.ruoyi.create.service;

import java.util.List;
import com.ruoyi.create.domain.PrompDiagram;

/**
 * prompt模板文生图相关推理参数Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
public interface IPrompDiagramService 
{
    /**
     * 查询prompt模板文生图相关推理参数
     * 
     * @param id prompt模板文生图相关推理参数主键
     * @return prompt模板文生图相关推理参数
     */
    public PrompDiagram selectPrompDiagramById(Long id);

    /**
     * 查询prompt模板文生图相关推理参数列表
     * 
     * @param prompDiagram prompt模板文生图相关推理参数
     * @return prompt模板文生图相关推理参数集合
     */
    public List<PrompDiagram> selectPrompDiagramList(PrompDiagram prompDiagram);

    /**
     * 新增prompt模板文生图相关推理参数
     * 
     * @param prompDiagram prompt模板文生图相关推理参数
     * @return 结果
     */
    public int insertPrompDiagram(PrompDiagram prompDiagram);

    /**
     * 修改prompt模板文生图相关推理参数
     * 
     * @param prompDiagram prompt模板文生图相关推理参数
     * @return 结果
     */
    public int updatePrompDiagram(PrompDiagram prompDiagram);

    /**
     * 批量删除prompt模板文生图相关推理参数
     * 
     * @param ids 需要删除的prompt模板文生图相关推理参数主键集合
     * @return 结果
     */
    public int deletePrompDiagramByIds(Long[] ids);

    /**
     * 删除prompt模板文生图相关推理参数信息
     * 
     * @param id prompt模板文生图相关推理参数主键
     * @return 结果
     */
    public int deletePrompDiagramById(Long id);
}
