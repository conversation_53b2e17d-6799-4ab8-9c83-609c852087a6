package com.ruoyi.create.mapper;

import java.util.List;
import com.ruoyi.create.domain.KnowledgeRouting;

/**
 * 知识库路由配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-30
 */
public interface KnowledgeRoutingMapper 
{
    /**
     * 查询知识库路由配置
     * 
     * @param id 知识库路由配置主键
     * @return 知识库路由配置
     */
    public KnowledgeRouting selectKnowledgeRoutingById(Long id);

    /**
     * 查询知识库路由配置列表
     * 
     * @param knowledgeRouting 知识库路由配置
     * @return 知识库路由配置集合
     */
    public List<KnowledgeRouting> selectKnowledgeRoutingList(KnowledgeRouting knowledgeRouting);

    /**
     * 新增知识库路由配置
     * 
     * @param knowledgeRouting 知识库路由配置
     * @return 结果
     */
    public int insertKnowledgeRouting(KnowledgeRouting knowledgeRouting);

    /**
     * 修改知识库路由配置
     * 
     * @param knowledgeRouting 知识库路由配置
     * @return 结果
     */
    public int updateKnowledgeRouting(KnowledgeRouting knowledgeRouting);

    /**
     * 删除知识库路由配置
     * 
     * @param id 知识库路由配置主键
     * @return 结果
     */
    public int deleteKnowledgeRoutingById(Long id);

    /**
     * 批量删除知识库路由配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKnowledgeRoutingByIds(Long[] ids);

    /**
     * 根据路由查询知识库
     */
    public List<KnowledgeRouting> selectKnowledgeByMenuRouting(String menuRouting);
}
