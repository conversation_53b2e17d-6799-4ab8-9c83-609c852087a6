package com.ruoyi.create.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.create.Vo.AmbitTeacherVo;
import com.ruoyi.create.Vo.CourseStatisticsVo;
import com.ruoyi.create.domain.ClassInfo;
import com.ruoyi.create.domain.CourseManagement;
import com.ruoyi.create.domain.StudentCourse;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 课程管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-10-11
 */
public interface CourseManagementMapper extends BaseMapper<CourseManagement> {

    /**
     * 查询课程管理
     *
     * @param id 课程管理主键
     * @return 课程管理
     */
    public CourseManagement selectCourseManagementById(Long id);

    /**
     * 查询课程管理列表
     *
     * @param courseManagement 课程管理
     * @return 课程管理集合
     */
    public List<CourseManagement> selectCourseManagementList(CourseManagement courseManagement);

    /**
     * 新增课程管理
     *
     * @param courseManagement 课程管理
     * @return 结果
     */
    public int insertCourseManagement(CourseManagement courseManagement);

    /**
     * 修改课程管理
     *
     * @param courseManagement 课程管理
     * @return 结果
     */
    public int updateCourseManagement(CourseManagement courseManagement);

    /**
     * 删除课程管理
     *
     * @param id 课程管理主键
     * @return 结果
     */
    public int deleteCourseManagementById(Long id);

    /**
     * 批量删除课程管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCourseManagementByIds(Long[] ids);

    public List<CourseManagement> selectCourseManagementAll();

    public List<CourseManagement> getAll();

    public List<String> selectByclassName(@Param("studentClass") String studentClass);

    public List<CourseManagement> selectCourseManagementList2(CourseManagement courseManagement);

    public String selectCourseManagementByStudentId(@Param("studentId") String studentId);

    public String selectCourseManagementByStudentId1(@Param("studentId") String studentId);

    public int deleteCourseManagement(CourseManagement courseManagement);

    public List<ClassInfo> selectCourseManagementByCourseName(@Param("courseName") String courseName);

    public String selectNameById(String teacherId);

    public int insertStudentCourse(CourseManagement courseManagement);


    public int insertSStudentCourse(StudentCourse studentCourse);

    public String selectStudentClassName(String classId);

    public List<CourseManagement> selectByJobId(@Param("jobId") String jobId,@Param("courseName") String courseName,@Param("universityId") Long universityId);

    public List<CourseManagement> selectCourseManagements(CourseManagement courseManagement);

    public String selectStudentClassName1(String studentId);

    public List<CourseManagement> selectCourseManagementList3(CourseManagement courseManagement);

    public String selectStudentNameById(String studentId);

    public int deleteCourseManagement1(CourseManagement courseManagement);

    public Long selectIdByClassName(@Param("courseName") String courseName, @Param("className") String className, @Param("jobId") String jobId);

    public List<CourseManagement> selectStudentCourseList(String studentId);

    CourseManagement selectStudentCourseInfo(CourseManagement courseManagement);
	List<CourseManagement> selectStudentCourseInf2(CourseManagement courseManagement);
	@MapKey("student_id")
	Map<String, HashMap<String,Object>>  getUserByStudentId(String studentId);

	List<CourseManagement> selectStudentCourseInfoList(Long id);

	String getDialogueRecordingCount(String userName);

	@MapKey("student_id")
	Map<String, HashMap<String, Object>> getUserByStudentIds(@Param("studentIdList") List<String> studentIdList);

	List<CourseManagement> selectStudentCourseInfo2(@Param("courseName") String courseName, @Param("courseClassId") Long courseClassId);

	List<CourseManagement> selectCourseManagementAll2();

	List<CourseManagement> selectCourseManagementAll3();

    List<CourseManagement> selectTermByJobId(@Param("jobId") String jobId, @Param("universityId") Long universityId);

    List<CourseManagement> selectStudentClassByJobId(@Param("jobId")String jobId,  @Param("universityId") Long universityId);

    int countStudentCourse(StudentCourse studentCourse);

    int deleteCourseManagement2(CourseManagement courseManagement);

    int deleteCourseName(StudentCourse studentCourse);

    int deleteCourseVideoByNameAndId(CourseManagement courseManagement);

    int deleteCourseCaseByNameAndId(CourseManagement courseManagement);

    int deletesCourseExercisesByNameAndId(CourseManagement courseManagement);

     Collection<Long> seleteCourseVideoBusiIdByNameAndId(CourseManagement courseManagement);

    Collection<Long> seleteCourseCaseBusiIdByNameAndId(CourseManagement courseManagement);

    Collection<Long> seletesCourseExercisesBusiIdByNameAndId(CourseManagement courseManagement);

    List<CourseManagement> selectDISTINCTCoursenameCourseManagementList(CourseManagement courseManagement);

    List<String> selectKcglList(@Param("jobId")String jobId, @Param("universityId")Long universityId);

    List<CourseManagement> selectByJobIds(@Param("jobIds")List<String> jobIds,@Param("courseName")String courseName);

    List<CourseManagement> selectTermByJobIds(@Param("jobIds")List<String> jobIds);

    List<CourseManagement> selectStudentClassByJobIds(@Param("jobIds")List<String> jobIds);

    CourseStatisticsVo getStudentCourseStatistics(String courseName);

    List<AmbitTeacherVo> getTeacherCourseStatistics(String courseName);
}
