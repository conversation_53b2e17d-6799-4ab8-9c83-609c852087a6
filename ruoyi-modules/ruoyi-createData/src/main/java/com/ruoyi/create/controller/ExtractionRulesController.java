package com.ruoyi.create.controller;


import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.ExtractionRules;
import com.ruoyi.create.service.IExtractionRulesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 提取规则Controller
 * 
 * <AUTHOR>
 * @date 2023-12-13
 */
@RestController
@RequestMapping("/rules")
public class ExtractionRulesController extends BaseController
{
    @Autowired
    private IExtractionRulesService extractionRulesService;

    /**
     * 查询提取规则列表
     */
    @RequiresPermissions("create:rules:list")
    @GetMapping("/list")
    public TableDataInfo list(ExtractionRules extractionRules)
    {
        startPage();
        List<ExtractionRules> list = extractionRulesService.selectExtractionRulesList(extractionRules);
        return getDataTable(list);
    }

    /**
     * 获取提取规则详细信息
     */
    @RequiresPermissions("create:rules:query")
    @GetMapping(value = "/{rulesId}")
    public AjaxResult getInfo(@PathVariable("rulesId") Long rulesId)
    {
        return success(extractionRulesService.selectExtractionRulesByRulesId(rulesId));
    }

    /**
     * 新增提取规则
     */
    @RequiresPermissions("create:rules:add")
    @Log(title = "提取规则", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ExtractionRules extractionRules)
    {
        return toAjax(extractionRulesService.insertExtractionRules(extractionRules));
    }

    /**
     * 修改提取规则
     */
    @RequiresPermissions("create:rules:edit")
    @Log(title = "提取规则", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ExtractionRules extractionRules)
    {
        return toAjax(extractionRulesService.updateExtractionRules(extractionRules));
    }

    /**
     * 删除提取规则
     */
    @RequiresPermissions("create:rules:remove")
    @Log(title = "提取规则", businessType = BusinessType.DELETE)
	@DeleteMapping("/{rulesIds}")
    public AjaxResult remove(@PathVariable Long[] rulesIds)
    {
        return toAjax(extractionRulesService.deleteExtractionRulesByRulesIds(rulesIds));
    }

    /**
     * 获取全部提取规则
     */
    @GetMapping("/find")
    public AjaxResult find()
    {
        return success(extractionRulesService.selectExtractionRulesAll());
    }
}
