package com.ruoyi.create.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.CareCallMapper;
import com.ruoyi.create.domain.CareCall;
import com.ruoyi.create.service.ICareCallService;

/**
 * 服务调用统计Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
@Service
public class CareCallServiceImpl implements ICareCallService 
{
    @Autowired
    private CareCallMapper careCallMapper;

    /**
     * 查询服务调用统计
     * 
     * @param id 服务调用统计主键
     * @return 服务调用统计
     */
    @Override
    public CareCall selectCareCallById(Long id)
    {
        return careCallMapper.selectCareCallById(id);
    }

    /**
     * 查询服务调用统计列表
     * 
     * @param careCall 服务调用统计
     * @return 服务调用统计
     */
    @Override
    public List<CareCall> selectCareCallList(CareCall careCall)
    {
        return careCallMapper.selectCareCallList(careCall);
    }

    /**
     * 新增服务调用统计
     * 
     * @param careCall 服务调用统计
     * @return 结果
     */
    @Override
    public int insertCareCall(CareCall careCall)
    {
        return careCallMapper.insertCareCall(careCall);
    }

    /**
     * 修改服务调用统计
     * 
     * @param careCall 服务调用统计
     * @return 结果
     */
    @Override
    public int updateCareCall(CareCall careCall)
    {
        return careCallMapper.updateCareCall(careCall);
    }

    /**
     * 批量删除服务调用统计
     * 
     * @param ids 需要删除的服务调用统计主键
     * @return 结果
     */
    @Override
    public int deleteCareCallByIds(Long[] ids)
    {
        return careCallMapper.deleteCareCallByIds(ids);
    }

    /**
     * 删除服务调用统计信息
     * 
     * @param id 服务调用统计主键
     * @return 结果
     */
    @Override
    public int deleteCareCallById(Long id)
    {
        return careCallMapper.deleteCareCallById(id);
    }
}
