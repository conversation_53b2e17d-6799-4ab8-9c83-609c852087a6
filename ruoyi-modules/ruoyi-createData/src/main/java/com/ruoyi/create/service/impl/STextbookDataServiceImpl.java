package com.ruoyi.create.service.impl;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.excel.EasyExcel;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.easyexcel.EasyExcelUtils;
import com.ruoyi.common.core.utils.easyexcel.Message;
import com.ruoyi.common.core.utils.uuid.UUID;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.domain.*;
import com.ruoyi.create.mapper.STextbookKeywordDataMapper;
import com.ruoyi.create.utils.Snowflake;
import com.ruoyi.system.api.RemoteDictTypeService;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.domain.SysDictData;
import com.ruoyi.system.api.domain.SysFileInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.STextbookDataMapper;
import com.ruoyi.create.service.ISTextbookDataService;

/**
 * 文献整理- 教材原始数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-31
 */
@Service
public class STextbookDataServiceImpl implements ISTextbookDataService 
{
    @Autowired
    private STextbookDataMapper sTextbookDataMapper;


    /*@Autowired
    private STextbookCountMapper sTextbookCountMapper;*/

    @Autowired
    private RemoteDictTypeService remoteDictTypeService;
    @Autowired
    private STextbookKeywordDataMapper sTextbookKeywordDataMapper;

    @Autowired
    private RemoteFileService remoteFileService;

    /**
     * 查询文献整理- 教材原始数据
     * 
     * @param id 文献整理- 教材原始数据主键
     * @return 文献整理- 教材原始数据
     */
    @Override
    public TextbookData selectSTextbookDataById(Long id)
    {
        return sTextbookDataMapper.selectSTextbookDataById(id);
    }

    /**
     * 查询文献整理- 教材原始数据列表
     * 
     * @param sTextbookData 文献整理- 教材原始数据
     * @return 文献整理- 教材原始数据
     */
    @Override
    public List<TextbookData> selectSTextbookDataList(TextbookData sTextbookData)
    {
        return sTextbookDataMapper.selectSTextbookDataList(sTextbookData);
    }

    /**
     * 新增文献整理- 教材原始数据
     * 
     * @param sTextbookData 文献整理- 教材原始数据
     * @return 结果
     */
    @Override
    public AjaxResult insertSTextbookData(TextbookData sTextbookData)
    {
        //读取文件
        Message message = new Message();
        SysFileInfo sysFileInfo = remoteFileService.getFileInfo(sTextbookData.getFileId());
        if(sysFileInfo ==null){
            return AjaxResult.error("未获取到文件");
        }
        InputStream inputStream = null;
        EasyExcelUtils easyExcelUtils = new EasyExcelUtils(TextbookData.class);  //创建工具类时传递class，用于后面比对表头使用
        List<TextbookData> roadDataList = new ArrayList<>();
        try {
            inputStream = new FileInputStream(sysFileInfo.getFilePath());
            //读取所有的sheet
            EasyExcel.read(inputStream, TextbookData.class,easyExcelUtils).doReadAll();
            //只能读取到最前面的一个sheet
            //EasyExcel.read(inputStream,LiteratrueData.class,easyExcelUtils).sheet().doRead();
            message = easyExcelUtils.getMessage();
            if(Message.OK == message.getType()){
                List<Object> list = easyExcelUtils.getList();
                if (null != list && list.size() > 0) {
                    Date nowDate = new Date();
                    for (int i = 0;i < list.size();i++) { //设置其他非excel字段的值
                        TextbookData roadData = (TextbookData) list.get(i);
                        roadData.setTextbookId(roadData.getTextbookId());
                        roadData.setTextbook(roadData.getTextbook());
                        roadData.setAcademicDiscipline(roadData.getAcademicDiscipline());
                        roadData.setColleName(roadData.getColleName());
                        roadData.setEducationalLevel(roadData.getEducationalLevel());
                        roadData.setMajorName(roadData.getMajorName());
                        roadData.setPublishingHouse(roadData.getPublishingHouse());
                        roadData.setAuthor(roadData.getAuthor());
                        roadData.setKnowledgeCategory(roadData.getKnowledgeCategory());
                        roadData.setCategory(roadData.getCategory());
                        roadData.setKeyword(roadData.getKeyword());
                        roadData.setCreateTime(nowDate);
                        roadData.setCreateBy(SecurityUtils.getUsername());
                        roadDataList.add(roadData);
                    }
                }
            }else{
                return AjaxResult.error(message.getMsg());
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        int i = sTextbookDataMapper.insertSTextbookDataList(roadDataList);
        int sum = sTextbookKeywordDataMapper.insertTextbookKeywordDataList(roadDataList);
        if(!(i>0)){
            return AjaxResult.error();
        }
        /*//生成目标表数据 并 修改目标表数据
        List<TextbookData> collect = roadDataList.stream()
                .distinct() // 去除 Keyword 字段的重复
                .collect(Collectors.toList());

        for (TextbookData data : collect) {
            TextbookCount textbookCount = new TextbookCount();
            textbookCount.setKeyword(data.getKeyword());
            List<TextbookCount> textbookCountList = sTextbookCountMapper.selectSTextbookCountList(textbookCount);
            if(!textbookCountList.isEmpty()){
                //统计本次上传的数量
                Long aLong = countKeywordOccurrences(roadDataList,data.getKeyword());
                TextbookCount textbookCounTmp = textbookCountList.get(0);
                textbookCounTmp.setCount(aLong);
                sTextbookCountMapper.updateSTextbookCount(textbookCounTmp);
            }else{
                Long aLong = countKeywordOccurrences(roadDataList,data.getKeyword());
                textbookCount.setTextbookId(data.getTextbookId());
                textbookCount.setTextbook(data.getTextbook());
                textbookCount.setCategory(data.getCategory());
                textbookCount.setKeyword(data.getKeyword());
                textbookCount.setSubjectId(
                        data.getCategory().equals("高影响力作者") ? "1" :
                                data.getCategory().equals("研究机构") ? "2" :
                                        "0"
                );
                textbookCount.setCount(aLong);

                sTextbookCountMapper.insertSTextbookCount(textbookCount);
            }
        }*/
        return AjaxResult.success();
    }

    public static Long countKeywordOccurrences(List<TextbookData> roadDataList, String keyword) {
        return roadDataList.stream()
                .filter(data -> data.getKeyword().equals(keyword))
                .count();
    }
    /**
     * 修改文献整理- 教材原始数据
     * 
     * @param sTextbookData 文献整理- 教材原始数据
     * @return 结果
     */
    @Override
    public int updateSTextbookData(TextbookData sTextbookData)
    {
        sTextbookData.setUpdateTime(DateUtils.getNowDate());
        return sTextbookDataMapper.updateSTextbookData(sTextbookData);
    }

    /**
     * 批量删除文献整理- 教材原始数据
     * 
     * @param ids 需要删除的文献整理- 教材原始数据主键
     * @return 结果
     */
    @Override
    public int deleteSTextbookDataByIds(Long[] ids)
    {
        return sTextbookDataMapper.deleteSTextbookDataByIds(ids);
    }

    /**
     * 删除文献整理- 教材原始数据信息
     * 
     * @param id 文献整理- 教材原始数据主键
     * @return 结果
     */
    @Override
    public int deleteSTextbookDataById(Long id)
    {
        return sTextbookDataMapper.deleteSTextbookDataById(id);
    }

    @Override
    public AjaxResult selectGroupTextbookData(TextbookData sTextbookData) {
        Snowflake snowflake = new Snowflake(1, 1);
        if(sTextbookData.getKeywordValue().equals("academic_discipline")&&StringUtils.isBlank(sTextbookData.getKeyword())){
            sTextbookData.setKeyword("新文科");
        }
        // 设置默认关键词
        List<SysDictData> knowledgeGraphList = remoteDictTypeService.dictTypeGetInfo("knowledge_graph", SecurityConstants.INNER);
        List<SysDictData> knowledgeTypeList = remoteDictTypeService.dictTypeGetInfo("knowledge_type", SecurityConstants.INNER);
        // 初始化节点数据列表
        List<LiteratrueNodesData> literatrueNodesDataList = new ArrayList<>();
        // 初始化连接数据列表
        List<LiteratrueLinks> literatrueLinksList = new ArrayList<>();

        // 添加顶级节点
        literatrueNodesDataList.add(createLiteratrueNodesData(0L, 1L, sTextbookData.getKeyword(),sTextbookData.getKeywordValue()));
        //二级节点
        List<Long> secondNadeList =new ArrayList<>();
        List<TextbookData>  textbookDataList = new ArrayList<>();
        SysDictData result = knowledgeGraphList.stream()
                .filter(data -> sTextbookData.getKeywordValue().equals(data.getDictLabel()))
                .findFirst()
                .orElse(null);
        if (result==null){
            return  AjaxResult.error("此关键词未定义相关信息");
        }


        if("knowledge_category".equals(sTextbookData.getKeywordValue())){
            TextbookKeywordData textbookKeywordData = new TextbookKeywordData();
            textbookKeywordData.setKnowledgeCategory(sTextbookData.getKeyword());
            List<String> category =sTextbookKeywordDataMapper.getCategory(textbookKeywordData);
            for (int i = 0; i <category.size() ; i++) {
                literatrueNodesDataList.add(createLiteratrueNodesData(i+1L, 2L, category.get(i),"category"));
                sTextbookData.setCategory(category.get(i));
                sTextbookData.setField("keyword");
                textbookDataList = sTextbookDataMapper.selectTextbookDataKnowledgeGraph(sTextbookData);
                addNodesAndLinks(textbookDataList, i+1L, 3L+i, literatrueNodesDataList, literatrueLinksList,"keyword",snowflake);
                secondNadeList.add(i+1L);
            }
        }else{
            String[] split =  result.getDictValue().split(",");
            for (int i = 0; i <split.length ; i++) {
                String dictValue = split[i];
                SysDictData resultNodesData = knowledgeTypeList.stream()
                        .filter(data -> dictValue.equals(data.getDictValue()))
                        .findFirst()
                        .orElse(null);
                if("knowledge".equals(dictValue)){
                    literatrueNodesDataList.add(createLiteratrueNodesData(i+1L, 2L, resultNodesData.getDictLabel(),resultNodesData.getDictValue()));
                    SysDictData result1 = knowledgeGraphList.stream()
                            .filter(data -> dictValue.equals(data.getDictLabel()))
                            .findFirst()
                            .orElse(null);
                    List<String> category =Arrays.asList(result1.getDictValue().split(","));
                    addNodesAndLinksBYknowledgeCategory(category,i+1L, 3L+i, literatrueNodesDataList, literatrueLinksList,"knowledge_category",snowflake);
                } else {
                    literatrueNodesDataList.add(createLiteratrueNodesData(i+1L, 2L, resultNodesData.getDictLabel(),resultNodesData.getDictValue()));
                        sTextbookData.setField(dictValue);
                        textbookDataList = sTextbookDataMapper.selectTextbookDataKnowledgeGraph(sTextbookData);

                    addNodesAndLinks(textbookDataList, i+1L, 3L+i, literatrueNodesDataList, literatrueLinksList,dictValue,snowflake);
                    secondNadeList.add(i+1L);
                }
            }
        }

        // 添加源节点链接
        addSourceNodeLinks(literatrueLinksList,secondNadeList);
        sTextbookData.setLiteratrueLinks(literatrueLinksList);
        sTextbookData.setLiteratrueNodesData(literatrueNodesDataList);
        return AjaxResult.success(sTextbookData);
    }


    /**
     * 知识图谱-新
     * @param sTextbookData
     * @return
     */
    @Override
    public AjaxResult selectNewGroupTextbookData(TextbookData sTextbookData) {
        Snowflake snowflake = new Snowflake(1, 1);
        if(sTextbookData.getKeywordValue().equals("new_liberal_arts")&&StringUtils.isBlank(sTextbookData.getKeyword())){
            sTextbookData.setKeyword("新文科");
        }
        // 设置默认关键词
        List<SysDictData> knowledgeGraphList = remoteDictTypeService.dictTypeGetInfo("knowledge_graph_new", SecurityConstants.INNER);
        List<SysDictData> knowledgeTypeList = remoteDictTypeService.dictTypeGetInfo("knowledge_type_select_new", SecurityConstants.INNER);
        List<SysDictData> knowledgeConfigList = remoteDictTypeService.dictTypeGetInfo("knowledge_config", SecurityConstants.INNER);
        List<SysDictData> knowledgeTypeSort = remoteDictTypeService.dictTypeGetInfo("knowledge_type_new", SecurityConstants.INNER);
        // 初始化节点数据列表
        List<LiteratrueNodesData> literatrueNodesDataList = new ArrayList<>();
        // 初始化连接数据列表
        List<LiteratrueLinks> literatrueLinksList = new ArrayList<>();

        // 添加顶级节点
        literatrueNodesDataList.add(createLiteratrueNodesData(0L, 1L, sTextbookData.getKeyword(),sTextbookData.getKeywordValue(),"1"));

        //判断二级是值还是节点
        //knowledgeTypeSort 通过字典项查询出当前类型的层级
        SysDictData labelSort = knowledgeTypeSort.stream()
                .filter(data -> sTextbookData.getKeywordValue().equals(data.getDictValue()))
                .findFirst()
                .orElse(null);
        //二级节点
        List<Long> secondNadeList =new ArrayList<>();
        List<TextbookData>  textbookDataList = new ArrayList<>();
        SysDictData result = knowledgeGraphList.stream()
                .filter(data -> sTextbookData.getKeywordValue().equals(data.getDictLabel()))
                .findFirst()
                .orElse(null);
        if (result==null){
            return  AjaxResult.error("此关键词未定义相关信息");
        }
        try {
            String[] split =  result.getDictValue().split(",");
            for (int i = 0; i <split.length ; i++) {
                    String dictValue = split[i];
                    SysDictData resultNodesData = knowledgeTypeList.stream()
                            .filter(data -> dictValue.equals(data.getDictValue()))
                            .findFirst()
                            .orElse(null);
                    if("knowledge".equals(dictValue)){
                        literatrueNodesDataList.add(createLiteratrueNodesData(i+1L, 3L, resultNodesData.getDictLabel(),resultNodesData.getDictValue()));
                        SysDictData result1 = knowledgeGraphList.stream()
                                .filter(data -> dictValue.equals(data.getDictLabel()))
                                .findFirst()
                                .orElse(null);
                        List<String> category =Arrays.asList(result1.getDictValue().split(","));
                        addNodesAndLinksBYknowledgeCategory(category,i+1L, 4L+i, literatrueNodesDataList, literatrueLinksList,"knowledge_category",snowflake);
                        secondNadeList.add(i+1L);
                    }else if("academic_discipline".equals(dictValue)){
                        SysDictData result1 = knowledgeConfigList.stream()
                                .filter(data -> dictValue.equals(data.getDictLabel()))
                                .findFirst()
                                .orElse(null);
                        List<String> academicDisciplineList =Arrays.asList(result1.getDictValue().split(","));
                        addNodesAndLinksBYknowledgeCategory(academicDisciplineList,0L, 3L, literatrueNodesDataList, literatrueLinksList,dictValue,snowflake);
                    } else if("colle_name".equals(dictValue)||"major_name".equals(dictValue)||"course".equals(dictValue)){
                        if("course".equals(dictValue)&&sTextbookData.getKeywordValue().equals("keyword")){
                            literatrueNodesDataList.add(createLiteratrueNodesData(i+1L, 3L, resultNodesData.getDictLabel(),resultNodesData.getDictValue()));
                            sTextbookData.setField(dictValue);
                            textbookDataList = sTextbookDataMapper.selectTextbookDataKnowledgeGraph(sTextbookData);
                            addNodesAndLinks(textbookDataList, i+1L, 4L+i, literatrueNodesDataList, literatrueLinksList,dictValue,snowflake);
                            secondNadeList.add(i+1L);
                        }else{
                            SysDictData result1 = knowledgeConfigList.stream()
                                    .filter(data -> sTextbookData.getKeyword().equals(data.getDictLabel()))
                                    .findFirst()
                                    .orElse(null);
                            List<String> colleNameList =Arrays.asList(result1.getDictValue().split(","));
                            addNodesAndLinksBYknowledgeCategory(colleNameList,0L, 3L, literatrueNodesDataList, literatrueLinksList,dictValue,snowflake);
                        }
                    } else if("knowledge_category".equals(dictValue)){
                        SysDictData result1 = knowledgeConfigList.stream()
                                .filter(data -> dictValue.equals(data.getDictLabel()))
                                .findFirst()
                                .orElse(null);
                        List<String> knowledgeCategoryList =Arrays.asList(result1.getDictValue().split(","));
                        addNodesAndLinksBYknowledgeCategory(knowledgeCategoryList,0L, 3L, literatrueNodesDataList, literatrueLinksList,dictValue,snowflake);
                        //获取理论知识、学术训练、社会实践在节点中id
                        List<LiteratrueNodesData>  CategoryLiteratrueNodesData =new ArrayList<>();
                        for (String s : knowledgeCategoryList) {
                            List<LiteratrueNodesData> nodesByName = getNodesByName(literatrueNodesDataList, s);
                            CategoryLiteratrueNodesData.add(nodesByName.get(0));
                        }
                        for (LiteratrueNodesData categoryLiteratrueNodesDatum : CategoryLiteratrueNodesData) {
                            TextbookData textbookDataTmp = new TextbookData();
                            textbookDataTmp.setKeyword(sTextbookData.getKeyword());
                            textbookDataTmp.setKeywordValue(sTextbookData.getKeywordValue());
                            textbookDataTmp.setField("category");
                            textbookDataTmp.setKnowledgeCategory(categoryLiteratrueNodesDatum.getName());
                            textbookDataList = sTextbookDataMapper.selectTextbookDataKnowledgeGraph(textbookDataTmp);
                            addNodesAndLinks(textbookDataList, categoryLiteratrueNodesDatum.getId(), 4L+i, literatrueNodesDataList, literatrueLinksList,"category",snowflake);
                        }
                    }else if("keyword".equals(dictValue)){
                        TextbookData textbookDataTmp = new TextbookData();
                        textbookDataTmp.setField(dictValue);
                        textbookDataTmp.setKeyword(sTextbookData.getKeyword());
                        textbookDataTmp.setKeywordValue(sTextbookData.getKeywordValue());
                        textbookDataTmp.setCategory(sTextbookData.getKeyword());
                        if(sTextbookData.getParentKeywordValue().equals("course")){
                            textbookDataTmp.setCourse(sTextbookData.getParentKeyword());
                        }else if (sTextbookData.getParentKeywordValue().equals("textbook")){
                            textbookDataTmp.setTextbook(sTextbookData.getParentKeyword());
                        }
                        textbookDataList = sTextbookDataMapper.selectTextbookDataKnowledgeGraph(textbookDataTmp);
                        addNodesAndLinks(textbookDataList, 0L, 3L, literatrueNodesDataList, literatrueLinksList,dictValue,snowflake);
                        secondNadeList.add(i+1L);
                    }else {
                        if("教材".equals(sTextbookData.getKeyword())){
                            literatrueNodesDataList.add(createLiteratrueNodesData(i+1L, 3L, resultNodesData.getDictLabel(),resultNodesData.getDictValue()));
                            TextbookData textbookDataTmp = new TextbookData();
                            textbookDataTmp.setField(dictValue);
                            textbookDataList = sTextbookDataMapper.selectTextbookDataKnowledgeGraph(textbookDataTmp);
                            addNodesAndLinks(textbookDataList, i+1L, 4L+i, literatrueNodesDataList, literatrueLinksList,dictValue,snowflake);
                            secondNadeList.add(i+1L);
                        }else{
                            literatrueNodesDataList.add(createLiteratrueNodesData(i+1L, 3L, resultNodesData.getDictLabel(),resultNodesData.getDictValue()));
                            sTextbookData.setField(dictValue);
                            textbookDataList = sTextbookDataMapper.selectTextbookDataKnowledgeGraph(sTextbookData);
                            addNodesAndLinks(textbookDataList, i+1L, 4L+i, literatrueNodesDataList, literatrueLinksList,dictValue,snowflake);
                            secondNadeList.add(i+1L);
                        }
                    }
            }

            //父级节点
            if(!"new_liberal_arts".equals(sTextbookData.getKeywordValue())){
                if("1".equals(sTextbookData.getParentFlag())){
                    if("academic_discipline".equals(sTextbookData.getKeywordValue())){
                        addNodesParentAndLinksBYknowledgeCategory("新文科",0L, 2L, literatrueNodesDataList, literatrueLinksList,"new_liberal_arts",snowflake,"1");
                    }else if("colle_name".equals(sTextbookData.getKeywordValue())||"major_name".equals(sTextbookData.getKeywordValue())||"course".equals(sTextbookData.getKeywordValue())){
                        /*SysDictData result1 = knowledgeConfigList.stream()
                                .filter(data -> sTextbookData.getKeyword().equals(data.getDictLabel()))
                                .findFirst()
                                .orElse(null);*/
                        SysDictData resultGraphList = knowledgeGraphList.stream()
                                .filter(data -> data.getDictValue() != null && !data.getDictValue().isEmpty())
                                .filter(data -> Arrays.stream(data.getDictValue().split(","))
                                        .anyMatch(label -> label.trim().equals(sTextbookData.getKeywordValue())))
                                .findFirst()
                                .orElse(null);

                        SysDictData result1 = knowledgeConfigList.stream()
                                .filter(data -> data.getDictValue() != null && !data.getDictValue().isEmpty() && data.getDictSort()==(labelSort.getDictSort()))
                                .filter(data -> Arrays.stream(data.getDictValue().split(","))
                                        .anyMatch(label -> label.trim().equals(sTextbookData.getKeyword())))
                                .findFirst()
                                .orElse(null);
                        addNodesParentAndLinksBYknowledgeCategory(result1.getDictLabel(),0L, 2L, literatrueNodesDataList, literatrueLinksList,resultGraphList.getDictLabel(),snowflake,"1");
                    }else if("textbook".equals(sTextbookData.getKeywordValue())) {
                        SysDictData resultGraphList = knowledgeGraphList.stream()
                                .filter(data -> data.getDictValue() != null && !data.getDictValue().isEmpty())
                                .filter(data -> Arrays.stream(data.getDictValue().split(","))
                                        .anyMatch(label -> label.trim().equals(sTextbookData.getKeywordValue())))
                                .findFirst()
                                .orElse(null);
                        TextbookData textbookDataTmp = new TextbookData();
                        if(!("教材".equals(sTextbookData.getKeyword()))){
                            textbookDataTmp.setKeyword(sTextbookData.getKeyword());
                            textbookDataTmp.setKeywordValue(sTextbookData.getKeywordValue());
                        }
                        textbookDataTmp.setField(sTextbookData.getKeywordValue());
                        textbookDataList = sTextbookDataMapper.selectTextbookDataKnowledgeGraph(textbookDataTmp);
                        addNodesParentAndLinksBYknowledgeCategory(textbookDataList.get(0).getCourse(),0L, 2L, literatrueNodesDataList, literatrueLinksList,resultGraphList.getDictLabel(),snowflake,"1");
                    }
                }else if("textbook".equals(sTextbookData.getKeywordValue())) {
                    SysDictData resultGraphList = knowledgeGraphList.stream()
                            .filter(data -> data.getDictValue() != null && !data.getDictValue().isEmpty())
                            .filter(data -> Arrays.stream(data.getDictValue().split(","))
                                    .anyMatch(label -> label.trim().equals(sTextbookData.getKeywordValue())))
                            .findFirst()
                            .orElse(null);
                    TextbookData textbookDataTmp = new TextbookData();
                    if(!("教材".equals(sTextbookData.getKeyword()))){
                        textbookDataTmp.setKeyword(sTextbookData.getKeyword());
                        textbookDataTmp.setKeywordValue(sTextbookData.getKeywordValue());
                    }
                    textbookDataTmp.setField(sTextbookData.getKeywordValue());
                    textbookDataList = sTextbookDataMapper.selectTextbookDataKnowledgeGraph(textbookDataTmp);
                    addNodesParentAndLinksBYknowledgeCategory(textbookDataList.get(0).getCourse(),0L, 2L, literatrueNodesDataList, literatrueLinksList,resultGraphList.getDictLabel(),snowflake,"1");
                }else if("keyword".equals(sTextbookData.getKeywordValue())) {
                    TextbookData textbookDataTmp = new TextbookData();
                    textbookDataTmp.setField(sTextbookData.getKeywordValue());
                    textbookDataTmp.setKeywordValue(sTextbookData.getKeywordValue());
                    textbookDataTmp.setKeyword(sTextbookData.getKeyword());
                    textbookDataList = sTextbookDataMapper.selectTextbookDataKnowledgeGraph(textbookDataTmp);
                    addNodesParentAndLinksBYknowledgeCategory(textbookDataList.get(0).getCourse(),0L, 2L, literatrueNodesDataList, literatrueLinksList,"course",snowflake,"1");
                }else if("course".equals(sTextbookData.getKeywordValue())) {
                    System.out.println(labelSort.getDictSort());
                    SysDictData result1 = knowledgeConfigList.stream()
                            .filter(data -> data.getDictValue() != null && !data.getDictValue().isEmpty() && data.getDictSort()==(labelSort.getDictSort()))
                            .filter(data -> Arrays.stream(data.getDictValue().split(","))
                                    .anyMatch(label -> label.trim().equals(sTextbookData.getKeyword())))
                            .findFirst()
                            .orElse(null);
                    addNodesParentAndLinksBYknowledgeCategory(result1.getDictLabel(),0L, 2L, literatrueNodesDataList, literatrueLinksList,"major_name",snowflake,"1");
                }else{
                    addNodesParentAndLinksBYknowledgeCategory(sTextbookData.getParentKeyword(),0L, 2L, literatrueNodesDataList, literatrueLinksList,sTextbookData.getParentKeywordValue(),snowflake,"1");
                }
            }
        } catch (Exception e) {
            return  AjaxResult.error("此关键词未定义相关信息");
        }
        // 添加源节点链接
        addSourceNodeLinks(literatrueLinksList,secondNadeList);
        sTextbookData.setLiteratrueLinks(literatrueLinksList);
        sTextbookData.setLiteratrueNodesData(literatrueNodesDataList);
        return AjaxResult.success(sTextbookData);
    }

    //思维导图
    @Override
    public Node getThinking() {
        List<MindMapping> disciplines = sTextbookDataMapper.selectMindMappingList();

        // 创建“新文科”根节点
        Node newArtsRoot = new Node("新文科");
        newArtsRoot.setLevel("1");
        // 使用复合键来避免重复添加相同的节点
        Map<String, Node> categoryNodes = new LinkedHashMap<>();
        Map<String, Node> majorNodes = new LinkedHashMap<>();
        Map<String, Node> subMajorNodes = new LinkedHashMap<>();
        Map<String, Node> courseNodes = new LinkedHashMap<>();
        newArtsRoot.setId(UUID.randomUUID()+"");
        for (MindMapping discipline : disciplines) {
            // 创建或获取学科门类节点
            String categoryKey = discipline.getAcademicDiscipline();
            String direction = "";
            if(categoryKey.equals("管理学")||
                    categoryKey.equals("艺术学")||
                    categoryKey.equals("文学")||
                    categoryKey.equals("经济学")){
                direction="right";
            }else {
                direction="left";
            }
            categoryNodes.putIfAbsent(categoryKey, new Node(discipline.getAcademicDiscipline()));
            Node categoryNode = categoryNodes.get(categoryKey);
            if (!newArtsRoot.getChildren().contains(categoryNode)) {
                categoryNode.setDirection(direction);
                categoryNode.setLevel("2");
                categoryNode.setId(UUID.randomUUID()+"");
                newArtsRoot.addChild(categoryNode);
            }

            // 创建或获取一级学科节点
            String majorKey = categoryKey + "-" + discipline.getColleName();
            majorNodes.putIfAbsent(majorKey, new Node(discipline.getColleName()));
            Node majorNode = majorNodes.get(majorKey);
            if (!categoryNode.getChildren().contains(majorNode)) {
                majorNode.setDirection(direction);
                majorNode.setLevel("3");
                majorNode.setId(UUID.randomUUID()+"");
                categoryNode.addChild(majorNode);
            }

            // 创建或获取二级学科节点
            String subMajorKey = majorKey + "-" + discipline.getMajorName();
            subMajorNodes.putIfAbsent(subMajorKey, new Node(discipline.getMajorName()));
            Node subMajorNode = subMajorNodes.get(subMajorKey);
            if (!majorNode.getChildren().contains(subMajorNode)) {
                subMajorNode.setDirection(direction);
                subMajorNode.setLevel("4");
                subMajorNode.setId(UUID.randomUUID()+"");
                majorNode.addChild(subMajorNode);
            }

            // 创建或获取课程节点
            String courseKey = subMajorKey + "-" + discipline.getCourseName();
            courseNodes.putIfAbsent(courseKey, new Node(discipline.getCourseName()));
            Node courseNode = courseNodes.get(courseKey);
            if (!subMajorNode.getChildren().contains(courseNode)) {
                courseNode.setDirection(direction);
                courseNode.setLevel("5");
                courseNode.setId(UUID.randomUUID()+"");
                subMajorNode.addChild(courseNode);
            }


            // 添加教材作为课程节点的子节点
            if (StringUtils.isNotBlank(discipline.getFileName())) {
                Node materialNode = new Node(discipline.getFileName());
                materialNode.setValue(discipline.getId());
                if (!courseNode.getChildren().contains(materialNode)) {
                    materialNode.setDirection(direction);
                    materialNode.setLevel("6");
                    materialNode.setId(UUID.randomUUID()+"");
                    courseNode.addChild(materialNode);
                    // 获取并添加知识点到教材节点
                    addKeywordsToMaterial(materialNode, discipline.getId());
                }
            }
        }

        return newArtsRoot;
    }

    private void addKeywordsToMaterial(Node materialNode, Long materialId) {
        // 获取该教材对应的知识点数据
        List<ThinkingKeyword> thinkingKeywords = sTextbookDataMapper.getThinkingKeyword(materialId);
        Map<String, Node> chapterNodes = new LinkedHashMap<>();
        Map<String, Node> keywordNodes = new LinkedHashMap<>();
        for (ThinkingKeyword tk : thinkingKeywords) {
            String chapterKey = tk.getChapter();
            String keywordKey = chapterKey + "-" + tk.getKeyword();

            // 创建或获取章节节点
            chapterNodes.putIfAbsent(chapterKey, new Node(tk.getChapter()));
            Node chapterNode = chapterNodes.get(chapterKey);

            // 创建或获取知识点节点
            keywordNodes.putIfAbsent(keywordKey, new Node(tk.getKeyword()));
            Node keywordNode = keywordNodes.get(keywordKey);

            // 将知识点节点添加到对应的章节节点下
            if (!chapterNode.getChildren().contains(keywordNode)) {
                keywordNode.setId(UUID.randomUUID() + "");
                keywordNode.setDirection(materialNode.getDirection());
                keywordNode.setLevel("8");
                chapterNode.addChild(keywordNode);
            }

            // 如果章节节点不在教材节点的子节点中，则添加
            if (!materialNode.getChildren().contains(chapterNode)) {
                chapterNode.setId(UUID.randomUUID() + "");
                chapterNode.setDirection(materialNode.getDirection());
                chapterNode.setLevel("7");
                materialNode.addChild(chapterNode);
            }
        }
    }

    @Override
    public List<Node> getThinkingKeyword(Long id) {
        // 从数据库获取 ThinkingKeyword 数据
        List<ThinkingKeyword> thinkingKeywords = sTextbookDataMapper.getThinkingKeyword(id);

        // 使用 Map 来避免重复添加相同的章节节点
        Map<String, Node> chapterNodes = new LinkedHashMap<>();
        Map<String, Node> keywordNodes = new LinkedHashMap<>();

        for (ThinkingKeyword tk : thinkingKeywords) {
            String chapterKey = tk.getChapter();
            String keywordKey = chapterKey + "-" + tk.getKeyword();

            // 创建或获取章节节点
            chapterNodes.putIfAbsent(chapterKey, new Node(tk.getChapter()));
            Node chapterNode = chapterNodes.get(chapterKey);

            // 创建或获取知识点节点
            keywordNodes.putIfAbsent(keywordKey, new Node(tk.getKeyword()));
            Node keywordNode = keywordNodes.get(keywordKey);

            // 将知识点节点添加到对应的章节节点下
            if (!chapterNode.getChildren().contains(keywordNode)) {
                chapterNode.addChild(keywordNode);
            }
        }

        // 返回章节节点的列表
        return new ArrayList<>(chapterNodes.values());
    }

    @Override
    public Counting getStatistics() {
        //学科数量
        int subject = sTextbookDataMapper.selectCountSubject();
        //课程数量
        int course = sTextbookDataMapper.selectCountCourse();
        //知识点数量
        int knowledge = sTextbookDataMapper.selectCountKnowledge();
        List<ProductSales> productSalesList1 = sTextbookDataMapper.selectProductSalesList();
        List<List<String>> productSalesList = new ArrayList<>();
        productSalesList.add(Arrays.asList("product","学科建设","课程体系","知 识 点"));


        for (ProductSales productSales : productSalesList1) {
            productSalesList.add(Arrays.asList(productSales.getProduct(),productSales.getSubject()+"",productSales.getCourse()+"",productSales.getKnowledge()/100.0+""));
        }
        // 创建一个 List<Map<String, String>> 类型的列表
        List<Map<String, String>> mapList = new ArrayList<>();

        // 为学科添加条目
        Map<String, String> entry1 = new HashMap<>();
        entry1.put("name", "学科建设");
        entry1.put("value", subject + ""); // 假设 subject 是一个变量，这里需要将其转换为字符串
        mapList.add(entry1);

        // 为课程添加条目
        Map<String, String> entry2 = new HashMap<>();
        entry2.put("name", "课程体系");
        entry2.put("value", course + ""); // 假设 course 是一个变量，这里需要将其转换为字符串
        mapList.add(entry2);
        // 为知识点添加条目
        Map<String, String> entry3 = new HashMap<>();
        entry3.put("name", "知 识 点");
        entry3.put("value", knowledge + ""); // 假设 knowledge 是一个变量，这里需要将其转换为字符串
        mapList.add(entry3);

        Counting counting = new Counting(mapList, productSalesList);
        return counting;
    }

    //方法用于创建 LiteratrueNodesData 对象
    private LiteratrueNodesData createLiteratrueNodesData(Long id, Long gradeFlag, String name,String keywordValue) {
        LiteratrueNodesData literatrueNodesData = new LiteratrueNodesData();
        literatrueNodesData.setId(id);
        literatrueNodesData.setName(name);
        literatrueNodesData.setGradeFlag(gradeFlag);
        literatrueNodesData.setKeywordValue(keywordValue);
        if("university_name".equals(keywordValue)||"author".equals(keywordValue)
                ||"publishing_house".equals(keywordValue)||"knowledge_category".equals(keywordValue)||"知识点".equals(name)){
            literatrueNodesData.setOnclickFlag(false);
        }else {
            literatrueNodesData.setOnclickFlag(true);
        }
        return literatrueNodesData;
    }

    private LiteratrueNodesData createLiteratrueNodesData(Long id, Long gradeFlag, String name,String keywordValue,String hostFlag) {
        LiteratrueNodesData literatrueNodesData = new LiteratrueNodesData();
        literatrueNodesData.setId(id);
        literatrueNodesData.setName(name);
        literatrueNodesData.setGradeFlag(gradeFlag);
        literatrueNodesData.setKeywordValue(keywordValue);
        literatrueNodesData.setOnclickFlag(false);
        literatrueNodesData.setHostFlag(hostFlag);
        return literatrueNodesData;
    }

    /**
     * 获取id
     * @return
     */
    public List<LiteratrueNodesData> getNodesByName(List<LiteratrueNodesData> literatureNodesDataList, String name) {
        return literatureNodesDataList.stream()
                .filter(node -> node.getName().equals(name))
                .collect(Collectors.toList());
    }


    /**
     * 方法用于添加节点和连接到列表中
     * @param textbookData
     * @param sourceId
     * @param gradeFlag
     * @param nodesDataList
     * @param linksList
     */
    private void addNodesAndLinks(List<TextbookData> textbookData, Long sourceId, Long gradeFlag,
                                  List<LiteratrueNodesData> nodesDataList, List<LiteratrueLinks> linksList,String dictValue,Snowflake snowflake) {

        for (TextbookData item : textbookData) {
            String name = null;
            if(dictValue.equals("academic_discipline")){
                name =  item.getAcademicDiscipline();
            }else if(dictValue.equals("university_name")){
                name =  item.getUniversityName();
            } else if(dictValue.equals("colle_name")){
                name =  item.getColleName();
            }else if(dictValue.equals("major_name")){
                name =  item.getMajorName();
            }else if(dictValue.equals("publishing_house")){
                name =  item.getPublishingHouse();
            }else if(dictValue.equals("textbook")){
                name =  item.getTextbook();
            }else if(dictValue.equals("author")){
                name =  item.getAuthor();
            }else if(dictValue.equals("category")){
                name =  item.getCategory();
            }else if(dictValue.equals("course")){
                name =  item.getCourse();
            }else{
                name =  item.getKeyword();
            }

            long id = snowflake.generateId();
            LiteratrueNodesData literatrueNodesData = createLiteratrueNodesData(id, gradeFlag,name,dictValue);
            literatrueNodesData.setCount(item.getCount());
            nodesDataList.add(literatrueNodesData);
            LiteratrueLinks literatrueLinks = new LiteratrueLinks();
            literatrueLinks.setSource(sourceId);
            literatrueLinks.setTarget(id);
            linksList.add(literatrueLinks);
        }
    }

    /**
     * 方法用于添加节点和连接到列表中
     * @param textbookData
     * @param sourceId
     * @param gradeFlag
     * @param nodesDataList
     * @param linksList
     */
    private void addNodesAndLinksBYknowledgeCategory(List<String> textbookData, Long sourceId, Long gradeFlag,
                                  List<LiteratrueNodesData> nodesDataList, List<LiteratrueLinks> linksList,String dictValue,Snowflake snowflake) {

        for (String item : textbookData) {

            long id = snowflake.generateId();
            LiteratrueNodesData literatrueNodesData = createLiteratrueNodesData(id, gradeFlag,item,dictValue);
            literatrueNodesData.setCount(null);
            nodesDataList.add(literatrueNodesData);
            LiteratrueLinks literatrueLinks = new LiteratrueLinks();
            literatrueLinks.setSource(sourceId);
            literatrueLinks.setTarget(id);
            linksList.add(literatrueLinks);
        }
    }


    /**
     * 方法用于添加节点和连接到列表中(添加父级节点)
     * @param textbookData
     * @param sourceId
     * @param gradeFlag
     * @param nodesDataList
     * @param linksList
     */
    private void addNodesParentAndLinksBYknowledgeCategory(String textbookData, Long sourceId, Long gradeFlag,
                                                     List<LiteratrueNodesData> nodesDataList, List<LiteratrueLinks> linksList,String dictValue,Snowflake snowflake,String parentFlag) {
            long id = snowflake.generateId();
            LiteratrueNodesData literatrueNodesData = createLiteratrueNodesData(id, gradeFlag,textbookData,dictValue);
            literatrueNodesData.setCount(null);
            literatrueNodesData.setParentFlag(parentFlag);
            nodesDataList.add(literatrueNodesData);
            LiteratrueLinks literatrueLinks = new LiteratrueLinks();
            literatrueLinks.setSource(sourceId);
            literatrueLinks.setTarget(id);
            linksList.add(literatrueLinks);
    }



    //方法用于添加源节点链接
    private void addSourceNodeLinks(List<LiteratrueLinks> linksList,List<Long> secondNadeList) {
        Long sourceNodeId = 0L;
        for (Long item : secondNadeList) {
            LiteratrueLinks literatrueLinks = new LiteratrueLinks();
            literatrueLinks.setSource(sourceNodeId);
            literatrueLinks.setTarget(item);
            linksList.add(literatrueLinks);
        }
    }
}
