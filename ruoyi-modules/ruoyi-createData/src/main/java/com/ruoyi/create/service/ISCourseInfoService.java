package com.ruoyi.create.service;

import java.util.List;
import com.ruoyi.create.domain.SCourseInfo;

/**
 * 课程信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-23
 */
public interface ISCourseInfoService 
{
    /**
     * 查询课程信息
     * 
     * @param id 课程信息主键
     * @return 课程信息
     */
    public SCourseInfo selectSCourseInfoById(Long id);

    /**
     * 查询课程信息列表
     * 
     * @param sCourseInfo 课程信息
     * @return 课程信息集合
     */
    public List<SCourseInfo> selectSCourseInfoList(SCourseInfo sCourseInfo);

    /**
     * 新增课程信息
     * 
     * @param sCourseInfo 课程信息
     * @return 结果
     */
    public int insertSCourseInfo(SCourseInfo sCourseInfo);

    /**
     * 修改课程信息
     * 
     * @param sCourseInfo 课程信息
     * @return 结果
     */
    public int updateSCourseInfo(SCourseInfo sCourseInfo);

    /**
     * 批量删除课程信息
     * 
     * @param ids 需要删除的课程信息主键集合
     * @return 结果
     */
    public int deleteSCourseInfoByIds(Long[] ids);

    /**
     * 删除课程信息信息
     * 
     * @param id 课程信息主键
     * @return 结果
     */
    public int deleteSCourseInfoById(Long id);
}
