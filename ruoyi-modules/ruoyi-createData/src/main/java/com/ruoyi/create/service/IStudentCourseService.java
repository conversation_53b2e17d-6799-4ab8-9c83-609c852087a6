package com.ruoyi.create.service;

import com.ruoyi.create.Vo.CourseInfoVo;
import com.ruoyi.create.Vo.StudentAttendanceVo;

import java.util.List;

public interface IStudentCourseService {
    /**
     * 获取学生课程列表
     * @param studentId
     * @return
     */
    List<CourseInfoVo> getStudentCourseList(String studentId);

    /**
     * 获取学生签到列表
     * @param studentId
     * @return
     */
    List<StudentAttendanceVo> selectStudentAttendanceList(String studentId);

    /**
     * 更新学生签到状态
     * @param studentId
     * @return
     */
    int updateStudentAttendance(String courseId, String studentId);
}
