package com.ruoyi.create.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.create.domain.HomeworkQuestionReq;
import com.ruoyi.create.service.IHomeworkQuestionReqService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 作业题型要求明细Controller
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@RestController
@RequestMapping("/req")
public class HomeworkQuestionReqController extends BaseController
{
    @Autowired
    private IHomeworkQuestionReqService homeworkQuestionReqService;

    /**
     * 查询作业题型要求明细列表
     */
    @RequiresPermissions("system:req:list")
    @GetMapping("/list")
    public TableDataInfo list(HomeworkQuestionReq homeworkQuestionReq)
    {
        startPage();
        List<HomeworkQuestionReq> list = homeworkQuestionReqService.selectHomeworkQuestionReqList(homeworkQuestionReq);
        return getDataTable(list);
    }

    /**
     * 导出作业题型要求明细列表
     */
    @RequiresPermissions("system:req:export")
    @Log(title = "作业题型要求明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HomeworkQuestionReq homeworkQuestionReq)
    {
        List<HomeworkQuestionReq> list = homeworkQuestionReqService.selectHomeworkQuestionReqList(homeworkQuestionReq);
        ExcelUtil<HomeworkQuestionReq> util = new ExcelUtil<HomeworkQuestionReq>(HomeworkQuestionReq.class);
        util.exportExcel(response, list, "作业题型要求明细数据");
    }

    /**
     * 获取作业题型要求明细详细信息
     */
    @RequiresPermissions("system:req:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(homeworkQuestionReqService.selectHomeworkQuestionReqById(id));
    }

    /**
     * 新增作业题型要求明细
     */
    @RequiresPermissions("system:req:add")
    @Log(title = "作业题型要求明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HomeworkQuestionReq homeworkQuestionReq)
    {
        return toAjax(homeworkQuestionReqService.insertHomeworkQuestionReq(homeworkQuestionReq));
    }

    /**
     * 修改作业题型要求明细
     */
    @RequiresPermissions("system:req:edit")
    @Log(title = "作业题型要求明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HomeworkQuestionReq homeworkQuestionReq)
    {
        return toAjax(homeworkQuestionReqService.updateHomeworkQuestionReq(homeworkQuestionReq));
    }

    /**
     * 删除作业题型要求明细
     */
    @RequiresPermissions("system:req:remove")
    @Log(title = "作业题型要求明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(homeworkQuestionReqService.deleteHomeworkQuestionReqByIds(ids));
    }
}
