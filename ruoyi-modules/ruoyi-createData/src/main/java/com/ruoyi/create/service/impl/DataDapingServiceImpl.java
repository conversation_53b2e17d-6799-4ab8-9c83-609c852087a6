package com.ruoyi.create.service.impl;

import com.ruoyi.create.Vo.DataDapingVo;
import com.ruoyi.create.domain.DataDaping.*;
import com.ruoyi.create.mapper.CourseVideoMapper;
import com.ruoyi.create.mapper.DataDaping.*;
import com.ruoyi.create.service.DataDapingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Service
public class DataDapingServiceImpl implements DataDapingService {


    // 装配 mapper
    @Resource
    private SCourseKnowledgeMapper sCourseKnowledgeMapper;

    @Resource
    private SWisdomStatisticsMapper sWisdomStatisticsMapper;

    @Resource
    private SPlatformRealMapper sPlatformRealMapper;

    @Resource
    private SKnowledgeRankingMapper sKnowledgeRankingMapper;

    @Resource
    private SDailyTrendMapper sDailyTrendMapper;

    @Resource
    private SPracticalTrainingMapper sPracticalTrainingMapper;

    @Resource
    private SStudentRankingMapper sStudentRankingMapper;

    @Resource
    private SStudentAcquiredMapper sStudentAcquiredMapper;

    @Resource
    private SWeeklyCompletionMapper sWeeklyCompletionMapper;


    @Override
    public DataDapingVo getDataDapingVo() {
        DataDapingVo vo = new DataDapingVo();

        vo.setWisdomStatistics(sWisdomStatisticsMapper.selectLatestWisdomStatistics());
        vo.setPlatformReal(sPlatformRealMapper.selectLatestPlatformReal());
        vo.setKnowledgeRankingList(sKnowledgeRankingMapper.selectLatestKnowledgeRankingList());
        vo.setDailyTrendList(sDailyTrendMapper.selectLatestDailyTrendList());
        vo.setPracticalTrainingList(sPracticalTrainingMapper.selectLatestPracticalTrainingList());
        vo.setCourseKnowledgeList(sCourseKnowledgeMapper.selectLatestSCourseKnowledge());
        vo.setStudentRankingList(sStudentRankingMapper.selectLatestStudentRankingList());
        vo.setStudentAcquiredList(sStudentAcquiredMapper.selectLatestStudentAcquiredList());
        vo.setWeeklyCompletionList(sWeeklyCompletionMapper.selectLatestWeeklyCompletionList());

        return vo;
    }





}
