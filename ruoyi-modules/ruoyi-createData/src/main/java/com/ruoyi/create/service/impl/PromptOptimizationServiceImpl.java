package com.ruoyi.create.service.impl;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map.Entry;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.baidubce.http.ApiExplorerClient;
import com.baidubce.http.HttpMethodName;
import com.baidubce.model.ApiExplorerRequest;
import com.baidubce.model.ApiExplorerResponse;
import com.baidubce.util.JsonUtils;
import com.ruoyi.baidu.api.BaiduApiService;
import com.ruoyi.baidu.api.dto.BaiduDto;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.create.utils.Snowflake;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.PromptOptimizationMapper;
import com.ruoyi.create.domain.PromptOptimization;
import com.ruoyi.create.service.IPromptOptimizationService;

/**
 * prompt模板优化Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Service
public class PromptOptimizationServiceImpl implements IPromptOptimizationService
{
    @Autowired
    private PromptOptimizationMapper promptOptimizationMapper;

    @Autowired
    private BaiduApiService baiduApiService;

    /**
     * 查询prompt模板优化
     *
     * @param id prompt模板优化主键
     * @return prompt模板优化
     */
    @Override
    public PromptOptimization selectPromptOptimizationById(Long id)
    {
        return promptOptimizationMapper.selectPromptOptimizationById(id);
    }

    /**
     * 查询prompt模板优化列表
     *
     * @param promptOptimization prompt模板优化
     * @return prompt模板优化
     */
    @Override
    public List<PromptOptimization> selectPromptOptimizationList(PromptOptimization promptOptimization)
    {
        return promptOptimizationMapper.selectPromptOptimizationList(promptOptimization);
    }

    /**
     * 新增prompt模板优化
     *
     * @param promptOptimization prompt模板优化
     * @return 结果
     */
    @Override
    public AjaxResult insertPromptOptimization(PromptOptimization promptOptimization)
    {
        // Access Key
        String ak = "ALTAKKmSe66vVoBN2yfvb53FVi";
        // Secret Key
        String sk = "47942585c20a43c2a364d3f351514135";
        String jsonBody = "";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("content", promptOptimization.getContent());

        JSONArray operations = createOperations(promptOptimization);
        jsonObject.put("operations", operations);

        jsonBody = jsonObject.toString();
//        String path = "https://qianfan.baidubce.com/wenxinworkshop/prompt/singleOptimize/create";
//        String headerJson = "{\"Content-Type\":\"application/json\"}";
//        String queryJson = "{}";
//        String jsonBody = "";
//
//        ApiExplorerRequest request = new ApiExplorerRequest(HttpMethodName.POST, path);
//        // 设置鉴权信息
//        request.setCredentials(ak, sk);
//        // 设置header参数
//        HashMap<String, String> headerJsonMap = JsonUtils.fromJsonString(headerJson, HashMap.class);
//        Iterator<Entry<String, String>> headerIterator = headerJsonMap.entrySet().iterator();
//        while (headerIterator.hasNext()) {
//            Entry<String, String> next = headerIterator.next();
//            request.addHeaderParameter(next.getKey(), next.getValue());
//        }
//        // 设置query参数
//        if(StringUtils.isNotEmpty(queryJson)){
//            HashMap<String, String> QueryJsonMap = JsonUtils.fromJsonString(queryJson, HashMap.class);
//            Iterator<Entry<String, String>> queryIterator = QueryJsonMap.entrySet().iterator();
//            while (queryIterator.hasNext()) {
//                Entry<String, String> next = queryIterator.next();
//                request.addQueryParameter(next.getKey(), next.getValue());
//            }
//        }
//        // 设置jsonBody参数
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("content", promptOptimization.getContent());
//
//        JSONArray operations = createOperations(promptOptimization);
//        jsonObject.put("operations", operations);
//
//        jsonBody = jsonObject.toString();
//
//
////        jsonObject.put("appID", "");
////        jsonObject.put("serviceName", "");
//        if (StringUtils.isNotEmpty(jsonBody)) {
//            request.setJsonBody(jsonBody);
//        }
//        ApiExplorerClient client = new ApiExplorerClient();
        try {
//            ApiExplorerResponse response = client.sendRequest(request);
//            JSONObject respJson = JSON.parseObject(response.getResult());
//
//            String errorCode = respJson.getString("error_code");
//            if(StringUtils.isNotEmpty(errorCode)) {
//                return AjaxResult.error(respJson.getString("error_msg"));
//            }
//
//            Boolean success = respJson.getBoolean("success");
//            if(!success) {
//                JSONObject message = respJson.getJSONObject("message");
//                return AjaxResult.error(message.getString("global"));
//            }
//
//            JSONObject result = respJson.getJSONObject("result");
            BaiduDto baiduDto = new BaiduDto();
            baiduDto.setAk(ak);
            baiduDto.setSk(sk);
            baiduDto.setJsonBody(jsonBody);
            String optimization = baiduApiService.insertPromptOptimization(baiduDto, SecurityConstants.INNER);

            Snowflake snowflake = new Snowflake(1, 1);
            long id = snowflake.generateId();
            promptOptimization.setId(id);
            promptOptimization.setOptimizationId(optimization);//prompt优化任务id
            promptOptimization.setProcessStatus(1L);

            promptOptimizationMapper.insertPromptOptimization(promptOptimization);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return AjaxResult.success(promptOptimization);
    }

    /**
     * 修改prompt模板优化
     *
     * @param promptOptimization prompt模板优化
     * @return 结果
     */
    @Override
    public int updatePromptOptimization(PromptOptimization promptOptimization)
    {
        return promptOptimizationMapper.updatePromptOptimization(promptOptimization);
    }

    /**
     * 批量删除prompt模板优化
     *
     * @param ids 需要删除的prompt模板优化主键
     * @return 结果
     */
    @Override
    public int deletePromptOptimizationByIds(Long[] ids)
    {
        return promptOptimizationMapper.deletePromptOptimizationByIds(ids);
    }

    /**
     * 删除prompt模板优化信息
     *
     * @param id prompt模板优化主键
     * @return 结果
     */
    @Override
    public int deletePromptOptimizationById(Long id)
    {
        return promptOptimizationMapper.deletePromptOptimizationById(id);
    }

    @Override
    public PromptOptimization getPromptInfo(Long id) {
        PromptOptimization promptOptimization = promptOptimizationMapper.selectById(id);
        String optimizationId  = promptOptimization.getOptimizationId();

        // Access Key
        String ak = "ALTAKKmSe66vVoBN2yfvb53FVi";
        // Secret Key
        String sk = "47942585c20a43c2a364d3f351514135";
//
//        String path = "https://qianfan.baidubce.com/wenxinworkshop/prompt/singleOptimize/info";
//        String headerJson = "{\"Content-Type\":\"application/json\"}";
//        String queryJson = "{}";
//        String jsonBody = "";
//
//        ApiExplorerRequest request = new ApiExplorerRequest(HttpMethodName.POST, path);
//        // 设置鉴权信息
//        request.setCredentials(ak, sk);
//        // 设置header参数
//        HashMap<String, String> headerJsonMap = JsonUtils.fromJsonString(headerJson, HashMap.class);
//        Iterator<Entry<String, String>> headerIterator = headerJsonMap.entrySet().iterator();
//        while (headerIterator.hasNext()) {
//            Entry<String, String> next = headerIterator.next();
//            request.addHeaderParameter(next.getKey(), next.getValue());
//        }
//        // 设置query参数
//        if(StringUtils.isNotEmpty(queryJson)){
//            HashMap<String, String> QueryJsonMap = JsonUtils.fromJsonString(queryJson, HashMap.class);
//            Iterator<Entry<String, String>> queryIterator = QueryJsonMap.entrySet().iterator();
//            while (queryIterator.hasNext()) {
//                Entry<String, String> next = queryIterator.next();
//                request.addQueryParameter(next.getKey(), next.getValue());
//            }
//        }
//
//        // 设置jsonBody参数
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("id", optimizationId);
//        jsonBody = jsonObject.toString();
//        if (StringUtils.isNotEmpty(jsonBody)) {
//            request.setJsonBody(jsonBody);
//        }
//        ApiExplorerClient client = new ApiExplorerClient();
        try {
//            ApiExplorerResponse response = client.sendRequest(request);
//            // 返回结果格式为Json字符串
//            System.out.println(response.getResult());

//            JSONObject respJson = JSON.parseObject(response.getResult());
//
//            JSONObject result = respJson.getJSONObject("result");
            BaiduDto baiduDto = new BaiduDto();
            baiduDto.setAk(ak);
            baiduDto.setSk(sk);
            baiduDto.setOptimizationId(optimizationId);
            JSONObject result = baiduApiService.getPromptInfo(baiduDto, SecurityConstants.INNER);
            if(!result.isEmpty()) {
                promptOptimization.setOptimizeContent(result.getString("optimizeContent"));
                promptOptimization.setProcessStatus((long) result.getIntValue("processStatus"));
                JSONObject inference = result.getJSONObject("inference");
                if(!inference.isEmpty()) {
                    promptOptimization.setInferenceBefore(inference.getString("before"));
                    promptOptimization.setInferenceAfter(inference.getString("after"));
                }
            }

            promptOptimizationMapper.updatePromptOptimization(promptOptimization);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return promptOptimization;
    }

    public JSONArray createOperations (PromptOptimization promptOptimization) {
        JSONArray operations = new JSONArray();

        JSONObject obj1 = new JSONObject();
        obj1.put("opType", 1);
        obj1.put("payload", StringUtils.equals(promptOptimization.getQualityOptFlag(), "true") ? 0 : 1);
        JSONObject obj2 = new JSONObject();
        obj2.put("opType", 2);
        obj2.put("payload", StringUtils.equals(promptOptimization.getShortPromptFlag(), "true") ? 0 : 1);
        JSONObject obj3 = new JSONObject();
        obj3.put("opType", 3);
        obj3.put("payload", promptOptimization.getIterationRound());
        JSONObject obj4 = new JSONObject();
        obj4.put("opType", 4);
        obj4.put("payload", StringUtils.equals(promptOptimization.getThoughtChainFlag(), "true") ? 0 : 1);

        operations.add(obj1);
        operations.add(obj2);
        operations.add(obj3);
        operations.add(obj4);

        return operations;
    }
}
