package com.ruoyi.create.mapper;

import java.util.List;
import com.ruoyi.create.domain.PromptTemplate;
import org.apache.ibatis.annotations.Param;

/**
 * prompt模板Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
public interface PromptTemplateMapper 
{
    /**
     * 查询prompt模板
     * 
     * @param id prompt模板主键
     * @return prompt模板
     */
    public PromptTemplate selectPromptTemplateById(Long id);

    /**
     * 查询prompt模板列表
     * 
     * @param promptTemplate prompt模板
     * @return prompt模板集合
     */
    public List<PromptTemplate> selectPromptTemplateList(PromptTemplate promptTemplate);

    /**
     * 新增prompt模板
     * 
     * @param promptTemplate prompt模板
     * @return 结果
     */
    public int insertPromptTemplate(PromptTemplate promptTemplate);

    /**
     * 修改prompt模板
     * 
     * @param promptTemplate prompt模板
     * @return 结果
     */
    public int updatePromptTemplate(PromptTemplate promptTemplate);

    /**
     * 删除prompt模板
     * 
     * @param id prompt模板主键
     * @return 结果
     */
    public int deletePromptTemplateById(Long id);

    /**
     * 批量删除prompt模板
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePromptTemplateByIds(Long[] ids);

    public List<PromptTemplate> selectPromptTemplateByIds(Long[] ids);

    List<PromptTemplate> selectPromptAll(String username);
}
