package com.ruoyi.create.mapper;

import java.util.List;

import com.ruoyi.create.Vo.PresentationVo;
import com.ruoyi.create.domain.KnowledgeBaseFile;
import com.ruoyi.create.domain.Presentation;
import com.ruoyi.create.dto.UserDto;

/**
 * 老师课程Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
public interface PresentationMapper {
    /**
     * 查询老师课程
     *
     * @param id 老师课程主键
     * @return 老师课程
     */
    public Presentation selectPresentationById(Long id);
    public Presentation selectPresentationByFileId(String presentationFileId);
    /**
     * 查询老师课程列表
     *
     * @param presentation 老师课程
     * @return 老师课程集合
     */
    public List<Presentation> selectPresentationList(Presentation presentation);

    public List<Presentation> selectPresentationListGroupByCourse(Presentation presentation);
    /**
     * 新增老师课程
     *
     * @param presentation 老师课程
     * @return 结果
     */
    public int insertPresentation(Presentation presentation);

    /**
     * 修改老师课程
     *
     * @param presentation 老师课程
     * @return 结果
     */
    public int updatePresentation(Presentation presentation);

    /**
     * 删除老师课程
     *
     * @param id 老师课程主键
     * @return 结果
     */
    public int deletePresentationById(Long id);

    /**
     * 批量删除老师课程
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePresentationByIds(Long[] ids);

//
//    List<PresentationVo> selectPresentationAllList(Presentation presentation);

    public int selectUByT(Long id);

    public int selectCByT(Long id);

    public String selectNameById(String id);

    public int delStudentPromotionByPId(Long presentationId);

    public int delStudentStudyRecordByPId(Long presentationId);

    public List<UserDto> selectUserByUserName(String username);

    public List<String> selectUserRoleKeyByUserName(String username);

    public List<KnowledgeBaseFile> selectTxtIdByCourse(KnowledgeBaseFile knowledgeBaseFile);

    public List<String> selectChapterById(Long id);

    public String selectFileNameById(Long id);

    /**
     * @description: 根据id修改video_page_indexes
     * @author: zhaoTianQi
     * @date: 2024/11/12 0:27
     * @param: presentation
     * @return: int
     **/
    int updatePresentationByPresentationId(Presentation presentation);

    Presentation selectPresentationByPresentationId(Long presentationId);

    List<String> selectKnowledgeByPresentation(Presentation presentation);
}
