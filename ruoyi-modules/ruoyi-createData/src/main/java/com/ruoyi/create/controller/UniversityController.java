package com.ruoyi.create.controller;

import java.util.HashMap;
import java.util.List;
import java.io.IOException;
import java.util.Map;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.system.api.domain.SysRole;
import com.ruoyi.system.api.domain.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.University;
import com.ruoyi.create.service.IUniversityService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 学校信息Controller
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
@RestController
@RequestMapping("/university")
public class UniversityController extends BaseController {
    @Autowired
    private IUniversityService universityService;

    /**
     * 查询学校信息列表
     */
    @RequiresPermissions("system:university:list")
    @GetMapping("/list")
    public TableDataInfo list(University university) {
        startPage();
        List<University> list = universityService.selectUniversityList(university);
        return getDataTable(list);
    }

    /**
     * 导出学校信息列表
     */
    @RequiresPermissions("system:university:export")
    @Log(title = "学校信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, University university) {
        List<University> list = universityService.selectUniversityList(university);
        ExcelUtil<University> util = new ExcelUtil<University>(University.class);
        util.exportExcel(response, list, "学校信息数据");
    }

    /**
     * 获取学校信息详细信息
     */
    @RequiresPermissions("system:university:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(universityService.selectUniversityById(id));
    }

    /**
     * 新增学校信息
     */
    @RequiresPermissions("system:university:add")
    @Log(title = "学校信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody University university) {
        return toAjax(universityService.insertUniversity(university));
    }

    /**
     * 修改学校信息
     */
    @RequiresPermissions("system:university:edit")
    @Log(title = "学校信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody University university) {
        return toAjax(universityService.updateUniversity(university));
    }

    /**
     * 删除学校信息
     */
    @RequiresPermissions("system:university:remove")
    @Log(title = "学校信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(universityService.deleteUniversityByIds(ids));
    }

    /**
     * 信息认证-查询全部
     */
    @RequiresPermissions("system:university:all")
    @GetMapping("/all/{roleId}")
    public AjaxResult selectUniversityAllByRoleId(@PathVariable("roleId") Long roleId) {
        List<University> list = universityService.selectUniversityListAll(roleId);
        return success(list);
    }

    /**
     * 认证审批查询用户对应学校信息
     */
    @InnerAuth
    @PostMapping("/getInfo")
    public AjaxResult getInfo(@RequestBody University university) {
        University list = universityService.getUniversityInfo(university);
        return success(list);
    }

    /**
     * 作业-查询全部学校信息
     */
    @RequiresPermissions("system:university:all")
    @GetMapping("/getAll")
    public AjaxResult getUniversityAll() {
        List<University> list = universityService.getUniversityAllUseForHm();
        return success(list);
    }
    /**
     * 作业-查询全部学校信息
     */
    @RequiresPermissions("system:university:all")
    @GetMapping("/getUniversity")
    public AjaxResult getUniversity() {
        List<University> list = universityService.getUniversityAllUseForHm();
        return success(list);
    }
    /**
     * 联盟课程 - 只查询查询全部的学校信息
     *
     * @description:
     * @author: zhaoTianQi
     * @date: 2024/10/28 16:22
     * @param:
     * @return:
     * @return: com.ruoyi.common.core.web.domain.AjaxResult
     **/
    @GetMapping("/getUniversityAll")
    public AjaxResult getSchoolAll() {
        return success(universityService.list());
    }

    /**
     * 联盟课程 - 只查询查询全部的学校信息 构建map数据
     *
     * @description:
     * @author: zhaoTianQi
     * @date: 2024/10/28 16:22
     * @param:
     * @return:
     * @return: com.ruoyi.common.core.web.domain.AjaxResult
     **/
    @GetMapping("/getUniversityAll2")
    public AjaxResult getSchoolAll2() {
        List<Map<String, Object>> collect = universityService.list().stream().map(item -> {
            Map<String, Object> map = new HashMap<>();
            map.put("key", String.valueOf(item.getId()));
            map.put("label", item.getUniverName());
            map.put("disabled", false);
            return map;
        }).collect(Collectors.toList());
        return success(collect);
    }

}
