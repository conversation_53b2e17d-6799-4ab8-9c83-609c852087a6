package com.ruoyi.create.mapper;

import com.ruoyi.create.domain.UserVoiceType;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户语音切换Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-13
 */
public interface UserVoiceTypeMapper {

    /**
     * 查询用户语音
     *
     * @param id 用户语音主键
     * @return 用户语音
     */
    public UserVoiceType selectUserVoiceTypeById(Long id);

    /**
     * 查询用户语音列表
     *
     * @param userVoiceType 用户语音
     * @return 用户语音集合
     */
    public List<UserVoiceType> selectUserVoiceTypeList(UserVoiceType userVoiceType);

    /**
     * 新增用户语音
     *
     * @param userVoiceType 用户语音
     * @return 结果
     */
    public int insertUserVoiceType(UserVoiceType userVoiceType);

    /**
     * 修改用户语音
     *
     * @param userVoiceType 用户语音
     * @return 结果
     */
    public int updateUserVoiceType(UserVoiceType userVoiceType);

    /**
     * 删除用户语音
     *
     * @param id 用户语音主键
     * @return 结果
     */
    public int deleteUserVoiceTypeById(Long id);

    /**
     * 批量删除用户语音
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUserVoiceTypeByIds(Long[] ids);

    public int updateUserVoiceTypeByUserName(UserVoiceType userVoiceType);

    public UserVoiceType selectUserVoiceTypeByUserName(@Param("userName") String userName);

    int updateUserVoiceTypeByUserNameTmp(@Param("userName")String userName,@Param("voiceType") String voiceType);
}
