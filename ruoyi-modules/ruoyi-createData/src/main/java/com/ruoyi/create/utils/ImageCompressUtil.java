package com.ruoyi.create.utils;

import javax.imageio.IIOException;
import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.stream.ImageInputStream;
import java.awt.*;
import java.awt.color.ColorSpace;
import java.awt.image.*;
import java.io.*;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;

import net.coobird.thumbnailator.Thumbnails;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.ObjectUtils;
/**
 * <AUTHOR>
 * @date ：Created in 2021-03-23
 * @description：测试图片压缩
 */
public class ImageCompressUtil {

    private static Logger log = LogManager.getLogger(ImageCompressUtil.class);

    /**
     * 按照比例进行缩放 批量
     *
     * @param files 待压缩的文件集合
     * @return 压缩后图片集合
     */
    public static List<File> ImageCompressToList(List<File> files) {
        List<File> fileCompress = new ArrayList<>();
        files.forEach(file -> {
            try {
                // 压缩并保存图片
                Thumbnails.of(new FileInputStream(file)).scale(0.8f)
                        .outputQuality(0.8f)
                        .outputFormat("jpeg").toFile(file);
                fileCompress.add(file);
            } catch (IOException e) {
                e.printStackTrace();
            }
        });
        return fileCompress;
    }

    /**
     * 按照比例进行缩放 逐个
     *
     * @param inputStream 待压缩的文件输入流
     * @return 压缩后图片
     */
    public static ByteArrayOutputStream ImageCompressToFile(InputStream inputStream) throws IOException {
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            // 压缩并保存图片
            Thumbnails.of(inputStream).scale(0.8f)
                    .outputQuality(0.8f).toOutputStream(outputStream);
            return outputStream;
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            if(Objects.nonNull(inputStream)){
                inputStream.close();
            }
        }

        return null;
    }

    /**
     * @param file
     * @return
     * @throws IOException
     */
    public static BufferedImage readImage(File file) throws IOException {

        return readImage(ImageIO.createImageInputStream(file));
    }

    /**
     * 处理ps保存的图片
     *
     * @param input
     * @return
     * @throws IOException
     */
    public static BufferedImage readImage(ImageInputStream input) throws IOException {
        Iterator<?> readers = ImageIO.getImageReaders(input);
        if (readers == null || !readers.hasNext()) {
            return null;
        }

        ImageReader reader = (ImageReader) readers.next();
        reader.setInput(input);

        BufferedImage image;
        try {
            // 尝试读取图片 (包括颜色的转换).
            //RGB
            image = reader.read(0);
        } catch (IIOException e) {
            // 读取Raster (没有颜色的转换).
            //CMYK
            Raster raster = reader.readRaster(0, null);
            image = createJPEG4(raster);
        } finally {
            if (ObjectUtils.isEmpty(input)) {
                input.close();
            }
        }

        return image;
    }


    private static BufferedImage createJPEG4(Raster raster) {
        int w = raster.getWidth();
        int h = raster.getHeight();
        byte[] rgb = new byte[w * h * 3];

        //彩色空间转换
        float[] Y = raster.getSamples(0, 0, w, h, 0, (float[]) null);
        float[] Cb = raster.getSamples(0, 0, w, h, 1, (float[]) null);
        float[] Cr = raster.getSamples(0, 0, w, h, 2, (float[]) null);
        float[] K = raster.getSamples(0, 0, w, h, 3, (float[]) null);
        int number = 3;
        for (int i = 0, imax = Y.length, base = 0; i < imax; i++, base += number) {
            float k = 220 - K[i], y = 255 - Y[i], cb = 255 - Cb[i],
                    cr = 255 - Cr[i];

            double val = y + 1.402 * (cr - 128) - k;
            val = (val - 128) * .65f + 128;
            rgb[base] = val < 0.0 ? (byte) 0 : val > 255.0 ? (byte) 0xff
                    : (byte) (val + 0.5);

            val = y - 0.34414 * (cb - 128) - 0.71414 * (cr - 128) - k;
            val = (val - 128) * .65f + 128;
            rgb[base + 1] = val < 0.0 ? (byte) 0 : val > 255.0 ? (byte) 0xff
                    : (byte) (val + 0.5);

            val = y + 1.772 * (cb - 128) - k;
            val = (val - 128) * .65f + 128;
            rgb[base + 2] = val < 0.0 ? (byte) 0 : val > 255.0 ? (byte) 0xff
                    : (byte) (val + 0.5);
        }


        raster = Raster.createInterleavedRaster(new DataBufferByte(rgb, rgb.length), w, h, w * 3, 3, new int[]{0, 1, 2}, null);

        ColorSpace cs = ColorSpace.getInstance(ColorSpace.CS_sRGB);
        ColorModel cm = new ComponentColorModel(cs, false, true, Transparency.OPAQUE, DataBuffer.TYPE_BYTE);
        return new BufferedImage(cm, (WritableRaster) raster, true, null);
    }

    private static InputStream bufferdImageToInputStream(BufferedImage image) throws IOException {
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        InputStream inputStream = null;
        try {
            ImageIO.write(image, "jpg", os);
            inputStream = new ByteArrayInputStream(os.toByteArray());
            return inputStream;
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            os.close();
            if(Objects.nonNull(inputStream)){
                inputStream.close();
            }

        }
        return null;
    }
}
