package com.ruoyi.create.service;

import com.ruoyi.create.Vo.StudentAttendanceVo;
import com.ruoyi.create.domain.LogosConfig;
import com.ruoyi.create.domain.StudentSign;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 动作明细Service接口
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
public interface IStudentSignService
{
    /**
     * 查询动作明细
     *
     * @param id 动作明细主键
     * @return 动作明细
     */
    public StudentSign selectStudentSignById(Long id);

    /**
     * 查询动作明细列表
     *
     * @param studentSign 动作明细
     * @return 动作明细集合
     */
    public List<StudentSign> selectStudentSignList(StudentSign studentSign);

    public List<StudentSign> selectStudentAttendanceList(StudentSign studentSign);

    public int updateStudentAttendance(StudentSign studentSign);

    public StudentSign uploadjpg(MultipartFile file);
}
