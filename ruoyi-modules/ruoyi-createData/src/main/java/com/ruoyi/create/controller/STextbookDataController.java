package com.ruoyi.create.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.TextbookData;
import com.ruoyi.create.service.ISTextbookDataService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 文献整理- 教材原始数据Controller
 * 
 * <AUTHOR>
 * @date 2024-07-31
 */
@RestController
@RequestMapping("/textbookdata")
public class STextbookDataController extends BaseController
{
    @Autowired
    private ISTextbookDataService sTextbookDataService;

    /**
     * 查询文献整理- 教材原始数据列表
     */
    @RequiresPermissions("create:textbookdata:list")
    @GetMapping("/list")
    public TableDataInfo list(TextbookData sTextbookData)
    {
        startPage();
        List<TextbookData> list = sTextbookDataService.selectSTextbookDataList(sTextbookData);
        return getDataTable(list);
    }

    /**
     * 导出文献整理- 教材原始数据列表
     */
    @RequiresPermissions("create:textbookdata:export")
    @Log(title = "文献整理- 教材原始数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TextbookData sTextbookData)
    {
        List<TextbookData> list = sTextbookDataService.selectSTextbookDataList(sTextbookData);
        ExcelUtil<TextbookData> util = new ExcelUtil<TextbookData>(TextbookData.class);
        util.exportExcel(response, list, "文献整理- 教材原始数据数据");
    }

    /**
     * 获取文献整理- 教材原始数据详细信息
     */
    @RequiresPermissions("create:textbookdata:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sTextbookDataService.selectSTextbookDataById(id));
    }

    /**
     * 新增文献整理- 教材原始数据
     */
    @RequiresPermissions("create:textbookdata:add")
    @Log(title = "文献整理- 教材原始数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TextbookData sTextbookData)
    {
        return sTextbookDataService.insertSTextbookData(sTextbookData);
    }

    /**
     * 修改文献整理- 教材原始数据
     */
    @RequiresPermissions("create:textbookdata:edit")
    @Log(title = "文献整理- 教材原始数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TextbookData sTextbookData)
    {
        return toAjax(sTextbookDataService.updateSTextbookData(sTextbookData));
    }

    /**
     * 删除文献整理- 教材原始数据
     */
    @RequiresPermissions("create:textbookdata:remove")
    @Log(title = "文献整理- 教材原始数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sTextbookDataService.deleteSTextbookDataByIds(ids));
    }


    /**
     * 新增文献整理- 教材原始数据
     */
    @RequiresPermissions("create:textbookdata:list")
    @PostMapping("tbKnowledgeMapping")
    public AjaxResult getKnowledgeMapping(@RequestBody TextbookData sTextbookData)
    {
        AjaxResult textbookData = sTextbookDataService.selectGroupTextbookData(sTextbookData);
        return textbookData;
    }

    /**
     * 知识图谱-新
     */
    @RequiresPermissions("create:textbookdata:tbKnowledgeMappingNew")
    @PostMapping("tbKnowledgeMappingNew")
    public AjaxResult gettbKnowledgeMappingNew(@RequestBody TextbookData sTextbookData)
    {
        AjaxResult textbookData = sTextbookDataService.selectNewGroupTextbookData(sTextbookData);
        return textbookData;
    }

    /**
     * 思维导图
     */
    @PostMapping("thinking")
    public AjaxResult getThinking()
    {
        return success(sTextbookDataService.getThinking());
    }

    /**
     * 思维导图
     */
    @GetMapping(value = "/keyword/{id}")
    public AjaxResult getThinkingKeyword(@PathVariable("id") Long id)
    {
        return success(sTextbookDataService.getThinkingKeyword(id));
    }

    /**
     * 统计数量
     */
    @PostMapping("statistics")
    public AjaxResult getStatistics()
    {
        return success(sTextbookDataService.getStatistics());
    }



}
