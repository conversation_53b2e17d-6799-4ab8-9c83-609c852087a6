package com.ruoyi.create.service.impl;

import java.security.Security;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.config.ResourcesConfig;
import com.ruoyi.create.mapper.CourseVideoMapper;
import com.ruoyi.create.utils.Snowflake;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysFileInfo;
import com.ruoyi.system.api.model.LoginUser;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.CourseMaterialsMapper;
import com.ruoyi.create.domain.CourseMaterials;
import com.ruoyi.create.service.ICourseMaterialsService;

import javax.annotation.Resource;

/**
 * 课程教案资料Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
@Service
public class CourseMaterialsServiceImpl implements ICourseMaterialsService {
    @Resource
    private CourseMaterialsMapper courseMaterialsMapper;
	@Resource
	private ResourcesConfig resourcesConfig;
    @Resource
    private RemoteFileService remoteFileService;
    @Resource
    private RemoteUserService remoteUserService;
    @Resource
    private CourseVideoMapper courseVideoMapper;

    /**
     * 查询课程教案资料
     *
     * @param id 课程教案资料主键
     * @return 课程教案资料
     */
    @Override
    public CourseMaterials selectCourseMaterialsById(Long id) {
        return courseMaterialsMapper.selectCourseMaterialsById(id);
    }

    /**
     * 查询课程教案资料列表
     *
     * @param courseMaterials 课程教案资料
     * @return 课程教案资料
     */
    @Override
    public List<CourseMaterials> selectCourseMaterialsList(CourseMaterials courseMaterials) {
        return courseMaterialsMapper.selectCourseMaterialsList(courseMaterials);
    }

    /**
     * 查询课程教案资料列表
     *
     * @param courseMaterials 课程教案资料
     * @return 课程教案资料
     */
    @Override
    public List<CourseMaterials> selectCourseMaterialsList2(CourseMaterials courseMaterials) {

//        if (SecurityUtils.getLoginUser().getRoles().contains("admin")) {
//            return courseMaterialsMapper.selectCourseMaterialsListAndFileNameAll(courseMaterials);
//        }else if (StringUtils.isNotBlank(SecurityUtils.getLoginUser().getSysUser().getJobId())){
//            // 是教师
//            courseMaterials.setLoginTeacherId(SecurityUtils.getLoginUser().getSysUser().getJobId());
//            return courseMaterialsMapper.selectCourseMaterialsListForTeacher(courseMaterials);
//        } else if (StringUtils.isNotBlank(SecurityUtils.getLoginUser().getSysUser().getStudentId())) {
//            // 是学生
//            courseMaterials.setLoginStudentId(SecurityUtils.getLoginUser().getSysUser().getStudentId());
//            return courseMaterialsMapper.selectCourseMaterialsListForStudent(courseMaterials);
//        }

	    List<CourseMaterials> courseMaterials1 = courseMaterialsMapper.selectCourseMaterialsLis2(courseMaterials);
	    courseMaterials1.forEach(item -> {
			item.setFilePathUrl(buildFilePathUrl(item.getFilePath()));
	    });
	    return courseMaterials1;
    }

	public String buildFilePathUrl(String filePath) {

		String localFilePrefix2 = resourcesConfig.getPrefix_2();
		String domain = resourcesConfig.getDomain();
		String os = System.getProperty("os.name").toLowerCase();
		String localFilePath = os.contains("win") ? resourcesConfig.getLocalFilePath_2(): resourcesConfig.getLocalFilePathLinux_2();

		if (StringUtils.isBlank(filePath)) {
			return null;
		}
		// 路径中的所有分隔符统一为正斜杠 "/"
		filePath = filePath.replaceAll("[\\\\/]+", "/");
		// 替换本地路径为映射的路径
		if (filePath.startsWith(localFilePath)) {
			String mappedPptPath = filePath.replace(localFilePath, localFilePrefix2);
			return domain + mappedPptPath;
		}
		return null;
	}
	
    /**
     * 新增课程教案资料
     *
     * @param courseMaterials 课程教案资料
     * @return 结果
     */
    @Override
    public int insertCourseMaterials(CourseMaterials courseMaterials) {
        // 使用雪花算法生成唯一的ID
        Snowflake snowflake = new Snowflake(1, 1);
        long id = snowflake.generateId();

        // 如果文件对象名称不为空，则调用远程文件服务建立文件和课程资料的关联
        if(ObjectUtils.isNotEmpty(courseMaterials.getFileObjectName())){
            String fileName = courseMaterials.getFileObjectName();
            Long[] longArray = new Long[] { Long.valueOf(fileName) };
            remoteFileService.relationFile(longArray,String.valueOf(id));
        }

        // 获取当前用户信息
        R<LoginUser> userAndRole = remoteUserService.getUserAndRole(SecurityUtils.getUserId(), SecurityConstants.INNER);
        LoginUser data2 = userAndRole.getData();
        String jobId = data2.getSysUser().getJobId();

        // 设置课程资料的ID、登录教师ID、创建时间和创建者
        courseMaterials.setId(id);
        courseMaterials.setLoginTeacherId(jobId);
        courseMaterials.setCreateTime(DateUtils.getNowDate());
        courseMaterials.setCreateBy(String.valueOf(SecurityUtils.getUserId()));

        // 调用Mapper接口插入课程资料信息
        return courseMaterialsMapper.insertCourseMaterials(courseMaterials);
    }

    /**
     * 修改课程教案资料
     *
     * @param courseMaterials 课程教案资料
     * @return 结果
     */
    @Override
    public int updateCourseMaterials(CourseMaterials courseMaterials) {
        if (courseMaterials.getId() == null) {
            throw new RuntimeException("缺少主键id");
        }
        courseMaterials.setUpdateTime(DateUtils.getNowDate());
        return courseMaterialsMapper.updateCourseMaterials(courseMaterials);
    }

    /**
     * 批量删除课程教案资料
     *
     * @param ids 需要删除的课程教案资料主键
     * @return 结果
     */
    /**
     * 批量删除课程教案资料
     *
     * @param ids 需要删除的课程教案资料主键数组
     * @return 删除结果，返回受影响的行数
     */
    @Override
    public int deleteCourseMaterialsByIds(Long[] ids) {
        // 初始化业务ID列表，用于存储与课程资料关联的文件业务ID
        List<Long> busiIdList = new ArrayList<>();
        // 根据课程资料ID查询相关的文件对象名称
        List<String> fileObjectNames = courseMaterialsMapper.selectFileObjectNameById(ids[0]);

        // 遍历文件对象名称列表，获取每个文件的业务ID，并添加到业务ID列表中
        for (String fileObjectName : fileObjectNames) {
            // 调用远程文件服务，获取文件信息
            SysFileInfo sysFileInfo = remoteFileService.getFileInfo(fileObjectName);
            // 将文件信息中的业务ID转换为Long类型，并添加到业务ID列表中
            Long busiId = Long.valueOf(sysFileInfo.getBusiId());
            busiIdList.add(busiId);
        }

        // 遍历业务ID列表，调用远程文件服务删除文件
        for (int i = 0; i < busiIdList.size(); i++) {
            // 调用远程文件服务，根据业务ID删除文件
            remoteFileService.deleteFile(busiIdList.get(i).toString());
        }

        // 调用课程资料映射器，根据ID数组删除课程资料
        return courseMaterialsMapper.deleteCourseMaterialsByIds(ids);
    }

    /**
     * 删除课程教案资料信息
     *
     * @param id 课程教案资料主键
     * @return 结果
     */
    @Override
    public int deleteCourseMaterialsById(Long id) {
        return courseMaterialsMapper.deleteCourseMaterialsById(id);
    }
}
