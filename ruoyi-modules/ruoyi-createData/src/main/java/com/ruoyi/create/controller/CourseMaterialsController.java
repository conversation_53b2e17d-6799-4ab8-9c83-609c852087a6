package com.ruoyi.create.controller;

import java.util.List;
import java.io.IOException;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.CourseMaterials;
import com.ruoyi.create.service.ICourseMaterialsService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 课程教案资料Controller
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
@RestController
@RequestMapping("/courseMaterials")
public class CourseMaterialsController extends BaseController {
    @Resource
    private ICourseMaterialsService courseMaterialsService;

    /**
     * 查询课程教案资料列表
     */
    @GetMapping("/list")
    public TableDataInfo list(CourseMaterials courseMaterials) {
        startPage();
        List<CourseMaterials> list = courseMaterialsService.selectCourseMaterialsList(courseMaterials);
        return getDataTable(list);
    }

    @GetMapping("/list2")
    public TableDataInfo list2(CourseMaterials courseMaterials) {
        startPage();
        List<CourseMaterials> list = courseMaterialsService.selectCourseMaterialsList2(courseMaterials);
        return getDataTable(list);
    }


    /**
     * 导出课程教案资料列表
     */
    @Log(title = "课程教案资料", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CourseMaterials courseMaterials) {
        List<CourseMaterials> list = courseMaterialsService.selectCourseMaterialsList(courseMaterials);
        ExcelUtil<CourseMaterials> util = new ExcelUtil<CourseMaterials>(CourseMaterials.class);
        util.exportExcel(response, list, "课程教案资料数据");
    }

    /**
     * 获取课程教案资料详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(courseMaterialsService.selectCourseMaterialsById(id));
    }

    /**
     * 新增课程教案资料
     */
    @Log(title = "课程教案资料", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CourseMaterials courseMaterials) {
        return toAjax(courseMaterialsService.insertCourseMaterials(courseMaterials));
    }

    /**
     * 新增课程教案
     */
    @Log(title = "新增课程教案", businessType = BusinessType.INSERT)
    @PostMapping("/addTeachPlan")
    public AjaxResult addTeachPlan(@RequestBody CourseMaterials courseMaterials) {
        courseMaterials.setType("1");// 类型1-教案 2-资料
        return toAjax(courseMaterialsService.insertCourseMaterials(courseMaterials));
    }

    /**
     * 修改课程教案
     */
    @Log(title = "修改课程教案", businessType = BusinessType.UPDATE)
    @PutMapping("/editTeachPlan")
    public AjaxResult editTeachPlan(@RequestBody CourseMaterials courseMaterials) {
        courseMaterials.setType("1");// 类型1-教案 2-资料
        return toAjax(courseMaterialsService.updateCourseMaterials(courseMaterials));
    }

    /**
     * 新增课程资料
     */
    @Log(title = "新增课程资料", businessType = BusinessType.INSERT)
    @PostMapping("/addMaterials")
    public AjaxResult addMaterials(@RequestBody CourseMaterials courseMaterials) {
        courseMaterials.setType("2");// 类型1-教案 2-资料
        return toAjax(courseMaterialsService.insertCourseMaterials(courseMaterials));
    }

    /**
     * 修改课程资料
     */
    @Log(title = "修改课程资料", businessType = BusinessType.UPDATE)
    @PutMapping("/editMaterials")
    public AjaxResult editMaterials(@RequestBody CourseMaterials courseMaterials) {
        courseMaterials.setType("2");// 类型1-教案 2-资料
        return toAjax(courseMaterialsService.updateCourseMaterials(courseMaterials));
    }

    /**
     * 修改课程教案资料
     */
    @Log(title = "课程教案资料", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CourseMaterials courseMaterials) {
        return toAjax(courseMaterialsService.updateCourseMaterials(courseMaterials));
    }

    /**
     * 删除课程教案资料
     */
    @Log(title = "课程教案资料", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(courseMaterialsService.deleteCourseMaterialsByIds(ids));
    }
}
