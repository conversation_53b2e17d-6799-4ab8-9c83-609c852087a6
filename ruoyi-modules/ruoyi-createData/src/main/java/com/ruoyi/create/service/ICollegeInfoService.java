package com.ruoyi.create.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.create.domain.CollegeInfo;

/**
 * 学院信息Service接口
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
public interface ICollegeInfoService extends IService<CollegeInfo>
{
    /**
     * 查询学院信息
     *
     * @param id 学院信息主键
     * @return 学院信息
     */
    public CollegeInfo selectCollegeInfoById(Long id);

    /**
     * 查询学院信息列表
     *
     * @param collegeInfo 学院信息
     * @return 学院信息集合
     */
    public List<CollegeInfo> selectCollegeInfoList(CollegeInfo collegeInfo);

    /**
     * 新增学院信息
     *
     * @param collegeInfo 学院信息
     * @return 结果
     */
    public int insertCollegeInfo(CollegeInfo collegeInfo);

    /**
     * 修改学院信息
     *
     * @param collegeInfo 学院信息
     * @return 结果
     */
    public int updateCollegeInfo(CollegeInfo collegeInfo);

    /**
     * 批量删除学院信息
     *
     * @param ids 需要删除的学院信息主键集合
     * @return 结果
     */
    public int deleteCollegeInfoByIds(Long[] ids);

    /**
     * 删除学院信息信息
     *
     * @param id 学院信息主键
     * @return 结果
     */
    public int deleteCollegeInfoById(Long id);

    List<CollegeInfo> getAll();
}
