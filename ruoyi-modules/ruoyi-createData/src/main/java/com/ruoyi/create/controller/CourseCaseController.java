package com.ruoyi.create.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.CourseCase;
import com.ruoyi.create.service.ICourseCaseService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 课程案例Controller
 * 
 * <AUTHOR>
 * @date 2024-12-27
 */
@RestController
@RequestMapping("/case")
public class CourseCaseController extends BaseController
{
    @Autowired
    private ICourseCaseService courseCaseService;

    /**
     * 查询课程案例列表
     */
    @RequiresPermissions("create:case:list")
    @GetMapping("/list")
    public TableDataInfo list(CourseCase courseCase)
    {
        startPage();
        List<CourseCase> list = courseCaseService.selectCourseCaseList(courseCase);
        return getDataTable(list);
    }


    /**
     * 学生查询课程案例列表
     */
    @GetMapping("/student/list")
    public TableDataInfo studentList(CourseCase courseCase)
    {
        startPage();
        List<CourseCase> list = courseCaseService.selectStudentCourseCaseList(courseCase);
        return getDataTable(list);
    }

    /**
     * 导出课程案例列表
     */
    @RequiresPermissions("create:case:export")
    @Log(title = "课程案例", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CourseCase courseCase)
    {
        List<CourseCase> list = courseCaseService.selectCourseCaseList(courseCase);
        ExcelUtil<CourseCase> util = new ExcelUtil<CourseCase>(CourseCase.class);
        util.exportExcel(response, list, "课程案例数据");
    }

    /**
     * 获取课程案例详细信息
     */
    @RequiresPermissions("create:case:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(courseCaseService.selectCourseCaseById(id));
    }

    /**
     * 新增课程案例
     */
    @RequiresPermissions("create:case:add")
    @Log(title = "课程案例", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CourseCase courseCase)
    {
        return toAjax(courseCaseService.insertCourseCase(courseCase));
    }

    /**
     * 修改课程案例
     */
    @RequiresPermissions("create:case:edit")
    @Log(title = "课程案例", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CourseCase courseCase)
    {
        return toAjax(courseCaseService.updateCourseCase(courseCase));
    }

    /**
     * 删除课程案例
     */
    @RequiresPermissions("create:case:remove")
    @Log(title = "课程案例", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(courseCaseService.deleteCourseCaseByIds(ids));
    }
}
