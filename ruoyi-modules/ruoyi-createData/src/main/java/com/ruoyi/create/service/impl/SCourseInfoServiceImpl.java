package com.ruoyi.create.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.SCourseInfoMapper;
import com.ruoyi.create.domain.SCourseInfo;
import com.ruoyi.create.service.ISCourseInfoService;

import javax.annotation.Resource;

/**
 * 课程信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
@Service
public class SCourseInfoServiceImpl implements ISCourseInfoService
{
    @Resource
    private SCourseInfoMapper sCourseInfoMapper;

    /**
     * 查询课程信息
     *
     * @param id 课程信息主键
     * @return 课程信息
     */
    @Override
    public SCourseInfo selectSCourseInfoById(Long id)
    {
        return sCourseInfoMapper.selectSCourseInfoById(id);
    }

    /**
     * 查询课程信息列表
     *
     * @param sCourseInfo 课程信息
     * @return 课程信息
     */
    @Override
    public List<SCourseInfo> selectSCourseInfoList(SCourseInfo sCourseInfo)
    {
        if (!SecurityUtils.isAdmin(SecurityUtils.getUserId())) {
            sCourseInfo.setCreateBy(SecurityUtils.getUsername());
        }
        return sCourseInfoMapper.selectSCourseInfoList(sCourseInfo);
    }

    /**
     * 新增课程信息
     *
     * @param sCourseInfo 课程信息
     * @return 结果
     */
    @Override
    public int insertSCourseInfo(SCourseInfo sCourseInfo)
    {
        sCourseInfo.setCreateBy(SecurityUtils.getUsername());
        sCourseInfo.setCreateTime(DateUtils.getNowDate());
        return sCourseInfoMapper.insertSCourseInfo(sCourseInfo);
    }

    /**
     * 修改课程信息
     *
     * @param sCourseInfo 课程信息
     * @return 结果
     */
    @Override
    public int updateSCourseInfo(SCourseInfo sCourseInfo)
    {
        sCourseInfo.setUpdateTime(DateUtils.getNowDate());
        return sCourseInfoMapper.updateSCourseInfo(sCourseInfo);
    }

    /**
     * 批量删除课程信息
     *
     * @param ids 需要删除的课程信息主键
     * @return 结果
     */
    @Override
    public int deleteSCourseInfoByIds(Long[] ids)
    {
        return sCourseInfoMapper.deleteSCourseInfoByIds(ids);
    }

    /**
     * 删除课程信息信息
     *
     * @param id 课程信息主键
     * @return 结果
     */
    @Override
    public int deleteSCourseInfoById(Long id)
    {
        return sCourseInfoMapper.deleteSCourseInfoById(id);
    }
}
