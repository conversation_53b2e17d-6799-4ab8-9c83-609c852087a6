package com.ruoyi.create.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.domain.CourseMaterials;
import com.ruoyi.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.CourseNotificationsMapper;
import com.ruoyi.create.domain.CourseNotifications;
import com.ruoyi.create.service.ICourseNotificationsService;

import javax.annotation.Resource;

/**
 * 课程通知Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
@Service
public class CourseNotificationsServiceImpl implements ICourseNotificationsService {
    @Resource
    private CourseNotificationsMapper courseNotificationsMapper;

    /**
     * 查询课程通知
     *
     * @param id 课程通知主键
     * @return 课程通知
     */
    @Override
    public CourseNotifications selectCourseNotificationsById(Long id) {
        return courseNotificationsMapper.selectCourseNotificationsById(id);
    }

    /**
     * 查询课程通知列表
     *
     * @param courseNotifications 课程通知
     * @return 课程通知
     */
    @Override
    public List<CourseNotifications> selectCourseNotificationsList(CourseNotifications courseNotifications) {
        return courseNotificationsMapper.selectCourseNotificationsList(courseNotifications);
    }

    @Override
    public List<CourseNotifications> selectCourseNotificationsList2(CourseNotifications courseNotifications) {
        // 设置当前登录用户id 不同的用户查询不同的通知列表
//        LoginUser loginUser = SecurityUtils.getLoginUser();
//        if (loginUser.getRoles().contains("admin")) {
//            // 管理员 返回全部数据
//            return courseNotificationsMapper.selectCourseNotificationsForAll(courseNotifications);
//        } else if (StringUtils.isNotBlank(loginUser.getSysUser().getJobId())) {
//            // 是教师
//            return courseNotificationsMapper.selectCourseNotificationsForTeacher(courseNotifications);
//        } else if (StringUtils.isNotBlank(loginUser.getSysUser().getStudentId())) {
//            // 是学生
//            return courseNotificationsMapper.selectCourseNotificationsForStudent(courseNotifications);
//        } else {
//            return new ArrayList<>();
//        }
        return courseNotificationsMapper.selectCourseNotificationsList2(courseNotifications);
    }

    /**
     * 新增课程通知
     *
     * @param courseNotifications 课程通知
     * @return 结果
     */
    @Override
    public int insertCourseNotifications(CourseNotifications courseNotifications) {
        if (StringUtils.isBlank(courseNotifications.getTitle()) || StringUtils.isBlank(courseNotifications.getContent())) {
            throw new RuntimeException("通知标题和内容不能为空");
        }
        courseNotifications.setCreateBy(String.valueOf(SecurityUtils.getUserId()));
        courseNotifications.setCreateTime(DateUtils.getNowDate());
        courseNotifications.setSendTime(DateUtils.getNowDate());
        courseNotifications.setSenderId(SecurityUtils.getUserId());
        return courseNotificationsMapper.insertCourseNotifications(courseNotifications);
    }


    /**
     * 修改课程通知
     *
     * @param courseNotifications 课程通知
     * @return 结果
     */
    @Override
    public int updateCourseNotifications(CourseNotifications courseNotifications) {
        if (Objects.isNull(courseNotifications.getId())) {
            throw new RuntimeException("唯一主键id不能为空");
        }
        courseNotifications.setUpdateTime(DateUtils.getNowDate());
        return courseNotificationsMapper.updateCourseNotifications(courseNotifications);
    }

    /**
     * 批量删除课程通知
     *
     * @param ids 需要删除的课程通知主键
     * @return 结果
     */
    @Override
    public int deleteCourseNotificationsByIds(Long[] ids) {
        return courseNotificationsMapper.deleteCourseNotificationsByIds(ids);
    }

    /**
     * 删除课程通知信息
     *
     * @param id 课程通知主键
     * @return 结果
     */
    @Override
    public int deleteCourseNotificationsById(Long id) {
        return courseNotificationsMapper.deleteCourseNotificationsById(id);
    }
}
