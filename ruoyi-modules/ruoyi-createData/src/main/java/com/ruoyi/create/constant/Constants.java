package com.ruoyi.create.constant;

import io.jsonwebtoken.Claims;

/**
 * 通用常量信息
 *
 * <AUTHOR>
 */
public class Constants {
    /**
     * UTF-8 字符集
     */
    public static final String UTF8 = "UTF-8";

    /**
     * GBK 字符集
     */
    public static final String GBK = "GBK";

    /**
     * http请求
     */
    public static final String HTTP = "http://";

    /**
     * https请求
     */
    public static final String HTTPS = "https://";

    /**
     * 通用成功标识
     */
    public static final String SUCCESS = "0";

    /**
     * 通用失败标识
     */
    public static final String FAIL = "1";

    /**
     * 登录成功
     */
    public static final String LOGIN_SUCCESS = "Success";

    /**
     * 操作成功
     */
    public static final String RESULT_SUCCESS = "success";

    /**
     * 注销
     */
    public static final String LOGOUT = "Logout";

    /**
     * 注册
     */
    public static final String REGISTER = "Register";

    /**
     * 登录失败
     */
    public static final String LOGIN_FAIL = "Error";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";

    /**
     * 登录用户 redis key
     */
    public static final String LOGIN_TOKEN_KEY = "login_tokens:";

    /**
     * 防重提交 redis key
     */
    public static final String REPEAT_SUBMIT_KEY = "repeat_submit:";

    public static final String EXCEL_IMPORT_KEY = "excle_import:";

    /**
     * 验证码有效期（分钟）
     */
    public static final Integer CAPTCHA_EXPIRATION = 2;

    /**
     * 令牌
     */
    public static final String TOKEN = "token";

    /**
     * 令牌请求头
     */
    public static final String HEADER = "Authorization";

    /**
     * 令牌前缀
     */
    public static final String TOKEN_PREFIX = "author ";

    /**
     * 令牌前缀
     */
    public static final String LOGIN_USER_KEY = "login_user_key";

    /**
     * 用户ID
     */
    public static final String JWT_USERID = "userid";

    /**
     * 用户名称
     */
    public static final String JWT_USERNAME = Claims.SUBJECT;

    /**
     * 用户头像
     */
    public static final String JWT_AVATAR = "avatar";

    /**
     * 创建时间
     */
    public static final String JWT_CREATED = "created";

    /**
     * 用户权限
     */
    public static final String JWT_AUTHORITIES = "authorities";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 重点人员 cache key
     */
    public static final String RISK_PEOPLE_KEY = "p::r:";

    /**
     * 重点场所员工 cache key
     */
    public static final String IMP_PEOPLE_KEY = "em::r:";

    /**
     * 组织机构管理 cache key
     */
    public static final String SYS_REGION_KEY = "r:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    /**
     * 资源映射路径 前缀
     */
    public static final String RESOURCE_PREFIX = "/profile";

    /**
     * RMI 远程方法调用
     */
    public static final String LOOKUP_RMI = "rmi://";

    /**
     * LDAP 远程方法调用
     */
    public static final String LOOKUP_LDAP = "ldap:";

    /**
     * LDAPS 远程方法调用
     */
    public static final String LOOKUP_LDAPS = "ldaps:";

    /**
     * 定时任务白名单配置（仅允许访问的包名，如其他需要可以自行添加）
     */
    public static final String[] JOB_WHITELIST_STR = {"com.kjgl"};

    /**
     * 定时任务违规的字符
     */
    public static final String[] JOB_ERROR_STR = {"java.net.URL", "javax.naming.InitialContext", "org.yaml.snakeyaml",
            "org.springframework", "org.apache"};

    /**
     * 用户登陆类型app
     */
    public static final String USER_TYPE_APP = "01";

    /**
     * 用户登陆类型pc
     */
    public static final String USER_TYPE_PC = "02";

    /**
     * 用户登陆类型PAD
     */
    public static final String USER_TYPE_PAD = "03";

    /**
     * 行政区划等级
     */
    public static final String REGION_LEVEL_PROVINCE = "省";
    public static final String REGION_LEVEL_CITY = "城市";
    public static final String REGION_LEVEL_DISTRICT = "市区";
    public static final String REGION_LEVEL_COUNTY = "街道";
    public static final String REGION_LEVEL_VILLAGE = "村";

    /**
     * 文件没有后缀时的默认类型
     */
    public static final String FILE_DEFAULT_SUFFIX = "default";
    public static final String FILE_DEFAULT_MODEL = "model";


    public static final String INSERT = "insert";
    public static final String DELETE = "delete";
    public static final String UPDATE = "update";


    //小程序
    public static final String MINI_APP01 = "mini01";
    public static final String WX_CP = "wxcp";
    public static final String PROVINCE_LEVEL = "province";
    public static final String CITY_LEVEL = "city";
    public static final String COUNTRY_LEVEL = "country";
    //全国地区树默认最上级编码
    public static final String BASIC_REGION_PARENT_CODE = "0";
    public static final String STATE_NO = "0";
    public static final String STATE_YES = "1";
    //居家隔离上报菜单
    public static final Long MENU_DIVIDE = 2L;
    public static final Long MENU_IMPORTANCE = 3L;
    public static final String TOWN_LEVEL = "town";
    public static final String VILLIAGE_LEVEL = "village";

    // 隔离点使用状态
    public static final String PLACE_STARTUSING = "1";

    // 隔离状态0为隔离1正在隔离 2 隔离接收
    public static final String RECEIVED_ISOLATION = "0";
    public static final String WHILE_ISOLATION = "1";
    public static final String ISOLATION_ACCEPT = "2";

    // 是否有风险
    public static final String NOT_RISKY = "0";
    public static final String HAVE_RISKY = "1";

    // 通知公告 10全部人员，20按照角色，30按照部门，40部分人员
    public static final String RECEIVE_ALL = "10";
    public static final String RECEIVE_ROLE = "20";
    public static final String RECEIVE_DEPARTMENT = "30";
    public static final String RECEIVE_PERSONNEL = "40";
    // 通知公告状态 10草稿，20已发布，30 撤销
    public static final String NOTICE_DRAFT = "10";
    public static final String NOTICE_RELEASE = "20";
    public static final String NOTICE_REVOKE = "30";

    public static final String GUAVA_CACHE_RISKPLACE = "guava_cache_riskplace";
    public static final String GUAVA_CACHE_SYSUSER = "guava_cache_sysuser";
    public static final String GUAVA_CACHE_REGION = "guava_cache_region";
    //境内
    public static final String TRAVELTYPE_IN = "0";
    public static final String TRAVELTYPE_OUT = "1";
    //入返肥报备核酸检测阳性
    public static final String CONTAIN_CHECK_RESULT_YANG = "1";

    /**
     * 小程序登陆用户缓存手机号
     */
    public static final String USER_MINI01_KEY = "u::min:";

    /**
     * 预警信息 忽略
     */
    public static final String WARNING_OVERLOOK = "2";

    /**
     * 黑名单hash的key
     */
    public static final String IP_BLACK_LIST = "i::b";

    /**
     * 请求拦截处理
     */
    public static final String IP_INTERCEPTOR = "i::i:";

    /**
     * 客户端类型  用于扫码判读是小程序还是PC
     */
    public static final String CLIENT_PC = "pc";
    //操作模块，入反肥
    public static final String REPORT_OPER_TYPE = "1";
    public static final String OPER_UPDATE = "update";

    /**
     * 默认密码有效期
     */
    public static final String DEFAULT_PASSWORD_EXPIRE = "90";

    public static final String ROLE_MINI_KEY = "miniKanBan";
    public static final Long MENU_KANBAN = 4L;

    //redis订阅频道前缀
    public static final String TOPIC_PRIFIX = "channel:";
    //任务调度实时组
    public static final String JOB_ACTUAL = "ACTUAL";
    public static final String NUCLEIN_YANG = "1";
    /**
     * 场所排序字典 type 键
     */
    public static final String COUNT_SORT_KEY = "sys_feicheng";

    /**
     *
     */
    public static final String CONTAIN_IMPORTANCE_PLACE = "contain_importance_place:";


    /**
     * 重点场所补丁开关
     * */
    public static final String IMPORTANCE_CONFIG_ENABLED = "sys:import:enabled";

    public static final Long DEPT_ROOT_NODE=1491608408513052674L;

    /**
     * 防疫网格id
     * */
    public static final Long DEPT_ID_GREAD = 1491608468269301761L;
    /**
     * 防疫网格根节点id
     */
    public static final String DEPT_ROOT_PLACE_NODE="1491608468269301761";
    /**
     * 防疫网格根节点祖籍id
     */
    public static final String DEPT_ROOT_NODE_ANCESTORS="1,1491608318792695801";
    public static final Long DEPT_ROOT_NODE_PARENT = 1491608318792695801L;

    //神思扫码校验类型常量类，二维码qrCode和身份证idCard
    public static final String SS_DEVICE_QRCODE = "qrCode";
    public static final String SS_DEVICE_IDCARD = "idCard";
    //是否开启重点场所报备队列报备
    public static final String SYS_CONFIG_QUEUE_RISKP_ENABLED = "sys.risk.r.enabled";

    //启用中的接口
    public static final String SERVER_CONFIG_START = "0";
    //接口启用缓存key
    public static final String CACHE_SERVER_CONFIG_KEY = "cache.s.c.k";

    //重点人员管理是否开启通知
    public static final String RISK_NOCICE_ENABLED = "sys.risk.n.enabled";

    //重点场所类型
    public static final Integer DEPT_PLACE_TYPE = 3;

    //核酸采样点类型
    public static final Integer DEPT_SAMPLING_TYPE = 4;

    //部门顶级节点 parentId
    public static final Long DEPT_ROOT_PARENT_ID = 1L;

    //超级管理员角色编码
    public static final String ADMIN_ROLE_KEY = "admin";

    //系统默认编码
    public static final String SYS_DEFAULT_PWD = "admin123";

    //是否从中高风险地区返回
    public static final String IS_FROM_RISK = "1";
    //重点人员管理类型 居家检测
    public static final String RISK_TYPE_NUCLEIN = "2";
    //重点人员管理类型 居家隔离
    public static final String RISK_TYPE_HOME = "0";
    //重点人员管理类型 隔离点隔离
    public static final String RISK_TYPE_ISOLATE = "1";

    // excel 定义excel模板的表头行号
    public static final Integer HEAD_ROW_NUMBER = 3;

    //是否隔离点是否接收
    public static final String HANDLER_ACCEPT = "1";
    public static final String HANDLER_NO_ACCEPT = "0";

    //居家隔离接收人角色
    public static final String ISOLATE_ROLE_RECEIVER = "jjgljsr";
    public static final String CATCH_DEVICE = "c::device:";
    public static final String WX_NOTICE_KEY = "sys:notice:enabled";

    /**
     * 行政区划全量key
     */
    public static final String SYS_ALL_REGION_KEY = "sys_region";

    //场所报备 是否查询报备数据
    public static final String IMP_REPORT_DAYS_ENABLE = "important.rt.b.enabled";

    //查询当前几天的报备数据
    public static final String IMP_REPORT_DAYS = "important.rt.b.days";

    //需要校验场所的字典值 对应 contain_hangye
    public static final String IMP_REPORT_PLACES = "important.rt.b.places";

    //采样点核酸预约开启状态
    public static final String SAMPLING_START = "10";
    //采样点核酸预约暂停状态
    public static final String SAMPLING_PAUSE = "20";

    //固定采样点
    public static final String SAMPLING_FIXED = "10";
    //临时采样点
    public static final String SAMPLING_TEMPORARY = "20";

    //核酸预约采样点角色
    public static final String ROLE_SAMPLING_KEY = "sampling";
    //核酸预约状态10预约未采样 20已采样 30 预约已过期 40 用户已取消
    public static final String SAMPLING_GUOQI = "30";
    public static final String SAMPLING_QUXIAO = "40";
    public static final String SAMPLING_FINISH = "20";
    public static final String SAMPLING_UNFINSH = "10";
    //小程序登陆保留的session_key
    public static final String MINI_CX_KEY = "mini::cx:";

    public static final String DEPT_MAPPING_KEY = "sys:mapping:dept";
    public static final String REGION_MAPPING_KEY = "sys:mapping:region";

    public static final String ZWY_CACHE = "sys:zwy";


    // redis cache key
    public static final String Sys_Dict_Theme = "sys_dict:theme";
    public static final String Sys_Dict_Custom_Theme = "sys_dict:custom_theme";

    // theme_file_path
    public static final String Sys_Dict_Theme_File_Path = "sys_dict:theme_file_path";


    public static final String Out_File_Folder = "outFileFolder";
    public static final String Theme_Folder_Base = "themeFolderBase";

}

