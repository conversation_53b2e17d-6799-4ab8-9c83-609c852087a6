package com.ruoyi.create.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.utils.Snowflake;
import com.ruoyi.create.utils.SubString;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysFileInfo;
import com.ruoyi.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.CourseVideoMapper;
import com.ruoyi.create.domain.CourseVideo;
import com.ruoyi.create.service.ICourseVideoService;

import javax.annotation.Resource;

/**
 * 课程视频Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-26
 */
@Service
public class CourseVideoServiceImpl implements ICourseVideoService 
{
    @Autowired
    private CourseVideoMapper courseVideoMapper;

    @Resource
    private RemoteFileService remoteFileService;

    @Resource
    private RemoteUserService remoteUserService;

    /**
     * 查询课程视频
     * 
     * @param id 课程视频主键
     * @return 课程视频
     */
    @Override
    public CourseVideo selectCourseVideoById(Long id)
    {
        return courseVideoMapper.selectCourseVideoById(id);
    }

    /**
     * 查询课程视频列表
     * 
     * @param courseVideo 课程视频
     * @return 课程视频
     */
    @Override
    public List<CourseVideo> selectCourseVideoList(CourseVideo courseVideo)
    {

        R<LoginUser> userAndRole = remoteUserService.getUserAndRole(SecurityUtils.getUserId(), SecurityConstants.INNER);
        LoginUser data2 = userAndRole.getData();
        String jobId = data2.getSysUser().getJobId();
        courseVideo.setTeacherId(jobId);
        return courseVideoMapper.selectCourseVideoList(courseVideo);
    }

    /**
     * 新增课程视频
     * 
     * @param courseVideo 课程视频
     * @return 结果
     */
    @Override
    public int insertCourseVideo(CourseVideo courseVideo)
    {
        Snowflake snowflake = new Snowflake(1, 1);
        long id = snowflake.generateId();
        if(ObjectUtils.isNotEmpty(courseVideo.getFileIds())){
            remoteFileService.relationFile(courseVideo.getFileIds(), String.valueOf(id));
        }
        R<LoginUser> userAndRole = remoteUserService.getUserAndRole(SecurityUtils.getUserId(), SecurityConstants.INNER);
        LoginUser data2 = userAndRole.getData();
        String jobId = data2.getSysUser().getJobId();
        courseVideo.setId(id);
        courseVideo.setTeacherId(jobId);
        courseVideo.setCreateBy(SecurityUtils.getUsername());
        courseVideo.setCreateTime(DateUtils.getNowDate());
        return courseVideoMapper.insertCourseVideo(courseVideo);
    }

    /**
     * 修改课程视频
     * 
     * @param courseVideo 课程视频
     * @return 结果
     */
    @Override
    public int updateCourseVideo(CourseVideo courseVideo)
    {
        courseVideo.setUpdateTime(DateUtils.getNowDate());
        return courseVideoMapper.updateCourseVideo(courseVideo);
    }

    /**
     * 批量删除课程视频
     * 
     * @param ids 需要删除的课程视频主键
     * @return 结果
     */
    @Override
    public int deleteCourseVideoByIds(Long[] ids)
    {
        remoteFileService.deleteFile(ids[0].toString());
        return courseVideoMapper.deleteCourseVideoByIds(ids);
    }

    /**
     * 删除课程视频信息
     * 
     * @param id 课程视频主键
     * @return 结果
     */
    @Override
    public int deleteCourseVideoById(Long id)
    {
        return courseVideoMapper.deleteCourseVideoById(id);
    }

    @Override
    public List<CourseVideo> selectStudentCourseVideoList(CourseVideo courseVideo) {
        return courseVideoMapper.selectCourseVideoList(courseVideo);
    }
}
