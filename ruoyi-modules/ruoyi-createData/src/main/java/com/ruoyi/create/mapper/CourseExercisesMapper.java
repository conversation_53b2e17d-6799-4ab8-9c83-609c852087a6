package com.ruoyi.create.mapper;

import java.util.List;
import com.ruoyi.create.domain.CourseExercises;

/**
 * 课程习题Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-27
 */
public interface CourseExercisesMapper 
{
    /**
     * 查询课程习题
     * 
     * @param id 课程习题主键
     * @return 课程习题
     */
    public CourseExercises selectCourseExercisesById(Long id);

    /**
     * 查询课程习题列表
     * 
     * @param courseExercises 课程习题
     * @return 课程习题集合
     */
    public List<CourseExercises> selectCourseExercisesList(CourseExercises courseExercises);

    /**
     * 新增课程习题
     * 
     * @param courseExercises 课程习题
     * @return 结果
     */
    public int insertCourseExercises(CourseExercises courseExercises);

    /**
     * 修改课程习题
     * 
     * @param courseExercises 课程习题
     * @return 结果
     */
    public int updateCourseExercises(CourseExercises courseExercises);

    /**
     * 删除课程习题
     * 
     * @param id 课程习题主键
     * @return 结果
     */
    public int deleteCourseExercisesById(Long id);

    /**
     * 批量删除课程习题
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCourseExercisesByIds(Long[] ids);
}
