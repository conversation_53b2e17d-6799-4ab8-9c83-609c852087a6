package com.ruoyi.create.controller;

import java.security.KeyFactory;
import java.security.interfaces.RSAPrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.io.IOException;
import java.util.Map;
import javax.crypto.Cipher;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.security.annotation.InnerAuth;
import org.apache.tomcat.util.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.AuthenInfor;
import com.ruoyi.create.service.IAuthenInforService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 鉴权信息Controller
 * 
 * <AUTHOR>
 * @date 2024-06-14
 */
@RestController
@RequestMapping("/infor")
public class AuthenInforController extends BaseController
{
    @Autowired
    private IAuthenInforService authenInforService;

    private String privateKey = "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBALL2yjem0ENoZaz2AFPSMNATos67ehwIDqJCuBmAmIOd9p+iKYdsWAbDUJrKezu7hdJBXeJzT3beLhCtuzt0EqL7DFl8i5RgQygO9hceTyt87errhPOpojVDTPs9hDBzSTY33ouI55jPH2I+8EblKaynLs08eXmjTGXsDqM5XYyZAgMBAAECgYA2oYudbXjJ+wZ+xCHZdKKeAkCC50whXnxJICDe+BiWpRPyKyiORI6ikeD7P7BazaXOR1IHnLe3S5+4S7CKN6awQUpE+f4ayYMfRoSbH8d8ZeioWTxmP24tgYgaF9Z3wEnesc3auZIyOnqH9eo0fsvjgKiq1ptWUP5BL0oLjo7gYQJBAP8szJbAOBTCgDXcUcD1Y3eJ4UDsoWBZ0xzQD2NinseKvwoaB2KBd4OSUPkPhhlIoL9OBM9T4agPI4hFmtKEgfUCQQCziunE0VBFfVGOKvk8QIT6F08F340tJr48OIsdJnZhOVjoQHqI3c1lFRxSuSGFxtyaqfHruWcyveJScYwkr6WVAkBEuNn4l5gC70b8OnPCFdRN81I43AGyIz7Z+abLS1obv2An5k6q1tdLFfK8wNOKp6azHt3owFx7mGgnYSeLHqipAkAidgBGobJZlCMqOX9bHDsp0X1+cBkl2HDdGDFDaBWCtcIl2fJrAL+irjmgex4/EhtXqFTh3NU8/QtKrbard/c9AkBSHXD0A2aPqPN2w9JYTIsXPV3ZLGg6/oOyVJ40DMu4Y2Ndw5tr42lM1i/DHFiR78ZqTRK/kMN5bb1Sn3kze4Cu";


    /**
     * 查询鉴权信息列表
     */
    @RequiresPermissions("create:infor:list")
    @GetMapping("/list")
    public TableDataInfo list(AuthenInfor authenInfor)
    {
        startPage();
        List<AuthenInfor> list = authenInforService.selectAuthenInforList(authenInfor);
        return getDataTable(list);
    }

    /**
     * 导出鉴权信息列表
     */
    @RequiresPermissions("create:infor:export")
    @Log(title = "鉴权信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AuthenInfor authenInfor)
    {
        List<AuthenInfor> list = authenInforService.selectAuthenInforList(authenInfor);
        ExcelUtil<AuthenInfor> util = new ExcelUtil<AuthenInfor>(AuthenInfor.class);
        util.exportExcel(response, list, "鉴权信息数据");
    }

    /**
     * 获取鉴权信息详细信息
     */
    @RequiresPermissions("create:infor:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(authenInforService.selectAuthenInforById(id));
    }

    /**
     * 新增鉴权信息
     */
    @RequiresPermissions("create:infor:add")
    @Log(title = "鉴权信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AuthenInfor authenInfor) throws Exception {
        return toAjax(authenInforService.insertAuthenInfor(authenInfor));
    }

    /**
     * 修改鉴权信息
     */
    @RequiresPermissions("create:infor:edit")
    @Log(title = "鉴权信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AuthenInfor authenInfor) throws Exception {
        return toAjax(authenInforService.updateAuthenInfor(authenInfor));
    }

    /**
     * 删除鉴权信息
     */
    @RequiresPermissions("create:infor:remove")
    @Log(title = "鉴权信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(authenInforService.deleteAuthenInforByIds(ids));
    }

    /**
     * 根据菜单路由获取鉴权信息
     */
    @PostMapping("/menu")
    public String getAuthenInfo(String menuRouting,String key)
    {
        return authenInforService.selectConfigByKey(menuRouting,key);
    }

    @InnerAuth
    @GetMapping("/all")
    public List<Map<String,String>> all()
    {
        List<AuthenInfor> list = authenInforService.selectAuthenInforAll();
        List<Map<String,String>> mapList = new ArrayList<>();
        for (AuthenInfor authenInfor : list) {
            try {
                Map<String, String> map = new HashMap<>();
                map.put("menuRouting", authenInfor.getMenuRouting());
                map.put("apiKey", decrypt(authenInfor.getApiKey()));
                map.put("secretKey", decrypt(authenInfor.getSecretKey()));
                map.put("apiUrl", decrypt(authenInfor.getApiUrl()));
                map.put("ak", decrypt(authenInfor.getAk()));
                map.put("sk", decrypt(authenInfor.getSk()));
                map.put("domainName", decrypt(authenInfor.getDomainName()));
                map.put("bosBucketName", decrypt(authenInfor.getBosBucketName()));
                mapList.add(map);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return mapList;
    }

    public String decrypt(String str) throws Exception {
        //64位解码加密后的字符串
        byte[] inputByte = Base64.decodeBase64(str.getBytes("UTF-8"));
        //base64编码的私钥
        byte[] decoded = Base64.decodeBase64(privateKey);
        RSAPrivateKey priKey = (RSAPrivateKey) KeyFactory.getInstance("RSA").generatePrivate(new PKCS8EncodedKeySpec(decoded));
        //RSA解密
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, priKey);
        String outStr = new String(cipher.doFinal(inputByte));
        return outStr;
    }
}
