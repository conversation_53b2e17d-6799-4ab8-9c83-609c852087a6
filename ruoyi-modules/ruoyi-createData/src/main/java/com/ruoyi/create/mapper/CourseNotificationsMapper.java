package com.ruoyi.create.mapper;

import java.util.List;
import com.ruoyi.create.domain.CourseNotifications;

/**
 * 课程通知Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
public interface CourseNotificationsMapper {
    /**
     * 查询课程通知
     *
     * @param id 课程通知主键
     * @return 课程通知
     */
    public CourseNotifications selectCourseNotificationsById(Long id);

    /**
     * 查询课程通知列表
     *
     * @param courseNotifications 课程通知
     * @return 课程通知集合
     */
    public List<CourseNotifications> selectCourseNotificationsList(CourseNotifications courseNotifications);

    /**
     * 新增课程通知
     *
     * @param courseNotifications 课程通知
     * @return 结果
     */
    public int insertCourseNotifications(CourseNotifications courseNotifications);

    /**
     * 修改课程通知
     *
     * @param courseNotifications 课程通知
     * @return 结果
     */
    public int updateCourseNotifications(CourseNotifications courseNotifications);

    /**
     * 删除课程通知
     *
     * @param id 课程通知主键
     * @return 结果
     */
    public int deleteCourseNotificationsById(Long id);

    /**
     * 批量删除课程通知
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCourseNotificationsByIds(Long[] ids);


    /** 查询登录用户是教师的通知*/
    List<CourseNotifications> selectCourseNotificationsForTeacher(CourseNotifications courseNotifications);
    /** 查询登录用户是学生的通知*/
    List<CourseNotifications> selectCourseNotificationsForStudent(CourseNotifications courseNotifications);
    /** 管理员查询全部的通知*/
    List<CourseNotifications> selectCourseNotificationsForAll(CourseNotifications courseNotifications);

    List<CourseNotifications> selectCourseNotificationsList2(CourseNotifications courseNotifications);
}
