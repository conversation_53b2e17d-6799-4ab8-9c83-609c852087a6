package com.ruoyi.create.controller;

import java.io.File;
import java.io.FileInputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.domain.ApplicationScenario;
import com.ruoyi.create.dto.UserDto;
import com.ruoyi.create.exception.CustomException;
import com.ruoyi.create.service.IApplicationScenarioService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.StudentScenario;
import com.ruoyi.create.service.IStudentScenarioService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 学生应用场景Controller
 *
 * <AUTHOR>
 * @date 2024-10-11
 */
@Slf4j
@RestController
@RequestMapping("/scenarioS")
public class StudentScenarioController extends BaseController
{
    @Autowired
    private IStudentScenarioService studentScenarioService;
    @Autowired
    private IApplicationScenarioService applicationScenarioService;
    /**
     * 查询学生应用场景列表
     */
    //@RequiresPermissions("test:scenario:list")
    @GetMapping("/list")
    public TableDataInfo list(StudentScenario studentScenario)
    {
        startPage();
        List<StudentScenario> list = studentScenarioService.selectStudentScenarioList(studentScenario);
        return getDataTable(list);
    }



    /**
     * 获取学生应用场景详细信息
     */
    //@RequiresPermissions("test:scenario:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(studentScenarioService.selectStudentScenarioById(id));
    }

    @GetMapping(value = "/bg/{id}")
    public void getBgById(@PathVariable("id") String id,HttpServletResponse response)
    {
        String username = SecurityUtils.getUsername();
        UserDto userDto = applicationScenarioService.selectUserByUserName(username);
        String imageUrl = applicationScenarioService.selectApplicationScenarioById(Long.valueOf(id)).getImagePath();

        if(userDto.getStudentId() != null && userDto.getStudentId() != ""){
            StudentScenario studentScenario = studentScenarioService.selectStudentScenarioBgById(StudentScenario.builder().scenario(id).studentId(Long.valueOf(userDto.getStudentId())).build());
            if (studentScenario!=null && studentScenario.getImageUrl()!=null && studentScenario.getImageUrl() != ""){
                imageUrl =studentScenario.getImageUrl();
            }
        }

       readFile(response,imageUrl);
    }


    /**
     * 新增学生应用场景
     */
    //@RequiresPermissions("test:scenario:add")
    @Log(title = "学生应用场景", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StudentScenario studentScenario)
    {
        return toAjax(studentScenarioService.insertStudentScenario(studentScenario));
    }

    /**
     * 修改学生应用场景
     */
    //@RequiresPermissions("test:scenario:edit")
    @Log(title = "学生应用场景", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody StudentScenario studentScenario)
    {
        return toAjax(studentScenarioService.updateStudentScenario(studentScenario));
    }

    /**
     * 删除学生应用场景
     */
    //@RequiresPermissions("test:scenario:remove")
    @Log(title = "学生应用场景", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(studentScenarioService.deleteStudentScenarioByIds(ids));
    }


    @Log(title = "学生应用场景", businessType = BusinessType.INSERT)
    @PostMapping("/addOrUpdate")
    public AjaxResult addOrUpdate(@RequestBody ApplicationScenario applicationScenario)
    {

        String username = SecurityUtils.getUsername();

        UserDto userDto = applicationScenarioService.selectUserByUserName(username);
        try {
            if (userDto.getUniversityId() == null || userDto.getCollegeId() == null) {
                System.out.println("登陆人学校学院部分为空");
                userDto.setUniversityId("");
                userDto.setCollegeId("");
            }
        } catch (NullPointerException e) {
            System.out.println("捕获到空指针异常：" + e.getMessage());
            // 这里可以处理异常，例如记录日志、提供默认值或者重新抛出异常等
        }

        StudentScenario studentScenario = StudentScenario.builder()
                .imageUrl(applicationScenario.getImagePath())
                .scenario(String.valueOf(applicationScenario.getId()))
                .studentId(Long.valueOf(userDto.getStudentId()))
                .school(userDto.getUniversityId())
                .college(userDto.getCollegeId())
                .build();
        return toAjax(studentScenarioService.insertOrUpdateStudentScenario(studentScenario));
    }

    @Log(title = "学生应用场景", businessType = BusinessType.INSERT)
    @PostMapping("/deleteImgByPath")
    public AjaxResult deleteImgByPath(@RequestBody ApplicationScenario applicationScenario)
    {
        studentScenarioService.deleteImgByPath(applicationScenario.getImagePath());
        return success();
    }

    public void readFile(HttpServletResponse response, String filepath) {
        File file = new File(filepath);
        if (!file.exists()) {
            log.warn("文件不存在: " + filepath);
            return;
        }

        try (FileInputStream fileInputStream = new FileInputStream(file);
             OutputStream outputStream = response.getOutputStream()) {

            // 根据文件扩展名或者文件内容自动设置 Content-Type
            String contentType = Files.probeContentType(Paths.get(filepath));
            if (contentType == null) {
                // 根据文件扩展名手动设置一些常见的Content-Type
                if (filepath.endsWith(".txt")) {
                    contentType = "text/plain; charset=UTF-8";
                } else if (filepath.endsWith(".html")) {
                    contentType = "text/html; charset=UTF-8";
                } else if (filepath.endsWith(".pdf")) {
                    contentType = "application/pdf";
                } else if (filepath.endsWith(".jpg") || filepath.endsWith(".jpeg")) {
                    contentType = "image/jpeg";
                } else if (filepath.endsWith(".png")) {
                    contentType = "image/png";
                } else {
                    contentType = "application/octet-stream";  // 默认类型，适合任意二进制文件
                }
            }

            // 设置响应的内容类型
            response.setContentType(contentType);

            byte[] bytes = new byte[4096];
            int len;
            while ((len = fileInputStream.read(bytes)) != -1) {
                outputStream.write(bytes, 0, len);
            }
            outputStream.flush();

        } catch (Exception e) {
            log.error("读取文件异常", e);
            throw new CustomException("读取文件异常");
        }
    }
}
