package com.ruoyi.create.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
public interface MySelfPortraitDtoMapper
{

        //1.知识点掌握度
        Double selectMaxProgressByStudentId(@Param("studentId") String studentId);
        //2.作业质量
        Double selectCorrectRateByStudentId(@Param("studentId") String studentId);
        //2.专业兴趣
        Integer selectOnTimeRateByStudentId(@Param("studentId") String studentId);
        //4.他人评语
        List<String> selectRemarksByStudentId(@Param("studentId") String studentId);







}