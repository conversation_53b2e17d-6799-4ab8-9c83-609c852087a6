package com.ruoyi.create.domain.DataDaping;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 知识库课程数量对象 s_course_knowledge
 * 
 * <AUTHOR>
 * @date 2025-04-08
 */
public class SCourseKnowledge extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 学科名称 */
    @Excel(name = "学科名称")
    private String subject;

    /** 课程数量 */
    @Excel(name = "课程数量")
    private Long counts;

    /** 统计时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "统计时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date statisticsTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setSubject(String subject) 
    {
        this.subject = subject;
    }

    public String getSubject() 
    {
        return subject;
    }
    public void setCount(Long count) 
    {
        this.counts = count;
    }

    public Long getCount() 
    {
        return counts;
    }
    public void setStatisticsTime(Date statisticsTime) 
    {
        this.statisticsTime = statisticsTime;
    }

    public Date getStatisticsTime() 
    {
        return statisticsTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("subject", getSubject())
            .append("counts", getCount())
            .append("statisticsTime", getStatisticsTime())
            .toString();
    }
}
