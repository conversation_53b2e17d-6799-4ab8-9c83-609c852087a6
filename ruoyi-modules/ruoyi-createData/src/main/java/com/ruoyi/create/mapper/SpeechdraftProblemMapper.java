package com.ruoyi.create.mapper;


import com.ruoyi.create.domain.SpeechdraftProblem;

import java.util.List;

/**
 * 讲演稿题目Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
public interface SpeechdraftProblemMapper
{
    /**
     * 查询讲演稿题目
     *
     * @param id 讲演稿题目主键
     * @return 讲演稿题目
     */
    public SpeechdraftProblem selectSpeechdraftProblemById(Long id);

    /**
     * 查询讲演稿题目列表
     *
     * @param speechdraftProblem 讲演稿题目
     * @return 讲演稿题目集合
     */
    public List<SpeechdraftProblem> selectSpeechdraftProblemList(SpeechdraftProblem speechdraftProblem);

    /**
     * 新增讲演稿题目
     *
     * @param speechdraftProblem 讲演稿题目
     * @return 结果
     */
    public int insertSpeechdraftProblem(SpeechdraftProblem speechdraftProblem);

    /**
     * 修改讲演稿题目
     *
     * @param speechdraftProblem 讲演稿题目
     * @return 结果
     */
    public int updateSpeechdraftProblem(SpeechdraftProblem speechdraftProblem);

    /**
     * 删除讲演稿题目
     *
     * @param id 讲演稿题目主键
     * @return 结果
     */
    public int deleteSpeechdraftProblemById(Long id);
    public int deleteSpeechdraftProblemBypresentationId(Long presentationId);

    /**
     * 批量删除讲演稿题目
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSpeechdraftProblemByIds(Long[] ids);

    int insertSpeechdraftProblemList(List<SpeechdraftProblem> speechdraftProblemArrayList);
}
