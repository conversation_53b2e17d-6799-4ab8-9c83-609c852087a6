package com.ruoyi.create.service.impl;

import com.baidubce.appbuilder.base.exception.AppBuilderServerException;
import com.baidubce.appbuilder.console.appbuilderclient.AppBuilderClient;
import com.baidubce.appbuilder.model.appbuilderclient.AppBuilderClientIterator;
import com.baidubce.appbuilder.model.appbuilderclient.AppBuilderClientResult;
import com.baidubce.appbuilder.model.appbuilderclient.Event;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.baidu.api.BaiduApiService;
import com.ruoyi.baidu.api.dto.BaiduDto;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.enums.QuestionTypes;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.domain.*;
import com.ruoyi.create.mapper.HomeworkMapper;
import com.ruoyi.create.mapper.PromptTemplateMapper;
import com.ruoyi.create.service.*;
import com.ruoyi.create.utils.UserUtils;
import com.ruoyi.system.api.ConfigService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysUser;
import okhttp3.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 作业信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@Service
@RefreshScope
public class HomeworkServiceImpl extends ServiceImpl<HomeworkMapper, Homework> implements IHomeworkService
{
    private Logger logger = LoggerFactory.getLogger(HomeworkServiceImpl.class);

    @Value("${dmx.apiKey}")
    private String apiKey;
    @Value("${dmx.secretKey}")
    private String secretKey;
    @Value("${dmx.apiURL}")
    private String apiURL;

    // 设置连接超时和读取超时
    private static final int connectTimeout = 60; // 连接超时时长，单位为秒
    private static final int readTimeout = 60; // 读取超时时长，单位为秒
    static final OkHttpClient HTTP_CLIENT = new OkHttpClient().newBuilder()
            .connectTimeout(connectTimeout, TimeUnit.SECONDS)
            .readTimeout(readTimeout, TimeUnit.SECONDS)
            .build();

    @Autowired
    private IHomeworkService homeworkService;
    @Autowired
    private HomeworkMapper homeworkMapper;
    @Autowired
    private IHomeworkQuestionReqService homeworkQuestionReqService;
    @Autowired
    private IHomeworkQuestionService homeworkQuestionService;
    @Autowired
    private IHomeworkQuestionOptionService homeworkQuestionOptionService;
    @Autowired
    private IHomeworkStudentService homeworkStudentService;
    @Autowired
    private IHomeworkStudentDetailService homeworkStudentDetailService;
    @Autowired
    private IClassInfoService classInfoService;
    @Autowired
    private ILessonService lessonService;
    @Autowired
    private RemoteUserService userService;
    @Autowired
    private UserUtils userUtils;
    @Autowired
    private ConfigService configService;

    @Autowired
    private PromptTemplateMapper promptTemplateMapper;

    @Autowired
    private BaiduApiService baiduApiService;

    /**
     * 查询作业信息
     *
     * @param id 作业信息主键
     * @return 作业信息
     */
    @Override
    public Homework selectHomeworkById(Long id)
    {
        //作业主表信息
        Homework homework = homeworkService.getById(id);

        //根据作业ID查询题目列表
        List<HomeworkQuestion> homeworkQuestions = homeworkQuestionService.list(new LambdaQueryWrapper<HomeworkQuestion>()
                .eq(HomeworkQuestion::getHmId, id));
        if(CollectionUtils.isNotEmpty(homeworkQuestions)) {
            //获取所有题目ID
            List<Long> questionIds = homeworkQuestions.stream()
                    .map(HomeworkQuestion::getId)
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(questionIds)) {
                //根据所有题目ID查询所有选项列表
                List<HomeworkQuestionOption> optionList = homeworkQuestionOptionService.list(new LambdaQueryWrapper<HomeworkQuestionOption>()
                        .in(HomeworkQuestionOption::getQuestionId, questionIds));
                //遍历题目
                homeworkQuestions.forEach(question -> {
                    //根据题目ID筛选题目选项
                    List<HomeworkQuestionOption> options = optionList.stream()
                            .filter(option -> option.getQuestionId().equals(question.getId()))
                            .collect(Collectors.toList());
                    //为题目选项字段赋值
                    question.setHomeworkQuestionOptions(options);
                });
            }

        }
        //为题目字段赋值
        homework.setHomeworkQuestions(homeworkQuestions);

        return homework;
    }

    /**
     * 查询作业信息列表
     *
     * @param homework 作业信息
     * @return 作业信息
     */
    @Override
    public List<Homework> selectHomeworkList(Homework homework)
    {
        //如果是不是管理员只能查询对应学校的
        if (!SecurityUtils.isAdmin(SecurityUtils.getUserId())){
            Long universityId = SecurityUtils.getLoginUser().getSysUser().getUniversityId();
            homework.setUniversityId(universityId);
        }
        // 获取权限
        long uid = SecurityUtils.getUserId();
        AjaxResult ajaxResult = userService.getInfo(SecurityUtils.getUserId(), SecurityConstants.INNER);
        List<Integer> userRoleid = (ArrayList) ajaxResult.get("roleIds");
        boolean isAdmin = userRoleid.contains(1) || userRoleid.contains(105);
        //查询作业
        new ArrayList<>();
        //管理员
        if(!isAdmin){
            homework.setCreateBy(userUtils.getNickName(uid));
        }
        List<Homework> homeworkList =  homeworkMapper.selectHomeworkList(homework);
        return homeworkList;
    }

    /**
     * 新增作业信息
     *
     * @param homework 作业信息
     * @return 结果
     */
    @Override
    public int insertHomework(Homework homework)
    {
        return homeworkMapper.insertHomework(homework);
    }

    /**
     * 修改作业信息
     *
     * @param homework 作业信息
     * @return 结果
     */
    @Override
    public int updateHomework(Homework homework)
    {
        /** 更新作业 **/
        homework.setUpdateBy(userUtils.getNickName(SecurityUtils.getUsername()));
        homework.setUpdateTime(DateUtils.getTime());
        homeworkService.update(homework, new LambdaUpdateWrapper<Homework>()
                .eq(Homework::getId, homework.getId()));
        /** 保存题目 和 题目选项 **/
        List<HomeworkQuestion> homeworkQuestions = homework.getHomeworkQuestions();//新题目
        if(CollectionUtils.isNotEmpty(homeworkQuestions)) {
            /** 删除题目和选项 **/
            //查询旧题目
            List<HomeworkQuestion> lastHomeWorkQuestions = homeworkQuestionService.list(new LambdaQueryWrapper<HomeworkQuestion>()
                    .eq(HomeworkQuestion::getHmId, homework.getId()));//旧题目
            //根据题目ID删除选项
            List<Long> questionIds = lastHomeWorkQuestions.stream().map(HomeworkQuestion::getId).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(questionIds)) {
                homeworkQuestionOptionService.remove(new LambdaQueryWrapper<HomeworkQuestionOption>()
                        .in(HomeworkQuestionOption::getQuestionId, questionIds));
            }
            //根据作业ID删除题目
            homeworkQuestionService.remove(new LambdaQueryWrapper<HomeworkQuestion>()
                    .eq(HomeworkQuestion::getHmId, homework.getId()));

            /** 新增题目和选项 **/
            homeworkQuestions.forEach(item -> {
                /**新增作业题目**/
                item.setHmId(homework.getId());
                homeworkQuestionService.save(item);

                /**新增题目选项**/
                List<HomeworkQuestionOption> homeworkQuestionOptions = item.getHomeworkQuestionOptions();
                if(CollectionUtils.isNotEmpty(homeworkQuestionOptions)) {
                    homeworkQuestionOptions.forEach(option -> {
                        option.setQuestionId(item.getId());
                    });
                    homeworkQuestionOptionService.saveBatch(homeworkQuestionOptions);
                }
            });
        }

        return 1;
    }

    /**
     * 批量删除作业信息
     *
     * @param ids 需要删除的作业信息主键
     * @return 结果
     */
    @Override
    public int deleteHomeworkByIds(Long[] ids)
    {
        /** 删除题目和选项 **/
        List<HomeworkQuestion> lastHomeWorkQuestions = homeworkQuestionService.list(new LambdaQueryWrapper<HomeworkQuestion>()
                .in(HomeworkQuestion::getHmId, ids));
        //根据题目ID删除选项
        List<Long> questionIds = lastHomeWorkQuestions.stream().map(HomeworkQuestion::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(questionIds)) {
            homeworkQuestionOptionService.remove(new LambdaQueryWrapper<HomeworkQuestionOption>()
                    .in(HomeworkQuestionOption::getQuestionId, questionIds));
        }
        //删除题目
        homeworkQuestionService.remove(new LambdaQueryWrapper<HomeworkQuestion>()
                .in(HomeworkQuestion::getHmId, ids));
        //删除学生作业明细表
        homeworkStudentDetailService.remove(new LambdaQueryWrapper<HomeworkStudentDetail>()
                .in(HomeworkStudentDetail::getHmId, ids));
        //删除学生作业列表
        homeworkStudentService.remove(new LambdaQueryWrapper<HomeworkStudent>()
                .in(HomeworkStudent::getHmId, ids));

        return homeworkMapper.deleteHomeworkByIds(ids);
    }

    /**
     * 删除作业信息信息
     *
     * @param id 作业信息主键
     * @return 结果
     */
    @Override
    public int deleteHomeworkById(Long id)
    {
        /** 删除题目和选项 **/
        List<HomeworkQuestion> lastHomeWorkQuestions = homeworkQuestionService.list(new LambdaQueryWrapper<HomeworkQuestion>()
                .eq(HomeworkQuestion::getHmId, id));
        //根据题目ID删除选项
        List<Long> questionIds = lastHomeWorkQuestions.stream().map(HomeworkQuestion::getId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(questionIds)) {
            homeworkQuestionOptionService.remove(new LambdaQueryWrapper<HomeworkQuestionOption>()
                    .in(HomeworkQuestionOption::getQuestionId, questionIds));
        }
        //删除题目
        homeworkQuestionService.remove(new LambdaQueryWrapper<HomeworkQuestion>()
                .eq(HomeworkQuestion::getHmId, id));
        //删除学生作业明细表
        homeworkStudentDetailService.remove(new LambdaQueryWrapper<HomeworkStudentDetail>()
                .eq(HomeworkStudentDetail::getHmId, id));
        //删除学生作业列表
        homeworkStudentService.remove(new LambdaQueryWrapper<HomeworkStudent>()
                .eq(HomeworkStudent::getHmId, id));

        return homeworkMapper.deleteHomeworkById(id);
    }

    @Override
    public AjaxResult createHomeWork(Homework homework) {
        //作业题型要求
        List<HomeworkQuestionReq> homeworkQuestionReqs = homework.getHomeworkQuestionReqs();
        //作业
        StringBuffer hm = new StringBuffer();

        if(CollectionUtils.isNotEmpty(homeworkQuestionReqs)) {
            //根据课程ID获取教材名称
//            Lesson lesson = homework.get;
            List<HomeworkQuestion> homeworkQuestions = new ArrayList<>();
            String homework_templateId = configService.getConfigKey2("homework_template", SecurityConstants.INNER).get("msg").toString();
            PromptTemplate promptTemplate = promptTemplateMapper.selectPromptTemplateById(Long.parseLong(homework_templateId));
            homeworkQuestionReqs.forEach(item -> {
                StringBuffer requestBuffer = new StringBuffer();



                /*String request_main = String.format("你是一名大学老师。请根据《%s》教材，出", lesson.getTextbookName());
                requestBuffer.append(request_main);

                String question_req = String.format("%s道%s。 ", item.getCount(), QuestionTypes.getInfoByCode(item.getQuestionType()));
                requestBuffer.append(question_req);*/
                String request_main = promptTemplate.getTemplateContent();
                request_main = String.format(request_main,homework.getLessonName(),item.getCount(),QuestionTypes.getInfoByCode(item.getQuestionType()));
                requestBuffer.append(request_main);
                if(StringUtils.isNotEmpty(homework.getElseDec())) {
                    requestBuffer.append("其他要求：").append(homework.getElseDec()).append("。");
                }

                //requestBuffer.append("要求：每次返回格式必须按下述要求返回：\n");

                /*requestBuffer.append("要求每道题目下方提供正确答案，不要提供答案解析。" +
                        "##参考以下试卷出题形式，按同样格式输出：\n" );*/

                // 1、先通过枚举的getEnumByType方法获取具体的枚举
                QuestionTypes questionTypes = QuestionTypes.getEnumByType(item.getQuestionType());
                switch (questionTypes) {
                    case SINGLE :
                        String hm_templateId_single = configService.getConfigKey2("homework_single", SecurityConstants.INNER).get("msg").toString();
                        PromptTemplate promptTemplate_single = promptTemplateMapper.selectPromptTemplateById(Long.parseLong(hm_templateId_single));
                        requestBuffer.append(promptTemplate_single.getTemplateContent());
                        //requestBuffer.append("问题答案简洁，必须在问题前带上“Q：”，不要答案解析，格式如：“Q：世界上最高的山是？”,“A.泰山B.华山,C.喜马拉雅山,D.印度恒河”，并给出答案，格式如：“答案：C”");
                        break;
                    case MULTIPLE :
                        //requestBuffer.append("问题答案简洁，必须在问题前带上“Q：”，不要答案解析，格式如：“Q：下列哪些河流是中国著名的长河？”,“A.长江B.黄河,C.亚马逊河,D.印度恒河”，并给出答案，格式如：“答案：A、B”");
                        String hm_templateId_multiple = configService.getConfigKey2("homework_multiple", SecurityConstants.INNER).get("msg").toString();
                        PromptTemplate promptTemplate_multiple = promptTemplateMapper.selectPromptTemplateById(Long.parseLong(hm_templateId_multiple));
                        requestBuffer.append(promptTemplate_multiple.getTemplateContent());
                        break;
                    case BLANK :
                        //requestBuffer.append("必须在问题前带上“Q：”，问题中空出答题部分，不要答案解析，格式如：“Q：世界上最高的山是_____”，并给出答案，格式如：“答案：喜马拉雅山”");
                        String hm_templateId_blank = configService.getConfigKey2("homework_blank", SecurityConstants.INNER).get("msg").toString();
                        PromptTemplate promptTemplate_blank = promptTemplateMapper.selectPromptTemplateById(Long.parseLong(hm_templateId_blank));
                        requestBuffer.append(promptTemplate_blank.getTemplateContent());
                        break;
                    case SHORTANSWER :
                        //requestBuffer.append("必须在问题前带上“Q：”，不要答案解析，格式如：“Q：世界上最高的山是什么山”，并给出答案，格式如：“答案：喜马拉雅山”");
                        String hm_templateId_shortAnswer = configService.getConfigKey2("homework_shortAnswer", SecurityConstants.INNER).get("msg").toString();
                        PromptTemplate promptTemplate_shortAnswer = promptTemplateMapper.selectPromptTemplateById(Long.parseLong(hm_templateId_shortAnswer));
                        requestBuffer.append(promptTemplate_shortAnswer.getTemplateContent());
                        break;
                    case NOUNDEFINITION :
                        requestBuffer.append("必须在问题前带上“Q：”，不要答案解析，格式如：“Q：世界上最高的山是什么山”，并给出答案，格式如：“答案：喜马拉雅山”");
                        break;
                    case CASEANALYSIS :
                        requestBuffer.append("格式如：案例:XXX、案例描述:XXX、问题:XXX、分析:XXX、答案:XXX;问题只出一道，以上以文字描述");
                        break;
                    case ESSAYQUESTION :
                        requestBuffer.append("必须在问题前带上“Q：”，不要答案解析，格式如：“Q：世界上最高的山是什么山”，并给出答案，格式如：“答案：喜马拉雅山”");
                        break;
                }



                logger.info("作业要求：" + requestBuffer);
                //构建聊天记录
                List<Messages> messagesList = new ArrayList<>();
                Messages messages = new Messages();
                messages.setRole("user");
                messages.setContent(requestBuffer.toString().replaceAll("\\r\\n|\\r|\\n", ""));
                messagesList.add(messages);

                //发送聊天内容
                try {
                    //模型
                   // String result = sendContent(messagesList);
                    //知识库
                    String result = knowledgeBase(messagesList).replaceAll("\\*", "");
                    logger.info("作业内容："+ result);
                    /** 解析作业文本 **/
                    List<HomeworkQuestion> questions = parseText(result, item.getQuestionType());
                    homeworkQuestions.addAll(questions);
                    //hm.append(result).append("\n");

                } catch (IOException e) {
                    e.printStackTrace();
                    logger.error(e.getMessage());
                }  catch (AppBuilderServerException e) {
                    e.printStackTrace();
                    logger.error(e.getMessage());
                }
            });
            return AjaxResult.success(homeworkQuestions);
        } else {
            return AjaxResult.error("请选择作业包含的题型");
        }
    }

    @Override
    public AjaxResult saveHomeWork(Homework homework) {
        //如果是不是管理员要写入对应的大学id
        if (!SecurityUtils.isAdmin(SecurityUtils.getUserId())){
            Long universityId = SecurityUtils.getLoginUser().getSysUser().getUniversityId();
            homework.setUniversityId(universityId);
        }
        /** 新增作业 **/
        homework.setCreateBy(userUtils.getNickName(SecurityUtils.getUsername()));
        homework.setCreateTime(DateUtils.getTime());
        homeworkService.save(homework);
        /** 保存题目 和 题目选项 **/
        List<HomeworkQuestion> homeworkQuestions = homework.getHomeworkQuestions();
        if(CollectionUtils.isNotEmpty(homeworkQuestions)) {
            homeworkQuestions.forEach(item -> {
                /**新增作业题目**/
                item.setHmId(homework.getId());
                homeworkQuestionService.save(item);

                /**新增题目选项**/
                List<HomeworkQuestionOption> homeworkQuestionOptions = item.getHomeworkQuestionOptions();
                if(CollectionUtils.isNotEmpty(homeworkQuestionOptions)) {
                    homeworkQuestionOptions.forEach(option -> {
                        option.setQuestionId(item.getId());
                    });
                    homeworkQuestionOptionService.saveBatch(homeworkQuestionOptions);
                }
            });
        }

        return AjaxResult.success();
    }

    @Override
    public AjaxResult publishHomeWork(Homework homework) {
        Long hmId = homework.getId();//作业ID

        Long[] classIds = homework.getClassIds();
        if(ObjectUtils.isEmpty(classIds)) {
            return AjaxResult.error("请选择需要发布的班级");
        }

        //根据班级ID查询班级列表
        List<ClassInfo> classInfos = classInfoService.list(new LambdaQueryWrapper<ClassInfo>()
                .in(ClassInfo::getId, classIds));

        //根据班级ID 跨服务查询指定班级下的学生列表
        List<SysUser> userList = userService.getUserByClassIds(classIds, SecurityConstants.INNER);
		if (userList.isEmpty()){
			throw new RuntimeException("当前班级下没有学生");
		}
        if(CollectionUtils.isNotEmpty(userList)) {
            List<HomeworkStudent> homeworkStudentList = new ArrayList<>();
            userList.forEach(user -> {
                Long classId = user.getClassId();
                /*Optional<ClassInfo> classInfo = classInfos.stream().filter(item -> item.getId() == classId).findFirst();
                classInfo.ifPresent(cl -> {

                });*/

                HomeworkStudent homeworkStudent = new HomeworkStudent();
                homeworkStudent.setHmId(hmId);
                homeworkStudent.setUserId(user.getUserId());
                homeworkStudent.setCollegeId(user.getCollegeId());
                homeworkStudent.setMajorId(user.getMajorId());
                homeworkStudent.setClassId(classId);
                homeworkStudent.setCutoffTime(homework.getCutoffTime());
                homeworkStudent.setStartTime(homework.getStartTime());
	            homeworkStudent.setCreateBy(String.valueOf(SecurityUtils.getUserId()));
	            homeworkStudent.setCreateTime(new Date());
	            homeworkStudent.setUniverId(SecurityUtils.getLoginUser().getSysUser().getUniversityId());
                homeworkStudentList.add(homeworkStudent);
            });

            /** 保存“作业发布明细表” **/
            homeworkStudentService.saveBatch(homeworkStudentList);
        }

        /**更新作业发布状态**/
        homeworkService.update(new LambdaUpdateWrapper<Homework>()
                .set(Homework::getPublishStatus, "1")
                .set(Homework::getPublishTime, DateUtils.getTime())
                .set(Homework::getUpdateBy, userUtils.getNickName(SecurityUtils.getUsername()))
                .set(Homework::getUpdateTime, DateUtils.getTime())
                .eq(Homework::getId, hmId));

        return AjaxResult.success();
    }

    public String sendContent(List<Messages> messagesList) throws IOException, JSONException {
        MediaType mediaType = MediaType.parse("application/json");
        System.out.println(messagesList.toString());
        RequestBody body = RequestBody.create(mediaType, "{\"messages\":" + messagesList.toString() + "}");
        System.out.println("{\"messages\":" + messagesList.toString() + "}");
        Request request = new Request.Builder()
                .url(apiURL+"?access_token=" + getAccessToken())
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        String string = response.body().string();
        JSONObject jsonObject = new JSONObject(string);


        return jsonObject.getString("result");
    }

    /**
     * 从用户的AK，SK生成鉴权签名（Access Token）
     *
     * @return 鉴权签名（Access Token）
     * @throws IOException IO异常
     */
    String getAccessToken() throws IOException, JSONException {
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        RequestBody body = RequestBody.create(mediaType, "grant_type=client_credentials&client_id=" + apiKey
                + "&client_secret=" + secretKey);
        Request request = new Request.Builder()
                .url("https://aip.baidubce.com/oauth/2.0/token")
                .method("POST", body)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        System.out.println(response);
        return new JSONObject(response.body().string()).getString("access_token");
    }

    public static List<HomeworkQuestion> parseText(String text, String questionType) {
        List<HomeworkQuestion> homeworkQuestions = new ArrayList<>();

        //Pattern questionTypePattern = Pattern.compile("[一二三四五六七八九十百千万亿]+、");
//        Pattern questionOrderPattern = Pattern.compile("\\d+\\.");
        Pattern questionOrderPattern = Pattern.compile("题目：");

        //按序号分割成一个个题目
        String[] questions = questionOrderPattern.split(text);

        //String questionType = null;//--题型
        if(StringUtils.equals(questionType, QuestionTypes.SINGLE.getCode())) {
            questionType = QuestionTypes.SINGLE.getCode();
            homeworkQuestions = getChooseQuestionInfo(questions, questionType);
        } else if(StringUtils.equals(questionType, QuestionTypes.MULTIPLE.getCode())) {
            questionType = QuestionTypes.MULTIPLE.getCode();
            homeworkQuestions = getChooseQuestionInfo(questions, questionType);
        } else if(StringUtils.equals(questionType, QuestionTypes.BLANK.getCode())) {
            questionType = QuestionTypes.BLANK.getCode();
            homeworkQuestions = getBlankQuestionInfo(questions, questionType);
        } else if(StringUtils.equals(questionType, QuestionTypes.SHORTANSWER.getCode())) {
            questionType = QuestionTypes.SHORTANSWER.getCode();
            homeworkQuestions = getBlankQuestionInfo(questions, questionType);
        } else if(StringUtils.equals(questionType, QuestionTypes.NOUNDEFINITION.getCode())) {
            questionType = QuestionTypes.NOUNDEFINITION.getCode();
            homeworkQuestions = getBlankQuestionInfo(questions, questionType);
        }
        else if(StringUtils.equals(questionType, QuestionTypes.CASEANALYSIS.getCode())) {
            questionType = QuestionTypes.CASEANALYSIS.getCode();
            homeworkQuestions = getCaseAnalysisInfo(questions, questionType);
        }
        else if(StringUtils.equals(questionType, QuestionTypes.ESSAYQUESTION.getCode())) {
            questionType = QuestionTypes.ESSAYQUESTION.getCode();
            homeworkQuestions = getBlankQuestionInfo(questions, questionType);
        } else {
            return null;
        }

        return homeworkQuestions;
    }

    /**
     * 解析选择题包括 单选和多选
     * @param questions
     * @param questionType
     * @return
     */
    public static List<HomeworkQuestion> getChooseQuestionInfo(String[] questions, String questionType) {
        List<HomeworkQuestion> homeworkQuestions = new ArrayList<>();
        Pattern questionAnswerPattern = Pattern.compile("\\s*([A-Za-z]+)");

        if(questions.length <= 1) {
            return  homeworkQuestions;
        }

        int questionOrder = 0;

        for(String question : questions) {
            //过滤掉文本中的第一行
            if(questionOrder == 0) {
                questionOrder++;
                continue;
            }

            //一个question为一个题目
            String[] questionLines = question.split("\n");
            int length = questionLines.length;
            //解析题目内容：按换行符分割，取第一行
            String questionContent = questionLines[0];

            //解析题目选项
            List<HomeworkQuestionOption> options = new ArrayList<>();
            for(int i=1;i < length-1; i++) {
                String option = questionLines[i];

                if(StringUtils.isNotBlank(option)) {
                    option = option.replaceAll("\\r\\n|\\r|\\n| |选项：", "");
                    String mark = null;
                    String text = null;
                    if(option.contains(".") && option.indexOf(".") == 1) {
                        int index = option.indexOf(".");
                        mark = option.substring(0, index);
                        text = option.substring(index+1);
                    } else if(option.contains("、") && option.indexOf("、") == 1) {
                        int index = option.indexOf("、");
                        mark = option.substring(0, index);
                        text = option.substring(index+1);
                    } else {
                        mark = "";
                        text = option;
                    }

                    HomeworkQuestionOption homeworkQuestionOption = new HomeworkQuestionOption();
                    homeworkQuestionOption.setOptionMark(mark);
                    homeworkQuestionOption.setOptionText(text);
                    options.add(homeworkQuestionOption);
                }
            }

            //解析答案
            String answerLine = questionLines[length -1];
            Matcher matcher = questionAnswerPattern.matcher(answerLine);
            StringBuffer correctAnswer = new StringBuffer();
            while (matcher.find()) {
                correctAnswer.append(matcher.group(1)).append("、");
            }
            if(StringUtils.isNotEmpty(correctAnswer)) {
                correctAnswer = correctAnswer.deleteCharAt(correctAnswer.length() - 1);
                //System.out.println("correctAnswer = " + correctAnswer);
            }


            HomeworkQuestion homeworkQuestion = new HomeworkQuestion();
            homeworkQuestion.setQuestionType(questionType);//题目类型
            homeworkQuestion.setQuestionOrder(questionOrder);//题目序号
            homeworkQuestion.setQuestion(questionContent);//题目内容
            homeworkQuestion.setCorrectAnswer(correctAnswer.toString());//正确答案
            homeworkQuestion.setHomeworkQuestionOptions(options);//题目选项

            homeworkQuestions.add(homeworkQuestion);

            questionOrder++;
        }

        return homeworkQuestions;
    }
    /**
     * 解析填空题
     * @param questions
     * @param questionType
     * @return
     */
    public static List<HomeworkQuestion> getBlankQuestionInfo(String[] questions, String questionType) {
        List<HomeworkQuestion> homeworkQuestions = new ArrayList<>();
        Pattern questionAnswerPattern = Pattern.compile("\\s*([A-Za-z]+)");

        int questionOrder = 0;
        for(String question : questions) {
            //过滤掉文本中的第一行
            if(questionOrder == 0) {
                questionOrder++;
                continue;
            }

            //一个question为一个题目
            String questionContent = null;
            String correctAnswer = null;
            if(question.contains("答案：")) {
                String[] questionSplit = question.split("答案：");
                //解析题目内容
                questionContent = questionSplit[0].replaceAll("\\r\\n|\\r|\\n", "");
                if(questionSplit.length > 1) {
                    correctAnswer = questionSplit[1].replaceAll("\\r\\n|\\r|\\n", "");
                }
            }

            HomeworkQuestion homeworkQuestion = new HomeworkQuestion();
            homeworkQuestion.setQuestionType(questionType);
            homeworkQuestion.setQuestionOrder(questionOrder);
            homeworkQuestion.setQuestion(questionContent);
            homeworkQuestion.setCorrectAnswer(correctAnswer);

            homeworkQuestions.add(homeworkQuestion);

            questionOrder++;
        }

        return homeworkQuestions;
    }

    /**
     * 解析案例分析题
     * @param questions
     * @param questionType
     * @return
     */
    public static List<HomeworkQuestion> getCaseAnalysisInfo(String[] questions, String questionType) {
        List<HomeworkQuestion> homeworkQuestions = new ArrayList<>();
        Pattern questionAnswerPattern = Pattern.compile("\\s*([A-Za-z]+)");
        int questionOrder = 0;
        for(String question : questions) {
            //过滤掉文本中的第一行
            if(questionOrder == 0) {
                questionOrder++;
                continue;
            }

            //一个question为一个题目
            String questionContent = null;
            String correctAnswer = null;
            if(question.contains("答案：")) {
                String[] questionSplit = question.split("答案：");
                //解析题目内容
                questionContent = questionSplit[0].replaceAll("\\r\\n|\\r|\\n", "");
                if(questionSplit.length > 1) {
                    correctAnswer = questionSplit[1].replaceAll("\\r\\n|\\r|\\n", "");
                }
            }

            HomeworkQuestion homeworkQuestion = new HomeworkQuestion();
            homeworkQuestion.setQuestionType(questionType);
            homeworkQuestion.setQuestionOrder(questionOrder);
            homeworkQuestion.setQuestion(questionContent);
            homeworkQuestion.setCorrectAnswer(correctAnswer);

            homeworkQuestions.add(homeworkQuestion);

            questionOrder++;
        }

        return homeworkQuestions;
    }

    /***
     * 知识库对话
     * @param messagesList
     * @return
     * @throws IOException
     * @throws AppBuilderServerException
     */
    public String knowledgeBase(List<Messages> messagesList) throws IOException, AppBuilderServerException {
        String appid = configService.getConfigKey2("appidHomework", SecurityConstants.INNER).get("msg").toString();
        String secretkey = configService.getConfigKey2("secretkey", SecurityConstants.INNER).get("msg").toString();
        BaiduDto baiduDto = new BaiduDto();
        baiduDto.setQuery(messagesList.toString());
        baiduDto.setAppid(appid);
        baiduDto.setSecretkey(secretkey);
        return baiduApiService.knowledgeBase(baiduDto,SecurityConstants.INNER);
//        Map<String, Object> result = getDataset(messagesList.toString(), appid, secretkey);
//        String answer = result.get("answer").toString();
//        ReferenceDetail ragDetail = (ReferenceDetail) result.get("ragDetail");
//        System.out.print("输出：");
//        System.out.println(answer);
//
//        String regex = "\\^\\[.*?\\]\\^";
//        Pattern pattern = Pattern.compile(regex);
//        Matcher matcher = pattern.matcher(answer);
//        return matcher.replaceAll("");

    }

//    public Map<String, Object> getDataset(String query, String appid, String secretkey) throws IOException, AppBuilderServerException {
//        Map<String, Object> map = new HashMap<String, Object>();
//        System.setProperty("APPBUILDER_TOKEN", secretkey);
//        AppBuilderClient builder = new AppBuilderClient(appid);
//        String conversationId = builder.createConversation();
//        // 填写上传文件路径
//        //String fileId = builder.uploadLocalFile(conversationId, "");
//        // 输入query
//        AppBuilderClientIterator itor = builder.run(query, conversationId, null, false);
//        AppBuilderClientResult response = new AppBuilderClientResult();
//        StringBuilder answer = new StringBuilder();
//        while (itor.hasNext()) {
//            response = itor.next();
//            answer.append(response.getAnswer());
//            for (Event event : response.getEvents()) {
//                switch (event.getContentType()) {
//                    case "rag":
//                        List<Object> references = (List<Object>) event.getDetail().get("references");
//                        for (Object reference : references) {
//                            ReferenceDetail ragDetail = com.baidubce.appbuilder.base.utils.json.JsonUtils.deserialize(com.baidubce.appbuilder.base.utils.json.JsonUtils.serialize(reference), ReferenceDetail.class);
//                            map.put("ragDetail", ragDetail);
//                        }
//                        break;
//                    default:
//                }
//            }
//        }
//        map.put("answer", answer);
//        return map;
//    }

        public static void main(String[] args) {
            String text = "作业内容：Q：以下哪些专业属于计算机科学与技术学科的下属专业？\n" +
                    "A.软件工程专业\n" +
                    "B.电子信息工程专业\n" +
                    "C.物理学专业\n" +
                    "D.工商管理专业\n" +
                    "答案：A、B\n" +
                    "\n" +
                    "Q：关于《null》教材，下列哪些叙述是正确的？\n" +
                    "A.该书内容涵盖了历史、文学、科学等多个领域。\n" +
                    "B.该书是专门面向大学生编写的专业教材。\n" +
                    "C.该书的作者是一位知名学者。\n" +
                    "D.该书只包含理论内容，没有实际应用案例。\n" +
                    "答案：A、B、C\n" +
                    "\n" +
                    "Q：在《null》教材中，关于市场营销组合，下列哪些元素属于其主要组成部分？\n" +
                    "A.产品策略\n" +
                    "B.生产策略\n" +
                    "C.分销策略\n" +
                    "D.财务策略\n" +
                    "答案：A、C";
            String regex = "Q：";
            Pattern pattern = Pattern.compile(regex);
            String[] result = pattern.split(text);
            for (String str : result) {
                System.out.println(str);
            }
        }

}
