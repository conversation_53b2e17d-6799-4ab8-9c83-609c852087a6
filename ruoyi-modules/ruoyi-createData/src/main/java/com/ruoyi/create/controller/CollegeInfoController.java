package com.ruoyi.create.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.create.domain.CollegeInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.service.ICollegeInfoService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 学院信息Controller
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
@RestController
@RequestMapping("/collegeInfo")
public class CollegeInfoController extends BaseController
{
    @Autowired
    private ICollegeInfoService collegeInfoService;

    /**
     * 查询学院信息列表
     */
    @RequiresPermissions("system:collegeInfo:list")
    @GetMapping("/list")
    public TableDataInfo list(CollegeInfo collegeInfo)
    {
        startPage();
        List<CollegeInfo> list = collegeInfoService.selectCollegeInfoList(collegeInfo);
        return getDataTable(list);
    }

    /**
     * 导出学院信息列表
     */
    @RequiresPermissions("system:collegeInfo:export")
    @Log(title = "学院信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CollegeInfo collegeInfo)
    {
        List<CollegeInfo> list = collegeInfoService.selectCollegeInfoList(collegeInfo);
        ExcelUtil<CollegeInfo> util = new ExcelUtil<CollegeInfo>(CollegeInfo.class);
        util.exportExcel(response, list, "学院信息数据");
    }

    /**
     * 获取学院信息详细信息
     */

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(collegeInfoService.selectCollegeInfoById(id));
    }

    /**
     * 新增学院信息
     */
    @RequiresPermissions("system:collegeInfo:add")
    @Log(title = "学院信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CollegeInfo collegeInfo)
    {
        return toAjax(collegeInfoService.insertCollegeInfo(collegeInfo));
    }

    /**
     * 修改学院信息
     */
    @RequiresPermissions("system:collegeInfo:edit")
    @Log(title = "学院信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CollegeInfo collegeInfo)
    {
        return toAjax(collegeInfoService.updateCollegeInfo(collegeInfo));
    }

    /**
     * 删除学院信息
     */
    @RequiresPermissions("system:collegeInfo:remove")
    @Log(title = "学院信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(collegeInfoService.deleteCollegeInfoByIds(ids));
    }

    /**
     * 获取学院信息详细信息
     */
    @RequiresPermissions("system:collegeInfo:all")
    @GetMapping("/all")
    public AjaxResult getAll()
    {
        return success(collegeInfoService.getAll());
    }
}
