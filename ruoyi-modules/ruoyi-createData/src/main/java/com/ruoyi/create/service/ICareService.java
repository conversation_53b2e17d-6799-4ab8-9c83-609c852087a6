package com.ruoyi.create.service;

import java.util.List;
import com.ruoyi.create.domain.Care;

/**
 * 服务Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
public interface ICareService 
{
    /**
     * 查询服务
     * 
     * @param id 服务主键
     * @return 服务
     */
    public Care selectCareById(Long id);

    /**
     * 查询服务列表
     * 
     * @param care 服务
     * @return 服务集合
     */
    public List<Care> selectCareList(Care care);

    /**
     * 新增服务
     * 
     * @param care 服务
     * @return 结果
     */
    public int insertCare(Care care);

    /**
     * 修改服务
     * 
     * @param care 服务
     * @return 结果
     */
    public int updateCare(Care care);

    /**
     * 批量删除服务
     * 
     * @param ids 需要删除的服务主键集合
     * @return 结果
     */
    public int deleteCareByIds(Long[] ids);

    /**
     * 删除服务信息
     * 
     * @param id 服务主键
     * @return 结果
     */
    public int deleteCareById(Long id);
}
