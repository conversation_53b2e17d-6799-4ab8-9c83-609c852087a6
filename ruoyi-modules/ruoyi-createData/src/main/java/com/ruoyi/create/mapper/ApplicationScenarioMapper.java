package com.ruoyi.create.mapper;

import java.util.List;
import com.ruoyi.create.domain.ApplicationScenario;
import com.ruoyi.create.domain.execl.Major;
import com.ruoyi.create.dto.UserDto;
import org.apache.ibatis.annotations.Param;


/**
 * 应用场景Mapper接口
 *
 * <AUTHOR>
 * @date 2024-10-10
 */
public interface ApplicationScenarioMapper
{
    /**
     * 查询应用场景
     *
     * @param id 应用场景主键
     * @return 应用场景
     */
    public ApplicationScenario selectApplicationScenarioById(Long id);

    /**
     * 查询应用场景列表
     *
     * @param applicationScenario 应用场景
     * @return 应用场景集合
     */
    public List<ApplicationScenario> selectApplicationScenarioList(ApplicationScenario applicationScenario);

    /**
     * 新增应用场景
     *
     * @param applicationScenario 应用场景
     * @return 结果
     */
    public int insertApplicationScenario(ApplicationScenario applicationScenario);

    /**
     * 修改应用场景
     *
     * @param applicationScenario 应用场景
     * @return 结果
     */
    public int updateApplicationScenario(ApplicationScenario applicationScenario);

    /**
     * 删除应用场景
     *
     * @param id 应用场景主键
     * @return 结果
     */
    public int deleteApplicationScenarioById(Long id);

    /**
     * 批量删除应用场景
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteApplicationScenarioByIds(Long[] ids);

    public List<UserDto> selectUserByUserName(String username);

    public List<String> selectUserRoleKeyByUserName(String username);

    List<String> selectUserNameByJobId(@Param("jobId")String jobId, @Param("universityId")Long universityId);


    List<Major> selectMajorListAll();

    List<ApplicationScenario> selectApplicationScenarioListByUserNames(List<String> list);


    int selectApplicationScenarioByNameAndMajor(@Param("applicationName")String applicationName, @Param("major")String major);
}
