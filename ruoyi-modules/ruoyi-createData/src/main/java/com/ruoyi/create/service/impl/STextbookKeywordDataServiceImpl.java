package com.ruoyi.create.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.STextbookKeywordDataMapper;
import com.ruoyi.create.domain.TextbookKeywordData;
import com.ruoyi.create.service.ISTextbookKeywordDataService;

/**
 * 知识图谱-教材关键词数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-09
 */
@Service
public class STextbookKeywordDataServiceImpl implements ISTextbookKeywordDataService 
{
    @Autowired
    private STextbookKeywordDataMapper sTextbookKeywordDataMapper;

    /**
     * 查询知识图谱-教材关键词数据
     * 
     * @param id 知识图谱-教材关键词数据主键
     * @return 知识图谱-教材关键词数据
     */
    @Override
    public TextbookKeywordData selectSTextbookKeywordDataById(Long id)
    {
        return sTextbookKeywordDataMapper.selectSTextbookKeywordDataById(id);
    }

    /**
     * 查询知识图谱-教材关键词数据列表
     * 
     * @param sTextbookKeywordData 知识图谱-教材关键词数据
     * @return 知识图谱-教材关键词数据
     */
    @Override
    public List<TextbookKeywordData> selectSTextbookKeywordDataList(TextbookKeywordData sTextbookKeywordData)
    {
        return sTextbookKeywordDataMapper.selectSTextbookKeywordDataList(sTextbookKeywordData);
    }

    /**
     * 新增知识图谱-教材关键词数据
     * 
     * @param sTextbookKeywordData 知识图谱-教材关键词数据
     * @return 结果
     */
    @Override
    public int insertSTextbookKeywordData(TextbookKeywordData sTextbookKeywordData)
    {
        sTextbookKeywordData.setCreateTime(DateUtils.getNowDate());
        return sTextbookKeywordDataMapper.insertSTextbookKeywordData(sTextbookKeywordData);
    }

    /**
     * 修改知识图谱-教材关键词数据
     * 
     * @param sTextbookKeywordData 知识图谱-教材关键词数据
     * @return 结果
     */
    @Override
    public int updateSTextbookKeywordData(TextbookKeywordData sTextbookKeywordData)
    {
        sTextbookKeywordData.setUpdateTime(DateUtils.getNowDate());
        return sTextbookKeywordDataMapper.updateSTextbookKeywordData(sTextbookKeywordData);
    }

    /**
     * 批量删除知识图谱-教材关键词数据
     * 
     * @param ids 需要删除的知识图谱-教材关键词数据主键
     * @return 结果
     */
    @Override
    public int deleteSTextbookKeywordDataByIds(Long[] ids)
    {
        return sTextbookKeywordDataMapper.deleteSTextbookKeywordDataByIds(ids);
    }

    /**
     * 删除知识图谱-教材关键词数据信息
     * 
     * @param id 知识图谱-教材关键词数据主键
     * @return 结果
     */
    @Override
    public int deleteSTextbookKeywordDataById(Long id)
    {
        return sTextbookKeywordDataMapper.deleteSTextbookKeywordDataById(id);
    }
}
