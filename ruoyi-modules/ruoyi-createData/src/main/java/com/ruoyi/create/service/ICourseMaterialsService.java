package com.ruoyi.create.service;

import java.util.List;
import com.ruoyi.create.domain.CourseMaterials;

/**
 * 课程教案资料Service接口
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
public interface ICourseMaterialsService
{
    /**
     * 查询课程教案资料
     *
     * @param id 课程教案资料主键
     * @return 课程教案资料
     */
    public CourseMaterials selectCourseMaterialsById(Long id);

    /**
     * 查询课程教案资料列表
     *
     * @param courseMaterials 课程教案资料
     * @return 课程教案资料集合
     */
    public List<CourseMaterials> selectCourseMaterialsList(CourseMaterials courseMaterials);
    List<CourseMaterials> selectCourseMaterialsList2(CourseMaterials courseMaterials);

    /**
     * 新增课程教案资料
     *
     * @param courseMaterials 课程教案资料
     * @return 结果
     */
    public int insertCourseMaterials(CourseMaterials courseMaterials);

    /**
     * 修改课程教案资料
     *
     * @param courseMaterials 课程教案资料
     * @return 结果
     */
    public int updateCourseMaterials(CourseMaterials courseMaterials);

    /**
     * 批量删除课程教案资料
     *
     * @param ids 需要删除的课程教案资料主键集合
     * @return 结果
     */
    public int deleteCourseMaterialsByIds(Long[] ids);

    /**
     * 删除课程教案资料信息
     *
     * @param id 课程教案资料主键
     * @return 结果
     */
    public int deleteCourseMaterialsById(Long id);


}
