package com.ruoyi.create.controller;

import com.ruoyi.baidu.api.BaiduApiService;
import com.ruoyi.baidu.api.dto.BaiduDto;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.domain.ApplicationScenario;
import com.ruoyi.create.domain.ApplicationScenarioKnowledgeBase;
import com.ruoyi.create.domain.NacosClient;
import com.ruoyi.create.domain.execl.Major;
import com.ruoyi.create.dto.UserDto;
import com.ruoyi.create.mapper.ApplicationScenarioKnowledgeBaseMapper;
import com.ruoyi.create.service.IApplicationScenarioService;
import com.ruoyi.create.utils.Snowflake;
import com.ruoyi.system.api.RemoteFileService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * 应用场景Controller
 *
 * <AUTHOR>
 * @date 2024-10-10
 */
@RestController
@RequestMapping("/scenario")
public class ApplicationScenarioController extends BaseController
{
    @Autowired
    private IApplicationScenarioService applicationScenarioService;
    @Resource
    private RemoteFileService remoteFileService;

    @Autowired
    private NacosClient nacosClient;
    @Resource
    private BaiduApiService baiduApiService;

    @Autowired
    private ApplicationScenarioKnowledgeBaseMapper applicationScenarioKnowledgeBaseMapper;

    /**
     * 查询专业列表
     */
    @GetMapping("/major")
    public TableDataInfo majorList(ApplicationScenario applicationScenario)
    {
       List<Major> majorNames = applicationScenarioService.selectMajorList();

//        // 转换为前端需要的格式
//        List<Map<String, String>> formattedList = new ArrayList<>();
//        for (String majorName : majorNames) {
//            Map<String, String> majorMap = new HashMap<>();
//            majorMap.put("value", majorName);  // 使用 major_name 填充 value 字段
//            formattedList.add(majorMap);
//        }
       return getDataTable(majorNames);

    }

    /**
     * 查询应用场景列表
     */
    //@RequiresPermissions("scenario:scenario:list")
    @GetMapping("/list")
    public TableDataInfo list(  @RequestParam(required = false) String major)
    {

        //获取分页信息
        PageDomain pageDomain = new PageDomain();


        ApplicationScenario applicationScenario = new ApplicationScenario();
        applicationScenario.setMajor(major);



        String username = SecurityUtils.getUsername();
        List<String> roleKey = applicationScenarioService.selectUserRoleKeyByUserName(username);

        applicationScenario.setCreateBy(username);
        UserDto userDto = applicationScenarioService.selectUserByUserName(username);
        if(roleKey.contains("admin")){
            System.out.println("白名单");
        }else{
            if (userDto.getUniversityId() == null || userDto.getCollegeId() == null || userDto.getMajorId() == null){
                System.out.println("登陆人学校学院专业部分为空");
            }
            applicationScenario.setSchool(userDto.getUniversityId());
            //applicationScenario.setCollege(userDto.getCollegeId());
        }

        startPage();
        List<ApplicationScenario> list = applicationScenarioService.selectApplicationScenarioList(applicationScenario);
        return getDataTable(list);
    }



    /**
     * 获取应用场景详细信息
     */
   // @RequiresPermissions("scenario:scenario:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(applicationScenarioService.selectApplicationScenarioById(id));
    }

    /**
     * 获取应用场景图片流
     */
    @GetMapping(value = "/image/{id}")
    public void getImageInfo(@PathVariable("id") Long id,HttpServletResponse response)
    {
        applicationScenarioService.selectApplicationScenarioImageById(id,response);
    }
    @GetMapping(value = "/imagePath/{id}")
    public R<?> getImagePath(@PathVariable("id") Long id)
    {
        return R.ok(applicationScenarioService.selectApplicationScenarioImagePathById(id));
    }

    /**
     * 新增应用场景
     */
    //@RequiresPermissions("scenario:scenario:add")
    @Log(title = "应用场景", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ApplicationScenario applicationScenario)
    {

        //绑定上雪花id
        Snowflake snowflake = new Snowflake(1, 1);
        long id = snowflake.generateId();
        if(ObjectUtils.isNotEmpty(applicationScenario.getFileId())){
            Long[] fileIds = new Long[]{ (applicationScenario.getFileId()) };
            remoteFileService.relationFile(fileIds,String.valueOf(id));
        }

        //如果是不是管理员只能查询对应学校的
        if (!SecurityUtils.isAdmin(SecurityUtils.getUserId())){
            Long universityId = SecurityUtils.getLoginUser().getSysUser().getUniversityId();
            applicationScenario.setUniversityId(universityId);
        }

        String username = SecurityUtils.getUsername();
        List<String> roleKey = applicationScenarioService.selectUserRoleKeyByUserName(username);

        applicationScenario.setCreateBy(username);
        UserDto userDto = applicationScenarioService.selectUserByUserName(username);
        if(roleKey.contains("admin")){
            System.out.println("白名单");
        }else{
            if (userDto.getUniversityId() == null || userDto.getCollegeId() == null || userDto.getMajorId() == null){
                System.out.println("登陆人学校学院专业部分为空");
            }
            applicationScenario.setSchool(userDto.getUniversityId());
            //applicationScenario.setCollege(userDto.getCollegeId());
            //applicationScenario.setMajor(userDto.getMajorId());
        }

        applicationScenario.setCreateTime(new Date());


        int row =applicationScenarioService.insertApplicationScenario(applicationScenario);
        //通过文件唯一id查询出插入之后的应用场景id存入知识库
        int applicationScenarioId = applicationScenarioService.selectApplicationScenarioByNameAndMajor(applicationScenario.getApplicationName(),applicationScenario.getMajor());
        //给应用场景创建相应的知识库
        String description ="null";
        String name = applicationScenario.getApplicationName();
        String secretkey=nacosClient.getSecretkey();
        BaiduDto baiduDto = new BaiduDto();
        baiduDto.setSecretkey(secretkey);
        baiduDto.setName(name);
        baiduDto.setDescription(description);
        baiduDto.setPathPrefix("场景");
        String kbId = baiduApiService.creatdataSetNew(baiduDto, SecurityConstants.INNER);
        ApplicationScenarioKnowledgeBase applicationScenarioKnowledgeBase = new ApplicationScenarioKnowledgeBase();
        applicationScenarioKnowledgeBase.setKbId(kbId);
        applicationScenarioKnowledgeBase.setApplicationScenarioId(String.valueOf(applicationScenarioId));
        applicationScenarioKnowledgeBase.setKbName(applicationScenario.getApplicationName());

        applicationScenarioKnowledgeBaseMapper.insert(applicationScenarioKnowledgeBase);
        return toAjax(row);
    }

    /**
     * 修改应用场景
     */
    //@RequiresPermissions("scenario:scenario:edit")
    @Log(title = "应用场景", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ApplicationScenario applicationScenario)
    {
        applicationScenario.setUpdateBy(SecurityUtils.getUsername());
        applicationScenario.setUpdateTime(new Date());
        return toAjax(applicationScenarioService.updateApplicationScenario(applicationScenario));
    }

    /**
     * 删除应用场景
     */
   // @RequiresPermissions("scenario:scenario:remove")
    @Log(title = "应用场景", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(applicationScenarioService.deleteApplicationScenarioByIds(ids));
    }
}
