package com.ruoyi.create.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.create.domain.StudentCourse;
import com.ruoyi.create.service.IStudentCoursesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 学生课程信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/studentcourses")
public class StudentCoursesController  extends BaseController {

    @Autowired
    private IStudentCoursesService studentCoursesService;

    /**
     * 获取学生课程信息列表
     */
    @GetMapping("/list")
    public AjaxResult list(StudentCourse studentCourse)
    {
        List<StudentCourse> studentCourses = studentCoursesService.selectStudentCoursesList(studentCourse);
        return success(studentCourses);
    }

    /**
     * 新增课程管理
     */
    @Log(title = "课程管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StudentCourse studentCourse)
    {
        return toAjax(studentCoursesService.insertStudentCourses(studentCourse));
    }

}
