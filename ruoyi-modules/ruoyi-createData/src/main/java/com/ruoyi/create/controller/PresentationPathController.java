package com.ruoyi.create.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.PresentationPath;
import com.ruoyi.create.service.IPresentationPathService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * ppt拆分Controller
 * 
 * <AUTHOR>
 * @date 2024-08-14
 */
@RestController
@RequestMapping("/path")
public class PresentationPathController extends BaseController
{
    @Autowired
    private IPresentationPathService presentationPathService;

    /**
     * 查询ppt拆分列表
     */
    @RequiresPermissions("create:path:list")
    @GetMapping("/list")
    public TableDataInfo list(PresentationPath presentationPath)
    {
        startPage();
        List<PresentationPath> list = presentationPathService.selectPresentationPathList(presentationPath);
        return getDataTable(list);
    }

    /**
     * 导出ppt拆分列表
     */
    @RequiresPermissions("create:path:export")
    @Log(title = "ppt拆分", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PresentationPath presentationPath)
    {
        List<PresentationPath> list = presentationPathService.selectPresentationPathList(presentationPath);
        ExcelUtil<PresentationPath> util = new ExcelUtil<PresentationPath>(PresentationPath.class);
        util.exportExcel(response, list, "ppt拆分数据");
    }

    /**
     * 获取ppt拆分详细信息
     */
    @RequiresPermissions("create:path:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(presentationPathService.selectPresentationPathById(id));
    }

    /**
     * 新增ppt拆分
     */
    @RequiresPermissions("create:path:add")
    @Log(title = "ppt拆分", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PresentationPath presentationPath)
    {
        return toAjax(presentationPathService.insertPresentationPath(presentationPath));
    }

    /**
     * 修改ppt拆分
     */
    @RequiresPermissions("create:path:edit")
    @Log(title = "ppt拆分", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PresentationPath presentationPath)
    {
        return toAjax(presentationPathService.updatePresentationPath(presentationPath));
    }

    /**
     * 删除ppt拆分
     */
    @RequiresPermissions("create:path:remove")
    @Log(title = "ppt拆分", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(presentationPathService.deletePresentationPathByIds(ids));
    }
}
