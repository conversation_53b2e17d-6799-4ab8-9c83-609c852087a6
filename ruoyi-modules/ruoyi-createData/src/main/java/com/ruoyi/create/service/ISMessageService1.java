package com.ruoyi.create.service;

import com.ruoyi.create.domain.SMessage;
import com.ruoyi.create.domain.SMessage1;

import java.util.List;

/**
 * 留言信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-05
 */
public interface ISMessageService1
{
    /**
     * 查询留言信息
     * 
     * @param msgId 留言信息主键
     * @return 留言信息
     */
    public SMessage1 selectSMessageByMsgId(Long msgId);

    /**
     * 查询留言信息列表
     * 
     * @param sMessage 留言信息
     * @return 留言信息集合
     */
    public List<SMessage1> selectSMessageList(SMessage1 sMessage);

    /**
     * 新增留言信息
     * 
     * @param sMessage 留言信息
     * @return 结果
     */
    public int insertSMessage(SMessage1 sMessage);

    /**
     * 修改留言信息
     * 
     * @param sMessage 留言信息
     * @return 结果
     */
    public int updateSMessage(SMessage1 sMessage);

    /**
     * 批量删除留言信息
     * 
     * @param msgIds 需要删除的留言信息主键集合
     * @return 结果
     */
    public int deleteSMessageByMsgIds(Long[] msgIds);

    /**
     * 删除留言信息信息
     * 
     * @param msgId 留言信息主键
     * @return 结果
     */
    public int deleteSMessageByMsgId(Long msgId);
}
