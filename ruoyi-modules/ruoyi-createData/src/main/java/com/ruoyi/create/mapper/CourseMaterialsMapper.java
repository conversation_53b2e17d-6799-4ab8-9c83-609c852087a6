package com.ruoyi.create.mapper;

import java.util.Collection;
import java.util.List;

import com.ruoyi.create.domain.CourseManagement;
import com.ruoyi.create.domain.CourseMaterials;

/**
 * 课程教案资料Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
public interface CourseMaterialsMapper
{
    /**
     * 查询课程教案资料
     *
     * @param id 课程教案资料主键
     * @return 课程教案资料
     */
    public CourseMaterials selectCourseMaterialsById(Long id);

    /**
     * 查询课程教案资料列表
     *
     * @param courseMaterials 课程教案资料
     * @return 课程教案资料集合
     */
    public List<CourseMaterials> selectCourseMaterialsList(CourseMaterials courseMaterials);

    /**
     * 新增课程教案资料
     *
     * @param courseMaterials 课程教案资料
     * @return 结果
     */
    public int insertCourseMaterials(CourseMaterials courseMaterials);

    /**
     * 修改课程教案资料
     *
     * @param courseMaterials 课程教案资料
     * @return 结果
     */
    public int updateCourseMaterials(CourseMaterials courseMaterials);

    /**
     * 删除课程教案资料
     *
     * @param id 课程教案资料主键
     * @return 结果
     */
    public int deleteCourseMaterialsById(Long id);

    /**
     * 批量删除课程教案资料
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCourseMaterialsByIds(Long[] ids);

    /**
     * 查询课程教案资料列表
     *
     * @param courseMaterials 课程教案资料
     * @return 课程教案资料集合
     */
    List<CourseMaterials> selectCourseMaterialsListAndFileName(CourseMaterials courseMaterials);
    /** 是admin不做限制 */
    List<CourseMaterials> selectCourseMaterialsListAndFileNameAll(CourseMaterials courseMaterials);
    /** 是教师*/
    List<CourseMaterials> selectCourseMaterialsListForTeacher(CourseMaterials courseMaterials);
    /** 是学生*/
    List<CourseMaterials> selectCourseMaterialsListForStudent(CourseMaterials courseMaterials);
    List<CourseMaterials> selectCourseMaterialsLis2(CourseMaterials courseMaterials);
    /** 根据课程id来删除教案*/
    public int deleteCourseMaterialsByStudentCourseId(Long id);
    /** 根据课程id来删除教案文件*/
    public int  deleteSysFileInfoByCourseId(Long id);

    public int deleteSysFileInfoById(Long id);
    /** 根据课程id来删除通知*/
    public int deleteCourseNotificationsByStudentCourseId(Long id);
    /** 根据课程id来删除讨论的回复*/
    public int deletesCourseTopicsRepliesByCourseId(Long id);
    /** 根据课程id来删除讨论*/
    public int deletesCourseTopicsByStudentCourseId(Long id);

    int deletesSignInSessionsByIdAndName(CourseManagement courseManagement);

    void deletesStudentSignByIdAndName(CourseManagement courseManagement);

    Collection<? extends Long >selectBusiidSysFileInfoByCourseId(Long id);

    List<String> selectFileObjectNameByCourseId(Long id);

    List<String> selectFileObjectNameById(Long id);
}
