package com.ruoyi.create.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.create.domain.AttendanceParam;
import com.ruoyi.create.domain.SCourseClass;
import com.ruoyi.create.domain.StudentSign;
import com.ruoyi.system.api.domain.StudentInfo;

import java.util.List;

/**
 * 课程班级关联Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
public interface SCourseClassMapper extends BaseMapper<SCourseClass>
{
    /**
     * 查询课程班级关联
     *
     * @param id 课程班级关联主键
     * @return 课程班级关联
     */
    public SCourseClass selectSCourseClassById(Long id);

    /**
     * 查询课程班级关联列表
     *
     * @param attendanceParam 课程班级关联
     * @return 课程班级关联集合
     */
    public List<AttendanceParam> selectSCourseClassList(AttendanceParam attendanceParam);

    public int insertStartSignInSession(AttendanceParam attendanceParam);

    public int insertStudentInfo(StudentSign studentSign);

    public String selectSignType(AttendanceParam attendanceParam);
}
