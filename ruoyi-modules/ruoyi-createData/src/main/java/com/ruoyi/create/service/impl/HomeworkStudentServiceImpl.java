package com.ruoyi.create.service.impl;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.service.IClassInfoService;
import com.ruoyi.create.utils.UserUtils;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.HomeworkStudentMapper;
import com.ruoyi.create.domain.HomeworkStudent;
import com.ruoyi.create.service.IHomeworkStudentService;

import javax.annotation.Resource;

/**
 * 作业状态Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-07
 */
@Service
public class HomeworkStudentServiceImpl extends ServiceImpl<HomeworkStudentMapper, HomeworkStudent> implements IHomeworkStudentService
{
    @Resource
    private HomeworkStudentMapper homeworkStudentMapper;

    @Resource
    private UserUtils userUtils;
	
    /**
     * 查询作业状态
     *
     * @param id 作业状态主键
     * @return 作业状态
     */
    @Override
    public HomeworkStudent selectHomeworkStudentById(Long id)
    {
        return homeworkStudentMapper.selectHomeworkStudentById(id);
    }

    /**
     * 查询作业状态列表
     *
     * @param homeworkStudent 作业状态
     * @return 作业状态
     */
    @Override
    public List<HomeworkStudent> selectHomeworkStudentList(HomeworkStudent homeworkStudent)
    {
	    homeworkStudent.setUserId(SecurityUtils.getUserId());
	    List<HomeworkStudent> homeworkStudents = homeworkStudentMapper.selectHomeworkStudentList(homeworkStudent);
	    // 获取当前时间
	    LocalDateTime currentTime = LocalDateTime.now();
	    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");  // 匹配 "2024-12-20 18:18:00" 格式
	    homeworkStudents.forEach(item->{
		    if ("0".equals(item.getCommitStatus())) {
			    // 比较当前时间和截止时间
			    // 如果当前时间大于截止时间，则作业已过期
			    // 否则，作业没有过期
			    item.setCutoffLabel(item.getCutoffTime() != null && currentTime.isAfter(LocalDateTime.parse(item.getCutoffTime(), formatter)));
		    }
	    });
	    return homeworkStudents;
    }

    /**
     * 新增作业状态
     *
     * @param homeworkStudent 作业状态
     * @return 结果
     */
    @Override
    public int insertHomeworkStudent(HomeworkStudent homeworkStudent)
    {
        return homeworkStudentMapper.insertHomeworkStudent(homeworkStudent);
    }

    /**
     * 修改作业状态
     *
     * @param homeworkStudent 作业状态
     * @return 结果
     */
    @Override
    public int updateHomeworkStudent(HomeworkStudent homeworkStudent)
    {
        homeworkStudent.setCorrectStatus("1");
        homeworkStudent.setCorrectTime(DateUtils.getNowDate());
        return homeworkStudentMapper.updateHomeworkStudent(homeworkStudent);
    }

    @Override
    public int updateHomeworkStudentCutOffTime(HomeworkStudent homeworkStudent)
    {
        homeworkStudent.setCorrectStatus("0");
        homeworkStudent.setCommitStatus("0");
	    int i = homeworkStudentMapper.updateHomeworkStudent(homeworkStudent);
	    if (i > 0) {
			// 清除完成时间
		    homeworkStudentMapper.updateHomeworkToClearCommitTime(homeworkStudent);
	    }
	    return i;
    }

    /**
     * 批量删除作业状态
     *
     * @param ids 需要删除的作业状态主键
     * @return 结果
     */
    @Override
    public int deleteHomeworkStudentByIds(Long[] ids)
    {
        return homeworkStudentMapper.deleteHomeworkStudentByIds(ids);
    }

    /**
     * 删除作业状态信息
     *
     * @param id 作业状态主键
     * @return 结果
     */
    @Override
    public int deleteHomeworkStudentById(Long id)
    {
        return homeworkStudentMapper.deleteHomeworkStudentById(id);
    }

    /**
     * 批改作业查询列表
     * @param homeworkStudent
     * @return
     */
    @Override
    public List<HomeworkStudent> selectCorrectHomeworkStudentList(HomeworkStudent homeworkStudent) {
	    List<HomeworkStudent> homeworkStudentList = homeworkStudentMapper.selectCorrectHomeworkStudentList(homeworkStudent);
	    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
	    // 获取当前时间
	    LocalDateTime currentTime = LocalDateTime.now();
		
        for (HomeworkStudent student : homeworkStudentList) {
            student.setNickName(userUtils.getNickName(student.getUserId()));
	        if ("0".equals(student.getCommitStatus())) { // 未提交
		        LocalDateTime cutoffTime = LocalDateTime.parse(student.getCutoffTime(), formatter);
		        // 比较当前时间和截止时间
		        if (currentTime.isAfter(cutoffTime)) {
			        // 如果当前时间大于截止时间，则作业已过期
			        student.setCutoffLabel(true);
			        student.setCommitStatus("3");
		        } else {
			        // 否则，作业没有过期
			        student.setCutoffLabel(false);
		        }
	        }
	        
        }
        return homeworkStudentList;
    }
    @Override
    public AjaxResult selectClaByHId(Long hmId) {
	    List<HomeworkStudent> homeworkStudents = homeworkStudentMapper.selectClaByHId(hmId);
	
//        return AjaxResult.success(classIdArray2);
	    
//	    List<Map<String, String>> list = new ArrayList<>();
//	    for (Long classId : classIds) {
//		    Map<String, String> map = homeworkStudentMapper.selectClassByHId(classId).get(classId);
//		    String s = map.get("univer_name");
//		    String s = map.get("univer_name");
//		    String s = map.get("class_name");
//		   
//	x
//		    list.add(map);
//	    }
	    return AjaxResult.success(homeworkStudents);


    }

}
