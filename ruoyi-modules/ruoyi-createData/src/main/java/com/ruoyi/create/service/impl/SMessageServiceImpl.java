package com.ruoyi.create.service.impl;

import java.util.ArrayList;
import java.util.List;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.domain.SMsgReply;
import com.ruoyi.create.mapper.SMsgReplyMapper;
import com.ruoyi.create.utils.Snowflake;
import com.ruoyi.system.api.RemoteUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.SMessageMapper;
import com.ruoyi.create.domain.SMessage;
import com.ruoyi.create.service.ISMessageService;

/**
 * 留言信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-05
 */
@Service
public class SMessageServiceImpl implements ISMessageService 
{
    @Autowired
    private SMessageMapper sMessageMapper;

    @Autowired
    private SMsgReplyMapper sMsgReplyMapper;

    @Autowired
    private RemoteUserService userService;

    /**
     * 查询留言信息
     * 
     * @param msgId 留言信息主键
     * @return 留言信息
     */
    @Override
    public SMessage selectSMessageByMsgId(Long msgId)
    {
        return sMessageMapper.selectSMessageByMsgId(msgId);
    }

    /**
     * 查询留言信息列表
     * 
     * @param sMessage 留言信息
     * @return 留言信息
     */
    @Override
    public List<SMessage> selectSMessageList(SMessage sMessage)
    {
        // 查询留言
        List<SMessage> sMessageList = sMessageMapper.selectSMessageList(sMessage);
        // 获取权限
        long uid = SecurityUtils.getUserId();
        AjaxResult ajaxResult = userService.getInfo(SecurityUtils.getUserId(), SecurityConstants.INNER);
        List<Integer> userRoleid = (ArrayList) ajaxResult.get("roleIds");

        for (SMessage itemsMessage : sMessageList) {
            List<SMsgReply> sMsgReplyList = sMsgReplyMapper.selectSMsgReplyPortion(itemsMessage.getMsgId());
            boolean isAdmin = userRoleid.contains(1) || userRoleid.contains(105);
            if (isAdmin || itemsMessage.getMsgUserId() == uid) {
                itemsMessage.setDeleteFlag(true);
                sMsgReplyList.forEach(itemSMsgReply -> itemSMsgReply.setDeleteFlag(true));
            } else {
                itemsMessage.setDeleteFlag(false);
                sMsgReplyList.stream()
                        .filter(s -> s.getReplyUserid() == uid)
                        .forEach(s -> s.setDeleteFlag(true));
            }
            itemsMessage.setsMsgReplyList(sMsgReplyList);
        }

        return sMessageList;
    }

    /**
     * 新增留言信息
     * 
     * @param sMessage 留言信息
     * @return 结果
     */
    @Override
    public int insertSMessage(SMessage sMessage)
    {
        Snowflake snowflake = new Snowflake(1, 1);
        long id = snowflake.generateId();
        sMessage.setMsgId(id);
        sMessage.setCreateTime(DateUtils.getNowDate());
        sMessage.setMsgUserId(SecurityUtils.getUserId());
        return sMessageMapper.insertSMessage(sMessage);
    }

    /**
     * 修改留言信息
     * 
     * @param sMessage 留言信息
     * @return 结果
     */
    @Override
    public int updateSMessage(SMessage sMessage)
    {
        return sMessageMapper.updateSMessage(sMessage);
    }

    /**
     * 批量删除留言信息
     * 
     * @param msgIds 需要删除的留言信息主键
     * @return 结果
     */
    @Override
    public int deleteSMessageByMsgIds(Long[] msgIds)
    {
        //删除留言的回复
        sMsgReplyMapper.deleteSMsgReplyByMsgIds(msgIds);
        //删除留言
        return sMessageMapper.deleteSMessageByMsgIds(msgIds);
    }

    /**
     * 删除留言信息信息
     * 
     * @param msgId 留言信息主键
     * @return 结果
     */
    @Override
    public int deleteSMessageByMsgId(Long msgId)
    {
        return sMessageMapper.deleteSMessageByMsgId(msgId);
    }
}
