package com.ruoyi.create.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.domain.*;
import com.ruoyi.create.service.IStudentSignService;
import com.ruoyi.create.utils.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import java.util.*;


/**
 * 学生签到历史表Controller
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
@RestController
@RequestMapping("/studentsign")
public class StudentSignController extends BaseController
{

    @Resource
    private IStudentSignService studentSignService;

    @Autowired
    private UserUtils userUtils;

    @GetMapping("/list")
    public TableDataInfo list(StudentSign studentSign)
    {
        startPage();
        List<StudentSign> list = studentSignService.selectStudentSignList(studentSign);
        return getDataTable(list);
    }

    @Log(title = "拼音字符与口型动作对应关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StudentSign studentSign)
    {
        List<StudentSign> list = studentSignService.selectStudentSignList(studentSign);
        ExcelUtil<StudentSign> util = new ExcelUtil<StudentSign>(StudentSign.class);
        util.exportExcel(response, list, "拼音字符与口型动作对应关系数据");
    }


    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(studentSignService.selectStudentSignById(id));
    }

    /**
     * 学生签到历史
     */
    @RequestMapping("/attendanceHistory")
    public TableDataInfo attendanceHistory(StudentSign studentSign) {
        // 获取学生id
        String studentId = userUtils.getSysUser(SecurityUtils.getUserId()).getStudentId();
        studentSign.setStudentId(studentId);
        List<StudentSign> list = studentSignService.selectStudentAttendanceList(studentSign);
        return getDataTable(list);
    }

    /**
     * 学生签到（普通）
     */
    @RequestMapping("/attendance")
    public AjaxResult attendance(StudentSign studentSign) {
        if (Objects.isNull(SecurityUtils.getUserId())) {
            return AjaxResult.error("请先登录");
        }
        String studentId = userUtils.getSysUser(SecurityUtils.getUserId()).getStudentId();
        if(Objects.isNull(studentId)){
            return AjaxResult.error("当前用户不是学生");
        }
        return AjaxResult.success(studentSignService.updateStudentAttendance(studentSign));
    }



    /**
     * 学生签到（拍照）
     */
    @PostMapping("/attendance1")
    public AjaxResult attendance1(@RequestBody StudentSign studentSign) {
        if (Objects.isNull(SecurityUtils.getUserId())) {
            return AjaxResult.error("请先登录");
        }
        String studentId = userUtils.getSysUser(SecurityUtils.getUserId()).getStudentId();
        if(Objects.isNull(studentId)){
            return AjaxResult.error("当前用户不是学生");
        }
        return AjaxResult.success(studentSignService.updateStudentAttendance(studentSign));
    }
}


