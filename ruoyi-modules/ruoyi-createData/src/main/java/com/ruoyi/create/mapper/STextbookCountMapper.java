package com.ruoyi.create.mapper;

import java.util.List;

import com.ruoyi.create.domain.LiteratrueKeywordCount;
import com.ruoyi.create.domain.TextbookCount;

/**
 * 文献整理- 教材数量目标Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-31
 */
public interface STextbookCountMapper 
{
    /**
     * 查询文献整理- 教材数量目标
     * 
     * @param id 文献整理- 教材数量目标主键
     * @return 文献整理- 教材数量目标
     */
    public TextbookCount selectSTextbookCountById(Long id);

    /**
     * 查询文献整理- 教材数量目标列表
     * 
     * @param sTextbookCount 文献整理- 教材数量目标
     * @return 文献整理- 教材数量目标集合
     */
    public List<TextbookCount> selectSTextbookCountList(TextbookCount sTextbookCount);

    /**
     * 新增文献整理- 教材数量目标
     * 
     * @param sTextbookCount 文献整理- 教材数量目标
     * @return 结果
     */
    public int insertSTextbookCount(TextbookCount sTextbookCount);

    /**
     * 修改文献整理- 教材数量目标
     * 
     * @param sTextbookCount 文献整理- 教材数量目标
     * @return 结果
     */
    public int updateSTextbookCount(TextbookCount sTextbookCount);

    /**
     * 删除文献整理- 教材数量目标
     * 
     * @param id 文献整理- 教材数量目标主键
     * @return 结果
     */
    public int deleteSTextbookCountById(Long id);

    /**
     * 批量删除文献整理- 教材数量目标
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSTextbookCountByIds(Long[] ids);

}
