package com.ruoyi.create.service.impl;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.domain.SMessage;
import com.ruoyi.create.domain.SMessage1;
import com.ruoyi.create.domain.SMsgReply;
import com.ruoyi.create.domain.SMsgReply1;
import com.ruoyi.create.mapper.SMessageMapper;
import com.ruoyi.create.mapper.SMessageMapper1;
import com.ruoyi.create.mapper.SMsgReplyMapper;
import com.ruoyi.create.mapper.SMsgReplyMapper1;
import com.ruoyi.create.service.ISMessageService;
import com.ruoyi.create.service.ISMessageService1;
import com.ruoyi.create.utils.Snowflake;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.RemoteUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import javax.annotation.Resource;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 留言信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-05
 */
@Service
public class SMessageServiceImpl1 implements ISMessageService1
{
    @Resource
    private SMessageMapper1 sMessageMapper;

    @Resource
    private SMsgReplyMapper1 sMsgReplyMapper;

  @Resource
    private RemoteUserService userService;
  @Resource
    private RemoteFileService remoteFileService;

    /**
     * 查询留言信息
     * 
     * @param msgId 留言信息主键
     * @return 留言信息
     */
    @Override
    public SMessage1 selectSMessageByMsgId(Long msgId)
    {
        return sMessageMapper.selectSMessageByMsgId(msgId);
    }

    /**
     * 查询留言信息列表
     * 
     * @param sMessage 留言信息
     * @return 留言信息
     */
    @Override
    public List<SMessage1> selectSMessageList(SMessage1 sMessage)
    {
        //获取用户身份检查是否是超级管理员
        if (!SecurityUtils.isAdmin(SecurityUtils.getUserId())) {
            sMessage.setMsgUserId(SecurityUtils.getUserId());
        }
        // 查询留言，调用留言映射器从数据库中获取留言列表
        List<SMessage1> sMessageList = sMessageMapper.selectSMessageList(sMessage);

        // 获取当前用户的 UID（用户 ID）
        long uid = SecurityUtils.getUserId();

        // 获取当前用户的权限信息，使用用户服务获取当前用户的角色信息
        AjaxResult ajaxResult = userService.getInfo(SecurityUtils.getUserId(), SecurityConstants.INNER);

        // 从权限信息中获取当前用户的角色 ID 列表
        List<Integer> userRoleid = (ArrayList) ajaxResult.get("roleIds");

        // 遍历查询到的留言列表
        for (SMessage1 itemsMessage : sMessageList) {

            // 获取与留言相关的回复列表
            List<SMsgReply1> sMsgReplyList = sMsgReplyMapper.selectSMsgReplyPortion(itemsMessage.getMsgId());

            // 判断当前用户是否是管理员（角色 ID 为 1 或 105 的用户是管理员）
            boolean isAdmin = userRoleid.contains(1) || userRoleid.contains(105);

            // 判断是否是管理员或者该留言的作者，决定是否可以删除该留言或回复
            if (isAdmin || itemsMessage.getMsgUserId() == uid) {
                // 如果是管理员或该留言的作者，将该留言的删除标志设为 true，表示该留言可以删除
                itemsMessage.setDeleteFlag(true);

                // 对于该留言的所有回复，设置删除标志为 true，表示这些回复可以删除
                sMsgReplyList.forEach(itemSMsgReply -> itemSMsgReply.setDeleteFlag(true));
            } else {
                // 如果不是管理员且不是该留言的作者，将该留言的删除标志设为 false，表示不可删除
                itemsMessage.setDeleteFlag(false);

                // 对于该留言的回复，如果回复的用户 ID 与当前用户相同，设置该回复的删除标志为 true
                sMsgReplyList.stream()
                        .filter(s -> s.getReplyUserid() == uid)
                        .forEach(s -> s.setDeleteFlag(true));
            }

            // 设置留言的回复列表，包含所有的回复信息
            itemsMessage.setsMsgReplyList1(sMsgReplyList);
        }

        // 返回查询到的留言列表
        return sMessageList;
    }


    /**
     * 新增留言信息
     * 
     * @param sMessage 留言信息
     * @return 结果
     */
    @Override
    public int insertSMessage(SMessage1 sMessage)
    {
        Snowflake snowflake = new Snowflake(1, 1);
        long id = snowflake.generateId();
        if(ObjectUtils.isNotEmpty(sMessage.getI())){
            remoteFileService.relationFile(sMessage.getI(),String.valueOf(id));
        }
        sMessage.setMsgId(id);
        sMessage.setCreateTime(DateUtils.getNowDate());
        sMessage.setMsgUserId(SecurityUtils.getUserId());

//        // 存在图片路径 获取图片路径中的数据 设置 将图片数据的值返回
//        if (sMessage.getImg()!=null) {
//            try {
//                //D:/ruoyi/uploadDataPath/temp/jpg/1/20250421/1745225604657025.jpg
//                String imagePath = sMessage.getImg();
//                String[] split = imagePath.split(",");
//                StringBuilder image= new StringBuilder();
//                for (String s : split) {
//                    image.append("data:image/png;base64,").append(encodeFileToBase64(s));
//                }
//                sMessage.setImg(image.toString());
//            } catch (Exception e) {
//                throw new RuntimeException(e);
//            }
//        }
        return sMessageMapper.insertSMessage(sMessage);
    }

    public static String encodeFileToBase64(String filePath) throws IOException {
        byte[] fileBytes = Files.readAllBytes(Paths.get(filePath));
        return Base64.getEncoder().encodeToString(fileBytes);
    }

    /**
     * 修改留言信息
     * 
     * @param sMessage 留言信息
     * @return 结果
     */
    @Override
    public int updateSMessage(SMessage1 sMessage)
    {
        return sMessageMapper.updateSMessage(sMessage);
    }

    /**
     * 批量删除留言信息
     * 
     * @param msgIds 需要删除的留言信息主键
     * @return 结果
     */
    @Override
    public int deleteSMessageByMsgIds(Long[] msgIds)
    {
        //删除留言的回复
        sMsgReplyMapper.deleteSMsgReplyByMsgIds(msgIds);
        //删除留言
        return sMessageMapper.deleteSMessageByMsgIds(msgIds);
    }

    /**
     * 删除留言信息信息
     * 
     * @param msgId 留言信息主键
     * @return 结果
     */
    @Override
    public int deleteSMessageByMsgId(Long msgId)
    {
        return sMessageMapper.deleteSMessageByMsgId(msgId);
    }
}
