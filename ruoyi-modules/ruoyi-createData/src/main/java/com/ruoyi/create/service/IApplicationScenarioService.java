package com.ruoyi.create.service;

import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.create.domain.ApplicationScenario;
import com.ruoyi.create.domain.execl.Major;
import com.ruoyi.create.dto.UserDto;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 应用场景Service接口
 *
 * <AUTHOR>
 * @date 2024-10-10
 */
public interface IApplicationScenarioService
{
    /**
     * 查询应用场景
     *
     * @param id 应用场景主键
     * @return 应用场景
     */
    public ApplicationScenario selectApplicationScenarioById(Long id);

    public void selectApplicationScenarioImageById(Long id, HttpServletResponse response);

    String selectApplicationScenarioImagePathById(Long id);

    /**
     * 查询应用场景列表
     *
     * @param applicationScenario 应用场景
     * @return 应用场景集合
     */
    public List<ApplicationScenario> selectApplicationScenarioList(ApplicationScenario applicationScenario);

    /**
     * 新增应用场景
     *
     * @param applicationScenario 应用场景
     * @return 结果
     */
    public int insertApplicationScenario(ApplicationScenario applicationScenario);

    /**
     * 修改应用场景
     *
     * @param applicationScenario 应用场景
     * @return 结果
     */
    public int updateApplicationScenario(ApplicationScenario applicationScenario);

    /**
     * 批量删除应用场景
     *
     * @param ids 需要删除的应用场景主键集合
     * @return 结果
     */
    public int deleteApplicationScenarioByIds(Long[] ids);

    /**
     * 删除应用场景信息
     *
     * @param id 应用场景主键
     * @return 结果
     */
    public int deleteApplicationScenarioById(Long id);

    public UserDto selectUserByUserName(String username);

    public List<String> selectUserRoleKeyByUserName(String username);


    List<Major> selectMajorList();


    int selectApplicationScenarioByNameAndMajor(String applicationName, String major);
}
