package com.ruoyi.create.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.create.domain.HomeworkQuestionOption;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.service.IHomeworkQuestionOptionService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 作业题目选项Controller
 *
 * <AUTHOR>
 * @date 2024-06-07
 */
@RestController
@RequestMapping("/option")
public class HomeworkQuestionOptionController extends BaseController
{
    @Autowired
    private IHomeworkQuestionOptionService homeworkQuestionOptionService;

    /**
     * 查询作业题目选项列表
     */
    @RequiresPermissions("create:option:list")
    @GetMapping("/list")
    public TableDataInfo list(HomeworkQuestionOption homeworkQuestionOption)
    {
        startPage();
        List<HomeworkQuestionOption> list = homeworkQuestionOptionService.selectHomeworkQuestionOptionList(homeworkQuestionOption);
        return getDataTable(list);
    }

    /**
     * 导出作业题目选项列表
     */
    @RequiresPermissions("create:option:export")
    @Log(title = "作业题目选项", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HomeworkQuestionOption homeworkQuestionOption)
    {
        List<HomeworkQuestionOption> list = homeworkQuestionOptionService.selectHomeworkQuestionOptionList(homeworkQuestionOption);
        ExcelUtil<HomeworkQuestionOption> util = new ExcelUtil<HomeworkQuestionOption>(HomeworkQuestionOption.class);
        util.exportExcel(response, list, "作业题目选项数据");
    }

    /**
     * 获取作业题目选项详细信息
     */
    @RequiresPermissions("create:option:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(homeworkQuestionOptionService.selectHomeworkQuestionOptionById(id));
    }

    /**
     * 新增作业题目选项
     */
    @RequiresPermissions("create:option:add")
    @Log(title = "作业题目选项", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HomeworkQuestionOption homeworkQuestionOption)
    {
        return toAjax(homeworkQuestionOptionService.insertHomeworkQuestionOption(homeworkQuestionOption));
    }

    /**
     * 修改作业题目选项
     */
    @RequiresPermissions("create:option:edit")
    @Log(title = "作业题目选项", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HomeworkQuestionOption homeworkQuestionOption)
    {
        return toAjax(homeworkQuestionOptionService.updateHomeworkQuestionOption(homeworkQuestionOption));
    }

    /**
     * 删除作业题目选项
     */
    @RequiresPermissions("create:option:remove")
    @Log(title = "作业题目选项", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(homeworkQuestionOptionService.deleteHomeworkQuestionOptionByIds(ids));
    }
}
