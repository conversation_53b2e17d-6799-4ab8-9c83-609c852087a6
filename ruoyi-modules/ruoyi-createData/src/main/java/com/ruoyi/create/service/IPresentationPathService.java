package com.ruoyi.create.service;

import java.io.File;
import java.io.IOException;
import java.util.List;
import com.ruoyi.create.domain.PresentationPath;

/**
 * ppt拆分Service接口
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
public interface IPresentationPathService
{




    //ppt 拆分独立ppt
    List<String> processPPT(File pptFile, String outputDir, String slidePrefix) throws IOException;

    List<String> processPPT(String pptFile, String outputDir, String slidePrefix) throws Exception;

    public int getPPTSize(File pptFile);

    int insertPresentationPath(List<String> path);


    /**
     * 查询ppt拆分
     *
     * @param id ppt拆分主键
     * @return ppt拆分
     */
    public PresentationPath selectPresentationPathById(Long id);

    /**
     * 查询ppt拆分列表
     *
     * @param presentationPath ppt拆分
     * @return ppt拆分集合
     */
    public List<PresentationPath> selectPresentationPathList(PresentationPath presentationPath);

    /**
     * 新增ppt拆分
     *
     * @param presentationPath ppt拆分
     * @return 结果
     */
    public int insertPresentationPath(PresentationPath presentationPath);

    /**
     * 修改ppt拆分
     *
     * @param presentationPath ppt拆分
     * @return 结果
     */
    public int updatePresentationPath(PresentationPath presentationPath);

    /**
     * 批量删除ppt拆分
     *
     * @param ids 需要删除的ppt拆分主键集合
     * @return 结果
     */
    public int deletePresentationPathByIds(Long[] ids);

    /**
     * 删除ppt拆分信息
     *
     * @param id ppt拆分主键
     * @return 结果
     */
    public int deletePresentationPathById(Long id);
}
