package com.ruoyi.create.mapper;

import java.util.List;
import com.ruoyi.create.domain.SMessage;

/**
 * 留言信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-05
 */
public interface SMessageMapper 
{
    /**
     * 查询留言信息
     * 
     * @param msgId 留言信息主键
     * @return 留言信息
     */
    public SMessage selectSMessageByMsgId(Long msgId);

    /**
     * 查询留言信息列表
     * 
     * @param sMessage 留言信息
     * @return 留言信息集合
     */
    public List<SMessage> selectSMessageList(SMessage sMessage);

    /**
     * 新增留言信息
     * 
     * @param sMessage 留言信息
     * @return 结果
     */
    public int insertSMessage(SMessage sMessage);

    /**
     * 修改留言信息
     * 
     * @param sMessage 留言信息
     * @return 结果
     */
    public int updateSMessage(SMessage sMessage);

    /**
     * 删除留言信息
     * 
     * @param msgId 留言信息主键
     * @return 结果
     */
    public int deleteSMessageByMsgId(Long msgId);

    /**
     * 批量删除留言信息
     * 
     * @param msgIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSMessageByMsgIds(Long[] msgIds);
}
