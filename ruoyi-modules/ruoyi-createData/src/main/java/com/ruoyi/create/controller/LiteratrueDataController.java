package com.ruoyi.create.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.LiteratrueData;
import com.ruoyi.create.domain.LiteratrueKeywordCount;
import com.ruoyi.create.service.ILiteratrueDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.ListIterator;

/**
 * 文献整理-原始数据Controller
 * 
 * <AUTHOR>
 * @date 2024-07-18
 */
@RestController
@RequestMapping("/literatruedata")
public class LiteratrueDataController extends BaseController
{
    @Autowired
    private ILiteratrueDataService literatrueDataService;

    /**
     * 查询文献整理-原始数据列表
     */
    @RequiresPermissions("create:literatruedata:list")
    @GetMapping("/list")
    public TableDataInfo list(LiteratrueData literatrueData)
    {
        startPage();
        List<LiteratrueData> list = literatrueDataService.selectLiteratrueDataList(literatrueData);
        return getDataTable(list);
    }

    /**
     * 导出文献整理-原始数据列表
     */
    @RequiresPermissions("create:literatruedata:export")
    @Log(title = "文献整理-原始数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LiteratrueData literatrueData)
    {
        List<LiteratrueData> list = literatrueDataService.selectLiteratrueDataList(literatrueData);
        ExcelUtil<LiteratrueData> util = new ExcelUtil<LiteratrueData>(LiteratrueData.class);
        util.exportExcel(response, list, "文献整理-原始数据数据");
    }

    /**
     * 获取文献整理-原始数据详细信息
     */
    @RequiresPermissions("create:literatruedata:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(literatrueDataService.selectLiteratrueDataById(id));
    }

    /**
     * 新增文献整理-原始数据
     */
    @RequiresPermissions("create:literatruedata:add")
    @Log(title = "文献整理-原始数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LiteratrueData literatrueData)
    {
        return literatrueDataService.insertLiteratrueData(literatrueData);
    }

    /**
     * 修改文献整理-原始数据
     */
    @RequiresPermissions("create:literatruedata:edit")
    @Log(title = "文献整理-原始数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LiteratrueData literatrueData)
    {
        return toAjax(literatrueDataService.updateLiteratrueData(literatrueData));
    }

    /**
     * 删除文献整理-原始数据
     */
    @RequiresPermissions("create:literatruedata:remove")
    @Log(title = "文献整理-原始数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(literatrueDataService.deleteLiteratrueDataByIds(ids));
    }
    /**
     * 查询文献整理-关键词点击查询论文列表
     */
    @RequiresPermissions("create:literatruedata:list")
    @GetMapping("/clickselect")
    public TableDataInfo selectClickLiteratrue(LiteratrueData literatrueData)
    {
        startPage();
        List<LiteratrueData> list = literatrueDataService.selectClickLiteratrue(literatrueData);
        return getDataTable(list);
    }


}
