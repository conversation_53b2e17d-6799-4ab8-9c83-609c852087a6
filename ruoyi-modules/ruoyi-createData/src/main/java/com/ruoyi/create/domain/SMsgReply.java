package com.ruoyi.create.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 留言与回复关联对象 s_msg_reply
 * 
 * <AUTHOR>
 * @date 2024-06-05
 */
public class SMsgReply extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 留言id */
    private Long msgId;

    /** 上一条回复留言id */
    @Excel(name = "上一条回复留言id")
    private Long lastReplyId;

    /** 回复id */
    @Excel(name = "回复id")
    private Long replyId;

    /** 回复内容 */
    @Excel(name = "回复内容")
    private String replyMsg;

    /** 上一条回复留言用户id */
    @Excel(name = "上一条回复留言用户id")
    private Long lastReplyUid;

    /** 上一条回复留言用户名称 */
    @Excel(name = "上一条回复留言用户名称")
    private String lastReplyUname;

    /** 回复人id */
    @Excel(name = "回复人id")
    private Long replyUserid;

    /** 回复人昵称 */
    @Excel(name = "回复人昵称")
    private String replyUserName;

    /** 是否可删除 */
    private Boolean deleteFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setMsgId(Long msgId)
    {
        this.msgId = msgId;
    }

    public Long getMsgId() 
    {
        return msgId;
    }
    public void setLastReplyId(Long lastReplyId) 
    {
        this.lastReplyId = lastReplyId;
    }

    public Long getLastReplyId() 
    {
        return lastReplyId;
    }
    public void setReplyId(Long replyId) 
    {
        this.replyId = replyId;
    }

    public Long getReplyId() 
    {
        return replyId;
    }
    public void setReplyMsg(String replyMsg) 
    {
        this.replyMsg = replyMsg;
    }

    public String getReplyMsg() 
    {
        return replyMsg;
    }
    public void setLastReplyUid(Long lastReplyUid) 
    {
        this.lastReplyUid = lastReplyUid;
    }

    public Long getLastReplyUid() 
    {
        return lastReplyUid;
    }
    public void setLastReplyUname(String lastReplyUname) 
    {
        this.lastReplyUname = lastReplyUname;
    }

    public String getLastReplyUname() 
    {
        return lastReplyUname;
    }
    public void setReplyUserid(Long replyUserid) 
    {
        this.replyUserid = replyUserid;
    }

    public Long getReplyUserid() 
    {
        return replyUserid;
    }
    public void setReplyUserName(String replyUserName) 
    {
        this.replyUserName = replyUserName;
    }

    public String getReplyUserName() 
    {
        return replyUserName;
    }

    public Boolean getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Boolean deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("msgId", getMsgId())
            .append("lastReplyId", getLastReplyId())
            .append("replyId", getReplyId())
            .append("replyMsg", getReplyMsg())
            .append("createTime", getCreateTime())
            .append("lastReplyUid", getLastReplyUid())
            .append("lastReplyUname", getLastReplyUname())
            .append("replyUserid", getReplyUserid())
            .append("replyUserName", getReplyUserName())
            .toString();
    }
}
