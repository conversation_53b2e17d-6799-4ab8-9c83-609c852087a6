package com.ruoyi.create.mapper;

import java.util.List;
import com.ruoyi.create.domain.AuthenInfor;
import org.apache.ibatis.annotations.Param;

/**
 * 鉴权信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-14
 */
public interface AuthenInforMapper 
{
    /**
     * 查询鉴权信息
     * 
     * @param id 鉴权信息主键
     * @return 鉴权信息
     */
    public AuthenInfor selectAuthenInforById(Long id);

    /**
     * 查询鉴权信息列表
     * 
     * @param authenInfor 鉴权信息
     * @return 鉴权信息集合
     */
    public List<AuthenInfor> selectAuthenInforList(AuthenInfor authenInfor);

    /**
     * 新增鉴权信息
     * 
     * @param authenInfor 鉴权信息
     * @return 结果
     */
    public int insertAuthenInfor(AuthenInfor authenInfor);

    /**
     * 修改鉴权信息
     * 
     * @param authenInfor 鉴权信息
     * @return 结果
     */
    public int updateAuthenInfor(AuthenInfor authenInfor);

    /**
     * 删除鉴权信息
     * 
     * @param id 鉴权信息主键
     * @return 结果
     */
    public int deleteAuthenInforById(Long id);

    /**
     * 批量删除鉴权信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAuthenInforByIds(Long[] ids);

    int countMenuRouting(String menuRouting);

    int countUpdateMenuRouting(AuthenInfor authenInfor);

    List<AuthenInfor> selectAuthenInforListByIds(Long[] ids);

    List<AuthenInfor> selectAuthenInforAll();
}
