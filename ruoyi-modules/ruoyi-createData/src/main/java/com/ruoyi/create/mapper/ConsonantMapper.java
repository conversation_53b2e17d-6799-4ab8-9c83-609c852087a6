package com.ruoyi.create.mapper;

import java.util.List;
import com.ruoyi.create.domain.Consonant;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 讲演稿声母韵母Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-26
 */
@Mapper
public interface ConsonantMapper
{
    /**
     * 查询讲演稿声母韵母
     *
     * @param id 讲演稿声母韵母主键
     * @return 讲演稿声母韵母
     */
    public Consonant selectConsonantById(Long id);

    /**
     * 查询讲演稿声母韵母列表
     *
     * @param consonant 讲演稿声母韵母
     * @return 讲演稿声母韵母集合
     */
    public List<Consonant> selectConsonantList(Consonant consonant);

    /**
     * 新增讲演稿声母韵母
     *
     * @param consonant 讲演稿声母韵母
     * @return 结果
     */
    public int insertConsonant(Consonant consonant);

    /**
     * 修改讲演稿声母韵母
     *
     * @param consonant 讲演稿声母韵母
     * @return 结果
     */
    public int updateConsonant(Consonant consonant);

    /**
     * 删除讲演稿声母韵母
     *
     * @param id 讲演稿声母韵母主键
     * @return 结果
     */
    public int deleteConsonantById(Long id);

    public int deleteConsonantByPresationId(Long id);
    /**
     * 批量删除讲演稿声母韵母
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteConsonantByIds(Long[] ids);

    /**
     * 批量更新 speechdraftId 字段
     *
     * @param oldSpeechdraftId 原始 speechdraftId
     * @param newSpeechdraftId 要替换成的新 speechdraftId
     * @return 影响的行数
     */
    int updateSpeechdraftId(@Param("oldSpeechdraftId") String oldSpeechdraftId,
                            @Param("newSpeechdraftId") String newSpeechdraftId);
}
