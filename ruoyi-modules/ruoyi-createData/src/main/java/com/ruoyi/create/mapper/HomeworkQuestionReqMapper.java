package com.ruoyi.create.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.create.domain.HomeworkQuestionReq;

import java.util.List;

/**
 * 作业题型要求明细Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
public interface HomeworkQuestionReqMapper extends BaseMapper<HomeworkQuestionReq>
{
    /**
     * 查询作业题型要求明细
     *
     * @param id 作业题型要求明细主键
     * @return 作业题型要求明细
     */
    public HomeworkQuestionReq selectHomeworkQuestionReqById(Long id);

    /**
     * 查询作业题型要求明细列表
     *
     * @param homeworkQuestionReq 作业题型要求明细
     * @return 作业题型要求明细集合
     */
    public List<HomeworkQuestionReq> selectHomeworkQuestionReqList(HomeworkQuestionReq homeworkQuestionReq);

    /**
     * 新增作业题型要求明细
     *
     * @param homeworkQuestionReq 作业题型要求明细
     * @return 结果
     */
    public int insertHomeworkQuestionReq(HomeworkQuestionReq homeworkQuestionReq);

    /**
     * 修改作业题型要求明细
     *
     * @param homeworkQuestionReq 作业题型要求明细
     * @return 结果
     */
    public int updateHomeworkQuestionReq(HomeworkQuestionReq homeworkQuestionReq);

    /**
     * 删除作业题型要求明细
     *
     * @param id 作业题型要求明细主键
     * @return 结果
     */
    public int deleteHomeworkQuestionReqById(Long id);

    /**
     * 批量删除作业题型要求明细
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHomeworkQuestionReqByIds(Long[] ids);
}
