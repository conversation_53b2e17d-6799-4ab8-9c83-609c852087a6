package com.ruoyi.create.service.impl;

import com.ruoyi.create.Vo.CourseInfoVo;
import com.ruoyi.create.Vo.StudentAttendanceVo;
import com.ruoyi.create.domain.StudentAttendance;
import com.ruoyi.create.mapper.CourseInfoMapper;
import com.ruoyi.create.mapper.StudentAttendanceMapper;
import com.ruoyi.create.service.IStudentCourseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class StudentCourseServiceImpl implements IStudentCourseService {

    @Autowired
    private CourseInfoMapper courseInfoMapper;

    @Autowired
    private StudentAttendanceMapper studentAttendanceMapper;

    /**
     *
     * @param studentId
     * @return
     */
    @Override
    public List<CourseInfoVo> getStudentCourseList(String studentId) {
        return courseInfoMapper.getStudentCourseListByStudentId(studentId);
    }

    /**
     * 获取学生签到列表列表
     * @param studentId
     * @return
     */
    @Override
    public List<StudentAttendanceVo> selectStudentAttendanceList(String studentId) {
        if (studentId != null) {
            return studentAttendanceMapper.selectStudentAttendanceListByStudentId(studentId);
        }
        return  new ArrayList<>();
    }

    /**
     * 更新签到状态
     * @param studentId
     * @return
     */
    @Override
    public int updateStudentAttendance(String courseId, String studentId) {
        // 查询签到记录通过课程id和学生id
        StudentAttendance stu = studentAttendanceMapper.selectStudentAttendanceByCourseIdAndStudentId(courseId,studentId).get(0);
        if(!Objects.equals(stu.getStatus(), "1")){
            stu.setStatus("1");
            return studentAttendanceMapper.updateStudentAttendance(stu);
        }
        // studentAttendanceMapper.updateStudentAttendance(stu);
        return 0;
    }
}
