package com.ruoyi.create.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.config.CustomThreadPool;
import com.ruoyi.create.domain.ClassStudentCourseReport;
import com.ruoyi.create.domain.CourseManagement;
import com.ruoyi.create.domain.HomeworkStudent;
import com.ruoyi.create.mapper.ClassStudentCourseReportMapper;
import com.ruoyi.create.mapper.CourseManagementMapper;
import com.ruoyi.create.mapper.HomeworkStudentMapper;
import com.ruoyi.create.service.IClassStudentCourseReportService;
import com.ruoyi.digitalHuman.api.domain.StudentStudyRecord;
import com.ruoyi.digitalHuman.api.remote.RemoteDigitalhumanService;
import com.ruoyi.intelligent.api.remote.RemoteIntelligent;
import com.ruoyi.system.api.ConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 课程班级信息统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-10
 */
@Service
@Slf4j
public class ClassStudentCourseReportServiceImpl implements IClassStudentCourseReportService {
    @Resource
    private ClassStudentCourseReportMapper classStudentCourseReportMapper;

    @Resource
    private RemoteDigitalhumanService remoteDigitalhuman;

    @Resource
    private HomeworkStudentMapper homeworkStudentMapper;

    @Resource
    private RemoteIntelligent remoteIntelligent;

    @Resource
    private CourseManagementMapper courseManagementMapper;

    @Resource
    private ConfigService configService;

    @Resource
    private CustomThreadPool customThreadPool;

    /**
     * 查询课程班级信息统计
     *
     * @param id 课程班级信息统计主键
     * @return 课程班级信息统计
     */
    @Override
    public ClassStudentCourseReport selectClassStudentCourseReportById(Long id) {
        return classStudentCourseReportMapper.selectClassStudentCourseReportById(id);
    }

    /**
     * 查询课程班级信息统计列表
     *
     * @param classStudentCourseReport 课程班级信息统计
     * @return 课程班级信息统计
     */
    @Override
    public List<ClassStudentCourseReport> selectClassStudentCourseReportList(ClassStudentCourseReport classStudentCourseReport) {
        return classStudentCourseReportMapper.selectClassStudentCourseReportList(classStudentCourseReport);
    }

    /**
     * 新增课程班级信息统计
     *
     * @param classStudentCourseReport 课程班级信息统计
     * @return 结果
     */
    @Override
    public int insertClassStudentCourseReport(ClassStudentCourseReport classStudentCourseReport) {
        classStudentCourseReport.setCreateTime(DateUtils.getNowDate());
        return classStudentCourseReportMapper.insertClassStudentCourseReport(classStudentCourseReport);
    }

    /**
     * 修改课程班级信息统计
     *
     * @param classStudentCourseReport 课程班级信息统计
     * @return 结果
     */
    @Override
    public int updateClassStudentCourseReport(ClassStudentCourseReport classStudentCourseReport) {
        classStudentCourseReport.setUpdateTime(DateUtils.getNowDate());
        return classStudentCourseReportMapper.updateClassStudentCourseReport(classStudentCourseReport);
    }

    /**
     * 批量删除课程班级信息统计
     *
     * @param ids 需要删除的课程班级信息统计主键
     * @return 结果
     */
    @Override
    public int deleteClassStudentCourseReportByIds(Long[] ids) {
        return classStudentCourseReportMapper.deleteClassStudentCourseReportByIds(ids);
    }

    /**
     * 删除课程班级信息统计信息
     *
     * @param id 课程班级信息统计主键
     * @return 结果
     */
    @Override
    public int deleteClassStudentCourseReportById(Long id) {
        return classStudentCourseReportMapper.deleteClassStudentCourseReportById(id);
    }

    /**
     * @description: 创建单个学生单门课程分析
     * @author: zhaoTianQi
     * @date: 2024/12/2 20:50
     * @param: courseManagement
     * @return: CourseAnalysisVo
     **/
    @Override
    public ClassStudentCourseReport createSingleCourseAnalysis(ClassStudentCourseReport courseReport) {
        if (StringUtils.isBlank(courseReport.getStudentId())
                || StringUtils.isBlank(courseReport.getCourseName())
                || courseReport.getCourseClassId() == null) {
            log.error("studentId | courseName | courseClassId 为空");
            return null;
        }
        ClassStudentCourseReport build = ClassStudentCourseReport.builder().build();
        build.setType("student");
        build.setCreateTime(DateUtils.getNowDate());
        build.setCreateBy(String.valueOf(SecurityUtils.getUserId()));
        // 设置课程班级id
        build.setCourseClassId(courseReport.getCourseClassId());
        // 设置学号
        build.setStudentId(courseReport.getStudentId());

        // 获取课程信息 id 和 学生id 查询
        CourseManagement courseManagement = new CourseManagement();
        courseManagement.setId(courseReport.getCourseClassId());
        courseManagement.setStudentId(courseReport.getStudentId());
        CourseManagement selectStudentCourseInfo = courseManagementMapper.selectStudentCourseInfo(courseManagement);

        // 设置课程班级名字
        build.setCourseClassName(selectStudentCourseInfo.getStudentClass());

        // 报告日期
        build.setReportDate(DateUtils.getNowDate());

        Map<String, HashMap<String, Object>> useMapMap = courseManagementMapper.getUserByStudentId(courseManagement.getStudentId());
        HashMap<String, Object> userMap = useMapMap.get(courseReport.getStudentId());
        if (userMap == null) {
            return new ClassStudentCourseReport();
        }
        // 昵称
        build.setNickName((String) userMap.get("nick_name"));
        // 课程名称
        build.setCourseName(selectStudentCourseInfo.getCourseName());
        // 用户id
        build.setUserId((Long) userMap.get("user_id"));
        // 总章节
        StudentStudyRecord studyRecord = StudentStudyRecord.builder()
                .course(courseReport.getCourseName())
                .major((Long) userMap.get("major_id"))
                .studentId(courseReport.getStudentId())
                .userName((String) userMap.get("user_name"))
                .userId(String.valueOf(userMap.get("user_id")))
                .build();

        AjaxResult result = remoteDigitalhuman.list3_1(studyRecord);
        @SuppressWarnings("unchecked")
        List<Map<String, String>> mapArrayList = (List<Map<String, String>>) result.get("data");

        // 设置总章节
        build.setTotalChapter(String.valueOf(mapArrayList.size()));

        // 已完成章节
        List<Map<String, String>> collect = mapArrayList.stream()
                .filter(map -> "100.0".equals(String.valueOf(map.get("progress"))))
                .collect(Collectors.toList());

        build.setFinishedChapter(String.valueOf(collect.size()));

        // 完成进度
        BigDecimal completionProgress = BigDecimal.ZERO;
        if (BigDecimal.ZERO.compareTo(new BigDecimal(build.getTotalChapter())) == 0) {
            build.setCompletionProgress("0.0%");
        } else {
            completionProgress = new BigDecimal(build.getFinishedChapter())
                    .divide(new BigDecimal(build.getTotalChapter()),
                            4, RoundingMode.HALF_UP);
            String completionProgressStr = completionProgress.multiply(new BigDecimal(100)).setScale(1, RoundingMode.HALF_UP) + "%";
            build.setCompletionProgress(completionProgressStr);
        }


        // 平均完成进度
        List<CourseManagement> selectStudentCourseInfoList = courseManagementMapper.selectStudentCourseInfoList(courseManagement.getId());
        List<String> studnetIdList = selectStudentCourseInfoList.stream().map(CourseManagement::getStudentId).collect(Collectors.toList());

        if (studnetIdList.isEmpty()) {
            build.setAverageCompletionProgress("0.0%");
        }

        HashMap<String, String> recordAllProgressMap = new HashMap<>();

        for (String studentId : studnetIdList) {
            Map<String, HashMap<String, Object>> map = courseManagementMapper.getUserByStudentId(studentId);
            HashMap<String, Object> map1 = map.get(studentId);
            if (map1 == null) {
                continue;
            }
            studyRecord.setMajor((Long) map1.get("major_id"));
            studyRecord.setStudentId(studentId);
            studyRecord.setUserName(String.valueOf(map1.get("user_name")));
            studyRecord.setUserId(String.valueOf(map1.get("user_id")));
            AjaxResult result1 = remoteDigitalhuman.list3_1(studyRecord);
            @SuppressWarnings("unchecked")
            List<Map<String, String>> mapArrayList1 = (List<Map<String, String>>) result1.get("data");
            List<Map<String, String>> itemCollect = mapArrayList1.stream()
                    .filter(item -> "100.0".equals(String.valueOf(item.get("progress"))))
                    .collect(Collectors.toList());
            recordAllProgressMap.put(studentId, mapArrayList1.size() + "/" + itemCollect.size());
        }

        // 初始化总进度和学生总数
        BigDecimal totalProgress = BigDecimal.ZERO;
        BigDecimal studentCount = new BigDecimal(recordAllProgressMap.size());
        // 使用 for 循环
        for (Map.Entry<String, String> entry : recordAllProgressMap.entrySet()) {
            String[] split = entry.getValue().split("/");
            BigDecimal denominator = new BigDecimal(split[0]); // 总章节数
            BigDecimal numerator = new BigDecimal(split[1]); // 完成的章节数


            // 检查分母是否为零
            if (denominator.compareTo(BigDecimal.ZERO) == 0) {
                totalProgress = totalProgress.add(BigDecimal.ZERO); // 累加进度
            } else {
                BigDecimal progressTmp = numerator.divide(denominator, 4, RoundingMode.HALF_UP); // 进度计算
                totalProgress = totalProgress.add(progressTmp); // 累加进度
            }
        }
        // 平均完成进度
        BigDecimal averageCompletionProgress = BigDecimal.ZERO;
        if (studentCount.compareTo(BigDecimal.ZERO) == 0) {
            build.setAverageCompletionProgress("0.0%");
        } else {
            averageCompletionProgress = totalProgress.divide(studentCount, 4, RoundingMode.HALF_UP);
            String averageCompletionProgressStr = averageCompletionProgress.multiply(new BigDecimal(100)).setScale(1, RoundingMode.HALF_UP) + "%";
            build.setAverageCompletionProgress(averageCompletionProgressStr);
        }

        // 与平均完成进度比较
        if (completionProgress.compareTo(averageCompletionProgress) == 0) {
            build.setComparison("持平");
        } else if (completionProgress.compareTo(averageCompletionProgress) > 0) {
            build.setComparison("领先");
        } else {
            build.setComparison("落后");
        }

        // 智能问答次数
        String intelligentQuestionAnsweringCount = courseManagementMapper.getDialogueRecordingCount((String) userMap.get("user_name"));
        build.setIntelligentQuestionAnsweringCount(intelligentQuestionAnsweringCount);

        // 平均每周问答次数
        Long averageWeeklyQuestionAnsweringCount = Long.parseLong(intelligentQuestionAnsweringCount) / 7L;
        build.setAverageWeeklyQuestionAnsweringCount(String.valueOf(averageWeeklyQuestionAnsweringCount));

        // 与班级平均问答次数比较
        long totalCount = 0L;
        for (CourseManagement management : selectStudentCourseInfoList) {
            String userName = management.getUserName();
            String count = courseManagementMapper.getDialogueRecordingCount(userName);
            totalCount += Long.parseLong(count);
        }
        long bg1 = totalCount / selectStudentCourseInfoList.size();
        build.setClassAverageQuestionAnsweringCount(Long.toString(bg1));

        if (selectStudentCourseInfoList.isEmpty()) {
            build.setComparisonWithClassAverageQuestionAnsweringCount("无进度信息");
        }
        if (Long.parseLong(intelligentQuestionAnsweringCount) == bg1) {
            build.setComparisonWithClassAverageQuestionAnsweringCount("持平");
//			build.setComparisonWithClassAverageQuestionAnsweringCount(intelligentQuestionAnsweringCount + "/" + bg1 + "/持平");
        } else if (Long.parseLong(intelligentQuestionAnsweringCount) > bg1) {
            build.setComparisonWithClassAverageQuestionAnsweringCount("领先");
//			build.setComparisonWithClassAverageQuestionAnsweringCount(intelligentQuestionAnsweringCount + "/" + bg1 + "/领先");
        } else if (Long.parseLong(intelligentQuestionAnsweringCount) < bg1) {
            build.setComparisonWithClassAverageQuestionAnsweringCount("落后");
//			build.setComparisonWithClassAverageQuestionAnsweringCount(intelligentQuestionAnsweringCount + "/" + bg1 + "/落后");
        }

        // 总作业数
        String totalHomework = homeworkStudentMapper.selectHomeworkStudentListCount(
                HomeworkStudent.builder()
                        .userId((Long) userMap.get("user_id"))
                        .build());
        build.setTotalHomework(totalHomework);

        // 已提交次数
        String submittedTimes = homeworkStudentMapper.selectHomeworkStudentListCount(
                HomeworkStudent.builder()
                        .userId((Long) userMap.get("user_id"))
                        .commitStatus("1")
                        .build());
        build.setSubmittedTimes(submittedTimes);

        // 提交比例
        if (totalHomework.equals("0") || submittedTimes.equals("0")) {
            build.setSubmissionRatio("0.0%");
        } else {
            BigDecimal submissionRatio = new BigDecimal(submittedTimes).divide(new BigDecimal(totalHomework),
                    4, RoundingMode.HALF_UP);
            String submissionRatioStr = submissionRatio.multiply(new BigDecimal(100)).setScale(1, RoundingMode.HALF_UP) + "%";
            build.setSubmissionRatio(submissionRatioStr);
        }

        // 学习进度建议
        build.setLearningProgressAdvice("建议根据课程任务合理规划学习时间，按时完成目标。优先攻克难点内容，加强复习与巩固。保持学习专注，" +
                "利用碎片时间提升效率。定期评估进度，调整策略，确保稳步提升。积极与老师或同学交流，解决疑惑，实现高效学习！");
        // 提问频次建议
        build.setFrequencyOfQuestionsAdvice("\n" +
                "建议在学习过程中积极提出问题，尤其是在遇到难点时，保持适当的提问频率。定期回顾所学内容，提出具体且有针对性的问题，有助于加深理解。" +
                "通过主动提问，及时解决疑问，避免知识盲区，促进学习的深度和广度。同时，适当控制提问的频率，确保问题能够被充分思考和解答。");
        // 学习策略建议
        build.setLearningStrategyAdvice("建议采用分阶段的学习策略，合理安排学习时间。将大任务拆分成小目标，逐步推进，确保每个阶段都能高效完成。结合定期复习与实践，" +
                "强化记忆和技能的掌握。此外，利用不同的学习资源，如视频、书籍、讨论组等，帮助理解复杂概念。最后，保持积极的学习态度，定期调整学习方法，灵活应对不同学习情境，提高学习效果。");
//		String suggestion = "当前课程" + selectStudentCourseInfo.getCourseName() + "学习情况如下：" +
//				"总章节" + build.getTotalChapter() + "，已完成章节" + build.getFinishedChapter() + "，完成进度" + build.getCompletionProgress() +
//				"，平均完成进度" + build.getAverageCompletionProgress() + "，与平均进度比较" + build.getComparison() +
//				"，智能问答次数" + build.getIntelligentQuestionAnsweringCount() + "，平均每周问答次数" + build.getAverageWeeklyQuestionAnsweringCount() +
//				"，与班级平均问答次数比较" + build.getComparisonWithClassAverageQuestionAnsweringCount() +
//				"，总作业数" + build.getTotalHomework() + "，已提交次数" + build.getSubmittedTimes() + "，提交比例" + build.getSubmissionRatio() + ",";
//		String tmp = "请根据以上信息给出100字以内的综合学习进度建议。";
//
//		AjaxResult res = remoteIntelligent.relatedAnswer(suggestion + tmp);
//		if (!res.get("code").toString().equals("200")) {
//			throw new RuntimeException("智能问答服务调用失败：" + res.get("msg"));
//		} else {
//			
//		}

//		// 提问频次建议
//		build.setFrequencyOfQuestionsAdvice(String.valueOf(res.get("msg")));
//		tmp = "请根据以上信息给出100字以内的综合提问频次建议。";
//		res = remoteIntelligent.relatedAnswer(suggestion + tmp);
//		if (!res.get("code").toString().equals("200")) {
//			throw new RuntimeException("智能问答服务调用失败：" + res.get("msg"));
//		} else {
//		
//		}
//		// 学习策略建议
//		tmp = "请根据以上信息给出100字以内的综合学习策略建议。";
//		res = remoteIntelligent.relatedAnswer(suggestion + tmp);
//		if (!res.get("code").toString().equals("200")) {
//			throw new RuntimeException("智能问答服务调用失败：" + res.get("msg"));
//		} else {
//			build.setLearningStrategyAdvice(String.valueOf(res.get("msg")));
//		}

        classStudentCourseReportMapper.insertClassStudentCourseReport(build);
        return build;
    }


    /**
     * @description: 获取单个学生单门课程分析
     * @author: zhaoTianQi
     * @date: 2024/12/10 14:24
     * @param: courseReport
     * @return: ClassStudentCourseReport
     **/
    @Override
    public ClassStudentCourseReport getSingleCourseAnalysis(ClassStudentCourseReport courseReport) {
        if (StringUtils.isBlank(courseReport.getStudentId())
                || StringUtils.isBlank(courseReport.getCourseName())
                || courseReport.getCourseClassId() == null) {

            log.error("studentId | courseName | courseClassId 为空");
            return null;
        }
        courseReport.setType("student");

        ClassStudentCourseReport singleCourseAnalysis = classStudentCourseReportMapper.getSingleCourseAnalysis(courseReport);
        if (singleCourseAnalysis != null) {
            return classStudentCourseReportMapper.getSingleCourseAnalysis(courseReport);
        } else {
            return this.createSingleCourseAnalysis(courseReport);
        }
    }


    /**
     * @description: 创建班级内全部学生课程分析数据
     * @author: zhaoTianQi
     * @date: 2024/12/10 14:46
     * @param: courseReport
     * @return: AjaxResult
     **/
    @Override
    public void createClassStudentCourseAnalysis(ClassStudentCourseReport courseReport) {
        if (StringUtils.isBlank(courseReport.getCourseName())
                || courseReport.getCourseClassId() == null) {

            log.error("courseName | courseClassId 为空");
            return;
        }
        // 通过 课程名字 和 课程班级id 获取班级内的所有的学生相关的数据
        List<CourseManagement> courseManagementList = courseManagementMapper.selectStudentCourseInfo2(courseReport.getCourseName(), courseReport.getCourseClassId());
        for (CourseManagement item : courseManagementList) {
            ClassStudentCourseReport build = ClassStudentCourseReport.builder()
                    .studentId(item.getStudentId())
                    .courseName(item.getCourseName())
                    .courseClassId(item.getId()).build();
            this.createSingleCourseAnalysis(build);
        }

    }

    /**
     * @description: 创建单个班级课程分析数据
     * @author: zhaoTianQi
     * @date: 15:34
     * @param: courseReport
     * @return: ClassStudentCourseReport
     */
    @Override
    public ClassStudentCourseReport createClassCourseAnalysis(ClassStudentCourseReport courseReport) {
        if (StringUtils.isBlank(courseReport.getCourseName())
                || courseReport.getCourseClassId() == null) {
            log.error("courseName | courseClassId 为空");
            return null;
        }
        ClassStudentCourseReport build = ClassStudentCourseReport.builder().build();
        build.setType("class");
        build.setCreateTime(DateUtils.getNowDate());
        build.setCreateBy(String.valueOf(SecurityUtils.getUserId()));
        build.setCourseClassId(courseReport.getCourseClassId());
        // 通过 课程名字 和 课程班级id 获取班级内的所有的学生相关的数据
        List<CourseManagement> courseManagementList = courseManagementMapper.selectStudentCourseInfo2(courseReport.getCourseName(), courseReport.getCourseClassId());
        List<String> collectStudentIdList = courseManagementList.stream().map(CourseManagement::getStudentId).collect(Collectors.toList());
        if (collectStudentIdList.isEmpty()) {
            return null;
        }
        List<ClassStudentCourseReport> list = classStudentCourseReportMapper.selectCourseReportInList(collectStudentIdList);
        if (list.isEmpty()) {
            return null;
        }
        // 设置班级名
        build.setCourseClassName(list.get(0).getCourseClassName());
        // 设置课程名
        build.setCourseName(list.get(0).getCourseName());
        // 设置报告日期
        build.setReportDate(DateUtils.getNowDate());
        String averageCompletionProgress = list.get(0).getAverageCompletionProgress().replace("%", "");
        String classAverageQuestionAnsweringCount = list.get(0).getClassAverageQuestionAnsweringCount();
        // 完成进度 领先的学生人数
        int countLeading = 0;
        // 完成进度持平的 学生人数
        int countFlat = 0;
        // 完成进度落后的学生人数
        int countLag = 0;
        // 作业 提交比例 高于 60% 人数
        int countP6 = 0;
        // 作业 提交比例 高于 90% 人数
        int countP9 = 0;
        // 作业 完成人数
        int countP100 = 0;
        // 作业完成度低于 60 的 学生姓名
        List<String> setStudentNameHomeWorkList = new ArrayList<>();
        // 问答次数 比例高于 60% 人数
        int qAnswerCountP6 = 0;
        // 问答次数 比例高于 90% 人数
        int qAnswerCountP9 = 0;
        // 作答次数 比例低于 60% 学生姓名
        List<String> qAnswerCountList = new ArrayList<>();

        for (ClassStudentCourseReport report : list) {
            // 完成进度
            String completionProgress = report.getCompletionProgress().replace("%", "");
            if (Double.parseDouble(completionProgress) > Double.parseDouble(averageCompletionProgress)) {
                countLeading++;
            } else if (Double.parseDouble(completionProgress) == Double.parseDouble(averageCompletionProgress)) {
                countFlat++;
            } else {
                countLag++;
            }
            // 提交比例
            String submissionRatio = report.getSubmissionRatio().replace("%", "");
            if (Double.parseDouble(submissionRatio) == 100.0) {
                countP100++;
            } else if (Double.parseDouble(submissionRatio) > 90.0) {
                countP9++;
            } else if (Double.parseDouble(submissionRatio) > 60.0) {
                countP6++;
            } else {
                setStudentNameHomeWorkList.add(report.getNickName());
            }

            // 问答次数
            int intelligentQuestionAnsweringCount = Integer.parseInt(report.getIntelligentQuestionAnsweringCount());
            if (intelligentQuestionAnsweringCount > Integer.parseInt(classAverageQuestionAnsweringCount) * 0.9) {
                qAnswerCountP9++;
            } else if (intelligentQuestionAnsweringCount > Integer.parseInt(classAverageQuestionAnsweringCount) * 0.6) {
                qAnswerCountP6++;
            } else {
                qAnswerCountList.add(report.getNickName());
            }
        }
        BigDecimal totalStudentCountBg = new BigDecimal(list.size());
        // 完成进度领先的学生比例
        build.setCountLeadingRatio(
                new BigDecimal(countLeading)
                        .divide(totalStudentCountBg, 5, RoundingMode.HALF_UP) // 先保留5位精度进行计算
                        .multiply(new BigDecimal(100)) // 转换为百分比
                        .setScale(1, RoundingMode.HALF_UP) // 最终保留1位小数
                        + "%"); // 拼接百分号
        // 完成进度持平的学生比例
        build.setCountFlatRatio(new BigDecimal(countFlat)
                .divide(totalStudentCountBg, 5, RoundingMode.HALF_UP)
                .multiply(new BigDecimal(100)) // 转换为百分比
                .setScale(1, RoundingMode.HALF_UP) // 最终保留1位小数
                + "%"); // 拼接百分号
        // 完成进度落后的学生比例
        build.setCountLagRatio(new BigDecimal(countLag)
                .divide(totalStudentCountBg, 5, RoundingMode.HALF_UP)
                .multiply(new BigDecimal(100)) // 转换为百分比
                .setScale(1, RoundingMode.HALF_UP) // 最终保留1位小数
                + "%"); // 拼接百分号

        // 作业完成百分之百比例
        build.setCountP100(new BigDecimal(countP100)
                .divide(totalStudentCountBg, 5, RoundingMode.HALF_UP)
                .multiply(new BigDecimal(100)) // 转换为百分比
                .setScale(1, RoundingMode.HALF_UP) // 最终保留1位小数
                + "%"); // 拼接百分号
        // 作业提交比例高于60%的比例
        build.setCountP6(new BigDecimal(countP6)
                .divide(totalStudentCountBg, 5, RoundingMode.HALF_UP)
                .multiply(new BigDecimal(100)) // 转换为百分比
                .setScale(1, RoundingMode.HALF_UP) // 最终保留1位小数
                + "%"); // 拼接百分号
        // 作业提交比例高于90%的比例
        build.setCountP9(new BigDecimal(countP9)
                .divide(totalStudentCountBg, 5, RoundingMode.HALF_UP)
                .multiply(new BigDecimal(100)) // 转换为百分比
                .setScale(1, RoundingMode.HALF_UP) // 最终保留1位小数
                + "%"); // 拼接百分号
        // 作业提交比例低于60%的学生姓名
        build.setLessThanP6NameStr(StringUtils.join(setStudentNameHomeWorkList, ","));

        // 问答次数高于90%的比例
        build.setQuoteAnswerCountP9(new BigDecimal(qAnswerCountP9)
                .divide(totalStudentCountBg, 5, RoundingMode.HALF_UP)
                .multiply(new BigDecimal(100)) // 转换为百分比
                .setScale(1, RoundingMode.HALF_UP) // 最终保留1位小数
                + "%"); // 拼接百分号
        // 问答次数高于60%的比例
        build.setQuoteAnswerCountP6(new BigDecimal(qAnswerCountP6)
                .divide(totalStudentCountBg, 5, RoundingMode.HALF_UP)
                .multiply(new BigDecimal(100)) // 转换为百分比
                .setScale(1, RoundingMode.HALF_UP) // 最终保留1位小数
                + "%"); // 拼接百分号
        // 问答次数低于60%的学生姓名
        build.setQuoteAnswerLessThantStr(StringUtils.join(qAnswerCountList, ","));

        // 班级学习进度建议
        build.setSuggestionsForClassProgress("建议班级根据学习进度调整教学节奏，确保每个学生都能跟上进度。对于进度较慢的同学，" +
                "可以提供个性化辅导，帮助他们弥补差距。定期进行小测试或评估，及时了解学生的掌握情况，针对性地调整教学内容。" +
                "同时，鼓励学生之间互相帮助，组织小组讨论和合作学习，促进知识的共享与深入理解。通过团队协作和自主学习相结合的方式，提高整个班级的学习效率。");

        // 班级课后作业建议
        build.setSuggestionsForClassHomework("建议班级课后作业要根据学生的学习进度和掌握情况来合理安排，避免过多的重复性练习，而是要注重深度和实践性。" +
                "可以结合课堂内容，设计具有挑战性的问题，鼓励学生独立思考并应用所学知识。同时，提供一定的作业反馈，" +
                "帮助学生发现并改正错误，提升他们的自学能力。作业量适中，既能够巩固所学，又不会让学生感到过于负担。同时，" +
                "建议定期进行作业总结，帮助学生理清知识点，提升他们的学习效果。");

        // 班级问答互动建议
        build.setSuggestionsForClassInteractions("鼓励学生积极提问，创建开放的课堂氛围，激发学生思考。通过设置富有挑战性的问题，引导学生深入思考，" +
                "避免单纯的记忆型问题。同时，可以采用小组合作答疑，促进学生间的互动与合作。教师应及时反馈，表扬正确回答并纠正错误，" +
                "提升学生信心和学习动力。合理安排互动时间，确保每个学生都能参与，提升课堂活跃度和学习效果");

//		// 班级学习进度建议
//		String suggestion = "当前班级完成进度领先学生比例" + build.getCountLeadingRatio() + "，" + "完成进度持平学生比例" + build.getCountFlatRatio()
//				+ "，" + "完成进度落后学生比例" + "作业完成百分之百比例" + build.getCountP100() + "，" + "作业提交比例高于60%的比例" + build.getCountP6()
//				+ "，" + "作业提交比例高于90%的比例" + build.getCountP9() + "，" + "问答次数高于90%的比例" + build.getQuoteAnswerCountP9()
//				+ "，" + "问答次数高于60%的比例" + build.getQuoteAnswerCountP6() + "，";
//		String tmp = "请根据以上信息给出100字以内的班级学习进度建议。";
//		AjaxResult res = remoteIntelligent.relatedAnswer(suggestion + tmp);
//		if (!res.get("code").toString().equals("200")) {
//			throw new RuntimeException("智能问答服务调用失败：" + res.get("msg"));
//		} else {
//			build.setSuggestionsForClassProgress(String.valueOf(res.get("msg")));
//		}
//		// 班级课后作业建议
//		tmp = "请根据以上信息给出100字以内的班级课后作业建议。";
//		res = remoteIntelligent.relatedAnswer(suggestion + tmp);
//		if (!res.get("code").toString().equals("200")) {
//			throw new RuntimeException("智能问答服务调用失败：" + res.get("msg"));
//		} else {
//			build.setSuggestionsForClassHomework(String.valueOf(res.get("msg")));
//		}
//		// 班级问答互动建议
//		tmp = "请根据以上信息给出100字以内的班级问答互动建议。";
//		res = remoteIntelligent.relatedAnswer(suggestion + tmp);
//		if (!res.get("code").toString().equals("200")) {
//			throw new RuntimeException("智能问答服务调用失败：" + res.get("msg"));
//		} else {
//			build.setSuggestionsForClassInteractions(String.valueOf(res.get("msg")));
//		}
        classStudentCourseReportMapper.insertClassStudentCourseReport(build);
        return build;
    }

    /**
     * @description: 获取班级课程分析数据
     * @author: zhaoTianQi
     * @date: 2024/12/11 10:23
     * @param: courseReport
     * @return: String
     */
    @Override
    public ClassStudentCourseReport getClassCourseAnalysis(ClassStudentCourseReport courseReport) {
        if (StringUtils.isBlank(courseReport.getCourseName())
                || courseReport.getCourseClassId() == null) {
            log.error("courseName | courseClassId 为空");
            return null;
        }
        // 设置类型
        courseReport.setType("class");

        ClassStudentCourseReport classCourseAnalysis = classStudentCourseReportMapper.getClassCourseAnalysis(courseReport);
        if (classCourseAnalysis == null) {
            return this.createClassCourseAnalysis(courseReport);
        } else {
            return classCourseAnalysis;
        }
    }

    /**
     * @description: 创建所有的班级课程分析数据
     * @author: zhaoTianQi
     * @date: 10:36
     * @param:
     * @return: void
     **/
    @Override
    public void createAllClassCourseAnalysis() {
        // 获取所有的班级数据
        List<CourseManagement> courseManagementList = courseManagementMapper.selectCourseManagementAll2();
        if (courseManagementList.isEmpty()) {
            return;
        }
        for (CourseManagement management : courseManagementList) {
            ClassStudentCourseReport build = ClassStudentCourseReport.builder()
                    .courseName(management.getCourseName())
                    .courseClassId(management.getId())
                    .build();
            this.createClassCourseAnalysis(build);

        }
    }

    /**
     * @description: 处理所有的课程分析 学生 和 班级
     * @author: zhaoTianQi
     * @date: 2024/12/11 12:36
     * @param:
     * @return: AjaxResult
     **/
    @Override
    public void dealAllCourseAnalysis() {
        log.info("开始处理所有的课程分析");

        // 获取所有的班级数据
        List<CourseManagement> courseManagementList1 = courseManagementMapper.selectCourseManagementAll2();
        if (courseManagementList1.isEmpty()) {
            return;
        }
        for (CourseManagement management : courseManagementList1) {
            try {
                customThreadPool.submitTask(() -> {
                    ClassStudentCourseReport build1 = ClassStudentCourseReport.builder()
                            .courseName(management.getCourseName())
                            .courseClassId(management.getId())
                            .build();
                    // 创建班级内全部学生课程分析数据
                    this.createClassStudentCourseAnalysis(build1);
                    // 创建单个班级课程分析数据
                    this.createClassCourseAnalysis(build1);
                });
            } catch (Exception e) {
                // 捕获并记录详细的错误信息
                log.error("处理班级课程分析时发生错误，班级ID: {}，课程名称courseName：{}，错误信息: {}",
                        management.getId(), management.getCourseName(), e.getMessage(), e);
            }
        }

        log.info("任务已提交到线程池");

    }
}
