package com.ruoyi.create.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.create.domain.MajorInfo;

/**
 * 专业信息Service接口
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
public interface IMajorInfoService extends IService<MajorInfo>
{
    /**
     * 查询专业信息
     *
     * @param id 专业信息主键
     * @return 专业信息
     */
    public MajorInfo selectMajorInfoById(Long id);

    /**
     * 查询专业信息列表
     *
     * @param majorInfo 专业信息
     * @return 专业信息集合
     */
    public List<MajorInfo> selectMajorInfoList(MajorInfo majorInfo);

    /**
     * 新增专业信息
     *
     * @param majorInfo 专业信息
     * @return 结果
     */
    public int insertMajorInfo(MajorInfo majorInfo);

    /**
     * 修改专业信息
     *
     * @param majorInfo 专业信息
     * @return 结果
     */
    public int updateMajorInfo(MajorInfo majorInfo);

    /**
     * 批量删除专业信息
     *
     * @param ids 需要删除的专业信息主键集合
     * @return 结果
     */
    public int deleteMajorInfoByIds(Long[] ids);

    /**
     * 删除专业信息信息
     *
     * @param id 专业信息主键
     * @return 结果
     */
    public int deleteMajorInfoById(Long id);
}
