package com.ruoyi.create.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.Vo.UserVoiceVo;
import com.ruoyi.create.domain.UserVoice;
import com.ruoyi.create.service.IUserVoiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 用户选择语音发言人Controller
 *
 * <AUTHOR>
 * @date 2024-08-09
 */
@RestController
@RequestMapping("/voiceRole")
public class UserVoiceController extends BaseController {

    @Autowired
    private IUserVoiceService sUserVoiceService;

    /**
     * 保存用户使用的发言人
     * @param userVoice
     * @return
     */
    @RequestMapping("/save")
    public AjaxResult save(UserVoice userVoice) {
        System.out.println(userVoice);
        return AjaxResult.success(sUserVoiceService.saveUserVoice(userVoice));
    }

    /**
     * 获取用户使用的发言人
     * @param
     * @return
     */
    @RequestMapping("/getUserVoiceRole")
    public AjaxResult getUserVoiceRole() {
        return AjaxResult.success(sUserVoiceService.selectSUserVoiceById());
    }
    /***
     * 获取系统默认的TTS选择
     * @return 系统当前选择的语音合成引擎
     */
    @PostMapping("getSystemTTSChoose")
    public AjaxResult getSystemTTSChoose() {
        return AjaxResult.success(sUserVoiceService.getSystemTTSChoose());
    }

    /**
     * 获取发言人列表
     * @return
     */
    @PostMapping("/list")
    public AjaxResult list() {
        List<UserVoiceVo> list = sUserVoiceService.list();
        return AjaxResult.success(list);
    }

    // ===============以下功能未用到=================

    /**
     * 查询【请填写功能名称】列表
     */
    @RequiresPermissions("create:voice:list")
    @GetMapping("/list")
    public TableDataInfo list(UserVoice userVoice)
    {
        startPage();
        List<UserVoice> list = sUserVoiceService.selectSUserVoiceList(userVoice);
        return getDataTable(list);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @RequiresPermissions("create:voice:export")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, UserVoice userVoice)
    {
        List<UserVoice> list = sUserVoiceService.selectSUserVoiceList(userVoice);
        ExcelUtil<UserVoice> util = new ExcelUtil<UserVoice>(UserVoice.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @RequiresPermissions("create:voice:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sUserVoiceService.selectSUserVoiceById(id));
    }

    /**
     * 新增【请填写功能名称】
     */
    @RequiresPermissions("create:voice:add")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UserVoice userVoice)
    {
        return toAjax(sUserVoiceService.insertSUserVoice(userVoice));
    }

    /**
     * 修改【请填写功能名称】
     */
    @RequiresPermissions("create:voice:edit")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UserVoice userVoice)
    {
        return toAjax(sUserVoiceService.updateSUserVoice(userVoice));
    }

    /**
     * 删除【请填写功能名称】
     */
    @RequiresPermissions("create:voice:remove")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sUserVoiceService.deleteSUserVoiceByIds(ids));
    }
}
