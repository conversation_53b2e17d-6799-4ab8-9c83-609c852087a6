package com.ruoyi.create.service.impl;


import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.domain.ExtractionRules;
import com.ruoyi.create.mapper.ExtractionRulesMapper;
import com.ruoyi.create.service.IExtractionRulesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 提取规则Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-12-13
 */
@Service
public class ExtractionRulesServiceImpl implements IExtractionRulesService
{
    @Resource
    private ExtractionRulesMapper extractionRulesMapper;

    /**
     * 查询提取规则
     * 
     * @param rulesId 提取规则主键
     * @return 提取规则
     */
    @Override
    public ExtractionRules selectExtractionRulesByRulesId(Long rulesId)
    {
        return extractionRulesMapper.selectExtractionRulesByRulesId(rulesId);
    }

    /**
     * 查询提取规则列表
     * 
     * @param extractionRules 提取规则
     * @return 提取规则
     */
    @Override
    public List<ExtractionRules> selectExtractionRulesList(ExtractionRules extractionRules)
    {
        extractionRules.setCreateBy(SecurityUtils.getUsername());
        return extractionRulesMapper.selectExtractionRulesList(extractionRules);
    }

    /**
     * 新增提取规则
     * 
     * @param extractionRules 提取规则
     * @return 结果
     */
    @Override
    public int insertExtractionRules(ExtractionRules extractionRules)
    {
        extractionRules.setCreateBy(SecurityUtils.getUsername());
        extractionRules.setCreateTime(DateUtils.getNowDate());
        return extractionRulesMapper.insertExtractionRules(extractionRules);
    }

    /**
     * 修改提取规则
     * 
     * @param extractionRules 提取规则
     * @return 结果
     */
    @Override
    public int updateExtractionRules(ExtractionRules extractionRules)
    {
        extractionRules.setUpdateTime(DateUtils.getNowDate());
        return extractionRulesMapper.updateExtractionRules(extractionRules);
    }

    /**
     * 批量删除提取规则
     * 
     * @param rulesIds 需要删除的提取规则主键
     * @return 结果
     */
    @Override
    public int deleteExtractionRulesByRulesIds(Long[] rulesIds)
    {
        return extractionRulesMapper.deleteExtractionRulesByRulesIds(rulesIds);
    }

    /**
     * 删除提取规则信息
     * 
     * @param rulesId 提取规则主键
     * @return 结果
     */
    @Override
    public int deleteExtractionRulesByRulesId(Long rulesId)
    {
        return extractionRulesMapper.deleteExtractionRulesByRulesId(rulesId);
    }

    @Override
    public List<ExtractionRules> selectExtractionRulesAll() {
        return extractionRulesMapper.selectExtractionRulesAll();
    }
}
