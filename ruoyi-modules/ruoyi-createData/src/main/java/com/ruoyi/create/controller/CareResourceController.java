package com.ruoyi.create.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.CareResource;
import com.ruoyi.create.service.ICareResourceService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 服务资源配置Controller
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
@RestController
@RequestMapping("/careResource")
public class CareResourceController extends BaseController
{
    @Autowired
    private ICareResourceService careResourceService;

    /**
     * 查询服务资源配置列表
     */
    @RequiresPermissions("create:resource:list")
    @GetMapping("/list")
    public TableDataInfo list(CareResource careResource)
    {
        startPage();
        List<CareResource> list = careResourceService.selectCareResourceList(careResource);
        return getDataTable(list);
    }

    /**
     * 导出服务资源配置列表
     */
    @RequiresPermissions("create:resource:export")
    @Log(title = "服务资源配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CareResource careResource)
    {
        List<CareResource> list = careResourceService.selectCareResourceList(careResource);
        ExcelUtil<CareResource> util = new ExcelUtil<CareResource>(CareResource.class);
        util.exportExcel(response, list, "服务资源配置数据");
    }

    /**
     * 获取服务资源配置详细信息
     */
    @RequiresPermissions("create:resource:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(careResourceService.selectCareResourceById(id));
    }

    /**
     * 新增服务资源配置
     */
    @RequiresPermissions("create:resource:add")
    @Log(title = "服务资源配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CareResource careResource)
    {
        return toAjax(careResourceService.insertCareResource(careResource));
    }

    /**
     * 修改服务资源配置
     */
    @RequiresPermissions("create:resource:edit")
    @Log(title = "服务资源配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CareResource careResource)
    {
        return toAjax(careResourceService.updateCareResource(careResource));
    }

    /**
     * 删除服务资源配置
     */
    @RequiresPermissions("create:resource:remove")
    @Log(title = "服务资源配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(careResourceService.deleteCareResourceByIds(ids));
    }
}
