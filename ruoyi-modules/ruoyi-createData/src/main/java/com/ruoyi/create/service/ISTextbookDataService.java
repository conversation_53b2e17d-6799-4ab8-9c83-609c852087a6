package com.ruoyi.create.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.create.domain.Counting;
import com.ruoyi.create.domain.Node;
import com.ruoyi.create.domain.TextbookData;

/**
 * 文献整理- 教材原始数据Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-31
 */
public interface ISTextbookDataService 
{
    /**
     * 查询文献整理- 教材原始数据
     * 
     * @param id 文献整理- 教材原始数据主键
     * @return 文献整理- 教材原始数据
     */
    public TextbookData selectSTextbookDataById(Long id);

    /**
     * 查询文献整理- 教材原始数据列表
     * 
     * @param sTextbookData 文献整理- 教材原始数据
     * @return 文献整理- 教材原始数据集合
     */
    public List<TextbookData> selectSTextbookDataList(TextbookData sTextbookData);

    /**
     * 新增文献整理- 教材原始数据
     * 
     * @param sTextbookData 文献整理- 教材原始数据
     * @return 结果
     */
    public AjaxResult insertSTextbookData(TextbookData sTextbookData);

    /**
     * 修改文献整理- 教材原始数据
     * 
     * @param sTextbookData 文献整理- 教材原始数据
     * @return 结果
     */
    public int updateSTextbookData(TextbookData sTextbookData);

    /**
     * 批量删除文献整理- 教材原始数据
     * 
     * @param ids 需要删除的文献整理- 教材原始数据主键集合
     * @return 结果
     */
    public int deleteSTextbookDataByIds(Long[] ids);

    /**
     * 删除文献整理- 教材原始数据信息
     * 
     * @param id 文献整理- 教材原始数据主键
     * @return 结果
     */
    public int deleteSTextbookDataById(Long id);

    AjaxResult selectGroupTextbookData(TextbookData sTextbookData);

    AjaxResult selectNewGroupTextbookData(TextbookData sTextbookData);

    Node getThinking();

    List<Node> getThinkingKeyword(Long id);

    Counting getStatistics();
}
