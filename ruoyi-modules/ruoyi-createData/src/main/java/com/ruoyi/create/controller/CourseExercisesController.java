package com.ruoyi.create.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.CourseExercises;
import com.ruoyi.create.service.ICourseExercisesService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 课程习题Controller
 * 
 * <AUTHOR>
 * @date 2024-12-27
 */
@RestController
@RequestMapping("/exercises")
public class CourseExercisesController extends BaseController
{
    @Autowired
    private ICourseExercisesService courseExercisesService;

    /**
     * 查询课程习题列表
     */
    @RequiresPermissions("create:exercises:list")
    @GetMapping("/list")
    public TableDataInfo list(CourseExercises courseExercises)
    {
        startPage();
        List<CourseExercises> list = courseExercisesService.selectCourseExercisesList(courseExercises);
        return getDataTable(list);
    }

    /**
     * 学生查询课程习题列表
     */
    @GetMapping("/student/list")
    public TableDataInfo studentList(CourseExercises courseExercises)
    {
        startPage();
        List<CourseExercises> list = courseExercisesService.selectStudentCourseExercisesList(courseExercises);
        return getDataTable(list);
    }

    /**
     * 导出课程习题列表
     */
    @RequiresPermissions("create:exercises:export")
    @Log(title = "课程习题", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CourseExercises courseExercises)
    {
        List<CourseExercises> list = courseExercisesService.selectCourseExercisesList(courseExercises);
        ExcelUtil<CourseExercises> util = new ExcelUtil<CourseExercises>(CourseExercises.class);
        util.exportExcel(response, list, "课程习题数据");
    }

    /**
     * 获取课程习题详细信息
     */
    @RequiresPermissions("create:exercises:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(courseExercisesService.selectCourseExercisesById(id));
    }

    /**
     * 新增课程习题
     */
    @RequiresPermissions("create:exercises:add")
    @Log(title = "课程习题", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CourseExercises courseExercises)
    {
        return toAjax(courseExercisesService.insertCourseExercises(courseExercises));
    }

    /**
     * 修改课程习题
     */
    @RequiresPermissions("create:exercises:edit")
    @Log(title = "课程习题", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CourseExercises courseExercises)
    {
        return toAjax(courseExercisesService.updateCourseExercises(courseExercises));
    }

    /**
     * 删除课程习题
     */
    @RequiresPermissions("create:exercises:remove")
    @Log(title = "课程习题", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(courseExercisesService.deleteCourseExercisesByIds(ids));
    }
}
