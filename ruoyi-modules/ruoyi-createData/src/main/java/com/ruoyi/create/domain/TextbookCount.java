package com.ruoyi.create.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 文献整理- 教材数量目标对象 s_textbook_count
 * 
 * <AUTHOR>
 * @date 2024-07-31
 */
public class TextbookCount extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 教材id */
    @Excel(name = "教材id")
    private Long textbookId;

    /** 教材 */
    @Excel(name = "教材")
    private String textbook;

    /** 主题 0-相关教材  1-理论基础 2-创新 3-作者 4-研究机构  */
    @Excel(name = "主题 0-相关教材  1-理论基础 2-创新 3-作者 4-研究机构 ")
    private String subjectId;

    /** 类别 */
    @Excel(name = "类别")
    private String category;

    /** 关键词 */
    @Excel(name = "关键词")
    private String keyword;

    /** 关键词数量 */
    @Excel(name = "关键词数量")
    private Long count;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTextbookId(Long textbookId) 
    {
        this.textbookId = textbookId;
    }

    public Long getTextbookId() 
    {
        return textbookId;
    }
    public void setTextbook(String textbook) 
    {
        this.textbook = textbook;
    }

    public String getTextbook() 
    {
        return textbook;
    }
    public void setSubjectId(String subjectId) 
    {
        this.subjectId = subjectId;
    }

    public String getSubjectId() 
    {
        return subjectId;
    }
    public void setCategory(String category) 
    {
        this.category = category;
    }

    public String getCategory() 
    {
        return category;
    }
    public void setKeyword(String keyword) 
    {
        this.keyword = keyword;
    }

    public String getKeyword() 
    {
        return keyword;
    }
    public void setCount(Long count) 
    {
        this.count = count;
    }

    public Long getCount() 
    {
        return count;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("textbookId", getTextbookId())
            .append("textbook", getTextbook())
            .append("subjectId", getSubjectId())
            .append("category", getCategory())
            .append("keyword", getKeyword())
            .append("count", getCount())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
