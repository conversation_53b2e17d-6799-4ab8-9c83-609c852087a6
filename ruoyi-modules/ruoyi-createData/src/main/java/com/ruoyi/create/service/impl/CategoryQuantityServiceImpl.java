package com.ruoyi.create.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.CategoryQuantityMapper;
import com.ruoyi.create.domain.CategoryQuantity;
import com.ruoyi.create.service.ICategoryQuantityService;

/**
 * 文献整理-类别统计Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Service
public class CategoryQuantityServiceImpl implements ICategoryQuantityService 
{
    @Autowired
    private CategoryQuantityMapper categoryQuantityMapper;

    /**
     * 查询文献整理-类别统计
     * 
     * @param id 文献整理-类别统计主键
     * @return 文献整理-类别统计
     */
    @Override
    public CategoryQuantity selectCategoryQuantityById(Long id)
    {
        return categoryQuantityMapper.selectCategoryQuantityById(id);
    }

    /**
     * 查询文献整理-类别统计列表
     * 
     * @param categoryQuantity 文献整理-类别统计
     * @return 文献整理-类别统计
     */
    @Override
    public List<CategoryQuantity> selectCategoryQuantityList(CategoryQuantity categoryQuantity)
    {
        return categoryQuantityMapper.selectCategoryQuantityList(categoryQuantity);
    }

    /**
     * 新增文献整理-类别统计
     * 
     * @param categoryQuantity 文献整理-类别统计
     * @return 结果
     */
    @Override
    public int insertCategoryQuantity(CategoryQuantity categoryQuantity)
    {
        return categoryQuantityMapper.insertCategoryQuantity(categoryQuantity);
    }

    /**
     * 修改文献整理-类别统计
     * 
     * @param categoryQuantity 文献整理-类别统计
     * @return 结果
     */
    @Override
    public int updateCategoryQuantity(CategoryQuantity categoryQuantity)
    {
        return categoryQuantityMapper.updateCategoryQuantity(categoryQuantity);
    }

    /**
     * 批量删除文献整理-类别统计
     * 
     * @param ids 需要删除的文献整理-类别统计主键
     * @return 结果
     */
    @Override
    public int deleteCategoryQuantityByIds(Long[] ids)
    {
        return categoryQuantityMapper.deleteCategoryQuantityByIds(ids);
    }

    /**
     * 删除文献整理-类别统计信息
     * 
     * @param id 文献整理-类别统计主键
     * @return 结果
     */
    @Override
    public int deleteCategoryQuantityById(Long id)
    {
        return categoryQuantityMapper.deleteCategoryQuantityById(id);
    }

    @Override
    public List<CategoryQuantity> selectCategoryCount(String title) {
        System.out.println(title+"+++++++++++++++++++");
        return categoryQuantityMapper.selectCategoryCount(title);
    }
}
