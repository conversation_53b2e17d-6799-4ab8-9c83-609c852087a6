package com.ruoyi.create.service;


import com.ruoyi.create.domain.SpeechdraftProblem;

import java.util.List;

/**
 * 讲演稿题目Service接口
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
public interface ISpeechdraftProblemService
{
    /**
     * 查询讲演稿题目
     *
     * @param id 讲演稿题目主键
     * @return 讲演稿题目
     */
    public SpeechdraftProblem selectSpeechdraftProblemById(Long id);

    /**
     * 查询讲演稿题目列表
     *
     * @param speechdraftProblem 讲演稿题目
     * @return 讲演稿题目集合
     */
    public List<SpeechdraftProblem> selectSpeechdraftProblemList(SpeechdraftProblem speechdraftProblem);

    /**
     * 新增讲演稿题目
     *
     * @param speechdraftProblem 讲演稿题目
     * @return 结果
     */
    public int insertSpeechdraftProblem(SpeechdraftProblem speechdraftProblem);

    /**
     * 修改讲演稿题目
     *
     * @param speechdraftProblem 讲演稿题目
     * @return 结果
     */
    public int updateSpeechdraftProblem(SpeechdraftProblem speechdraftProblem);

    /**
     * 批量删除讲演稿题目
     *
     * @param ids 需要删除的讲演稿题目主键集合
     * @return 结果
     */
    public int deleteSpeechdraftProblemByIds(Long[] ids);

    public int deleteSpeechdraftProblemBypresentationId(Long presentationId);


    /**
     * 删除讲演稿题目信息
     *
     * @param id 讲演稿题目主键
     * @return 结果
     */
    public int deleteSpeechdraftProblemById(Long id);


    int insertSpeechdraftProblemList(List<SpeechdraftProblem> speechdraftProblemArrayList);
}
