package com.ruoyi.create.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.MajorInfoMapper;
import com.ruoyi.create.domain.MajorInfo;
import com.ruoyi.create.service.IMajorInfoService;

/**
 * 专业信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
@Service
public class MajorInfoServiceImpl extends ServiceImpl<MajorInfoMapper, MajorInfo> implements IMajorInfoService
{
    @Autowired
    private MajorInfoMapper majorInfoMapper;

    /**
     * 查询专业信息
     *
     * @param id 专业信息主键
     * @return 专业信息
     */
    @Override
    public MajorInfo selectMajorInfoById(Long id)
    {
        return majorInfoMapper.selectMajorInfoById(id);
    }

    /**
     * 查询专业信息列表
     *
     * @param majorInfo 专业信息
     * @return 专业信息
     */
    @Override
    public List<MajorInfo> selectMajorInfoList(MajorInfo majorInfo)
    {
        return majorInfoMapper.selectMajorInfoList(majorInfo);
    }

    /**
     * 新增专业信息
     *
     * @param majorInfo 专业信息
     * @return 结果
     */
    @Override
    public int insertMajorInfo(MajorInfo majorInfo)
    {
        majorInfo.setCreateTime(DateUtils.getNowDate());
        return majorInfoMapper.insertMajorInfo(majorInfo);
    }

    /**
     * 修改专业信息
     *
     * @param majorInfo 专业信息
     * @return 结果
     */
    @Override
    public int updateMajorInfo(MajorInfo majorInfo)
    {
        majorInfo.setUpdateTime(DateUtils.getNowDate());
        return majorInfoMapper.updateMajorInfo(majorInfo);
    }

    /**
     * 批量删除专业信息
     *
     * @param ids 需要删除的专业信息主键
     * @return 结果
     */
    @Override
    public int deleteMajorInfoByIds(Long[] ids)
    {
        return majorInfoMapper.deleteMajorInfoByIds(ids);
    }

    /**
     * 删除专业信息信息
     *
     * @param id 专业信息主键
     * @return 结果
     */
    @Override
    public int deleteMajorInfoById(Long id)
    {
        return majorInfoMapper.deleteMajorInfoById(id);
    }
}
