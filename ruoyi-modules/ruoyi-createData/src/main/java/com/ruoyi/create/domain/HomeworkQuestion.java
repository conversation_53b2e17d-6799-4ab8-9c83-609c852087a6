package com.ruoyi.create.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.core.annotation.Excel;

import java.util.List;

/**
 * 作业题目表(HomeworkQuestion)表实体类
 *
 * <AUTHOR>
 * @since 2024-06-07 10:08:19
 */
@Data
@TableName("s_homework_question")
public class HomeworkQuestion {
    private static final long serialVersionUID = 1L;

    /**
     * 题目id
     **/
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 作业id
     **/
    @TableField("hm_id")
    @Excel(name = "作业id")
    private Long hmId;

    /**
     * 题目类型
     **/
    @TableField("question_type")
    @Excel(name = "题目类型")
    private String questionType;

    /**
     * 题目序号
     **/
    @TableField("question_order")
    @Excel(name = "题目序号")
    private Integer questionOrder;

    /**
     * 题目
     **/
    @TableField("question")
    @Excel(name = "题目")
    private String question;

    /**
     * 正确答案，多选题答案以逗号分割
     **/
    @TableField("correct_answer")
    @Excel(name = "正确答案，多选题答案以逗号分割")
    private String correctAnswer;

    @TableField(exist = false)
    private List<HomeworkQuestionOption> homeworkQuestionOptions;

}

