package com.ruoyi.create.service;

import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.create.domain.LiteratrueKeywordCount;

import java.util.List;

/**
 * 文献整理-关键词统计Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-18
 */
public interface ILiteratrueKeywordCountService 
{
    /**
     * 查询文献整理-关键词统计
     * 
     * @param id 文献整理-关键词统计主键
     * @return 文献整理-关键词统计
     */
    public LiteratrueKeywordCount selectLiteratrueKeywordCountById(Long id);

    /**
     * 查询文献整理-关键词统计列表
     * 
     * @param literatrueKeywordCount 文献整理-关键词统计
     * @return 文献整理-关键词统计集合
     */
    public List<LiteratrueKeywordCount> selectLiteratrueKeywordCountList(LiteratrueKeywordCount literatrueKeywordCount);

    /**
     * 新增文献整理-关键词统计
     * 
     * @param literatrueKeywordCount 文献整理-关键词统计
     * @return 结果
     */
    public int insertLiteratrueKeywordCount(LiteratrueKeywordCount literatrueKeywordCount);

    /**
     * 修改文献整理-关键词统计
     * 
     * @param literatrueKeywordCount 文献整理-关键词统计
     * @return 结果
     */
    public int updateLiteratrueKeywordCount(LiteratrueKeywordCount literatrueKeywordCount);

    /**
     * 批量删除文献整理-关键词统计
     * 
     * @param ids 需要删除的文献整理-关键词统计主键集合
     * @return 结果
     */
    public int deleteLiteratrueKeywordCountByIds(Long[] ids);

    /**
     * 删除文献整理-关键词统计信息
     * 
     * @param id 文献整理-关键词统计主键
     * @return 结果
     */
    public int deleteLiteratrueKeywordCountById(Long id);

    AjaxResult selectGroupLiteratrueKeywordCountList(LiteratrueKeywordCount literatrueKeywordCount);
}
