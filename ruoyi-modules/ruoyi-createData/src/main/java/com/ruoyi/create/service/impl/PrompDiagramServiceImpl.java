package com.ruoyi.create.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.PrompDiagramMapper;
import com.ruoyi.create.domain.PrompDiagram;
import com.ruoyi.create.service.IPrompDiagramService;

/**
 * prompt模板文生图相关推理参数Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
@Service
public class PrompDiagramServiceImpl implements IPrompDiagramService 
{
    @Autowired
    private PrompDiagramMapper prompDiagramMapper;

    /**
     * 查询prompt模板文生图相关推理参数
     * 
     * @param id prompt模板文生图相关推理参数主键
     * @return prompt模板文生图相关推理参数
     */
    @Override
    public PrompDiagram selectPrompDiagramById(Long id)
    {
        return prompDiagramMapper.selectPrompDiagramById(id);
    }

    /**
     * 查询prompt模板文生图相关推理参数列表
     * 
     * @param prompDiagram prompt模板文生图相关推理参数
     * @return prompt模板文生图相关推理参数
     */
    @Override
    public List<PrompDiagram> selectPrompDiagramList(PrompDiagram prompDiagram)
    {
        return prompDiagramMapper.selectPrompDiagramList(prompDiagram);
    }

    /**
     * 新增prompt模板文生图相关推理参数
     * 
     * @param prompDiagram prompt模板文生图相关推理参数
     * @return 结果
     */
    @Override
    public int insertPrompDiagram(PrompDiagram prompDiagram)
    {
        return prompDiagramMapper.insertPrompDiagram(prompDiagram);
    }

    /**
     * 修改prompt模板文生图相关推理参数
     * 
     * @param prompDiagram prompt模板文生图相关推理参数
     * @return 结果
     */
    @Override
    public int updatePrompDiagram(PrompDiagram prompDiagram)
    {
        return prompDiagramMapper.updatePrompDiagram(prompDiagram);
    }

    /**
     * 批量删除prompt模板文生图相关推理参数
     * 
     * @param ids 需要删除的prompt模板文生图相关推理参数主键
     * @return 结果
     */
    @Override
    public int deletePrompDiagramByIds(Long[] ids)
    {
        return prompDiagramMapper.deletePrompDiagramByIds(ids);
    }

    /**
     * 删除prompt模板文生图相关推理参数信息
     * 
     * @param id prompt模板文生图相关推理参数主键
     * @return 结果
     */
    @Override
    public int deletePrompDiagramById(Long id)
    {
        return prompDiagramMapper.deletePrompDiagramById(id);
    }
}
