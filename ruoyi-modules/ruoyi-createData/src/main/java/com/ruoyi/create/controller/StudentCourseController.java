package com.ruoyi.create.controller;


import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.Vo.CourseInfoVo;
import com.ruoyi.create.Vo.StudentAttendanceVo;
import com.ruoyi.create.domain.CourseInfo;
import com.ruoyi.create.domain.StudentAttendance;
import com.ruoyi.create.service.IStudentCourseService;
import com.ruoyi.create.utils.UserUtils;
import com.ruoyi.system.api.domain.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/student")
public class StudentCourseController extends BaseController {

    @Autowired
    private IStudentCourseService studentCourseService;

    @Autowired
    private UserUtils userUtils;

    /**
     * 获取学生课程列表
     */
    @RequestMapping("/getStudentCourseList")
    public TableDataInfo getStudentCourseList() {
        startPage();
        // 获取学生id
        SysUser sysUser = userUtils.getSysUser(SecurityUtils.getUserId());
        List<CourseInfoVo> courseInfoList = studentCourseService.getStudentCourseList(sysUser.getStudentId());
        return getDataTable(courseInfoList);
    }

    /**
     * 学生签到
     */
    @RequestMapping("/attendance/{courseId}")
    public AjaxResult attendance(@Valid @PathVariable("courseId") String courseId) {
        if (Objects.isNull(SecurityUtils.getUserId())) {
            return AjaxResult.error("请先登录");
        }
        String studentId = userUtils.getSysUser(SecurityUtils.getUserId()).getStudentId();
        if(Objects.isNull(studentId)){
            return AjaxResult.error("当前用户不是学生");
        }
        return AjaxResult.success(studentCourseService.updateStudentAttendance(courseId, studentId));
    }

    /**
     * 学生签到历史
     */
    @RequestMapping("/attendanceHistory")
    public TableDataInfo attendanceHistory() {
        startPage();
        // 获取学生id
        SysUser sysUser = userUtils.getSysUser(SecurityUtils.getUserId());
        List<StudentAttendanceVo> list = studentCourseService.selectStudentAttendanceList(sysUser.getStudentId());
        return getDataTable(list);
    }
}
