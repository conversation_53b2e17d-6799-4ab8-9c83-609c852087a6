package com.ruoyi.create.service.impl;

import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.domain.SysDept;
import com.ruoyi.create.domain.execl.*;
import com.ruoyi.create.domain.execl.Class;
import com.ruoyi.create.dto.UserDto;
import com.ruoyi.create.exception.CustomException;
import com.ruoyi.create.mapper.ExeclStuAndTeaSaveMapper;
import com.ruoyi.create.mapper.PresentationMapper;
import com.ruoyi.create.service.IExeclStuAndTeaSaveService;
import com.ruoyi.create.service.IPresentationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;

@Slf4j
@Service
public class ExeclStuAndTeaSaveServiceImpl implements IExeclStuAndTeaSaveService {

    @Resource
    ExeclStuAndTeaSaveMapper execlStuAndTeaSaveMapper;
    @Resource
    IPresentationService presentationService;

    private String BaseUniversityName="山东财经大学";
    private Long BaseUniversityId=1L;


    @Override
    public void delExeclPath(Execl execl) {
     deleteFile(new File(execl.getFilePath()));
    }
    // 文件删除方法
    private void deleteFile(File file) {
        try {
            if (file.exists() && !file.delete()) {
                throw new IOException("删除文件失败: " + file.getAbsolutePath());
            }
        } catch (Exception e) {
            // 记录异常日志，或者根据需求处理
            System.err.println("删除文件失败: " + e.getMessage());
            // 根据需要抛出自定义异常
            throw new CustomException("删除文件失败: " + file.getAbsolutePath(), e);
        }
    }

    @Override
    public void ececlDataSave2(Execl execl) {
       // String filePath = "C:\\Users\\<USER>\\Desktop\\execl\\教职工基础信息.xls";//学生基本信息  教职工基础信息
//        System.out.println("-------------------------");
        System.out.println(execl);
//        System.out.println("-------------------------");
        if (execl.getIsStu() > 0){
            if (execl.getIsStu() == 1){
                System.out.println(">0");
                System.out.println("1");
            }else {
                System.out.println(">0");
                System.out.println("2");
            }
            System.out.println(">0");
        }else {
            System.out.println("<0");

        }

    }

    @Override
    public void ececlDataSave(Execl execl) {
        this.getUniverNameByUser();
       if (execl.getIsStu() > 0){
           List<Student> extractInfoListS = getExtractInfoListS(execl);
           // 定义每个批次的大小
           int batchSize = 3000;

            // 计算需要执行多少个批次
           int totalBatches = (int) Math.ceil((double) extractInfoListS.size() / batchSize);

            // 遍历所有批次并执行插入或更新操作
           for (int i = 0; i < totalBatches; i++) {
               // 确定当前批次的开始和结束位置
               int fromIndex = i * batchSize;
               int toIndex = Math.min(fromIndex + batchSize, extractInfoListS.size());

               // 获取当前批次的数据
               List<Student> batch = extractInfoListS.subList(fromIndex, toIndex);

               // 调用方法插入或更新当前批次的数据
               execlStuAndTeaSaveMapper.insertOrUpdateBatchS(batch);
           }
           execlStuAndTeaSaveMapper.deleteByDeFalg(); //删除  标记已删除的用户
           if (execl.getIsAutoRegister() == 0 && execl.getIsAutoAuth() == 0){
               //注册 并认证
               if (execl.getIsStu() == 1){
                   insertOrUpdateBatchS(extractInfoListS);
               }else {
                   insertOrUpdateBatchSP(extractInfoListS);
               }
           }
           if (execl.getIsAutoRegister() == 0 && execl.getIsAutoAuth() == 1){
               // 仅注册
               registerS(extractInfoListS);
           }

       }else {
           List<Teacher> extractInfoListT = getExtractInfoListT(execl);
           execlStuAndTeaSaveMapper.insertOrUpdateBatchT(extractInfoListT);  //批量插入老师表
           execlStuAndTeaSaveMapper.deleteByDeFalg(); //删除  标记已删除的用户
           if (execl.getIsAutoRegister() == 0 && execl.getIsAutoAuth() == 0){
               insertOrUpdateBatchT(extractInfoListT);
           }
           if (execl.getIsAutoRegister() == 0 && execl.getIsAutoAuth() == 1){
               registerT(extractInfoListT);

           }

       }
    }



    public Long getUniverIdByUser(){
        String username = SecurityUtils.getUsername();
        List<String> roleKey = presentationService.selectUserRoleKeyByUserName(username);

        UserDto userDto = presentationService.selectUserByUserName(username);
        if(roleKey.contains("admin")){
            System.out.println("白名单");
            return Long.valueOf(1);
        }else if(roleKey.contains("apiece")) {
            if (userDto.getUniversityId() == null ){
                System.out.println("登陆人学校");
                return Long.valueOf(1);
            }
            return Long.valueOf(userDto.getUniversityId());
        }else{
            if (userDto.getUniversityId() == null || userDto.getCollegeId() == null || userDto.getMajorId() == null){
                System.out.println("登陆人学校学院专业部分为空");
                return Long.valueOf(1);
            }
            return Long.valueOf(userDto.getUniversityId());
        }
    }
    public void getUniverNameByUser(){
        Long universityId = getUniverIdByUser();
        String universityName = execlStuAndTeaSaveMapper.selectUniverNameById(universityId);
        this.BaseUniversityId=universityId;
        this.BaseUniversityName=universityName;
    }


    //获取数据总
    public void getExtractInfoList(Execl execl){

        try (FileInputStream fis = new FileInputStream(execl.getFilePath());
             Workbook workbook = WorkbookFactory.create(fis)) {

            // 获取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);
            // 获取第一行（标题行）
            Row firstRow = sheet.getRow(0);
            if (firstRow != null) {
                // 获取列名和对应的索引
                Map<String, Integer> columnMap = getColumnMap(firstRow);

                if (columnMap.containsKey("学号") && execl.getIsStu() == 0) {

                    if (columnMap.containsKey("层次")) {
                        System.out.println("研究生");
                    }
                        // 处理学生信息
                    System.out.println("学生信息");
                    List<Student> extractInfoListS = getExtractSInfoList(sheet, columnMap);
                    System.out.println("=======================");
                    //insertOrUpdateBatchS(extractInfoListS);  //java批量  插入学生表  并插入用户角色表
                    //execlStuAndTeaSaveMapper.insertOrUpdateBatchS(extractInfoListS);//批量插入学生表
                    //execlStuAndTeaSaveMapper.insertOrUpdateBatchUS(extractInfoListS);//批量插入用户表
                    // System.out.println(extractInfoListS);
//                    execlStuAndTeaSaveMapper.insertUserRoleBatchS(extractInfoListS);//批量插入用户角色表
                    System.out.println("=======================");
                    //System.out.println(extractInfoListS);
                    //FileUtil.writeListToFile(extractInfoListS, "C:\\Users\\<USER>\\Desktop\\execl\\output.txt");
//                    Student student =(Student) extractInfoListS.get(1);
//                    System.out.println(student);

                } else if (columnMap.containsKey("教工号") && execl.getIsStu() == 1) {
                    // 处理老师信息
                    System.out.println("老师信息");
                    List<Teacher> extractInfoListT = getExtractTInfoList(sheet, columnMap);
                    //execlStuAndTeaSaveMapper.insertOrUpdateBatchT(extractInfoListT);  //批量插入老师表

                    //execlStuAndTeaSaveMapper.insertOrUpdateBatchUT(extractInfoListT);   // 批量插入用户表
                    //insertOrUpdateBatchT(extractInfoListT);   //java批量  插入用户表  并插入用户角色表

//                    Teacher teacher =(Teacher) extractInfoListT.get(1);
//                    System.out.println(teacher);
                    //System.out.println(extractInfoListT);

                } else {
                    System.out.println("无法识别的类型");
                    throw new CustomException("文件格式不符合");
                }
            }

        } catch (IOException e) {
            e.printStackTrace();
        } catch (InvalidFormatException e) {
            e.printStackTrace();
        }
    }

    //区分 获取数据 学生
    public List<Student> getExtractInfoListS(Execl execl){

        try (FileInputStream fis = new FileInputStream(execl.getFilePath());
             Workbook workbook = WorkbookFactory.create(fis)) {

            // 获取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);
            // 获取第一行（标题行）
            Row firstRow = sheet.getRow(0);
            if (firstRow != null) {
                // 获取列名和对应的索引
                Map<String, Integer> columnMap = getColumnMap(firstRow);

                if (columnMap.containsKey("学号") && execl.getIsStu() > 0) {
                    System.out.println("学生信息");

                    List<Student> extractInfoListS = null;
                    if (columnMap.containsKey("层次") && execl.getIsStu() == 2) {
                        System.out.println("研究生");
                        extractInfoListS = getExtractSPInfoList(sheet, columnMap);
                    }else {
                        System.out.println("本科生");
                        extractInfoListS = getExtractSInfoList(sheet, columnMap);
                    }
                    // 处理学生信息
                    //System.out.println("=======================");
                    //insertOrUpdateBatchS(extractInfoListS);  //java批量  插入学生表  并插入用户角色表
                    //execlStuAndTeaSaveMapper.insertOrUpdateBatchS(extractInfoListS);//批量插入学生表
                    //execlStuAndTeaSaveMapper.insertOrUpdateBatchUS(extractInfoListS);//批量插入用户表
                    // System.out.println(extractInfoListS);
//                    execlStuAndTeaSaveMapper.insertUserRoleBatchS(extractInfoListS);//批量插入用户角色表
                    //System.out.println("=======================");
                    //System.out.println(extractInfoListS);
                    //FileUtil.writeListToFile(extractInfoListS, "C:\\Users\\<USER>\\Desktop\\execl\\output.txt");
//                    Student student =(Student) extractInfoListS.get(1);
//                    System.out.println(student);
                    return extractInfoListS;

                } else {
                    System.out.println("无法识别的类型");
                    throw new CustomException("文件格式不符合");
                }
            }

        } catch (IOException e) {
            e.printStackTrace();
        } catch (InvalidFormatException e) {
            e.printStackTrace();
        }
        return null;
    }

    //区分获取数据 老师
    public List<Teacher> getExtractInfoListT(Execl execl){

        try (FileInputStream fis = new FileInputStream(execl.getFilePath());
             Workbook workbook = WorkbookFactory.create(fis)) {

            // 获取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);
            // 获取第一行（标题行）
            Row firstRow = sheet.getRow(0);
            if (firstRow != null) {
                // 获取列名和对应的索引
                Map<String, Integer> columnMap = getColumnMap(firstRow);

               if (columnMap.containsKey("教工号") && execl.getIsStu() == 0) {
                    // 处理老师信息
                    System.out.println("老师信息");
                    List<Teacher> extractInfoListT = getExtractTInfoList(sheet, columnMap);
                    //execlStuAndTeaSaveMapper.insertOrUpdateBatchT(extractInfoListT);  //批量插入老师表

                    //execlStuAndTeaSaveMapper.insertOrUpdateBatchUT(extractInfoListT);   // 批量插入用户表
                    //insertOrUpdateBatchT(extractInfoListT);   //java批量  插入用户表  并插入用户角色表

//                    Teacher teacher =(Teacher) extractInfoListT.get(1);
//                    System.out.println(teacher);
                    //System.out.println(extractInfoListT);
                   return extractInfoListT;
                } else {
                    System.out.println("无法识别的类型");
                    throw new CustomException("文件格式不符合");
                }
            }

        } catch (IOException e) {
            e.printStackTrace();
        } catch (InvalidFormatException e) {
            e.printStackTrace();
        }
        return null;
    }


    //自动注册  未认证
    public void registerS(List<Student> studentList){
        execlStuAndTeaSaveMapper.insertOrUpdateBatchUS(studentList);
    }
    public void registerT(List<Teacher> teacherList){
        execlStuAndTeaSaveMapper.insertOrUpdateBatchUT(teacherList);
    }

    //自动认证 更新认证状态 插入用户角色表  注册
    //学生
    public void insertOrUpdateBatchS(List<Student> studentList){
        for (Student s:studentList) {
            Long deptID = insertOrUpdateDeptByUC(this.BaseUniversityName, s.getColleName());
            s.setDeptId(deptID);
            execlStuAndTeaSaveMapper.insertOrUpdateUS(s);
            execlStuAndTeaSaveMapper.insertUserRoleS(s.getUserId());
        }
    }
    //研究生
    public void insertOrUpdateBatchSP(List<Student> studentList){
        for (Student s:studentList) {
            Long deptID = insertOrUpdateDeptByUC(this.BaseUniversityName, s.getColleNameSP());
            s.setDeptId(deptID);
            execlStuAndTeaSaveMapper.insertOrUpdateUS(s);
            execlStuAndTeaSaveMapper.insertUserRoleSP(s.getUserId());
        }
    }
    //老师
    public void insertOrUpdateBatchT(List<Teacher> teacherList){
        for (Teacher t:teacherList) {
            Long deptID = insertOrUpdateDeptByUC(this.BaseUniversityName,t.getDeptName().equals("") ? t.getColleName() : t.getDeptName());
            t.setDeptId(deptID);
            execlStuAndTeaSaveMapper.insertOrUpdateUT(t);
            execlStuAndTeaSaveMapper.insertUserRoleT(t.getUserId());
        }
    }

    public Long insertOrUpdateDeptByUC(String univerName,String colleName){
        SysDept deptP = execlStuAndTeaSaveMapper.selectDeptInit(SysDept.builder()
                .ancestors(String.valueOf(0))
                .parentId(0L).build());
        SysDept sysDeptU = execlStuAndTeaSaveMapper.selectDeptByNameAndPId(SysDept.builder()
                .deptName(univerName)
                .parentId(deptP.getDeptId()).build());
        SysDept buildU = SysDept.builder()
                .deptName(univerName).parentId(deptP.getDeptId())
                .ancestors(deptP.getParentId() + "," + deptP.getDeptId())
                .orderNum(0).status("0").delFlag("0")
                .createBy(SecurityUtils.getUsername()).createTime(new Date())
                .build();
        if (sysDeptU == null){
            int i = execlStuAndTeaSaveMapper.insertOrUpdateDept(buildU);
            if(i <= 0){
                throw new RuntimeException("插入失败");
            }

            SysDept sysDeptC = execlStuAndTeaSaveMapper.selectDeptByNameAndPId(SysDept.builder()
                    .deptName(colleName)
                    .parentId(buildU.getDeptId()).build());
            SysDept buildC = SysDept.builder()
                    .deptName(colleName).parentId(buildU.getDeptId())
                    .ancestors(deptP.getParentId() + "," + deptP.getDeptId()+","+buildU.getDeptId())
                    .orderNum(0).status("0").delFlag("0")
                    .createBy(SecurityUtils.getUsername()).createTime(new Date())
                    .build();
            if (sysDeptC == null){
                int j = execlStuAndTeaSaveMapper.insertOrUpdateDept(buildC);
                if(j <= 0){
                    throw new RuntimeException("插入失败");
                }
                return buildC.getDeptId();
            }
            return sysDeptC.getDeptId();


        }else {
            SysDept sysDeptC = execlStuAndTeaSaveMapper.selectDeptByNameAndPId(SysDept.builder()
                    .deptName(colleName)
                    .parentId(sysDeptU.getDeptId())
                    .build());
            SysDept buildC = SysDept.builder()
                    .deptName(colleName).parentId(sysDeptU.getDeptId()).ancestors(deptP.getParentId() + "," + deptP.getDeptId()+","+sysDeptU.getDeptId())
                    .orderNum(0).status("0").delFlag("0")
                    .createBy(SecurityUtils.getUsername()).createTime(new Date())
                    .build();
            if (sysDeptC == null){
                int m = execlStuAndTeaSaveMapper.insertOrUpdateDept(buildC);
                if(m <= 0){
                    throw new RuntimeException("插入失败");
                }
                return buildC.getDeptId();
            }

            return sysDeptC.getDeptId();

        }

    }

    //自动认证总  没有角色关系
    public void authenticationS(List<Student> studentList){
//        for (Student s:studentList) {
//            String userId = execlStuAndTeaSaveMapper.selectUserIdByUserName(s.getStudentId());
//            execlStuAndTeaSaveMapper.updateAuthStatusByUserId(User.builder().authStatus("2").userId(Long.valueOf(userId)).build());
//        }
        execlStuAndTeaSaveMapper.insertUserRoleBatchS(studentList);
    }
    public void authenticationSP(List<Student> studentList){
        execlStuAndTeaSaveMapper.insertUserRoleBatchSP(studentList);
    }
    public void authenticationT(List<Teacher> teacherList){
        execlStuAndTeaSaveMapper.insertOrUpdateBatchT(teacherList);
    }

    //获取学院 没有创建
    public  void getCollS(Student student){
        //System.out.println(student.getColleName());
        List<Long> collIds = execlStuAndTeaSaveMapper.selectCollIdByName(student.getColleName(),this.BaseUniversityId);
        if (collIds.size()==0 || collIds==null){
            Coll build = new Coll();
            build.setColleName(student.getColleName());
            build.setUniverId(this.BaseUniversityId);
            build.setCreateBy(SecurityUtils.getUsername());
            build.setCreateTime(new Date());
            execlStuAndTeaSaveMapper.insertColl(build);
            collIds.add(build.getId());
            student.setColleId(build.getId());
        }else {
            student.setColleId(collIds.get(0));
        }
    }
    //获取学院 没有创建
    public  void getCollSP(Student student){
        //System.out.println(student.getColleName());
        List<Long> collIds = execlStuAndTeaSaveMapper.selectCollIdByName(student.getColleName(),this.BaseUniversityId);
        if (collIds.size()==0 || collIds==null){
            Coll build = new Coll();
            build.setColleName(student.getColleNameSP());
            build.setUniverId(this.BaseUniversityId);
            build.setCreateBy(SecurityUtils.getUsername());
            build.setCreateTime(new Date());
            execlStuAndTeaSaveMapper.insertColl(build);
            collIds.add(build.getId());
            student.setColleId(build.getId());
        }else {
            student.setColleId(collIds.get(0));
        }
    }

    //获取专业 链接学院 没有创建
    public  void getMajS(Student student){
        // System.out.println(student.getColleName());
        List<Long> majIds = execlStuAndTeaSaveMapper.selectMajIdByName(student.getMajorName(),this.BaseUniversityId);
        if (majIds.size()==0 || majIds==null){
            getCollS(student);
            Major build = Major.builder()
                    .majorName(student.getMajorName())
                    .univerId(this.BaseUniversityId)
                    .colleId(student.getColleId())
                    .createBy(SecurityUtils.getUsername())
                    .createTime(new Date())
                    .build();
            execlStuAndTeaSaveMapper.insertMajor(build);
            majIds.add(build.getId());
            student.setMajorId(build.getId());
        }else {
            student.setMajorId(majIds.get(0));
        }
    }
    //获取专业 链接学院 没有创建
    public  void getMajSP(Student student){
        // System.out.println(student.getColleName());
        List<Long> majIds = execlStuAndTeaSaveMapper.selectMajIdByName(student.getMajorName(),this.BaseUniversityId);
        if (majIds.size()==0 || majIds==null){
            getCollSP(student);
            Major build = Major.builder()
                    .majorName(student.getMajorName())
                    .univerId(this.BaseUniversityId)
                    .colleId(student.getColleId())
                    .createBy(SecurityUtils.getUsername())
                    .createTime(new Date())
                    .build();
            execlStuAndTeaSaveMapper.insertMajor(build);
            majIds.add(build.getId());
            student.setMajorId(build.getId());
        }else {
            student.setMajorId(majIds.get(0));
        }
    }
    //获取班级 链接学院 专业 没有创建
    public  void getClaS(Student student){
        // System.out.println(student.getColleName());
        List<Long> claIds = execlStuAndTeaSaveMapper.selectClaIdByName(student.getClassName(),this.BaseUniversityId);
        if (claIds.size()==0 || claIds==null){
            getMajS(student);
            getCollS(student);
            Class build = Class.builder()
                    .className(student.getClassName())
                    .univerId(this.BaseUniversityId)
                    .colleId(student.getColleId())
                    .majorId(student.getMajorId())
                    .createBy(SecurityUtils.getUsername())
                    .createTime(new Date())
                    .build();
            execlStuAndTeaSaveMapper.insertCla(build);
            claIds.add(build.getId());
            student.setClassId(build.getId());
        }else {
            student.setClassId(claIds.get(0));
        }
    }
    //获取学院 没有创建
    public  void getCollT(Teacher teacher){
        //System.out.println(student.getColleName());
        List<Long> collIds = execlStuAndTeaSaveMapper.selectCollIdByName(teacher.getColleName(),this.BaseUniversityId);
        if (collIds.size()==0 || collIds==null){
            Coll build = new Coll();
            build.setColleName(teacher.getColleName());
            build.setUniverId(this.BaseUniversityId);
            build.setCreateBy(SecurityUtils.getUsername());
            build.setCreateTime(new Date());
            execlStuAndTeaSaveMapper.insertColl(build);
            collIds.add(build.getId());
            teacher.setColleId(build.getId());
        }else {
            teacher.setColleId(collIds.get(0));
        }
    }



    public  List<Map<String, String>> getExtractInfoList(Sheet sheet,Map<String, Integer> columnMap,boolean isSt){
        List<Map<String, String>> list=new ArrayList<>();
        if (isSt){
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Map<String, String> studentInfo = extractInfoS(sheet.getRow(i), columnMap);
                list.add(studentInfo);
            }
        }else {
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Map<String, String> studentInfo = extractInfoT(sheet.getRow(i), columnMap);
                list.add(studentInfo);
            }
        }


        return list;
    }

    //学生信息补充
    public List<Student> getExtractSInfoList(Sheet sheet, Map<String, Integer> columnMap){
        List<Student> list=new ArrayList<>();
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            boolean isRowValid = true;
            if (row != null && row.getCell(1) != null) {
                int m=1;
                for (int j = 0; j < row.getLastCellNum(); j++) {
                    Cell cell = row.getCell(j);
                    if (cell == null || getCellValue(cell).trim().isEmpty()) {
                        m++;
                    }
                    if (m==row.getLastCellNum()){
                        isRowValid=false;
                        break;
                    }
                }
                if (!isRowValid) {
                    break;
                }
                System.out.println("行索引: " + i);
                Student studentInfo = extractStudentInfo(sheet.getRow(i), columnMap);
                studentInfo.setSexMark(studentInfo.getSex().equals("男") ? 0 : 1);
                studentInfo.setSchoolStatus(studentInfo.getSchoolStatusStr().equals("离校") ? 0 : 1);
                studentInfo.setStudentStatus(studentInfo.getStudentStatusStr().equals("无学籍") ? 0 : 1);
                studentInfo.setStudentCurrentStatus(studentInfo.getStudentCurrentStatusStr() == "休学" ? 0 : 1);
                studentInfo.setCreateBy(SecurityUtils.getUsername());
                studentInfo.setCreateTime(new Date());
                studentInfo.setUniverId(this.BaseUniversityId);
                studentInfo.setUniverName(this.BaseUniversityName);
                getClaS(studentInfo);
                getMajS(studentInfo);
                getCollS(studentInfo);
                list.add(studentInfo);
            } else {
                System.out.println("行索引: " + i + " 是空的");
            }
            if (!isRowValid) {
                break;
            }
        }
        return list;
    }
    public List<Student> getExtractSPInfoList(Sheet sheet, Map<String, Integer> columnMap){
        List<Student> list=new ArrayList<>();
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            boolean isRowValid = true;
            if (row != null && row.getCell(1) != null) {
                int m=1;
                for (int j = 0; j < row.getLastCellNum(); j++) {
                    Cell cell = row.getCell(j);
                    if (cell == null || getCellValue(cell).trim().isEmpty()) {
                       m++;
                    }
                    if (m==row.getLastCellNum()){
                        isRowValid=false;
                        break;
                    }
                }
                if (!isRowValid) {
                    break;
                }
                System.out.println("行索引: " + i);
                Student studentInfo = extractStudentInfo(sheet.getRow(i), columnMap);
                studentInfo.setSexMark(studentInfo.getSex().equals("男") ? 0 : 1);
                studentInfo.setSchoolStatus(studentInfo.getSchoolStatusStr().equals("离校") ? 0 : 1);
                studentInfo.setStudentStatus(studentInfo.getStudentStatusStr().equals("无学籍") ? 0 : 1);
                studentInfo.setStudentCurrentStatus(studentInfo.getStudentCurrentStatusStr() == "休学" ? 0 : 1);
                studentInfo.setColleName(studentInfo.getColleNameSP());
                studentInfo.setCurrentGrade(studentInfo.getCurrentGradeSP());
                studentInfo.setCreateBy(SecurityUtils.getUsername());
                studentInfo.setCreateTime(new Date());
                studentInfo.setUniverId(this.BaseUniversityId);
                studentInfo.setUniverName(this.BaseUniversityName);
                getMajSP(studentInfo);
                getCollSP(studentInfo);
                list.add(studentInfo);
            } else {
                System.out.println("行索引: " + i + " 是空的");
            }
            if (!isRowValid) {
                break;
            }

        }
        return list;
    }

    public List<Teacher> getExtractTInfoList(Sheet sheet, Map<String, Integer> columnMap){
        List<Teacher> list=new ArrayList<>();
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            boolean isRowValid = true;
            if (row != null && row.getCell(1) != null) {
                int m=1;
                for (int j = 0; j < row.getLastCellNum(); j++) {
                    Cell cell = row.getCell(j);
                    if (cell == null || getCellValue(cell).trim().isEmpty()) {
                        m++;
                    }
                    if (m==row.getLastCellNum()){
                        isRowValid=false;
                        break;
                    }
                }
                if (!isRowValid) {
                    break;
                }
                System.out.println("行索引: " + i);
                Teacher teacher = extractTeacherInfo(sheet.getRow(i), columnMap);
                getCollT(teacher);
                list.add(teacher);
            } else {
                System.out.println("行索引: " + i + " 是空的");
            }
            if (!isRowValid) {
                break;
            }
        }
        return list;
    }


//----------------------------------------------------------------------
    // 提取信息，基于动态的列名索引
    public  Map<String, String> extractInfoT(Row row, Map<String, Integer> columnMap) {
        Map<String, String> info = new HashMap<>();
        // 根据列名提取相关信息
        info.put("教工号", getCellValue(row, columnMap.get("教工号")));
        info.put("姓名", getCellValue(row, columnMap.get("姓名")));
        info.put("教师所属单位", getCellValue(row, columnMap.get("教师所属单位")));
        info.put("教师所属学院", getCellValue(row, columnMap.get("教师所属学院")));
        return info;
    }

    public Teacher extractTeacherInfo(Row row, Map<String, Integer> columnMap) {

        return Teacher.builder()
                .teacherId(Long.valueOf(getCellValue(row, columnMap.get("教工号"))))
                .teacherName(getCellValue(row, columnMap.get("姓名")))
                .univerId(this.BaseUniversityId)
                .univerName(this.BaseUniversityName)
                .deptName(getCellValue(row, columnMap.get("教师所属单位")).equals("") ? getCellValue(row, columnMap.get("教师所属学院")) : getCellValue(row, columnMap.get("教师所属单位")))
                .colleName(getCellValue(row, columnMap.get("教师所属学院")))
                .build();
    }


    public  Map<String, String> extractInfoS(Row row, Map<String, Integer> columnMap) {
        Map<String, String> info = new HashMap<>();

        // 根据列名提取相关信息
        info.put("学号", getCellValue(row, columnMap.get("学号")));
        info.put("姓名", getCellValue(row, columnMap.get("姓名")));
        info.put("性别", getCellValue(row, columnMap.get("性别")));

        info.put("院系", getCellValue(row, columnMap.get("院系")));
        info.put("专业名称", getCellValue(row, columnMap.get("专业名称")));
        info.put("班级", getCellValue(row, columnMap.get("班级")));

        info.put("学制", getCellValue(row, columnMap.get("学制")));
        info.put("当前所在级", getCellValue(row, columnMap.get("当前所在级")));

        info.put("在校状态", getCellValue(row, columnMap.get("在校状态")));
        info.put("学籍状态", getCellValue(row, columnMap.get("学籍状态")));
        info.put("学生当前状态", getCellValue(row, columnMap.get("学生当前状态")));

        info.put("层次", getCellValue(row, columnMap.get("层次")));
        return info;
    }

    public Student extractStudentInfo(Row row, Map<String, Integer> columnMap) {
        return Student.builder()
                .studentId(getCellValue(row, columnMap.get("学号")))
                .studentName(getCellValue(row, columnMap.get("姓名")))
                .sex(getCellValue(row, columnMap.get("性别")))
                .univerId(this.BaseUniversityId)
                .univerName(this.BaseUniversityName)
                .colleName(getCellValueOrDefault(row, columnMap.get("院系"),"0"))
                .colleNameSP(getCellValueOrDefault(row, columnMap.get("学院名称"),"0"))
                .majorName(getCellValue(row, columnMap.get("专业名称")))
                .className(getCellValue(row, columnMap.get("班级")))
                .educationalSystem(parseToInt(getCellValue(row, columnMap.get("学制"))))
                .currentGrade(parseToInt(getCellValueOrDefault(row, columnMap.get("当前所在级"),"0")))
                .currentGradeSP(parseToInt(getCellValueOrDefault(row, columnMap.get("年级"),"0")))
                .studentStatusStr(getCellValue(row, columnMap.get("在校状态")))
                .schoolStatusStr(getCellValue(row, columnMap.get("学籍状态")))
                .studentCurrentStatusStr(getCellValue(row, columnMap.get("学生当前状态")))
                .gradation(getCellValue(row, columnMap.get("层次")))
                .build();
    }
//----------------------------------------------------------------------

    public String getCellValueOrDefault(Row row, Integer cellIndex, String defaultValue) {
        String value = getCellValue(row, cellIndex);
        // 如果值为空或者无效，返回默认值
        if (value == null || value.trim().isEmpty()) {
            return defaultValue;
        }
        return value;
    }


    public int parseToInt(String str) {
        try {
            if (str == null || str.trim().isEmpty()) {
                // 如果字符串为空或null，返回一个默认值
                return 0; // 或者根据你的业务逻辑返回其他默认值
            }
            // 尝试将字符串解析为 Double 并转换为整数
            double value = Double.parseDouble(str);
            return (int) value;  // 去掉小数部分
        } catch (NumberFormatException e) {
            // 如果无法解析，打印异常并返回默认值
            System.out.println("Invalid number format: " + str);
            return 0;  // 根据你的需求返回一个默认值
        }
    }


    // 获取第一行的列名和对应的索引
    public  Map<String, Integer> getColumnMap(Row firstRow) {
        Map<String, Integer> columnMap = new HashMap<>();
        for (Cell cell : firstRow) {
            String columnName = cell.getStringCellValue();
            if (columnName != null && !columnName.isEmpty()) {
                columnMap.put(columnName, cell.getColumnIndex());
            }
        }
        return columnMap;
    }

    // 获取单元格的值
    public  String getCellValue2(Row row, Integer cellIndex) {
        if (cellIndex != null && row != null) {
            Cell cell = row.getCell(cellIndex);
            if (cell != null) {
                return cell.getStringCellValue();
            }
        }
        return "";
    }
    //poi 5.23
//    public String getCellValue(Row row, Integer cellIndex) {
//        if (cellIndex != null && row != null) {
//            Cell cell = row.getCell(cellIndex);
//            if (cell != null) {
//                switch (cell.getCellType()) {
//                    case STRING:
//                        return cell.getStringCellValue();
//                    case NUMERIC:
//                        // 如果你需要将数值转换为字符串
//                        return String.valueOf(cell.getNumericCellValue());
//                    case BOOLEAN:
//                        return String.valueOf(cell.getBooleanCellValue());
//                    case FORMULA:
//                        // 如果单元格是公式，你可以选择使用以下代码处理
//                        return cell.getCellFormula();
//                    default:
//                        return "";
//                }
//            }
//        }
//        return "";
//    }
    public String getCellValue(Row row, Integer cellIndex) {
        if (cellIndex != null && row != null) {
            Cell cell = row.getCell(cellIndex);
            if (cell != null) {
                switch (cell.getCellType()) {
                    case Cell.CELL_TYPE_STRING:
                        return cell.getStringCellValue();
                    case Cell.CELL_TYPE_NUMERIC:
                        // 判断是否为日期类型
                        if (DateUtil.isCellDateFormatted(cell)) {
                            return cell.getDateCellValue().toString();
                        }
                        long numericValue = (long) cell.getNumericCellValue();
                        return String.valueOf(numericValue);
                    case Cell.CELL_TYPE_BOOLEAN:
                        return String.valueOf(cell.getBooleanCellValue());
                    case Cell.CELL_TYPE_FORMULA:
                        return cell.getCellFormula();
                    default:
                        return "";
                }
            }
        }
        return "";
    }
    private static String getCellValue(Cell cell) {
            if (cell != null) {
                switch (cell.getCellType()) {
                    case Cell.CELL_TYPE_STRING:
                        return cell.getStringCellValue();
                    case Cell.CELL_TYPE_NUMERIC:
                        // 判断是否为日期类型
                        if (DateUtil.isCellDateFormatted(cell)) {
                            return cell.getDateCellValue().toString();
                        }
                        long numericValue = (long) cell.getNumericCellValue();
                        return String.valueOf(numericValue);
                    case Cell.CELL_TYPE_BOOLEAN:
                        return String.valueOf(cell.getBooleanCellValue());
                    case Cell.CELL_TYPE_FORMULA:
                        return cell.getCellFormula();
                    default:
                        return "";
                }
            }
        return "";
    }
}
