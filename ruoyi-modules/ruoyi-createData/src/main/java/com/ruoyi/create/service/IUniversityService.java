package com.ruoyi.create.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.create.domain.University;

/**
 * 学校信息Service接口
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
public interface IUniversityService extends IService<University>
{
    /**
     * 查询学校信息
     *
     * @param id 学校信息主键
     * @return 学校信息
     */
    public University selectUniversityById(Long id);

    /**
     * 查询学校信息列表
     *
     * @param university 学校信息
     * @return 学校信息集合
     */
    public List<University> selectUniversityList(University university);

    /**
     * 新增学校信息
     *
     * @param university 学校信息
     * @return 结果
     */
    public int insertUniversity(University university);

    /**
     * 修改学校信息
     *
     * @param university 学校信息
     * @return 结果
     */
    public int updateUniversity(University university);

    /**
     * 批量删除学校信息
     *
     * @param ids 需要删除的学校信息主键集合
     * @return 结果
     */
    public int deleteUniversityByIds(Long[] ids);

    /**
     * 删除学校信息信息
     *
     * @param id 学校信息主键
     * @return 结果
     */
    public int deleteUniversityById(Long id);

    List<University> selectUniversityListAll(Long roleId);

    University getUniversityInfo(University university);

    List<University> getUniversityAllUseForHm();
    List<University> getUniversityUseForHm();
}
