package com.ruoyi.create.controller;

import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.create.domain.ExternalCertification;
import com.ruoyi.create.service.IExternalCertificationService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/externalCertification")
public class ExternalCertificationController extends BaseController {
    @Resource
    private IExternalCertificationService externalCertificationService;

    @PostMapping("/add")
    public AjaxResult ExternalCertification(@RequestBody ExternalCertification externalCertification){
      return success(externalCertificationService.add(externalCertification));
    }

    @PostMapping("/checkStuOrTea")
    public AjaxResult checkStuOrTea(@RequestBody ExternalCertification externalCertification){
        return externalCertificationService.checkStuOrTea(externalCertification);
    }
}
