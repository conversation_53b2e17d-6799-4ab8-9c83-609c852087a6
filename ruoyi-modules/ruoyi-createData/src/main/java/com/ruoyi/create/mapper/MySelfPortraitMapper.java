package com.ruoyi.create.mapper;

import com.ruoyi.create.domain.MySelfPortrait;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


@Mapper
public interface MySelfPortraitMapper {

        //        为画像表添加分析预测
        int addOrUpdateYuceFenxi(MySelfPortrait myselfPortrait);
        //        查询画像信息
        MySelfPortrait selectById(String studentId);

        // 改成 update
        void updateByStudentId(MySelfPortrait m);

         void insert(MySelfPortrait m);





}