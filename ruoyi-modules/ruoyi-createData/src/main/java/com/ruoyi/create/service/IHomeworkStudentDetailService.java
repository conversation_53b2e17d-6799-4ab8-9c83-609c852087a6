package com.ruoyi.create.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.create.domain.Homework;
import com.ruoyi.create.domain.HomeworkStudentDetail;

/**
 * 学生作业答案详情Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-07
 */
public interface IHomeworkStudentDetailService extends IService<HomeworkStudentDetail>
{
    /**
     * 查询学生作业答案详情
     * 
     * @param id 学生作业答案详情主键
     * @return 学生作业答案详情
     */
    public HomeworkStudentDetail selectHomeworkStudentDetailById(Long id);

    /**
     * 查询学生作业答案详情列表
     * 
     * @param homeworkStudentDetail 学生作业答案详情
     * @return 学生作业答案详情集合
     */
    public List<HomeworkStudentDetail> selectHomeworkStudentDetailList(HomeworkStudentDetail homeworkStudentDetail);

    /**
     * 新增学生作业答案详情
     * 
     * @param homeworkStudentDetail 学生作业答案详情
     * @return 结果
     */
    public int insertHomeworkStudentDetail(HomeworkStudentDetail homeworkStudentDetail);

    /**
     * 修改学生作业答案详情
     * 
     * @param homeworkStudentDetail 学生作业答案详情
     * @return 结果
     */
    public int updateHomeworkStudentDetail(HomeworkStudentDetail homeworkStudentDetail);

    /**
     * 批量删除学生作业答案详情
     * 
     * @param ids 需要删除的学生作业答案详情主键集合
     * @return 结果
     */
    public int deleteHomeworkStudentDetailByIds(Long[] ids);

    /**
     * 删除学生作业答案详情信息
     * 
     * @param id 学生作业答案详情主键
     * @return 结果
     */
    public int deleteHomeworkStudentDetailById(Long id);

    AjaxResult insertHomeworkStudentDetailList(List<HomeworkStudentDetail> homeworkStudentDetail);


    List<HomeworkStudentDetail> selectHomeworkStudentDetailListAll(HomeworkStudentDetail homeworkStudentDetail);
}
