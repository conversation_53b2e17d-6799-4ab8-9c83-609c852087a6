package com.ruoyi.create.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.CourseVideo;
import com.ruoyi.create.service.ICourseVideoService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 课程视频Controller
 * 
 * <AUTHOR>
 * @date 2024-12-26
 */
@RestController
@RequestMapping("/video")
public class CourseVideoController extends BaseController
{
    @Autowired
    private ICourseVideoService courseVideoService;

    /**
     * 查询课程视频列表
     */
    @RequiresPermissions("create:video:list")
    @GetMapping("/list")
    public TableDataInfo list(CourseVideo courseVideo)
    {
        startPage();
        List<CourseVideo> list = courseVideoService.selectCourseVideoList(courseVideo);
        return getDataTable(list);
    }

    /**
     * 学生查询课程视频列表
     */
    @GetMapping("/student/list")
    public TableDataInfo studentList(CourseVideo courseVideo)
    {
        startPage();
        List<CourseVideo> list = courseVideoService.selectStudentCourseVideoList(courseVideo);
        return getDataTable(list);
    }

    /**
     * 导出课程视频列表
     */
    @RequiresPermissions("create:video:export")
    @Log(title = "课程视频", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CourseVideo courseVideo)
    {
        List<CourseVideo> list = courseVideoService.selectCourseVideoList(courseVideo);
        ExcelUtil<CourseVideo> util = new ExcelUtil<CourseVideo>(CourseVideo.class);
        util.exportExcel(response, list, "课程视频数据");
    }

    /**
     * 获取课程视频详细信息
     */
    @RequiresPermissions("create:video:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(courseVideoService.selectCourseVideoById(id));
    }

    /**
     * 新增课程视频
     */
    @RequiresPermissions("create:video:add")
    @Log(title = "课程视频", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CourseVideo courseVideo)
    {
        return toAjax(courseVideoService.insertCourseVideo(courseVideo));
    }

    /**
     * 修改课程视频
     */
    @RequiresPermissions("create:video:edit")
    @Log(title = "课程视频", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CourseVideo courseVideo)
    {
        return toAjax(courseVideoService.updateCourseVideo(courseVideo));
    }

    /**
     * 删除课程视频
     */
    @RequiresPermissions("create:video:remove")
    @Log(title = "课程视频", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(courseVideoService.deleteCourseVideoByIds(ids));
    }
}
