package com.ruoyi.create.service;

import java.util.List;
import com.ruoyi.create.domain.TextbookKeywordData;

/**
 * 知识图谱-教材关键词数据Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-09
 */
public interface ISTextbookKeywordDataService 
{
    /**
     * 查询知识图谱-教材关键词数据
     * 
     * @param id 知识图谱-教材关键词数据主键
     * @return 知识图谱-教材关键词数据
     */
    public TextbookKeywordData selectSTextbookKeywordDataById(Long id);

    /**
     * 查询知识图谱-教材关键词数据列表
     * 
     * @param sTextbookKeywordData 知识图谱-教材关键词数据
     * @return 知识图谱-教材关键词数据集合
     */
    public List<TextbookKeywordData> selectSTextbookKeywordDataList(TextbookKeywordData sTextbookKeywordData);

    /**
     * 新增知识图谱-教材关键词数据
     * 
     * @param sTextbookKeywordData 知识图谱-教材关键词数据
     * @return 结果
     */
    public int insertSTextbookKeywordData(TextbookKeywordData sTextbookKeywordData);

    /**
     * 修改知识图谱-教材关键词数据
     * 
     * @param sTextbookKeywordData 知识图谱-教材关键词数据
     * @return 结果
     */
    public int updateSTextbookKeywordData(TextbookKeywordData sTextbookKeywordData);

    /**
     * 批量删除知识图谱-教材关键词数据
     * 
     * @param ids 需要删除的知识图谱-教材关键词数据主键集合
     * @return 结果
     */
    public int deleteSTextbookKeywordDataByIds(Long[] ids);

    /**
     * 删除知识图谱-教材关键词数据信息
     * 
     * @param id 知识图谱-教材关键词数据主键
     * @return 结果
     */
    public int deleteSTextbookKeywordDataById(Long id);
}
