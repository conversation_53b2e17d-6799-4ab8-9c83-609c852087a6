package com.ruoyi.create.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.Vo.AmbitTeacherVo;
import com.ruoyi.create.Vo.CourseStatisticsVo;
import com.ruoyi.create.domain.ClassInfo;
import com.ruoyi.create.domain.CourseManagement;
import com.ruoyi.create.domain.StudentCourse;
import com.ruoyi.create.dto.CourseManagementDto;
import com.ruoyi.create.mapper.CourseManagementMapper;
import com.ruoyi.create.mapper.CourseMaterialsMapper;
import com.ruoyi.create.service.ICourseManagementService;
import com.ruoyi.create.utils.Snowflake;
import com.ruoyi.create.utils.UserUtils;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysDictData;
import com.ruoyi.system.api.domain.SysFileInfo;
import com.ruoyi.system.api.model.LoginUser;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.ruoyi.common.core.utils.PageUtils.startPage;

/**
 * 课程管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-11
 */
@Service
public class CourseManagementServiceImpl extends ServiceImpl<CourseManagementMapper, CourseManagement> implements ICourseManagementService {
	@Resource
	private CourseMaterialsMapper courseMaterialsMapper;

	@Resource
	private CourseManagementMapper courseManagementMapper;

	@Resource
	private RemoteUserService remoteUserService;

	@Resource
	private RedisService redisService;
	@Resource
	private RemoteFileService remoteFileService;
	@Resource
	private UserUtils userUtils;

	@Value("${logo.path.file-path-win}")
	String localFilePathWin;// D:/ruoyi/uploadDataPath

	@Value("${logo.path.file-path-linux}")
	String localFilePathLinux;// /home/<USER>/uploadDataPath

	/**
	 * 查询课程管理
	 *
	 * @param id 课程管理主键
	 * @return 课程管理
	 */
	@Override
	public CourseManagement selectCourseManagementById(Long id) {
		return courseManagementMapper.selectCourseManagementById(id);
	}

	/**
	 * 查询课程管理列表
	 *
	 * @param courseManagement 课程管理
	 * @return 课程管理
	 */
	@Override
	public List<CourseManagement> selectCourseManagementList(CourseManagement courseManagement) {
		//工号（教师ID）
		R<LoginUser> userAndRole = remoteUserService.getUserAndRole(SecurityUtils.getUserId(), SecurityConstants.INNER);
		LoginUser data2 = userAndRole.getData();
		String jobId = data2.getSysUser().getJobId();
		Snowflake snowflake = new Snowflake(1, 1);
		//如果是不是管理员只能查询对应学校的
		if (!SecurityUtils.isAdmin(SecurityUtils.getUserId())){
			Long universityId = SecurityUtils.getLoginUser().getSysUser().getUniversityId();
			courseManagement.setUniversityId(universityId);
		}
		//如果为超级管理员则查出所有课程
		if (data2.getSysUser().isAdmin()) {
			//使用getListAll
			return getListAll(jobId, courseManagement.getCourseName(), snowflake);
		}
		//通过工号查询出课程组内所有教师的工号
		List<String> kcglList = courseManagementMapper.selectKcglList(jobId,courseManagement.getUniversityId());
		//通过返回的值是否为空来判断是否加入课程组
		//如果不为空则使用getList
		if (kcglList != null && kcglList.size() > 0) {


			return getListByJobIds(kcglList,courseManagement.getCourseName(),snowflake);
//			List<CourseManagement> courseManagementList = new ArrayList<>();
//			for (String s : kcglList){
//				List<CourseManagement> courseManagementList1 = getList(s, snowflake);
//					courseManagementList.addAll(courseManagementList1);
//			}
//			return courseManagementList;
		}
		//如果查出为空代表老师没有加入课程组，则仅仅使用老师自分工号查询
		List<CourseManagement> courseManagementList = getList(jobId,courseManagement.getCourseName(), snowflake,courseManagement.getUniversityId());
		return courseManagementList;
	}

	@Nullable
	private List<CourseManagement> getListByJobIds(List<String> jobIds, String courseName,Snowflake snowflake) {
		startPage();
		//查看课程
		List<CourseManagement> courseManagementList = courseManagementMapper.selectByJobIds(jobIds,courseName);
		//查看学期
		List<CourseManagement> termList = courseManagementMapper.selectTermByJobIds(jobIds);
		//查看班级
		List<CourseManagement> studentClassList = courseManagementMapper.selectStudentClassByJobIds(jobIds);
		if (courseManagementList != null) {
			termList.forEach(course ->{
				List<CourseManagement> studentClassList1 = new ArrayList<>();
				for (CourseManagement management : studentClassList) {
					if(course.getTeacherId().equals(management.getTeacherId())&&
							course.getTerm().equals(management.getTerm())&&
							course.getCourseName().equals(management.getCourseName())){
						studentClassList1.add(management);
					}
				}
				course.setId(snowflake.generateId());
				course.setChildren(studentClassList1);
			} );
			courseManagementList.forEach(course -> {
				List<CourseManagement> termList1 = new ArrayList<>();
				for (CourseManagement management : termList) {
					if(course.getTeacherId().equals(management.getTeacherId())&&
							course.getCourseName().equals(management.getCourseName())){
						management.setId(snowflake.generateId());
						termList1.add(management);
					}
				}
				course.setId(snowflake.generateId());
				course.setChildren(termList1);
			});
		}
		return courseManagementList;
	}
	@Nullable
	private List<CourseManagement> getList(String jobId, String courseName, Snowflake snowflake,Long universityId) {
		// 查询课程、学期、班级
		List<CourseManagement> courseList = courseManagementMapper.selectByJobId(jobId, courseName,universityId);
		List<CourseManagement> termList = courseManagementMapper.selectTermByJobId(jobId,universityId);
		List<CourseManagement> classList = courseManagementMapper.selectStudentClassByJobId(jobId,universityId);

		if (courseList == null) {
			return Collections.emptyList();
		}

		// 1. 组装学期下的班级
		termList.forEach(term -> {
			List<CourseManagement> children = classList.stream()
				.filter(cls -> Objects.equals(term.getTeacherId(), cls.getTeacherId())
							&& Objects.equals(term.getTerm(), cls.getTerm())
							&& Objects.equals(term.getCourseName(), cls.getCourseName()))
				.collect(Collectors.toList());
			term.setId(snowflake.generateId());
			term.setChildren(children);
		});

		// 2. 组装课程下的学期
		courseList.forEach(course -> {
			List<CourseManagement> children = termList.stream()
				.filter(term -> Objects.equals(course.getTeacherId(), term.getTeacherId())
							 && Objects.equals(course.getCourseName(), term.getCourseName()))
				.peek(term -> term.setId(snowflake.generateId()))
				.collect(Collectors.toList());
			course.setId(snowflake.generateId());
			course.setChildren(children);
		});

		return courseList;
	}

	@Nullable
	private List<CourseManagement> getListAll(String jobId, String courseName,Snowflake snowflake) {
		jobId = "";
		//查看课程
		//如果是超级管理员则查询所有课程所以传一个空值
		List<CourseManagement> courseManagementList = courseManagementMapper.selectByJobId(jobId,courseName,null);
		//查看学期
		List<CourseManagement> termList = courseManagementMapper.selectTermByJobId(jobId, null);
		//查看班级
		List<CourseManagement> studentClassList = courseManagementMapper.selectStudentClassByJobId(jobId, null);
		if (courseManagementList != null) {
			termList.forEach(course ->{
				List<CourseManagement> studentClassList1 = new ArrayList<>();
				for (CourseManagement management : studentClassList) {
					if(course.getTeacherId().equals(management.getTeacherId())&&
							course.getTerm().equals(management.getTerm())&&
							course.getCourseName().equals(management.getCourseName())){
						studentClassList1.add(management);
					}
				}
				course.setId(snowflake.generateId());
				course.setChildren(studentClassList1);
			} );
			courseManagementList.forEach(course -> {
				List<CourseManagement> termList1 = new ArrayList<>();
				for (CourseManagement management : termList) {
					if (StringUtils.isBlank(course.getTeacherId())) {
						continue;
					}
					if (StringUtils.isBlank(management.getCourseName())) {
						continue;
					}
					if(course.getTeacherId().equals(management.getTeacherId())&&
							course.getCourseName().equals(management.getCourseName())){
						management.setId(snowflake.generateId());
						termList1.add(management);
					}
				}
				course.setId(snowflake.generateId());
				course.setChildren(termList1);
			});
		}
		return courseManagementList;
	}

	/**
	 * 查询课程管理列表（小程序）
	 *
	 * @param courseManagement 课程管理
	 * @return 课程管理
	 */
	@Override
	public List<CourseManagement> selectCourseManagementList2(CourseManagement courseManagement) {
		String studentId = userUtils.getSysUser(SecurityUtils.getUserId()).getStudentId();
		if (!Objects.isNull(studentId)) {
			// 学生课程
			courseManagement.setStudentId(studentId);
			List<CourseManagement> courseManagementList = courseManagementMapper.selectCourseManagementList(courseManagement);
			if (courseManagementList != null) {
				for (int i = 0; i < courseManagementList.size(); i++) {
					String teacherId = courseManagementList.get(i).getTeacherId();
					String teacherName = courseManagementMapper.selectNameById(teacherId);
					courseManagementList.get(i).setTeacherName(teacherName);
					courseManagementList.get(i).setLoginSign("0");
				}
			}
			return courseManagementList;
		} else {
			// 教师课程
			List<CourseManagement> courseManagementList = courseManagementMapper.selectCourseManagementList(courseManagement);
			if (courseManagementList != null) {
				for (int i = 0; i < courseManagementList.size(); i++) {
					String teacherId = courseManagementList.get(i).getTeacherId();
					String teacherName = courseManagementMapper.selectNameById(teacherId);
					courseManagementList.get(i).setTeacherName(teacherName);
					courseManagementList.get(i).setLoginSign("1");
				}
			}
			return courseManagementList;
		}
	}

	/**
	 * 查询课程管理列表（小程序）
	 *
	 * @param courseManagement 课程管理
	 * @return 课程管理
	 */
	@Override
	public List<CourseManagement> selectCourseManagementList3(CourseManagement courseManagement) {
		String studentId = userUtils.getSysUser(SecurityUtils.getUserId()).getStudentId();
		if (!Objects.isNull(studentId)) {
			// 学生课程
			courseManagement.setStudentId(studentId);
			List<CourseManagement> courseManagementList = courseManagementMapper.selectCourseManagementList(courseManagement);
			if (courseManagementList != null) {
				for (int i = 0; i < courseManagementList.size(); i++) {
					String teacherId = courseManagementList.get(i).getTeacherId();
					String teacherName = courseManagementMapper.selectNameById(teacherId);
					courseManagementList.get(i).setTeacherName(teacherName);
					courseManagementList.get(i).setLoginSign("0");
				}
			}
			return courseManagementList;
		} else {
			// 教师课程
			List<CourseManagement> courseManagementList = courseManagementMapper.selectDISTINCTCoursenameCourseManagementList(courseManagement);
			if (courseManagementList != null) {
				for (int i = 0; i < courseManagementList.size(); i++) {
					String teacherId = courseManagementList.get(i).getTeacherId();
					String teacherName = courseManagementMapper.selectNameById(teacherId);
					courseManagementList.get(i).setTeacherName(teacherName);
					courseManagementList.get(i).setLoginSign("1");
				}
			}
			return courseManagementList;
		}
	}
	@Override
	public Integer IsTeacherOrStudent() {
		if (StringUtils.isNotBlank(SecurityUtils.getLoginUser().getSysUser().getJobId())) {
			// 是老师返回1
			return 1;
		} else {
			// 不是老师返回0
			return 0;
		}
	}

	@Override
	public Long selectIdByClassName(String courseName, String className) {
		//工号（教师ID）
		R<LoginUser> userAndRole = remoteUserService.getUserAndRole(SecurityUtils.getUserId(), SecurityConstants.INNER);
		LoginUser data2 = userAndRole.getData();
		String jobId = data2.getSysUser().getJobId();
		return courseManagementMapper.selectIdByClassName(courseName, className, jobId);
	}

	@Override
	public List<CourseManagement> selectStudentCourseList() {
		String studentId = userUtils.getSysUser(SecurityUtils.getUserId()).getStudentId();
		return courseManagementMapper.selectStudentCourseList(studentId);
	}

	@Override
	public String selectNameById(String teacherId) {
		return courseManagementMapper.selectNameById(teacherId);
	}

	@Override
	public int insertCourseName(StudentCourse studentCourse) throws Exception {
		//工号（教师ID）
		R<LoginUser> userAndRole = remoteUserService.getUserAndRole(SecurityUtils.getUserId(), SecurityConstants.INNER);
		LoginUser data2 = userAndRole.getData();
		String jobId = data2.getSysUser().getJobId();
		studentCourse.setTeacherId(jobId);
		studentCourse.setCreateBy(SecurityUtils.getUsername());
		studentCourse.setCreateTime(DateUtils.getNowDate());
		if(courseManagementMapper.countStudentCourse(studentCourse)>0){
			throw new Exception("课程:"+studentCourse.getCourseName()+"已创建");
		}
		return courseManagementMapper.insertSStudentCourse(studentCourse);
	}

    @Override
    public int deleteCourseName(StudentCourse studentCourse) {
		//根据课程名和工号来删除班级

		//工号（教师ID）
		R<LoginUser> userAndRole = remoteUserService.getUserAndRole(SecurityUtils.getUserId(), SecurityConstants.INNER);
		LoginUser data2 = userAndRole.getData();
		String jobId = data2.getSysUser().getJobId();
		//设置工号（教师ID）
		studentCourse.setTeacherId(jobId);
		//删除关联的习题
		//删除关联的案例
		//删除管理的视频

        return courseManagementMapper.deleteCourseName(studentCourse);
    }


    /**
	 * 查询课程管理列表（全）
	 *
	 * @param courseManagement 课程管理
	 * @return 课程管理
	 */
	@Override
	public List<CourseManagement> selectCourseManagementList1(CourseManagement courseManagement) {


		List<CourseManagement> courseManagementsList = courseManagementMapper.selectCourseManagementList3(courseManagement);
		if (courseManagementsList != null) {
			for (int i = 0; i < courseManagementsList.size(); i++) {
				courseManagementsList.get(i).setStudentName(courseManagementMapper.selectStudentNameById(courseManagementsList.get(i).getStudentId()));
			}
		}
		return courseManagementsList;
	}

	/**
	 * 新增课程管理
	 *
	 * @param courseManagement 课程管理
	 * @return 结果
	 */
	@Override
	public int insertCourseManagement(CourseManagement courseManagement) {

		//如果是不是管理员新增时需要有对应学校的id
		if (!SecurityUtils.isAdmin(SecurityUtils.getUserId())){
			Long universityId = SecurityUtils.getLoginUser().getSysUser().getUniversityId();
			courseManagement.setUniversityId(universityId);
		}
		//工号（教师ID）
		R<LoginUser> userAndRole = remoteUserService.getUserAndRole(SecurityUtils.getUserId(), SecurityConstants.INNER);
		LoginUser data2 = userAndRole.getData();
		String jobId = data2.getSysUser().getJobId();
		courseManagement.setTeacherId(jobId);
		courseManagement.setCreateBy(SecurityUtils.getUsername());
		courseManagement.setCreateTime(DateUtils.getNowDate());
		if ("0".equals(courseManagement.getCourseType())) {// 必修
			List<String> listStudentClass = courseManagement.getKcglList();
			for (int i = 0; i < listStudentClass.size(); i++) {
				String classId = listStudentClass.get(i);
				String studentClassName = courseManagementMapper.selectStudentClassName(classId);
				courseManagement.setStudentClass(studentClassName);
				courseManagementMapper.insertStudentCourse(courseManagement);
				Long id = courseManagement.getId();
				List<String> listStudentId = courseManagementMapper.selectByclassName(listStudentClass.get(i));
				for (int j = 0; j < listStudentId.size(); j++) {
					courseManagement.setId(id);
					courseManagement.setStudentId(listStudentId.get(j));
					courseManagementMapper.insertCourseManagement(courseManagement);
				}
			}
		} else if ("1".equals(courseManagement.getCourseType())) {// 选修
			if (courseManagement.getKcglList() == null && courseManagement.getXhList().size() > 0) {// 只传文件
				List<String> xhList = courseManagement.getXhList();
				List<Object> cacheObject = redisService.getCacheObject("sys_dict:semester");
				List<SysDictData> dictCacheList = cacheObject.stream()
						.map(dataObj -> JSON.parseObject(JSON.toJSONString(dataObj), SysDictData.class))
						.collect(Collectors.toList());
				dictCacheList.forEach(item -> {
					if (item.getDictValue().equals(courseManagement.getTerm())) {
						courseManagement.setStudentClass(item.getDictLabel() + "临班");
					}
				});
				courseManagementMapper.insertStudentCourse(courseManagement);
				Long id = courseManagement.getId();
				for (int i = 0; i < xhList.size(); i++) {
					courseManagement.setId(id);
					courseManagement.setStudentId(xhList.get(i));
					courseManagement.setStudentClass(courseManagementMapper.selectStudentClassName1(xhList.get(i)));
					courseManagementMapper.insertCourseManagement(courseManagement);
				}
			} else if (courseManagement.getKcglList() != null && courseManagement.getXhList().size() > 0) {// 既传文件又传班级
				List<Object> cacheObject = redisService.getCacheObject("sys_dict:semester");
				List<SysDictData> dictCacheList = cacheObject.stream()
						.map(dataObj -> JSON.parseObject(JSON.toJSONString(dataObj), SysDictData.class))
						.collect(Collectors.toList());
				dictCacheList.forEach(item -> {
					if (item.getDictValue().equals(courseManagement.getTerm())) {
						courseManagement.setStudentClass(item.getDictLabel() + "临班");
					}
				});
				courseManagementMapper.insertStudentCourse(courseManagement);
				Long id = courseManagement.getId();
				List<String> xhList = courseManagement.getXhList();
				for (int i = 0; i < xhList.size(); i++) {
					courseManagement.setId(id);
					courseManagement.setStudentId(xhList.get(i));
					courseManagement.setStudentClass(courseManagementMapper.selectStudentClassName1(xhList.get(i)));
					courseManagementMapper.insertCourseManagement(courseManagement);
				}
				List<String> listStudentClass = courseManagement.getKcglList();
				for (int i = 0; i < listStudentClass.size(); i++) {
					String classId = listStudentClass.get(i);
					String studentClassName = courseManagementMapper.selectStudentClassName(classId);
					courseManagement.setStudentClass(studentClassName);
					List<String> listStudentId = courseManagementMapper.selectByclassName(listStudentClass.get(i));
					for (int j = 0; j < listStudentId.size(); j++) {
						courseManagement.setId(id);
						courseManagement.setStudentId(listStudentId.get(j));
						courseManagementMapper.insertCourseManagement(courseManagement);
					}
				}
			} else if (courseManagement.getKcglList() != null && courseManagement.getXhList().size() == 0) {// 只传班级
				List<String> listStudentClass = courseManagement.getKcglList();
				for (int i = 0; i < listStudentClass.size(); i++) {
					String classId = listStudentClass.get(i);
					String studentClassName = courseManagementMapper.selectStudentClassName(classId);
					courseManagement.setStudentClass(studentClassName);
					courseManagementMapper.insertStudentCourse(courseManagement);
					Long id = courseManagement.getId();
					List<String> listStudentId = courseManagementMapper.selectByclassName(listStudentClass.get(i));
					for (int j = 0; j < listStudentId.size(); j++) {
						courseManagement.setId(id);
						courseManagement.setStudentId(listStudentId.get(j));
						courseManagementMapper.insertCourseManagement(courseManagement);
					}
				}
			}

		}
		return 1;
	}

	/**
	 * 修改课程管理
	 *
	 * @param courseManagement 课程管理
	 * @return 结果
	 */
	@Override
	public int updateCourseManagement(CourseManagement courseManagement) {
		courseManagement.setUpdateTime(DateUtils.getNowDate());
		return courseManagementMapper.updateCourseManagement(courseManagement);
	}

	/**
	 * 批量删除课程管理
	 *
	 * @param ids 需要删除的课程管理主键
	 * @return 结果
	 */
	@Override
	public int deleteCourseManagementByIds(Long[] ids) {
		return courseManagementMapper.deleteCourseManagementByIds(ids);
	}

	/**
	 * 删除课程管理信息
	 *
	 * @param id 课程管理主键
	 * @return 结果
	 */
	@Override
	public int deleteCourseManagementById(Long id) {
		return courseManagementMapper.deleteCourseManagementById(id);
	}

/**
 * 删除课程管理信息
 *
 * 此方法负责删除课程管理对象及其相关联的所有资料和信息，包括但不限于文件、通知、讨论和签到记录
 * 它首先确定课程和教师的关联，然后依次删除与课程相关的所有资料和信息
 *
 * @param courseManagement 课程管理对象，包含课程信息和教师ID等数据
 * @return 返回删除操作的结果，通常表示删除成功或失败
 */
@Override
public int deleteCourseManagement(CourseManagement courseManagement) {

    //拿到老师的id和课程名来确定签到表
    //工号（教师ID）
    R<LoginUser> userAndRole = remoteUserService.getUserAndRole(SecurityUtils.getUserId(), SecurityConstants.INNER);
    LoginUser data2 = userAndRole.getData();
    String jobId = data2.getSysUser().getJobId();
    courseManagement.setTeacherId(jobId);

    //删除课程时把课程所关联的资料都删除
    //删除教案和删除资料用一个方法
    //先查出busiid然后在根据这个id来删除
    List<Long> busiIdList = new ArrayList<>();
    //busiIdList.addAll(courseMaterialsMapper.selectBusiidSysFileInfoByCourseId(courseManagement.getId()));
    //根据file_object_name找到busiid
    List<String> fileObjectNames = courseMaterialsMapper.selectFileObjectNameByCourseId(courseManagement.getId());

    for (String fileObjectName : fileObjectNames) {
        SysFileInfo sysFileInfo = remoteFileService.getFileInfo(fileObjectName);
        Long busiId = Long.valueOf(sysFileInfo.getBusiId());
        busiIdList.add(busiId);
    }
    for (int i = 0; i < busiIdList.size(); i++) {
        remoteFileService.deleteFile(busiIdList.get(i).toString());
    }

    //删除教案使用课程的id来删除课件信息表和文件表


    	// 删除 s_course_materials 中的记录
    courseMaterialsMapper.deleteCourseMaterialsByStudentCourseId(courseManagement.getId());





    //删除通知
    courseMaterialsMapper.deleteCourseNotificationsByStudentCourseId(courseManagement.getId());

    //删除讨论
    int result1 = courseMaterialsMapper.deletesCourseTopicsRepliesByCourseId(courseManagement.getId());
    //先删除讨论的回复
    if (result1 > -1) {
    	// 第二个方法：删除 s_course_materials 中的记录
    	courseMaterialsMapper.deletesCourseTopicsByStudentCourseId(courseManagement.getId());
    } else {
    	// 第一个删除操作失败的处理（如果需要）
    	throw new RuntimeException("Failed to delete file info.");
    }
//	//删除签到类型s_sign_in_sessions
//		int result2 = courseMaterialsMapper.deletesSignInSessionsByIdAndName(courseManagement);
//		//删除签到
//		if (result2 > -1) {
//			// 第二个方法：删除 s_student_sign 中的记录
//			courseMaterialsMapper.deletesStudentSignByIdAndName(courseManagement);
//		} else {
//			// 第一个删除操作失败的处理（如果需要）
//			throw new RuntimeException("Failed to delete file info.");
//		}

    courseManagementMapper.deleteCourseManagement(courseManagement);
    return courseManagementMapper.deleteCourseManagement1(courseManagement);
}

	//外层的课程删除
	@Override
	public int deleteCourseManagement1(CourseManagement courseManagement) {
		//拿到教师id
		R<LoginUser> userAndRole = remoteUserService.getUserAndRole(SecurityUtils.getUserId(), SecurityConstants.INNER);
		LoginUser data2 = userAndRole.getData();
		String jobId = data2.getSysUser().getJobId();
		courseManagement.setTeacherId(jobId);
		//顺带删这些课程的资料
		//先查出他们的busiId存入集合

		// 使用 List<Longr> 存储
		List<Long> busiIdList = new ArrayList<>();
		busiIdList.addAll(courseManagementMapper.seleteCourseVideoBusiIdByNameAndId(courseManagement));
		busiIdList.addAll(courseManagementMapper.seleteCourseCaseBusiIdByNameAndId(courseManagement));
		busiIdList.addAll(courseManagementMapper.seletesCourseExercisesBusiIdByNameAndId(courseManagement));
		//使用busiId进行删除
		for (int i = 0; i < busiIdList.size(); i++) {
			remoteFileService.deleteFile(busiIdList.get(i).toString());
		}

  	    //再删除这些表
		courseManagementMapper.deleteCourseVideoByNameAndId(courseManagement);
		courseManagementMapper.deleteCourseCaseByNameAndId(courseManagement);
		courseManagementMapper.deletesCourseExercisesByNameAndId(courseManagement);
		return courseManagementMapper.deleteCourseManagement2(courseManagement);
	}

	@Override
	public List<ClassInfo> selectCourseManagementByCourseName(String courseName) {
		return courseManagementMapper.selectCourseManagementByCourseName(courseName);
	}

	@Override
	public List<CourseManagement> getAll() {
		return courseManagementMapper.getAll();
	}

	/**
	 * 上传课程管理表
	 *
	 * @param file 课程管理
	 * @return 结果
	 */
	@Override
	public void uploadCourseManagement(MultipartFile file) {
		try {
			long uniqueID = System.currentTimeMillis();
			// 获取上传的文件名 photo1.jpg
			String fileName = file.getOriginalFilename();
			fileName = uniqueID + "-" + fileName;
			String os = System.getProperty("os.name").toLowerCase();
			String speechdraftpath = "";
			if (os.contains("win")) {
				speechdraftpath = localFilePathWin;
			} else if (os.contains("nix") || os.contains("nux") || os.contains("mac")) {
				speechdraftpath = localFilePathLinux;
				;
			} else {
				throw new UnsupportedOperationException("Unsupported operating system: " + os);
			}
			String filePath = speechdraftpath + "kcgl";
			// 检查并创建目录
			Path uploadPath = Paths.get(filePath);
			if (!Files.exists(uploadPath)) {
				Files.createDirectories(uploadPath);
			}
			// 保存文件到指定路径
			Path filePath1 = uploadPath.resolve(fileName);
			Files.write(filePath1, file.getBytes());
		} catch (Exception e) {
			e.printStackTrace();
		}
	}


	@Override
	public CourseStatisticsVo getCourseStatistics(CourseManagementDto courseManagementDto) {
		CourseStatisticsVo vo = new CourseStatisticsVo();

		String courseName = courseManagementDto.getCourseName();
		if (StringUtils.isBlank(courseName)) {
			return new CourseStatisticsVo();
		}
		// 获取学生人数
		CourseStatisticsVo studentStatistics = courseManagementMapper.getStudentCourseStatistics(courseName);
		if (studentStatistics == null) {
			return vo;
		}
		vo = courseManagementMapper.getStudentCourseStatistics(courseName);
		// 参与学习人数
		vo.setParticipantCount(vo.getTotalStudentCount());

		// 教师
		List<AmbitTeacherVo> ambitTeacherVoList = courseManagementMapper.getTeacherCourseStatistics(courseName);
		if (ambitTeacherVoList.isEmpty()) {
			return vo;
		}

		// 统计教师人数
		vo.setTeamMemberCount(ambitTeacherVoList.size());

		for (AmbitTeacherVo ambitTeacherVo : ambitTeacherVoList) {
			String title = ambitTeacherVo.getTitle();
			if ("教授".equals(title)) {
				vo.setProfessorCount(vo.getProfessorCount() + 1);
			}else if ("副教授".equals(title)) {
				vo.setAssociateProfessorCount(vo.getAssociateProfessorCount() + 1);
			} else if ("讲师".equals(title)) {
				vo.setLecturerCount(vo.getLecturerCount() + 1);
			} else {
				vo.setOtherPersonnelCount(vo.getOtherPersonnelCount() + 1);
			}
		}
		return vo;
	}
}

