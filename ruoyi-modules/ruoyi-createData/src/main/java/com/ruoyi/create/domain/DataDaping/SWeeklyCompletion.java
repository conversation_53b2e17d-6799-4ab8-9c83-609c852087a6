package com.ruoyi.create.domain.DataDaping;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 学生作业周完成情况对象 s_weekly_completion
 * 
 * <AUTHOR>
 * @date 2025-04-08
 */
public class SWeeklyCompletion extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 星期 */
    @Excel(name = "星期")
    private String weekDay;

    /** 完成作业数量 */
    @Excel(name = "完成作业数量")
    private Long homeworkCount;

    /** 迟到作业数量 */
    @Excel(name = "迟到作业数量")
    private Long lateHomeworkCount;

    /** 未完成作业数量 */
    @Excel(name = "未完成作业数量")
    private Long incompleteHomeworkCount;

    /** 发布作业 */
    @Excel(name = "发布作业")
    private Long putHomework;

    /** 发布作业 */
    @Excel(name = "发布作业")
    private Long putPpt;

    /** 统计时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "统计时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date statisticsTime;


    public Long getPutHomework() {
        return putHomework;
    }

    public void setPutHomework(Long putHomework) {
        this.putHomework = putHomework;
    }

    public Long getPutPpt() {
        return putPpt;
    }

    public void setPutPpt(Long putPpt) {
        this.putPpt = putPpt;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setWeekDay(String weekDay) 
    {
        this.weekDay = weekDay;
    }

    public String getWeekDay() 
    {
        return weekDay;
    }
    public void setHomeworkCount(Long homeworkCount) 
    {
        this.homeworkCount = homeworkCount;
    }

    public Long getHomeworkCount() 
    {
        return homeworkCount;
    }
    public void setLateHomeworkCount(Long lateHomeworkCount) 
    {
        this.lateHomeworkCount = lateHomeworkCount;
    }

    public Long getLateHomeworkCount() 
    {
        return lateHomeworkCount;
    }
    public void setIncompleteHomeworkCount(Long incompleteHomeworkCount) 
    {
        this.incompleteHomeworkCount = incompleteHomeworkCount;
    }

    public Long getIncompleteHomeworkCount() 
    {
        return incompleteHomeworkCount;
    }
    public void setStatisticsTime(Date statisticsTime) 
    {
        this.statisticsTime = statisticsTime;
    }

    public Date getStatisticsTime() 
    {
        return statisticsTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("weekDay", getWeekDay())
            .append("homeworkCount", getHomeworkCount())
            .append("lateHomeworkCount", getLateHomeworkCount())
            .append("putHomework", getPutHomework())
            .append("putPpt", getPutPpt())
            .append("incompleteHomeworkCount", getIncompleteHomeworkCount())
            .append("statisticsTime", getStatisticsTime())
            .toString();
    }
}
