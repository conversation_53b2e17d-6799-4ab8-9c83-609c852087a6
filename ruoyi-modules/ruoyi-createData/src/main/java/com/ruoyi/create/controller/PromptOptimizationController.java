package com.ruoyi.create.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.PromptOptimization;
import com.ruoyi.create.service.IPromptOptimizationService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * prompt模板优化Controller
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@RestController
@RequestMapping("/optimization")
public class PromptOptimizationController extends BaseController
{
    @Autowired
    private IPromptOptimizationService promptOptimizationService;

    /**
     * 查询prompt模板优化列表
     */
    @RequiresPermissions("create:optimization:list")
    @GetMapping("/list")
    public TableDataInfo list(PromptOptimization promptOptimization)
    {
        startPage();
        List<PromptOptimization> list = promptOptimizationService.selectPromptOptimizationList(promptOptimization);
        return getDataTable(list);
    }

    /**
     * 导出prompt模板优化列表
     */
    @RequiresPermissions("create:optimization:export")
    @Log(title = "prompt模板优化", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PromptOptimization promptOptimization)
    {
        List<PromptOptimization> list = promptOptimizationService.selectPromptOptimizationList(promptOptimization);
        ExcelUtil<PromptOptimization> util = new ExcelUtil<PromptOptimization>(PromptOptimization.class);
        util.exportExcel(response, list, "prompt模板优化数据");
    }

    /**
     * 获取prompt模板优化详细信息
     */
    @RequiresPermissions("create:optimization:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(promptOptimizationService.selectPromptOptimizationById(id));
    }

    /**
     * 新增prompt模板优化
     */
    @RequiresPermissions("create:optimization:add")
    @Log(title = "prompt模板优化", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PromptOptimization promptOptimization)
    {
        return promptOptimizationService.insertPromptOptimization(promptOptimization);
    }

    /**
     * 修改prompt模板优化
     */
    @RequiresPermissions("create:optimization:edit")
    @Log(title = "prompt模板优化", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PromptOptimization promptOptimization)
    {
        return toAjax(promptOptimizationService.updatePromptOptimization(promptOptimization));
    }

    /**
     * 删除prompt模板优化
     */
    @RequiresPermissions("create:optimization:remove")
    @Log(title = "prompt模板优化", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(promptOptimizationService.deletePromptOptimizationByIds(ids));
    }

    /**
     * 创建prompt优化任务
     */
    @RequiresPermissions("create:optimization:add")
    @Log(title = "prompt模板优化", businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public AjaxResult createPrompt(@RequestBody PromptOptimization promptOptimization)
    {
        return promptOptimizationService.insertPromptOptimization(promptOptimization);
    }

    /**
     * 获取prompt优化任务的详情
     */
    @RequiresPermissions("create:optimization:info")
    @GetMapping(value = "/info/{id}")
    public AjaxResult getPromptInfo(@PathVariable("id") Long id)
    {
        return success(promptOptimizationService.getPromptInfo(id));
    }

}
