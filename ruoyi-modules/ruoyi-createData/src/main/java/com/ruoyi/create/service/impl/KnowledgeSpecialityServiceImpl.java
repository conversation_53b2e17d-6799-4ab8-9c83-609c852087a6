package com.ruoyi.create.service.impl;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.create.domain.University;
import com.ruoyi.create.service.IUniversityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.KnowledgeSpecialityMapper;
import com.ruoyi.create.domain.KnowledgeSpeciality;
import com.ruoyi.create.service.IKnowledgeSpecialityService;

/**
 * 知识库专业配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-30
 */
@Service
public class KnowledgeSpecialityServiceImpl implements IKnowledgeSpecialityService 
{
    @Autowired
    private KnowledgeSpecialityMapper knowledgeSpecialityMapper;


    @Autowired
    private IUniversityService universityService;


    /**
     * 查询知识库专业配置
     * 
     * @param id 知识库专业配置主键
     * @return 知识库专业配置
     */
    @Override
    public KnowledgeSpeciality selectKnowledgeSpecialityById(Long id)
    {
        KnowledgeSpeciality knowledgeSpeciality = knowledgeSpecialityMapper.selectKnowledgeSpecialityById(id);
        Long universityId = knowledgeSpeciality.getUniversityId();
        Long collegeId = knowledgeSpeciality.getCollegeId();
        Long majorId = knowledgeSpeciality.getMajorId();
        University university = new University();
        university.setId(universityId);
        university.setCollegeId(collegeId);
        university.setMajorId(majorId);
        University universityInfo = universityService.getUniversityInfo(university);
        if(universityInfo!=null){
            String univerName = universityInfo.getUniverName();
            String colleName = universityInfo.getColleName();
            String majorName = universityInfo.getMajorName();
            List<String> tempAffiliatedUnitsName = Arrays.asList(univerName, colleName, majorName).stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            String[] affiliatedUnitNameArray = tempAffiliatedUnitsName.toArray(new String[tempAffiliatedUnitsName.size()]);
            knowledgeSpeciality.setAffiliatedUnitName(affiliatedUnitNameArray);
        }
        List<Long> tempAffiliatedUnits = Arrays.asList(universityId, collegeId, majorId).stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        Long[] affiliatedUnitArray = tempAffiliatedUnits.toArray(new Long[tempAffiliatedUnits.size()]);
        knowledgeSpeciality.setAffiliatedUnit(affiliatedUnitArray);
        return knowledgeSpeciality;
    }

    /**
     * 查询知识库专业配置列表
     * 
     * @param knowledgeSpeciality 知识库专业配置
     * @return 知识库专业配置
     */
    @Override
    public List<KnowledgeSpeciality> selectKnowledgeSpecialityList(KnowledgeSpeciality knowledgeSpeciality)
    {
        return knowledgeSpecialityMapper.selectKnowledgeSpecialityList(knowledgeSpeciality);
    }

    /**
     * 新增知识库专业配置
     * 
     * @param knowledgeSpeciality 知识库专业配置
     * @return 结果
     */
    @Override
    public int insertKnowledgeSpeciality(KnowledgeSpeciality knowledgeSpeciality)
    {
        Long[] affiliatedUnit = knowledgeSpeciality.getAffiliatedUnit();
        knowledgeSpeciality.setUniversityId(affiliatedUnit != null && affiliatedUnit.length > 0 ? affiliatedUnit[0] : null); //
        knowledgeSpeciality.setCollegeId(affiliatedUnit != null && affiliatedUnit.length > 1 ? affiliatedUnit[1] : null); //
        knowledgeSpeciality.setMajorId(affiliatedUnit != null && affiliatedUnit.length > 2 ? affiliatedUnit[2] : null); //
        knowledgeSpeciality.setCreateTime(DateUtils.getNowDate());
        return knowledgeSpecialityMapper.insertKnowledgeSpeciality(knowledgeSpeciality);
    }

    /**
     * 修改知识库专业配置
     * 
     * @param knowledgeSpeciality 知识库专业配置
     * @return 结果
     */
    @Override
    public int updateKnowledgeSpeciality(KnowledgeSpeciality knowledgeSpeciality)
    {
        Long[] affiliatedUnit = knowledgeSpeciality.getAffiliatedUnit();
        knowledgeSpeciality.setUniversityId(affiliatedUnit != null && affiliatedUnit.length > 0 ? affiliatedUnit[0] : null); //
        knowledgeSpeciality.setCollegeId(affiliatedUnit != null && affiliatedUnit.length > 1 ? affiliatedUnit[1] : null); //
        knowledgeSpeciality.setMajorId(affiliatedUnit != null && affiliatedUnit.length > 2 ? affiliatedUnit[2] : null); //
        knowledgeSpeciality.setUpdateTime(DateUtils.getNowDate());
        return knowledgeSpecialityMapper.updateKnowledgeSpeciality(knowledgeSpeciality);
    }

    /**
     * 批量删除知识库专业配置
     * 
     * @param ids 需要删除的知识库专业配置主键
     * @return 结果
     */
    @Override
    public int deleteKnowledgeSpecialityByIds(Long[] ids)
    {
        return knowledgeSpecialityMapper.deleteKnowledgeSpecialityByIds(ids);
    }

    /**
     * 删除知识库专业配置信息
     * 
     * @param id 知识库专业配置主键
     * @return 结果
     */
    @Override
    public int deleteKnowledgeSpecialityById(Long id)
    {
        return knowledgeSpecialityMapper.deleteKnowledgeSpecialityById(id);
    }

    /**
     * 根据专业d查询知识库配置
     */
    @Override
    public KnowledgeSpeciality selectKnowledgeSpecialityByMajorInfoId(Long majorId)
    {
        return knowledgeSpecialityMapper.selectKnowledgeSpecialityByMajorInfoId(majorId);
    }
}
