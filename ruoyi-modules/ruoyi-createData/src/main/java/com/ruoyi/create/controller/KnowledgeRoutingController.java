package com.ruoyi.create.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.KnowledgeRouting;
import com.ruoyi.create.service.IKnowledgeRoutingService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 知识库路由配置Controller
 * 
 * <AUTHOR>
 * @date 2024-07-30
 */
@RestController
@RequestMapping("/routing")
public class KnowledgeRoutingController extends BaseController
{
    @Autowired
    private IKnowledgeRoutingService knowledgeRoutingService;

    /**
     * 查询知识库路由配置列表
     */
    @RequiresPermissions("create:routing:list")
    @GetMapping("/list")
    public TableDataInfo list(KnowledgeRouting knowledgeRouting)
    {
        startPage();
        List<KnowledgeRouting> list = knowledgeRoutingService.selectKnowledgeRoutingList(knowledgeRouting);
        return getDataTable(list);
    }

    /**
     * 导出知识库路由配置列表
     */
    @RequiresPermissions("create:routing:export")
    @Log(title = "知识库路由配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KnowledgeRouting knowledgeRouting)
    {
        List<KnowledgeRouting> list = knowledgeRoutingService.selectKnowledgeRoutingList(knowledgeRouting);
        ExcelUtil<KnowledgeRouting> util = new ExcelUtil<KnowledgeRouting>(KnowledgeRouting.class);
        util.exportExcel(response, list, "知识库路由配置数据");
    }

    /**
     * 获取知识库路由配置详细信息
     */
    @RequiresPermissions("create:routing:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(knowledgeRoutingService.selectKnowledgeRoutingById(id));
    }

    /**
     * 新增知识库路由配置
     */
    @RequiresPermissions("create:routing:add")
    @Log(title = "知识库路由配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KnowledgeRouting knowledgeRouting)
    {
        return toAjax(knowledgeRoutingService.insertKnowledgeRouting(knowledgeRouting));
    }

    /**
     * 修改知识库路由配置
     */
    @RequiresPermissions("create:routing:edit")
    @Log(title = "知识库路由配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KnowledgeRouting knowledgeRouting)
    {
        return toAjax(knowledgeRoutingService.updateKnowledgeRouting(knowledgeRouting));
    }

    /**
     * 删除知识库路由配置
     */
    @RequiresPermissions("create:routing:remove")
    @Log(title = "知识库路由配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(knowledgeRoutingService.deleteKnowledgeRoutingByIds(ids));
    }

    /**
     * 获取知识库路由配置详细信息
     */
    @GetMapping(value = "/getKnowledge/{menuRouting}")
    public List<KnowledgeRouting> getKnowledgeRoutingById(@PathVariable("menuRouting") String menuRouting)
    {
        return knowledgeRoutingService.selectKnowledgeByMenuRouting(menuRouting);
    }
}
