package com.ruoyi.create.service;

import java.util.List;
import com.ruoyi.create.domain.AuthenInfor;

/**
 * 鉴权信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-14
 */
public interface IAuthenInforService 
{
    /**
     * 查询鉴权信息
     * 
     * @param id 鉴权信息主键
     * @return 鉴权信息
     */
    public AuthenInfor selectAuthenInforById(Long id);

    /**
     * 查询鉴权信息列表
     * 
     * @param authenInfor 鉴权信息
     * @return 鉴权信息集合
     */
    public List<AuthenInfor> selectAuthenInforList(AuthenInfor authenInfor);

    /**
     * 新增鉴权信息
     * 
     * @param authenInfor 鉴权信息
     * @return 结果
     */
    public int insertAuthenInfor(AuthenInfor authenInfor) throws Exception;

    /**
     * 修改鉴权信息
     * 
     * @param authenInfor 鉴权信息
     * @return 结果
     */
    public int updateAuthenInfor(AuthenInfor authenInfor) throws Exception;

    /**
     * 批量删除鉴权信息
     * 
     * @param ids 需要删除的鉴权信息主键集合
     * @return 结果
     */
    public int deleteAuthenInforByIds(Long[] ids);

    /**
     * 删除鉴权信息信息
     * 
     * @param id 鉴权信息主键
     * @return 结果
     */
    public int deleteAuthenInforById(Long id);

    String selectConfigByKey(String menuRouting, String key);

    List<AuthenInfor> selectAuthenInforAll();
}
