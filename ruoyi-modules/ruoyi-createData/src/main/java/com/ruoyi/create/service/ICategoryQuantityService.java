package com.ruoyi.create.service;

import java.util.List;
import com.ruoyi.create.domain.CategoryQuantity;

/**
 * 文献整理-类别统计Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
public interface ICategoryQuantityService 
{
    /**
     * 查询文献整理-类别统计
     * 
     * @param id 文献整理-类别统计主键
     * @return 文献整理-类别统计
     */
    public CategoryQuantity selectCategoryQuantityById(Long id);

    /**
     * 查询文献整理-类别统计列表
     * 
     * @param categoryQuantity 文献整理-类别统计
     * @return 文献整理-类别统计集合
     */
    public List<CategoryQuantity> selectCategoryQuantityList(CategoryQuantity categoryQuantity);

    /**
     * 新增文献整理-类别统计
     * 
     * @param categoryQuantity 文献整理-类别统计
     * @return 结果
     */
    public int insertCategoryQuantity(CategoryQuantity categoryQuantity);

    /**
     * 修改文献整理-类别统计
     * 
     * @param categoryQuantity 文献整理-类别统计
     * @return 结果
     */
    public int updateCategoryQuantity(CategoryQuantity categoryQuantity);

    /**
     * 批量删除文献整理-类别统计
     * 
     * @param ids 需要删除的文献整理-类别统计主键集合
     * @return 结果
     */
    public int deleteCategoryQuantityByIds(Long[] ids);

    /**
     * 删除文献整理-类别统计信息
     * 
     * @param id 文献整理-类别统计主键
     * @return 结果
     */
    public int deleteCategoryQuantityById(Long id);

    List<CategoryQuantity> selectCategoryCount(String title);
}
