package com.ruoyi.create.mapper;


import com.ruoyi.create.domain.ExtractionRules;

import java.util.List;

/**
 * 提取规则Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-12-13
 */
public interface ExtractionRulesMapper 
{
    /**
     * 查询提取规则
     * 
     * @param rulesId 提取规则主键
     * @return 提取规则
     */
    public ExtractionRules selectExtractionRulesByRulesId(Long rulesId);

    /**
     * 查询提取规则列表
     * 
     * @param extractionRules 提取规则
     * @return 提取规则集合
     */
    public List<ExtractionRules> selectExtractionRulesList(ExtractionRules extractionRules);

    /**
     * 新增提取规则
     * 
     * @param extractionRules 提取规则
     * @return 结果
     */
    public int insertExtractionRules(ExtractionRules extractionRules);

    /**
     * 修改提取规则
     * 
     * @param extractionRules 提取规则
     * @return 结果
     */
    public int updateExtractionRules(ExtractionRules extractionRules);

    /**
     * 删除提取规则
     * 
     * @param rulesId 提取规则主键
     * @return 结果
     */
    public int deleteExtractionRulesByRulesId(Long rulesId);

    /**
     * 批量删除提取规则
     * 
     * @param rulesIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExtractionRulesByRulesIds(Long[] rulesIds);

    List<ExtractionRules> selectExtractionRulesAll();
}
