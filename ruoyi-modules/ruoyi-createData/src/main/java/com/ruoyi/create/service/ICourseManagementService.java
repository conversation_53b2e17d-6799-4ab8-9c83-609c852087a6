package com.ruoyi.create.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.create.Vo.CourseStatisticsVo;
import com.ruoyi.create.domain.ClassInfo;
import com.ruoyi.create.domain.CourseManagement;
import com.ruoyi.create.domain.StudentCourse;
import com.ruoyi.create.dto.CourseManagementDto;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 课程管理Service接口
 *
 * <AUTHOR>
 * @date 2024-10-11
 */

public interface ICourseManagementService extends IService<CourseManagement> {

    /**
     * 查询课程管理
     *
     * @param id 课程管理主键
     * @return 课程管理
     */
    public CourseManagement selectCourseManagementById(Long id);

    /**
     * 查询课程管理列表
     *
     * @param courseManagement 课程管理
     * @return 课程管理集合
     */
    public List<CourseManagement> selectCourseManagementList(CourseManagement courseManagement);

    /**
     * 新增课程管理
     *
     * @param courseManagement 课程管理
     * @return 结果
     */
    public int insertCourseManagement(CourseManagement courseManagement);

    /**
     * 修改课程管理
     *
     * @param courseManagement 课程管理
     * @return 结果
     */
    public int updateCourseManagement(CourseManagement courseManagement);

    /**
     * 批量删除课程管理
     *
     * @param ids 需要删除的课程管理主键集合
     * @return 结果
     */
    public int deleteCourseManagementByIds(Long[] ids);

    /**
     * 删除课程管理信息
     *
     * @param id 课程管理主键
     * @return 结果
     */
    public int deleteCourseManagementById(Long id);

    List<CourseManagement> getAll();

    /**
     * 上传课程管理表
     *
     * @param file 课程管理表
     * @return 结果
     */
    public void uploadCourseManagement(MultipartFile file);

    public List<CourseManagement> selectCourseManagementList1(CourseManagement courseManagement);

    public int deleteCourseManagement(CourseManagement courseManagement);

    public List<ClassInfo> selectCourseManagementByCourseName(String courseName);

    public int deleteCourseManagement1(CourseManagement courseManagement);

    public List<CourseManagement> selectCourseManagementList2(CourseManagement courseManagement);
    public List<CourseManagement> selectCourseManagementList3(CourseManagement courseManagement);


    public Integer IsTeacherOrStudent();

    public Long selectIdByClassName(String courseName, String className);

    public List<CourseManagement> selectStudentCourseList();

    public String selectNameById(String teacherId);

    int insertCourseName(StudentCourse studentCourse) throws Exception;

    int deleteCourseName(StudentCourse studentCourse);

    CourseStatisticsVo getCourseStatistics(CourseManagementDto courseManagementDto);
}
