package com.ruoyi.create.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.domain.*;
import com.ruoyi.create.mapper.HomeworkMapper;
import com.ruoyi.create.mapper.HomeworkStudentMapper;
import com.ruoyi.create.service.IHomeworkQuestionOptionService;
import com.ruoyi.create.service.IHomeworkQuestionService;
import com.ruoyi.create.service.IHomeworkStudentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.HomeworkStudentDetailMapper;
import com.ruoyi.create.service.IHomeworkStudentDetailService;

/**
 * 学生作业答案详情Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-07
 */
@Service
public class HomeworkStudentDetailServiceImpl extends ServiceImpl<HomeworkStudentDetailMapper, HomeworkStudentDetail> implements IHomeworkStudentDetailService
{
    @Autowired
    private HomeworkStudentDetailMapper homeworkStudentDetailMapper;

    @Autowired
    private HomeworkMapper homeworkMapper;

    @Autowired
    private HomeworkStudentMapper homeworkStudentMapper;

    @Autowired
    private IHomeworkStudentService homeworkStudentService;

    @Autowired
    private IHomeworkQuestionService homeworkQuestionService;
    @Autowired
    private IHomeworkQuestionOptionService homeworkQuestionOptionService;

    /**
     * 查询学生作业答案详情
     * 
     * @param id 学生作业答案详情主键
     * @return 学生作业答案详情
     */
    @Override
    public HomeworkStudentDetail selectHomeworkStudentDetailById(Long id)
    {
        return homeworkStudentDetailMapper.selectHomeworkStudentDetailById(id);
    }

    /**
     * 查询学生作业答案详情列表
     * 
     * @param homeworkStudentDetail 学生作业答案详情
     * @return 学生作业答案详情
     */
    @Override
    public List<HomeworkStudentDetail> selectHomeworkStudentDetailList(HomeworkStudentDetail homeworkStudentDetail)
    {
        return homeworkStudentDetailMapper.selectHomeworkStudentDetailList(homeworkStudentDetail);
    }

    /**
     * 新增学生作业答案详情
     * 
     * @param homeworkStudentDetail 学生作业答案详情
     * @return 结果
     */
    @Override
    public int insertHomeworkStudentDetail(HomeworkStudentDetail homeworkStudentDetail)
    {
        return homeworkStudentDetailMapper.insertHomeworkStudentDetail(homeworkStudentDetail);
    }

    /**
     * 修改学生作业答案详情
     * 
     * @param homeworkStudentDetail 学生作业答案详情
     * @return 结果
     */
    @Override
    public int updateHomeworkStudentDetail(HomeworkStudentDetail homeworkStudentDetail)
    {
        return homeworkStudentDetailMapper.updateHomeworkStudentDetail(homeworkStudentDetail);
    }

    /**
     * 批量删除学生作业答案详情
     * 
     * @param ids 需要删除的学生作业答案详情主键
     * @return 结果
     */
    @Override
    public int deleteHomeworkStudentDetailByIds(Long[] ids)
    {
        return homeworkStudentDetailMapper.deleteHomeworkStudentDetailByIds(ids);
    }

    /**
     * 删除学生作业答案详情信息
     * 
     * @param id 学生作业答案详情主键
     * @return 结果
     */
    @Override
    public int deleteHomeworkStudentDetailById(Long id)
    {
        return homeworkStudentDetailMapper.deleteHomeworkStudentDetailById(id);
    }

    @Override
    public AjaxResult insertHomeworkStudentDetailList(List<HomeworkStudentDetail> homeworkStudentDetail) {
        //判断当前作业是否存在
        Homework homework = homeworkMapper.selectHomeworkById(homeworkStudentDetail.get(0).getHmId());
        if(homework==null){
            //返回错误信息
            return AjaxResult.error("当前作业已被删除");
        }
        Long userId = SecurityUtils.getUserId();
	    homeworkStudentDetail.forEach(detail -> {
				    detail.setUserId(userId);
				    detail.setCreateTime(DateUtils.getNowDate());
			    } );
        int  successsum = homeworkStudentDetailMapper.insertHomeworkStudentDetailList(homeworkStudentDetail);
        HomeworkStudent homeworkStudent =new HomeworkStudent();
        homeworkStudent.setUserId(userId);
        homeworkStudent.setHmId(homework.getId());
        homeworkStudent.setCommitStatus("1");
        homeworkStudent.setCommitTime(DateUtils.getNowDate());
        if(successsum>0){
            homeworkStudentMapper.updateHomeworkStudentByHmId(homeworkStudent);
        }
        return AjaxResult.success();
    }

    @Override
    public List<HomeworkStudentDetail> selectHomeworkStudentDetailListAll(HomeworkStudentDetail homeworkStudentDetail) {
        List<HomeworkStudentDetail> homeworkStudentDetailList =homeworkStudentDetailMapper.selectHomeworkStudentDetailListAll(homeworkStudentDetail);
        for (HomeworkStudentDetail studentDetail : homeworkStudentDetailList) {
            HomeworkQuestion homeworkQuestion = homeworkQuestionService.selectHomeworkQuestionById(studentDetail.getQuestionId());
            studentDetail.setHomeworkQuestion(homeworkQuestion);
            studentDetail.setQuestionType(homeworkQuestion.getQuestionType());
            studentDetail.setQuestion(homeworkQuestion.getQuestion());
            List<HomeworkQuestionOption> optionList = homeworkQuestionOptionService.list(new LambdaQueryWrapper<HomeworkQuestionOption>()
                    .eq(HomeworkQuestionOption::getQuestionId, homeworkQuestion.getId()));
            studentDetail.setHomeworkQuestionOptionList(optionList);
        }
        return  homeworkStudentDetailList;
    }
}
