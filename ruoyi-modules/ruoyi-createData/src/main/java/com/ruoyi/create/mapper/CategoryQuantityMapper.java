package com.ruoyi.create.mapper;

import java.util.List;
import com.ruoyi.create.domain.CategoryQuantity;

/**
 * 文献整理-类别统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
public interface CategoryQuantityMapper 
{
    /**
     * 查询文献整理-类别统计
     * 
     * @param id 文献整理-类别统计主键
     * @return 文献整理-类别统计
     */
    public CategoryQuantity selectCategoryQuantityById(Long id);

    /**
     * 查询文献整理-类别统计列表
     * 
     * @param categoryQuantity 文献整理-类别统计
     * @return 文献整理-类别统计集合
     */
    public List<CategoryQuantity> selectCategoryQuantityList(CategoryQuantity categoryQuantity);

    /**
     * 新增文献整理-类别统计
     * 
     * @param categoryQuantity 文献整理-类别统计
     * @return 结果
     */
    public int insertCategoryQuantity(CategoryQuantity categoryQuantity);

    /**
     * 修改文献整理-类别统计
     * 
     * @param categoryQuantity 文献整理-类别统计
     * @return 结果
     */
    public int updateCategoryQuantity(CategoryQuantity categoryQuantity);

    /**
     * 删除文献整理-类别统计
     * 
     * @param id 文献整理-类别统计主键
     * @return 结果
     */
    public int deleteCategoryQuantityById(Long id);

    /**
     * 批量删除文献整理-类别统计
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCategoryQuantityByIds(Long[] ids);

    List<CategoryQuantity> selectCategoryCount(String title);
}
