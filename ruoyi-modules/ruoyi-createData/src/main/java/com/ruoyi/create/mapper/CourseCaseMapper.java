package com.ruoyi.create.mapper;

import java.util.List;
import com.ruoyi.create.domain.CourseCase;

/**
 * 课程案例Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-27
 */
public interface CourseCaseMapper 
{
    /**
     * 查询课程案例
     * 
     * @param id 课程案例主键
     * @return 课程案例
     */
    public CourseCase selectCourseCaseById(Long id);

    /**
     * 查询课程案例列表
     * 
     * @param courseCase 课程案例
     * @return 课程案例集合
     */
    public List<CourseCase> selectCourseCaseList(CourseCase courseCase);

    /**
     * 新增课程案例
     * 
     * @param courseCase 课程案例
     * @return 结果
     */
    public int insertCourseCase(CourseCase courseCase);

    /**
     * 修改课程案例
     * 
     * @param courseCase 课程案例
     * @return 结果
     */
    public int updateCourseCase(CourseCase courseCase);

    /**
     * 删除课程案例
     * 
     * @param id 课程案例主键
     * @return 结果
     */
    public int deleteCourseCaseById(Long id);

    /**
     * 批量删除课程案例
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCourseCaseByIds(Long[] ids);
}
