package com.ruoyi.create.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 学生签到历史对象 s_student_attendance
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
public class StudentAttendance extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 课程id */
    @Excel(name = "课程id")
    private Long courseId;

    /** 班级id */
    @Excel(name = "班级id")
    private Long classId;

    /** 学生id */
    @Excel(name = "学生id")
    private Long studentId;

    /** 签到时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "签到时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date attendanceTime;

    /** 签到截至时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "签到截至时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date attendanceDeadline;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** 签到方式 */
    @Excel(name = "签到方式")
    private String attendanceMethod;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setCourseId(Long courseId)
    {
        this.courseId = courseId;
    }

    public Long getCourseId()
    {
        return courseId;
    }
    public void setClassId(Long classId)
    {
        this.classId = classId;
    }

    public Long getClassId()
    {
        return classId;
    }
    public void setStudentId(Long studentId)
    {
        this.studentId = studentId;
    }

    public Long getStudentId()
    {
        return studentId;
    }
    public void setAttendanceTime(Date attendanceTime)
    {
        this.attendanceTime = attendanceTime;
    }

    public Date getAttendanceTime()
    {
        return attendanceTime;
    }
    public void setAttendanceDeadline(Date attendanceDeadline)
    {
        this.attendanceDeadline = attendanceDeadline;
    }

    public Date getAttendanceDeadline()
    {
        return attendanceDeadline;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setAttendanceMethod(String attendanceMethod)
    {
        this.attendanceMethod = attendanceMethod;
    }

    public String getAttendanceMethod()
    {
        return attendanceMethod;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("courseId", getCourseId())
                .append("classId", getClassId())
                .append("studentId", getStudentId())
                .append("attendanceTime", getAttendanceTime())
                .append("attendanceDeadline", getAttendanceDeadline())
                .append("status", getStatus())
                .append("attendanceMethod", getAttendanceMethod())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
