package com.ruoyi.create.mapper;

import java.util.List;

import com.ruoyi.create.domain.SMsgReply;
import com.ruoyi.create.domain.TopicsReply;

/**
 * 课程留言与回复关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-04
 */
public interface TopicsReplyMapper 
{
    /**
     * 查询课程留言与回复关联
     * 
     * @param id 课程留言与回复关联主键
     * @return 课程留言与回复关联
     */
    public TopicsReply selectTopicsReplyById(Long id);

    /**
     * 查询课程留言与回复关联列表
     * 
     * @param topicsReply 课程留言与回复关联
     * @return 课程留言与回复关联集合
     */
    public List<TopicsReply> selectTopicsReplyList(TopicsReply topicsReply);

    /**
     * 新增课程留言与回复关联
     * 
     * @param topicsReply 课程留言与回复关联
     * @return 结果
     */
    public int insertTopicsReply(TopicsReply topicsReply);

    /**
     * 修改课程留言与回复关联
     * 
     * @param topicsReply 课程留言与回复关联
     * @return 结果
     */
    public int updateTopicsReply(TopicsReply topicsReply);

    /**
     * 删除课程留言与回复关联
     * 
     * @param id 课程留言与回复关联主键
     * @return 结果
     */
    public int deleteTopicsReplyById(Long id);

    /**
     * 批量删除课程留言与回复关联
     * 
     * @param msgIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTopicsReplyByIds(Long[] msgIds);

    List<TopicsReply> selectTopicsReply(Long msgId);

    List<TopicsReply> selectTopicsReplyAll(Long msgId);

    int deleteTopicsReplylyIds(Long[] replyIds);

    int countTopicsReply(Long msgId);
}
