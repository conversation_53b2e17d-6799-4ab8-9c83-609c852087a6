package com.ruoyi.create.service.impl;

import java.util.List;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.domain.Presentation;
import com.ruoyi.create.mapper.PresentationMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.ExamineTxtMapper;
import com.ruoyi.create.domain.ExamineTxt;
import com.ruoyi.create.service.IExamineTxtService;

import javax.annotation.Resource;

/**
 * 课件审核建议Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-19
 */
@Service
public class ExamineTxtServiceImpl implements IExamineTxtService
{
    @Resource
    private ExamineTxtMapper examineTxtMapper;
    @Resource
    private PresentationMapper presentationMapper;

    /**
     * 查询课件审核建议
     *
     * @param id 课件审核建议主键
     * @return 课件审核建议
     */
    @Override
    public ExamineTxt selectExamineTxtById(Long id)
    {
        return examineTxtMapper.selectExamineTxtById(id);
    }

    @Override
    public String selectSuggestionByPId(Long id)
    {
        return examineTxtMapper.selectSuggestionByPId(id);
    }
    /**
     * 查询课件审核建议列表
     *
     * @param examineTxt 课件审核建议
     * @return 课件审核建议
     */
    @Override
    public List<ExamineTxt> selectExamineTxtList(ExamineTxt examineTxt)
    {
        return examineTxtMapper.selectExamineTxtList(examineTxt);
    }

    /**
     * 新增课件审核建议
     *
     * @param examineTxt 课件审核建议
     * @return 结果
     */
    @Override
    public int insertExamineTxt(ExamineTxt examineTxt)
    {
        examineTxtMapper.deleteExamineTxtByPId(examineTxt.getPresentionId());

        examineTxt.setCreateBy(SecurityUtils.getUsername());
        examineTxt.setCreateTime(DateUtils.getNowDate());
        examineTxt.setStatus(1);
        if (examineTxt.getIsExamine()==1) {
            presentationMapper.updatePresentation(Presentation.builder().id(examineTxt.getPresentionId()).isExamine(1).build());
            examineTxt.setSuggestion("");
        }
        if (examineTxt.getIsExamine()==0) {
            presentationMapper.updatePresentation(Presentation.builder().id(examineTxt.getPresentionId()).isExamine(2).build());
        }
        return examineTxtMapper.insertExamineTxt(examineTxt);
    }

    /**
     * 修改课件审核建议
     *
     * @param examineTxt 课件审核建议
     * @return 结果
     */
    @Override
    public int updateExamineTxt(ExamineTxt examineTxt)
    {
        examineTxt.setUpdateBy(SecurityUtils.getUsername());
        examineTxt.setUpdateTime(DateUtils.getNowDate());
        return examineTxtMapper.updateExamineTxt(examineTxt);
    }

    /**
     * 批量删除课件审核建议
     *
     * @param ids 需要删除的课件审核建议主键
     * @return 结果
     */
    @Override
    public int deleteExamineTxtByIds(Long[] ids)
    {
        return examineTxtMapper.deleteExamineTxtByIds(ids);
    }

    /**
     * 删除课件审核建议信息
     *
     * @param id 课件审核建议主键
     * @return 结果
     */
    @Override
    public int deleteExamineTxtById(Long id)
    {
        return examineTxtMapper.deleteExamineTxtById(id);
    }
    @Override
    public int deleteExamineTxtByPId(Long id)
    {
        return examineTxtMapper.deleteExamineTxtByPId(id);
    }

}
