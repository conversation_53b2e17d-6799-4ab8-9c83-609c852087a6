package com.ruoyi.create.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.create.domain.HomeworkQuestion;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.service.IHomeworkQuestionService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 作业题目Controller
 *
 * <AUTHOR>
 * @date 2024-06-07
 */
@RestController
@RequestMapping("/question")
public class HomeworkQuestionController extends BaseController
{
    @Autowired
    private IHomeworkQuestionService homeworkQuestionService;

    /**
     * 查询作业题目列表
     */
    @RequiresPermissions("create:question:list")
    @GetMapping("/list")
    public TableDataInfo list(HomeworkQuestion homeworkQuestion)
    {
        startPage();
        List<HomeworkQuestion> list = homeworkQuestionService.selectHomeworkQuestionList(homeworkQuestion);
        return getDataTable(list);
    }

    /**
     * 导出作业题目列表
     */
    @RequiresPermissions("create:question:export")
    @Log(title = "作业题目", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HomeworkQuestion homeworkQuestion)
    {
        List<HomeworkQuestion> list = homeworkQuestionService.selectHomeworkQuestionList(homeworkQuestion);
        ExcelUtil<HomeworkQuestion> util = new ExcelUtil<HomeworkQuestion>(HomeworkQuestion.class);
        util.exportExcel(response, list, "作业题目数据");
    }

    /**
     * 获取作业题目详细信息
     */
    @RequiresPermissions("create:question:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(homeworkQuestionService.selectHomeworkQuestionById(id));
    }

    /**
     * 新增作业题目
     */
    @RequiresPermissions("create:question:add")
    @Log(title = "作业题目", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HomeworkQuestion homeworkQuestion)
    {
        return toAjax(homeworkQuestionService.insertHomeworkQuestion(homeworkQuestion));
    }

    /**
     * 修改作业题目
     */
    @RequiresPermissions("create:question:edit")
    @Log(title = "作业题目", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HomeworkQuestion homeworkQuestion)
    {
        return toAjax(homeworkQuestionService.updateHomeworkQuestion(homeworkQuestion));
    }

    /**
     * 删除作业题目
     */
    @RequiresPermissions("create:question:remove")
    @Log(title = "作业题目", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(homeworkQuestionService.deleteHomeworkQuestionByIds(ids));
    }
}
