package com.ruoyi.create.mapper;

import com.ruoyi.create.domain.UserVoice;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-09
 */
@Mapper
public interface UserVoiceMapper
{
    /**
     * 查询【请填写功能名称】
     *
     * @param UserId 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public UserVoice selectUserVoiceById(Long UserId);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param userVoice 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<UserVoice> selectUserVoiceList(UserVoice userVoice);

    /**
     * 新增【请填写功能名称】
     *
     * @param userVoice 【请填写功能名称】
     * @return 结果
     */
    public int insertUserVoice(UserVoice userVoice);

    /**
     * 修改【请填写功能名称】
     *
     * @param userVoice 【请填写功能名称】
     * @return 结果
     */
    public int updateUserVoice(UserVoice userVoice);

    /**
     * 删除【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteUserVoiceById(Long id);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUserVoiceByIds(Long[] ids);
}
