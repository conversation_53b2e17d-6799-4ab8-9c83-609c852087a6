package com.ruoyi.create.service.impl;

import java.util.List;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.domain.CourseTopicsReplies;
import com.ruoyi.create.mapper.CourseTopicsRepliesMapper;
import com.ruoyi.create.mapper.TopicsReplyMapper;
import com.ruoyi.create.service.ICourseTopicsService;
import com.ruoyi.system.api.RemoteUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.CourseTopicsMapper;
import com.ruoyi.create.domain.CourseTopics;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 课程讨论话题Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
@Service
public class CourseTopicsServiceImpl implements ICourseTopicsService {
    @Resource
    private CourseTopicsMapper courseTopicsMapper;

    @Autowired
    private CourseTopicsRepliesMapper courseTopicsRepliesMapper;


    @Autowired
    private TopicsReplyMapper topicsReplyMapper;

    /**
     * 查询课程讨论话题
     *
     * @param id 课程讨论话题主键
     * @return 课程讨论话题
     */
    @Override
    public CourseTopics selectCourseTopicsById(Long id) {
        return courseTopicsMapper.selectCourseTopicsById(id);
    }

    /**
     * 查询课程讨论话题列表
     *
     * @param courseTopics 课程讨论话题
     * @return 课程讨论话题
     */
    @Override
    public List<CourseTopics> selectCourseTopicsList(CourseTopics courseTopics) {
        return courseTopicsMapper.selectCourseTopicsList(courseTopics);
    }

    /**
     * 新增课程讨论话题
     *
     * @param courseTopics 课程讨论话题
     * @return 结果
     */
    @Override
    public int insertCourseTopics(CourseTopics courseTopics) {
        courseTopics.setCreateTime(DateUtils.getNowDate());
        courseTopics.setCreateBy(String.valueOf(SecurityUtils.getUserId()));
        courseTopics.setSendId(SecurityUtils.getUserId());
        if (courseTopics.getSort() == null) {
            courseTopics.setSort(0L);
        }
        courseTopics.setLikeCount(0L);

        return courseTopicsMapper.insertCourseTopics(courseTopics);
    }

    /**
     * 修改课程讨论话题
     *
     * @param courseTopics 课程讨论话题
     * @return 结果
     */
    @Override
    public int updateCourseTopics(CourseTopics courseTopics) {
        if (courseTopics.getId() == null) {
            throw new RuntimeException("唯一主键id不能为空");
        }
        courseTopics.setUpdateTime(DateUtils.getNowDate());
        return courseTopicsMapper.updateCourseTopics(courseTopics);
    }

    /**
     * 批量删除课程讨论话题
     *
     * @param ids 需要删除的课程讨论话题主键
     * @return 结果
     */
    @Override
    public int deleteCourseTopicsByIds(Long[] ids) {
        return courseTopicsMapper.deleteCourseTopicsByIds(ids);
    }

    /**
     * 删除课程讨论话题信息
     *
     * @param id 课程讨论话题主键
     * @return 结果
     */
    @Override
    public int deleteCourseTopicsById(Long id) {
        return courseTopicsMapper.deleteCourseTopicsById(id);
    }

    /**
     * 查询课程讨论话题列表
     *
     * @param courseTopics 课程讨论话题
     * @return 课程讨论话题
     */
    @Override
    public List<CourseTopics> selectCourseTopicsListCondition(CourseTopics courseTopics) {
//        LoginUser loginUser = SecurityUtils.getLoginUser();
//        if (loginUser.getRoles().contains("admin")) {
//            // 是管理员
//            return courseTopicsMapper.listAllTopics(courseTopics);
//        } else if (StringUtils.isNotBlank(loginUser.getSysUser().getJobId())) {
//            // 是教师
//            return courseTopicsMapper.listAllTopicsForTeacher(courseTopics);
//        }else if (StringUtils.isNotBlank(loginUser.getSysUser().getStudentId())){
//            // 是学生
//            courseTopics.setLoginUserId(loginUser.getSysUser().getStudentId());
//            return courseTopicsMapper.listAllTopicsForStudent(courseTopics);
//        }
        return courseTopicsMapper.selectCourseTopicsList2(courseTopics);
    }


    /**
     * @description: 批量删除课程讨论话题信息，并且删除回复信息
     * @author: zhaoTianQi
     * @date: 2024/11/8 10:27
     * @param:
     * @param: ids
     * @return:
     * @return: int
     **/
    @Override
    @Transactional
    public int deleteCourseTopicsAndReplaceByIds(Long[] ids) {
        List<CourseTopicsReplies> courseTopicsReplies = courseTopicsRepliesMapper.selectCourseTopicsRepliesByIds(ids);
        if (courseTopicsReplies.size()>0){
            Long[] msgIds = new Long[courseTopicsReplies.size()];
            for (int i = 0; i < courseTopicsReplies.size(); i++) {
                msgIds[i] = courseTopicsReplies.get(i).getMsgId();
            }
            topicsReplyMapper.deleteTopicsReplyByIds(msgIds);
            courseTopicsRepliesMapper.deleteCourseTopicsRepliesByIds(msgIds);
        }

        return courseTopicsMapper.deleteCourseTopicsByIds(ids);
    }
}
