package com.ruoyi.create.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.domain.UserVoiceType;
import com.ruoyi.create.service.IUserVoiceTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 用户语音切换Controller
 *
 * <AUTHOR>
 * @date 2024-09-13
 */
@RestController
@RequestMapping("/uservoicetype")
public class UserVoiceTypeController extends BaseController
{
    @Autowired
    private IUserVoiceTypeService userVoiceTypeService;

    /**
     * 查询用户语音列表
     */
    @RequiresPermissions("create:uservoicetype:list")
    @GetMapping("/list")
    public TableDataInfo list(UserVoiceType userVoiceType)
    {
        startPage();
        List<UserVoiceType> list = userVoiceTypeService.selectUserVoiceTypeList(userVoiceType);
        return getDataTable(list);
    }

    /**
     * 导出用户语音列表
     */
    @RequiresPermissions("create:uservoicetype:export")
    @Log(title = "用户语音切换", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, UserVoiceType userVoiceType)
    {
        List<UserVoiceType> list = userVoiceTypeService.selectUserVoiceTypeList(userVoiceType);
        ExcelUtil<UserVoiceType> util = new ExcelUtil<UserVoiceType>(UserVoiceType.class);
        util.exportExcel(response, list, "用户语音切换数据");
    }

    /**
     * 获取用户语音详细信息
     */
    @RequiresPermissions("create:uservoicetype:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(userVoiceTypeService.selectUserVoiceTypeById(id));
    }

    /**
     * 新增用户语音
     */
    @RequiresPermissions("create:uservoicetype:add")
    @Log(title = "用户语音切换", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UserVoiceType userVoiceType)
    {
        return toAjax(userVoiceTypeService.insertUserVoiceType(userVoiceType));
    }

    /**
     * 修改用户语音
     */
    @RequiresPermissions("create:uservoicetype:edit")
    @Log(title = "用户语音切换", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UserVoiceType userVoiceType)
    {
        return toAjax(userVoiceTypeService.updateUserVoiceType(userVoiceType));
    }

    /**
     * 删除用户语音
     */
    @RequiresPermissions("create:uservoicetype:remove")
    @Log(title = "用户语音切换", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(userVoiceTypeService.deleteUserVoiceTypeByIds(ids));
    }

    /**
     * 根据登陆人存储语音种类
     */
    @Log(title = "用户语音切换", businessType = BusinessType.INSERT)
    @PostMapping("/storageVoice")
    public AjaxResult storageVoice(@RequestBody UserVoiceType userVoiceType)
    {
        return toAjax(userVoiceTypeService.updateUserVoiceTypeByStorage(userVoiceType));
    }

    /**
     * 根据登陆人查询语音种类
     */
    @GetMapping("/selectVoiceType")
    public AjaxResult selectVoiceType()
    {
        UserVoiceType userVoiceType = userVoiceTypeService.selectUserVoiceTypeByUserName(SecurityUtils.getUsername());
        if(userVoiceType != null){
            if(userVoiceType.getVoiceType()==null || userVoiceType.getVoiceType()==""){
                userVoiceType.setVoiceType("CN");
            }
        }else{
            UserVoiceType userVoiceType1 = new UserVoiceType();
            userVoiceType1.setVoiceType("CN");
            return success(userVoiceType1);
        }

        return success(userVoiceType);
    }
}
