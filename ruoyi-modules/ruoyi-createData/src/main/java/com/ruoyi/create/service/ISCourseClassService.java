package com.ruoyi.create.service;

import com.ruoyi.create.domain.AttendanceParam;
import com.ruoyi.create.domain.SCourseClass;

import java.util.List;

/**
 * 课程班级关联Service接口
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
public interface ISCourseClassService
{
    /**
     * 查询课程班级关联
     *
     * @param id 课程班级关联主键
     * @return 课程班级关联
     */
    public SCourseClass selectSCourseClassById(Long id);

    /**
     * 查询课程班级关联列表
     *
     * @param attendanceParam 课程班级关联
     * @return 课程班级关联集合
     */
    public List<AttendanceParam> selectSCourseClassList(AttendanceParam attendanceParam);

    /**
     * 发起签到
     *
     * @param attendanceParam 课程班级关联主键
     * @return 结果
     */
    public int initiateAttendance(AttendanceParam attendanceParam);
}
