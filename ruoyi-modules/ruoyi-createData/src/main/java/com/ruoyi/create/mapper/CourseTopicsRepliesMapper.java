package com.ruoyi.create.mapper;

import java.util.List;
import com.ruoyi.create.domain.CourseTopicsReplies;

/**
 * 课程讨论话题回复Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-04
 */
public interface CourseTopicsRepliesMapper 
{
    /**
     * 查询课程讨论话题回复
     * 
     * @param id 课程讨论话题回复主键
     * @return 课程讨论话题回复
     */
    public CourseTopicsReplies selectCourseTopicsRepliesById(Long id);

    /**
     * 查询课程讨论话题回复列表
     * 
     * @param courseTopicsReplies 课程讨论话题回复
     * @return 课程讨论话题回复集合
     */
    public List<CourseTopicsReplies> selectCourseTopicsRepliesList(CourseTopicsReplies courseTopicsReplies);

    /**
     * 新增课程讨论话题回复
     * 
     * @param courseTopicsReplies 课程讨论话题回复
     * @return 结果
     */
    public int insertCourseTopicsReplies(CourseTopicsReplies courseTopicsReplies);

    /**
     * 修改课程讨论话题回复
     * 
     * @param courseTopicsReplies 课程讨论话题回复
     * @return 结果
     */
    public int updateCourseTopicsReplies(CourseTopicsReplies courseTopicsReplies);

    /**
     * 删除课程讨论话题回复
     * 
     * @param id 课程讨论话题回复主键
     * @return 结果
     */
    public int deleteCourseTopicsRepliesById(Long id);

    /**
     * 批量删除课程讨论话题回复
     * 
     * @param msgIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCourseTopicsRepliesByIds(Long[] msgIds);

    List<CourseTopicsReplies> selectCourseTopicsRepliesByIds(Long[] ids);
}
