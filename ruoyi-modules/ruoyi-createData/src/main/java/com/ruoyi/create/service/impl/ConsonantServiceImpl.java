package com.ruoyi.create.service.impl;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.create.domain.PlatParam;
import com.ruoyi.create.domain.PptSpeechDraft;
import com.ruoyi.system.api.RemoteDictTypeService;
import com.ruoyi.system.api.domain.SysDictData;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.ConsonantMapper;
import com.ruoyi.create.domain.Consonant;
import com.ruoyi.create.service.IConsonantService;

import javax.annotation.Resource;

/**
 * 讲演稿声母韵母Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-26
 */
@Service
public class ConsonantServiceImpl implements IConsonantService
{
    @Resource
    private ConsonantMapper consonantMapper;

    @Resource
    private RemoteDictTypeService remoteDictTypeService;

    /**
     * 查询讲演稿声母韵母
     *
     * @param id 讲演稿声母韵母主键
     * @return 讲演稿声母韵母
     */
    @Override
    public Consonant selectConsonantById(Long id)
    {
        return consonantMapper.selectConsonantById(id);
    }

    /**
     * 查询讲演稿声母韵母列表
     *
     * @param consonant 讲演稿声母韵母
     * @return 讲演稿声母韵母
     */
    @Override
    public List<Consonant> selectConsonantList(Consonant consonant)
    {
        return consonantMapper.selectConsonantList(consonant);
    }

    /**
     * 新增讲演稿声母韵母
     *
     * @param consonant 讲演稿声母韵母
     * @return 结果
     */
    @Override
    public int insertConsonant(Consonant consonant)
    {
        consonant.setCreateTime(DateUtils.getNowDate());
        return consonantMapper.insertConsonant(consonant);
    }

    /**
     * 修改讲演稿声母韵母
     *
     * @param consonant 讲演稿声母韵母
     * @return 结果
     */
    @Override
    public int updateConsonant(Consonant consonant)
    {
        consonant.setUpdateTime(DateUtils.getNowDate());
        return consonantMapper.updateConsonant(consonant);
    }

    /**
     * 批量删除讲演稿声母韵母
     *
     * @param ids 需要删除的讲演稿声母韵母主键
     * @return 结果
     */
    @Override
    public int deleteConsonantByIds(Long[] ids)
    {
        return consonantMapper.deleteConsonantByIds(ids);
    }

    /**
     * 删除讲演稿声母韵母信息
     *
     * @param id 讲演稿声母韵母主键
     * @return 结果
     */
    @Override
    public int deleteConsonantById(Long id)
    {
        return consonantMapper.deleteConsonantById(id);
    }

    @Override
    public int deleteConsonantByPresationId(Long id)
    {
        return consonantMapper.deleteConsonantByPresationId(id);
    }


    @Override
    public Consonant getConsonant(PlatParam platParam) {

        Consonant consonant = Consonant.builder()
                .consonant(getConsonantStr(platParam.getMotionNoList()))
                .indexPage(String.valueOf(platParam.getIndexPage()))
                .indexSentence(String.valueOf(platParam.getIndexSentence()))
                .txtSentence(platParam.getTxtSentence())
                .motion(platParam.getMotion())
                .status(1)
                .speechdraftId(platParam.getPresentationFrom())
                .build();
        consonant.setCreateTime(new Date());
        return consonant;
    }

    @Override
    public String getConsonantStr(List<String> list) {
        String consonantStr = consonantToStr(list, "/", "/");
        return consonantStr;
    }

    private static String consonantToStr(List<String> motionNoList, String consonantSplide, String consonantPrefix) {
        List<String> result = new ArrayList<>();
        Pattern actionPattern = Pattern.compile("@action:\\w+");

        for (int i = 0; i < motionNoList.size(); i++) {
            String current = removeTrailingNumber(motionNoList.get(i));
            String next = i + 1 < motionNoList.size() ? removeTrailingNumber(motionNoList.get(i + 1)) : "";

            // 将标点符号替换为 "/"
            if (isPunctuation(current)) {
                result.add(consonantSplide);
                continue;
            }

            // 如果匹配到动作标识，添加动作标识并在后面加 "/"
            if (actionPattern.matcher(current).matches()) {
                result.add(current + consonantSplide);
                continue;
            }


            if (next.isEmpty() || isConsonant(next)) { // 如果下一个是空字符串或者是声母
                if(splitVowels(current).size()>1){
                    result.add(consonantSplide);
                }else {
                    result.add(current + consonantSplide);
                }

            } else if (isVowel(next)) {
                // 处理伪韵母的拆分
                List<String> splitVowels = splitVowels(next);
                if (splitVowels.size() > 1) {
                    // 如果是伪韵母，拆分并添加
                    for (int j = 0; j < splitVowels.size(); j++) {
                        if (j > 0) {
                            result.add(consonantPrefix); // 添加分隔符
                        }
                        result.add(current + consonantPrefix + splitVowels.get(j));
                        current = ""; // 清空 current 以防重复添加
                    }
                } else {

                    result.add(current + consonantPrefix );
                }

            }else {
                if(splitVowels(current).size()<=1){
                    result.add(current);
                }
            }
        }

        // 拼接最后结果，去掉多余的分隔符
        String output = String.join("", result).replaceAll("[" + consonantPrefix + consonantSplide + "]+$", "").replaceAll("//","/").replaceAll("u:","u");
        System.out.println(output);
        return output;
    }

    // 拆分伪韵母
    private static List<String> splitVowels(String s) {
        List<String> split = new ArrayList<>();
        // 列出所有的伪韵母及其对应的拆分
        Map<String, List<String>> vowelSplits = new HashMap<>();

        vowelSplits.put("iang", Arrays.asList("i", "ang"));
        vowelSplits.put("iong", Arrays.asList("i", "ong"));
        vowelSplits.put("uang", Arrays.asList("u", "ang"));

        vowelSplits.put("uai", Arrays.asList("u", "ai"));
        vowelSplits.put("üan", Arrays.asList("ü", "an"));
        vowelSplits.put("uan", Arrays.asList("u", "an"));
        vowelSplits.put("iao", Arrays.asList("i", "ao"));
        vowelSplits.put("ian", Arrays.asList("i", "an"));

       // vowelSplits.put("in", Arrays.asList("i", "n"));
        vowelSplits.put("ua", Arrays.asList("u", "a"));
        vowelSplits.put("uo", Arrays.asList("u", "o"));
        vowelSplits.put("ia", Arrays.asList("i", "a"));




        if (vowelSplits.containsKey(s)) {
            split.addAll(vowelSplits.get(s));
        } else {
            split.add(s); // 如果不是伪韵母，直接返回
        }

        return split;
    }

    // 去掉字符串末尾的数字
    private static String removeTrailingNumber(String s) {
        return s.replaceAll("\\d+$", "");
    }

    private static boolean isConsonant(String s) {
        // 判断是否是声母
        return s.matches("b|p|m|f|d|t|n|l|g|k|h|j|q|x|zh|ch|sh|r|z|c|s|y|w");
    }

    private static boolean isVowel(String s) {
        // 判断是否是韵母
        return s.matches(".*(a|o|e|i|u|ü|ai|ei|ui|ao|ou|iu|ie|üe|er|an|en|in|un|ün|ang|eng|ing|ong).*");
    }

    private static boolean isPunctuation(String s) {
        return s.matches("[，。！？]");
    }






    @Override
    public List<PptSpeechDraft> getPptSpeechDraft(String speechdraftPath) {

        List<SysDictData> digitalhumanAction = remoteDictTypeService.dictTypeGetInfo("digitalhuman_action", SecurityConstants.INNER);

        //获取每一页存放演讲内容
        List<PptSpeechDraft> lists = readLinesByPage(speechdraftPath,digitalhumanAction);

        return lists;
    }

    public List<PptSpeechDraft> readLinesByPage(String filePath, List<SysDictData> digitalhumanAction) {
        List<PptSpeechDraft> currentPage = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String pageString = "";
            String line;
            while ((line = reader.readLine()) != null) {
                // 检查是否是 @page 行
                if (line.contains("@page")) {
                    // 去掉 @page
                    line = line.replace("@page", "").trim();
                    if (StringUtils.isNotBlank(line)) {
                        pageString = pageString + line;
                        PptSpeechDraft aoo = new PptSpeechDraft();
                        aoo.setBeforeAnalysisContent(pageString); // 保留未处理的内容
                        aoo.setContent(removeKeywords(pageString, digitalhumanAction)); // 处理并设置内容
                        currentPage.add(aoo); // 添加行到当前页面
                        pageString = "";
                    }
                } else {
                    if (StringUtils.isNotBlank(line)) {
                        pageString = pageString + line;
                    }
                }
            }
            if (StringUtils.isNotBlank(pageString)) { // 最后一行数据
                PptSpeechDraft aoo = new PptSpeechDraft();
                aoo.setBeforeAnalysisContent(pageString); // 保留未处理的内容
                aoo.setContent(removeKeywords(pageString, digitalhumanAction)); // 处理并设置内容
                currentPage.add(aoo); // 添加行到当前页面
            }

        } catch (IOException e) {
            e.printStackTrace();
        }

        return currentPage;
    }





    @Override
    public PlatParam getPlatParam(PlatParam platParam) {
        List<String> strings = analyzeTextAndExtractActions(platParam.getBeforeAnalysisContent());
        platParam.setMotionNoList(strings);
        return platParam;
    }



    private String removeKeywords(String content, List<SysDictData> digitalhumanAction) {
        for (SysDictData data : digitalhumanAction) {
            // 假设关键词是以 "@action:" 开头
            String keyword = data.getDictValue();
            if (content.contains(keyword)) {
                content = content.replace(keyword, "");
            }
        }
        return content.trim();
    }

    public static List<String> analyzeTextAndExtractActions(String text) {
        // 定义一个正则表达式来匹配整个 @action: 开头的标签
        Pattern actionPattern = Pattern.compile("@action:\\w+");

        // 创建一个 Matcher 对象来匹配文本
        Matcher matcher = actionPattern.matcher(text);

        List<String> allElements = new ArrayList<>();
        List<String> segments = new ArrayList<>();

        int lastEnd = 0;
        while (matcher.find()) {
            // 保存动作标签
            String action = matcher.group();

            // 保存动作标签前的文本段
            segments.add(text.substring(lastEnd, matcher.start()));

            // 分析这段文本中的声母和韵母
            for (int i = lastEnd; i < matcher.start(); i++) {
                char c = text.charAt(i);
                if (Character.UnicodeBlock.of(c) == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS) { // 只处理中文字符
                    String pinyin =getPinyin(String.valueOf(c));
                    String shengmu =getShengmu(pinyin);
                    String yunmu =getYunmu(pinyin);
//                    String pinyin = PinyinHelper.toHanyuPinyinStringArray(c)[0];
//                    String shengmu = pinyin.substring(0, 1); // 声母
//                    String yunmu = pinyin.substring(1);      // 韵母

                    allElements.add(shengmu);
                    allElements.add(yunmu);
                } else {
                    // 如果不是中文字符，直接添加到列表中
                    allElements.add(String.valueOf(c));
                }
            }

            // 添加动作标签
            allElements.add(action);

            // 更新 lastEnd
            lastEnd = matcher.end();
        }

        // 保存最后一个文本段
        segments.add(text.substring(lastEnd));

        // 分析最后一个文本段中的声母和韵母
        for (int i = lastEnd; i < text.length(); i++) {
            char c = text.charAt(i);
            if (Character.UnicodeBlock.of(c) == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS) { // 只处理中文字符
                String pinyin =getPinyin(String.valueOf(c));
                String shengmu =getShengmu(pinyin);
                String yunmu =getYunmu(pinyin);
//                String pinyin = PinyinHelper.toHanyuPinyinStringArray(c)[0];
//                String shengmu = pinyin.substring(0, 1); // 声母
//                String yunmu = pinyin.substring(1);      // 韵母
                allElements.add(shengmu);
                allElements.add(yunmu);
            } else {
                // 如果不是中文字符，直接添加到列表中
                allElements.add(String.valueOf(c));
            }
        }

        return allElements;
    }

    public static String getPinyin(String chineseCharacter) {
        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        String pinyin ="";
        try {
            // 获取汉字的拼音
            String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(chineseCharacter.charAt(0), format);
            if (pinyinArray != null) {
                pinyin = pinyinArray[0];
 //               System.out.println(pinyin);
//                System.out.println("拼音: " + pinyin);
//
//                // 分离声母和韵母
//                String shengmu = getShengmu(pinyin);
//                String yunmu = getYunmu(pinyin);
//
//                System.out.println("声母: " + shengmu);
//                System.out.println("韵母: " + yunmu);
            }
        } catch (BadHanyuPinyinOutputFormatCombination e) {
            e.printStackTrace();
        }
        return pinyin;
    }

    private static String getShengmu(String pinyin) {
        String[] shengmuArray = {"b", "p", "m", "f", "d", "t", "n", "l", "g", "k", "h", "j", "q", "x", "zh", "ch", "sh", "r", "z", "c", "s", "y", "w"};
        for (String shengmu : shengmuArray) {
            if (pinyin.startsWith(shengmu)) {
                return shengmu;
            }
        }
        return "";
    }

    private static String getYunmu(String pinyin) {
        String shengmu = getShengmu(pinyin);
        if (!shengmu.isEmpty()) {
            return pinyin.substring(shengmu.length());
        } else {
            return pinyin;
        }
    }


    @Override
    public int updateSpeechdraftId(String oldSpeechdraftId, String newSpeechdraftId) {
        return consonantMapper.updateSpeechdraftId(oldSpeechdraftId, newSpeechdraftId);
    }





}
