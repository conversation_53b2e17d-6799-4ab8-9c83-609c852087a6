package com.ruoyi.create.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.create.domain.SMessage;
import com.ruoyi.create.domain.SMsgReply;
import com.ruoyi.create.service.ISMsgReplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 留言与回复关联Controller
 * 
 * <AUTHOR>
 * @date 2024-06-05
 */
@RestController
@RequestMapping("/reply")
public class SMsgReplyController extends BaseController
{
    @Autowired
    private ISMsgReplyService sMsgReplyService;

    /**
     * 查询留言与回复关联列表
     */
    @RequiresPermissions("system:reply:list")
    @GetMapping("/list")
    public TableDataInfo list(SMsgReply sMsgReply)
    {
        startPage();
        List<SMsgReply> list = sMsgReplyService.selectSMsgReplyList(sMsgReply);
        return getDataTable(list);
    }

    /**
     * 导出留言与回复关联列表
     */
    @RequiresPermissions("system:reply:export")
    @Log(title = "留言与回复关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SMsgReply sMsgReply)
    {
        List<SMsgReply> list = sMsgReplyService.selectSMsgReplyList(sMsgReply);
        ExcelUtil<SMsgReply> util = new ExcelUtil<SMsgReply>(SMsgReply.class);
        util.exportExcel(response, list, "留言与回复关联数据");
    }

    /**
     * 获取留言与回复关联详细信息
     */
    @RequiresPermissions("system:reply:query")
    @GetMapping(value = "/{msgId}")

    public AjaxResult getInfo(@PathVariable("msgId") Long msgId)
    {
        return success(sMsgReplyService.selectSMsgReplyByMsgId(msgId));
    }

    /**
     * 新增留言与回复关联
     */
    @RequiresPermissions("system:reply:add")
    @Log(title = "留言与回复关联", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SMsgReply sMsgReply)
    {
        return toAjax(sMsgReplyService.insertSMsgReply(sMsgReply));
    }

    /**
     * 修改留言与回复关联
     */
    @RequiresPermissions("system:reply:edit")
    @Log(title = "留言与回复关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SMsgReply sMsgReply)
    {
        return toAjax(sMsgReplyService.updateSMsgReply(sMsgReply));
    }

    /**
     * 删除留言与回复关联
     */
    @RequiresPermissions("system:reply:remove")
    @Log(title = "留言与回复关联", businessType = BusinessType.DELETE)
	@DeleteMapping("/{msgIds}")
    public AjaxResult remove(@PathVariable Long[] msgIds)
    {
        return toAjax(sMsgReplyService.deleteSMsgReplyByMsgIds(msgIds));
    }

    /**
     * 查询此留言的全部回复信息
     */
    @RequiresPermissions("system:reply:all")
    @GetMapping("/all/{msgId}")
    public AjaxResult selectMsgAndReplyAll(@PathVariable("msgId") Long msgId)
    {
        List<SMessage> list = sMsgReplyService.selectMsgAndReplyAll(msgId);
        return success(list);
    }

    /**
     * 删除留言与回复关联
     */
    @RequiresPermissions("system:reply:removeReply")
    @Log(title = "留言与回复关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/removeReply/{replyIds}")
    public AjaxResult removeReplyIds(@PathVariable Long[] replyIds)
    {
        return toAjax(sMsgReplyService.deleteSMsgReplyByReplyIds(replyIds));
    }
}
