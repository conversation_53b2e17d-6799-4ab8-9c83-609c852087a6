package com.ruoyi.create.mapper;

import com.github.houbb.heaven.annotation.reflect.Param;
import com.ruoyi.create.Vo.StudentAttendanceVo;
import com.ruoyi.create.domain.AttendanceParam;
import com.ruoyi.create.domain.CourseManagement;
import com.ruoyi.create.domain.StudentAttendance;
import com.ruoyi.create.domain.StudentSign;

import java.util.List;

/**
 * 老师课程Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
public interface StudentSignMapper {

    /**
     * 查询学生学习历史
     *
     * @param presentationId 学生学习历史主键
     * @return 学生学习历史
     */
    public StudentSign selectStudentSignById(Long presentationId);

    /**
     * 查询学生学习历史列表
     *
     * @param studentSign 学生学习历史
     * @return 学生学习历史集合
     */
    public List<StudentSign> selectStudentSignList(StudentSign studentSign);


    public List<StudentSign> selectBystudentId(String studentId);

    public String selectIsAttendanceById(Long id);

    public void insertStudentInfo(StudentSign studentSign);

    public List<CourseManagement> selectStudentIdList(StudentSign studentSign);

    public void deleteStudentInfo(StudentSign studentSign);

    public List<StudentSign> selectStudentAttendanceListByStudentId(StudentSign studentSign);

    public List<StudentSign> selectStudentAttendanceByCourseIdAndStudentId(String courseName, String studentId);

    public int updateStudentAttendance(StudentSign stu);

    public String selectClassByStudentId(String studentId);

    public AttendanceParam selectById(Long id);

    public String selectSignType(StudentSign studentSign);


    public String selectSignTypeBySignId(StudentSign studentSign);


}
