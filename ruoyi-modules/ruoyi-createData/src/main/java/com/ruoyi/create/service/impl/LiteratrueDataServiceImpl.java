package com.ruoyi.create.service.impl;

import com.alibaba.excel.EasyExcel;
import com.ruoyi.common.core.utils.easyexcel.EasyExcelUtils;
import com.ruoyi.common.core.utils.easyexcel.Message;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.domain.LiteratrueData;
import com.ruoyi.create.domain.LiteratrueKeywordCount;
import com.ruoyi.create.mapper.LiteratrueDataMapper;
import com.ruoyi.create.mapper.LiteratrueKeywordCountMapper;
import com.ruoyi.create.service.ILiteratrueDataService;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.domain.SysFileInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 文献整理-原始数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-18
 */
@Service
public class LiteratrueDataServiceImpl implements ILiteratrueDataService
{
    @Autowired
    private LiteratrueDataMapper literatrueDataMapper;



    @Autowired
    private LiteratrueKeywordCountMapper literatrueKeywordCountMapper;

    @Resource
    private RemoteFileService remoteFileService;

    /**
     * 查询文献整理-原始数据
     * 
     * @param id 文献整理-原始数据主键
     * @return 文献整理-原始数据
     */
    @Override
    public LiteratrueData selectLiteratrueDataById(Long id)
    {
        return literatrueDataMapper.selectLiteratrueDataById(id);
    }

    /**
     * 查询文献整理-原始数据列表
     * 
     * @param literatrueData 文献整理-原始数据
     * @return 文献整理-原始数据
     */
    @Override
    public List<LiteratrueData> selectLiteratrueDataList(LiteratrueData literatrueData)
    {
        return literatrueDataMapper.selectLiteratrueDataList(literatrueData);
    }

    /**
     * 新增文献整理-原始数据
     * 
     * @param literatrueData 文献整理-原始数据
     * @return 结果
     */
    @Override
    public AjaxResult insertLiteratrueData(LiteratrueData literatrueData)
    {
        //读取文件
        Message message = new Message();
        SysFileInfo sysFileInfo = remoteFileService.getFileInfo(literatrueData.getFileId());
        if(sysFileInfo ==null){
            return AjaxResult.error("未获取到文件");
        }
        InputStream inputStream = null;
        EasyExcelUtils easyExcelUtils = new EasyExcelUtils(LiteratrueData.class);  //创建工具类时传递class，用于后面比对表头使用
        List<LiteratrueData> roadDataList = new ArrayList<>();
        try {
            inputStream = new FileInputStream(sysFileInfo.getFilePath());
            //读取所有的sheet
            EasyExcel.read(inputStream, LiteratrueData.class,easyExcelUtils).doReadAll();
            //只能读取到最前面的一个sheet
           //EasyExcel.read(inputStream,LiteratrueData.class,easyExcelUtils).sheet().doRead();
            message = easyExcelUtils.getMessage();
            if(Message.OK == message.getType()){
                List<Object> list = easyExcelUtils.getList();
                if (null != list && list.size() > 0) {
                    Date nowDate = new Date();
                    for (int i = 0;i < list.size();i++) { //设置其他非excel字段的值
                        LiteratrueData roadData = (LiteratrueData) list.get(i);
                        roadData.setTitleId(roadData.getTitleId());
                        roadData.setTitle(roadData.getTitle());
                        roadData.setCategory(roadData.getCategory());
                        roadData.setKeyword(roadData.getKeyword());
                        roadData.setCreateTime(nowDate);
                        roadData.setCreateBy(SecurityUtils.getUsername());
                        roadDataList.add(roadData);
                    }

                }
            }else{
                return AjaxResult.error(message.getMsg());
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        /*Map<String, Long> titleToIdMap = new HashMap<>();
        Long nextId = literatrueDataMapper.selectTitleId(); // 从 1 开始分配 id
        nextId = nextId!=null?nextId:1L;
        for (LiteratrueData roadData : roadDataList) {
            String title = roadData.getTitle();
            if (!titleToIdMap.containsKey(title)) {
                // 如果标题不在 map 中，则添加它并分配一个新 id
                titleToIdMap.put(title, nextId);
                nextId++; // 为下一个标题准备新的 id
            }
            // 获取当前标题对应的 id
            Long titleId = titleToIdMap.get(title);
            roadData.setTitleId(titleId);
        }*/
        int i = literatrueDataMapper.insertLiteratrueDataList(roadDataList);
        if(!(i>0)){
            return AjaxResult.error();
        }
        //生成目标表数据 并 修改目标表数据
        List<LiteratrueData> collect = roadDataList.stream()
                .distinct() // 去除 Keyword 字段的重复
                .collect(Collectors.toList());

        for (LiteratrueData data : collect) {
            LiteratrueKeywordCount literatrueKeywordCount = new LiteratrueKeywordCount();
            literatrueKeywordCount.setKeyword(data.getKeyword());
            List<LiteratrueKeywordCount> literatrueKeywordCounts = literatrueKeywordCountMapper.selectLiteratrueKeywordCountList(literatrueKeywordCount);
            if(!literatrueKeywordCounts.isEmpty()){
                //统计本次上传的数量
                Long aLong = countKeywordOccurrences(roadDataList,data.getKeyword());
                LiteratrueKeywordCount literatrueKeywordCount1 = literatrueKeywordCounts.get(0);
                literatrueKeywordCount1.setCount(aLong);
                literatrueKeywordCountMapper.updateLiteratrueKeywordCount(literatrueKeywordCount1);
            }else{
                Long aLong = countKeywordOccurrences(roadDataList,data.getKeyword());
                literatrueKeywordCount.setTitleId(data.getTitleId());
                literatrueKeywordCount.setTitle(data.getTitle());
                literatrueKeywordCount.setCategory(data.getCategory());
                literatrueKeywordCount.setKeyword(data.getKeyword());
                literatrueKeywordCount.setSubjectId(
                        data.getCategory().equals("高影响力作者") ? "1" :
                                data.getCategory().equals("研究机构") ? "2" :
                                        "0"
                );
                literatrueKeywordCount.setCount(aLong);

                literatrueKeywordCountMapper.insertLiteratrueKeywordCount(literatrueKeywordCount);
            }
        }
        return AjaxResult.success();
    }

    public static Long countKeywordOccurrences(List<LiteratrueData> roadDataList, String keyword) {
        return roadDataList.stream()
                .filter(data -> data.getKeyword().equals(keyword))
                .count();
    }



    /**
     * 修改文献整理-原始数据
     * 
     * @param literatrueData 文献整理-原始数据
     * @return 结果
     */
    @Override
    public int updateLiteratrueData(LiteratrueData literatrueData)
    {
        return literatrueDataMapper.updateLiteratrueData(literatrueData);
    }

    /**
     * 批量删除文献整理-原始数据
     * 
     * @param ids 需要删除的文献整理-原始数据主键
     * @return 结果
     */
    @Override
    public int deleteLiteratrueDataByIds(Long[] ids)
    {
        return literatrueDataMapper.deleteLiteratrueDataByIds(ids);
    }

    /**
     * 删除文献整理-原始数据信息
     * 
     * @param id 文献整理-原始数据主键
     * @return 结果
     */
    @Override
    public int deleteLiteratrueDataById(Long id)
    {
        return literatrueDataMapper.deleteLiteratrueDataById(id);
    }

    @Override
    public List<LiteratrueData> selectClickLiteratrue(LiteratrueData literatrueData) {
        //根据初始化关键词  查询论文
        List<LiteratrueData> literatrueDataList = literatrueDataMapper.selectClickLiteratrueData(literatrueData);
        return literatrueDataList;
    }
}
