package com.ruoyi.create.service.impl;

import java.util.List;

import com.ruoyi.create.domain.StudentEvaluation;
import com.ruoyi.create.mapper.StudentEvaluationMapper;
import com.ruoyi.create.service.StudentEvaluationService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 学生测评Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-19
 */
@Service
public class StudentEvaluationServiceImpl implements StudentEvaluationService
{
    @Resource
    private StudentEvaluationMapper studentEvaluationMapper;

    /**
     * 查询学生测评
     *
     * @param
     * @return 学生测评
     */
    @Override
    public StudentEvaluation selectStudentEvaluationById(Long id)
    {
        return studentEvaluationMapper.selectStudentEvaluationById(id);
    }

    /**
     * 查询学生测评列表
     *
     * @param
     * @return 学生测评
     */
    @Override
    public List<StudentEvaluation> selectStudentEvaluationList(String studentId)
    {
        return studentEvaluationMapper.selectStudentEvaluationList(studentId);
    }

    /**
     * 新增学生测评
     *
     * @param studentEvaluation 学生测评
     * @return 结果
     */
    @Override
    public int insertStudentEvaluation(StudentEvaluation studentEvaluation)
    {
        return studentEvaluationMapper.insertStudentEvaluation(studentEvaluation);
    }

    /**
     * 修改学生测评
     *
     * @param sStudentEvaluation 学生测评
     * @return 结果
     */
    @Override
    public int updateStudentEvaluation(StudentEvaluation sStudentEvaluation)
    {
        return studentEvaluationMapper.updateStudentEvaluation(sStudentEvaluation);
    }

    /**
     * 批量删除学生测评
     *
     * @param ids 需要删除的学生测评主键
     * @return 结果
     */
    @Override
    public int deleteStudentEvaluationByIds(Long[] ids)
    {
        return studentEvaluationMapper.deleteStudentEvaluationByIds(ids);
    }

    /**
     * 删除学生测评信息
     *
     * @param id 学生测评主键
     * @return 结果
     */
    @Override
    public int deleteStudentEvaluationById(String id)
    {
        return studentEvaluationMapper.deleteStudentEvaluationById(id);
    }
}
