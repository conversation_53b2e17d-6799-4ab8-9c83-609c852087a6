package com.ruoyi.create.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.TextbookCount;
import com.ruoyi.create.service.ISTextbookCountService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 文献整理- 教材数量目标Controller
 * 
 * <AUTHOR>
 * @date 2024-07-31
 */
@RestController
@RequestMapping("/textbookcount")
public class STextbookCountController extends BaseController
{
    @Autowired
    private ISTextbookCountService sTextbookCountService;

    /**
     * 查询文献整理- 教材数量目标列表
     */
    @RequiresPermissions("create:textbookcount:list")
    @GetMapping("/list")
    public TableDataInfo list(TextbookCount sTextbookCount)
    {
        startPage();
        List<TextbookCount> list = sTextbookCountService.selectSTextbookCountList(sTextbookCount);
        return getDataTable(list);
    }

    /**
     * 导出文献整理- 教材数量目标列表
     */
    @RequiresPermissions("create:textbookcount:export")
    @Log(title = "文献整理- 教材数量目标", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TextbookCount sTextbookCount)
    {
        List<TextbookCount> list = sTextbookCountService.selectSTextbookCountList(sTextbookCount);
        ExcelUtil<TextbookCount> util = new ExcelUtil<TextbookCount>(TextbookCount.class);
        util.exportExcel(response, list, "文献整理- 教材数量目标数据");
    }

    /**
     * 获取文献整理- 教材数量目标详细信息
     */
    @RequiresPermissions("create:textbookcount:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sTextbookCountService.selectSTextbookCountById(id));
    }

    /**
     * 新增文献整理- 教材数量目标
     */
    @RequiresPermissions("create:textbookcount:add")
    @Log(title = "文献整理- 教材数量目标", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TextbookCount sTextbookCount)
    {
        return toAjax(sTextbookCountService.insertSTextbookCount(sTextbookCount));
    }

    /**
     * 修改文献整理- 教材数量目标
     */
    @RequiresPermissions("create:textbookcount:edit")
    @Log(title = "文献整理- 教材数量目标", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TextbookCount sTextbookCount)
    {
        return toAjax(sTextbookCountService.updateSTextbookCount(sTextbookCount));
    }

    /**
     * 删除文献整理- 教材数量目标
     */
    @RequiresPermissions("create:textbookcount:remove")
    @Log(title = "文献整理- 教材数量目标", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sTextbookCountService.deleteSTextbookCountByIds(ids));
    }
}
