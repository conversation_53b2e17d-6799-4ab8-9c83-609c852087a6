package com.ruoyi.create.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.create.domain.Homework;
import com.ruoyi.create.service.IHomeworkService;
import com.ruoyi.create.service.IHomeworkStudentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 作业信息Controller
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@RestController
@RequestMapping("/homework")
@Api(tags = "生成作业")
public class HomeworkController extends BaseController
{
    @Autowired
    private IHomeworkService homeworkService;
    @Autowired
    private IHomeworkStudentService homeworkStudentService;

    /**
     * 查询作业信息列表
     */
    @RequiresPermissions("system:homework:list")
    @GetMapping("/list")
    public TableDataInfo list(Homework homework)
    {
        startPage();
        List<Homework> list = homeworkService.selectHomeworkList(homework);
        return getDataTable(list);
    }

    /**
     * 导出作业信息列表
     */
    @RequiresPermissions("system:homework:export")
    @Log(title = "作业信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Homework homework)
    {
        List<Homework> list = homeworkService.selectHomeworkList(homework);
        ExcelUtil<Homework> util = new ExcelUtil<Homework>(Homework.class);
        util.exportExcel(response, list, "作业信息数据");
    }

    /**
     * 获取作业信息详细信息
     */
    @RequiresPermissions("system:homework:query")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取作业信息详细信息", notes = "包含作业信息、题目信息、题目选项信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(homeworkService.selectHomeworkById(id));
    }

    /**
     * 新增作业信息
     */
    @RequiresPermissions("system:homework:add")
    @Log(title = "作业信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Homework homework)
    {
        return toAjax(homeworkService.insertHomework(homework));
    }

    /**
     * 修改作业信息
     */
    @RequiresPermissions("system:homework:edit")
    @Log(title = "作业信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Homework homework)
    {
        return toAjax(homeworkService.updateHomework(homework));
    }

    /**
     * 删除作业信息
     */
    @RequiresPermissions("system:homework:remove")
    @Log(title = "作业信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(homeworkService.deleteHomeworkByIds(ids));
    }

    /**
     * 生成作业
     */
    @RequiresPermissions("system:homework:create")
    @Log(title = "生成作业")
    @PostMapping("/createHomeWork")
    @ApiOperation(value = "生成作业", notes = "根据要求让大模型生成作业，并解析作业，将题目列表返回给前端")
    public AjaxResult createHomeWork(@RequestBody Homework homework)
    {
        return homeworkService.createHomeWork(homework);
    }

    /**
     * 保存作业
     */
    @RequiresPermissions("system:homework:save")
    @Log(title = "保存作业", businessType = BusinessType.INSERT)
    @PostMapping("/saveHomeWork")
    @ApiOperation(value = "保存作业", notes = "保存作业主表和题目细表、题目选项细表")
    public AjaxResult saveHomeWork(@RequestBody Homework homework)
    {
        return homeworkService.saveHomeWork(homework);
    }

    /**
     * 发布作业
     */
    @RequiresPermissions("system:homework:publish")
    @Log(title = "发布作业", businessType = BusinessType.INSERT)
    @PostMapping("/publish")
    @ApiOperation(value = "发布作业", notes = "根据选择的班级ID，将作业发布给班级下的学生")
    public AjaxResult publish(@RequestBody Homework homework)
    {
        return homeworkService.publishHomeWork(homework);
    }


    /**
     * 获取该作业的已布置的班级
     */
    @Log(title = "获取该作业的已布置的班级", businessType = BusinessType.INSERT)
    @GetMapping("/selectClaByHId")
    @ApiOperation(value = "获取该作业的已布置的班级", notes = "根据选择的作业ID，获取班级")
    public AjaxResult selectClaByHId(String hmId)
    {
        return homeworkStudentService.selectClaByHId(Long.valueOf(hmId));
    }
}
