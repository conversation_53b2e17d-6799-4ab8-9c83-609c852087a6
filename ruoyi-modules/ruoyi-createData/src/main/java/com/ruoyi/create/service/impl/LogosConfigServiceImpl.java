package com.ruoyi.create.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.domain.LogosConfig;
import com.ruoyi.create.mapper.CircularBannerConfigMapper;
import com.ruoyi.create.mapper.LogosConfigMapper;
import com.ruoyi.create.service.ILogosConfigService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

/**
 * logo图片存储Service业务层处理
 *
 * <AUTHOR>
 * @create 2024-09-05
 */
@Service
public class LogosConfigServiceImpl implements ILogosConfigService {

    @Value("${logo.path.file-path-win}")
    String localFilePathWin;// D:/ruoyi/uploadDataPath

    @Value("${logo.path.file-path-linux}")
    String localFilePathLinux;// /home/<USER>/uploadDataPath

    @Resource
    private LogosConfigMapper logosConfigMapper;
    @Resource
    private CircularBannerConfigMapper circularBannerConfigMapper;

    /**
     * 查询Logo图片配置列表
     *
     * @param logosConfig 图片配置
     * @return Logo图片配置
     */
    @Override
    public List<LogosConfig> selectLogosConfigList(LogosConfig logosConfig)
    {
        List<LogosConfig> logosConfigList = logosConfigMapper.selectLogosConfigList(logosConfig);

        return logosConfigList;
    }

    /**
     * 获取所有logo图片的路径
     *
     * @param logoPosition logo展示位置
     * @return 结果
     */
    public List<String> selectLogosConfigListAll(String logoPosition)
    {
        String univerName = circularBannerConfigMapper.selectUniversity(SecurityUtils.getUsername());
        List<LogosConfig> logosConfigListAll = logosConfigMapper.selectLogosConfigListAll(logoPosition,univerName);
        List<String> fileNameList = new ArrayList<>();
        List<String> filePathList = new ArrayList<>();
        if(logosConfigListAll.size()==0){
            logosConfigListAll = logosConfigMapper.selectLogosConfigBySchool(logoPosition,"山东财经大学");
        }
        for (LogosConfig logosConfigList : logosConfigListAll) {
            fileNameList.add(logosConfigList.getLogoName());
        }
        for (String s : fileNameList) {
            filePathList.add("/logo/"+s);
        }
        return filePathList;
    }

    /**
     * 获得登录人所在学校名称
     * @param username 登录人名称
     * @return 结果
     */
    @Override
    public String selectUniversity(String username) {
        return circularBannerConfigMapper.selectUniversity(username);
    }

    /**
     * 上传Logo图片至服务器
     *@param file Logo图片
     * @return 结果
     */
    @Override
    public LogosConfig uploadLogosConfig(MultipartFile file) {
        try {
            String univerName = circularBannerConfigMapper.selectUniversity(SecurityUtils.getUsername());
            LogosConfig logosConfig = new LogosConfig();
            long uniqueID = System.currentTimeMillis();
            // 获取上传的文件名 photo1.jpg
            String fileName = file.getOriginalFilename();
            fileName = uniqueID+"-"+fileName;

            String os = System.getProperty("os.name").toLowerCase();
            String speechdraftpath = "";
            if (os.contains("win")) {
                speechdraftpath = localFilePathWin;
            } else if (os.contains("nix") || os.contains("nux") || os.contains("mac")) {
                speechdraftpath = localFilePathLinux;;
            } else {
                throw new UnsupportedOperationException("Unsupported operating system: " + os);
            }
            String filePath = speechdraftpath+"logo";
            // 检查并创建目录
            Path uploadPath = Paths.get(filePath);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }
            // 保存文件到指定路径
            Path filePath1 = uploadPath.resolve(fileName);
            Files.write(filePath1, file.getBytes());
            logosConfig.setLogoName(fileName);
            logosConfig.setLogoId(uniqueID);
            logosConfig.setSchool(univerName);
            return logosConfig;
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 提交Logo图片配置
     *
     * @param logoConfig Logo图片配置
     * @return 结果
     */
    @Override
    public int updateLogosConfigByLogoName(LogosConfig logoConfig) {
        List<LogosConfig> logosConfigList = logosConfigMapper.selectLogosConfigByLogoName(logoConfig.getLogoName());
        if(logosConfigList.size()==0){
            String ssUrl="";
            String os = System.getProperty("os.name").toLowerCase();
            if (os.contains("win")) {
                ssUrl=localFilePathWin;
            } else if (os.contains("nix") || os.contains("nux") || os.contains("mac")) {
                ssUrl=localFilePathLinux;;
            } else {
                throw new UnsupportedOperationException("Unsupported operating system: " + os);
            }
            logoConfig.setLogoPath(ssUrl+"logo");
            logoConfig.setCreateTime(DateUtils.getNowDate());
            logoConfig.setCreateBy(SecurityUtils.getUsername());
        }
        return logosConfigMapper.insertLogosConfig(logoConfig);
    }

    /**
     * 查询Logo图片存储
     *
     * @param id Logo图片存储主键
     * @return Logo图片存储
     */
    @Override
    public LogosConfig selectLogosConfigById(Long id)
    {
        return logosConfigMapper.selectLogosConfigById(id);
    }

    /**
     * 修改Logo图片存储
     *
     * @param logosConfig Logo图片存储
     * @return 结果
     */
    @Override
    public int updateLogosConfig(LogosConfig logosConfig)
    {
        List<LogosConfig> logosConfigList = logosConfigMapper.selectLogosConfigByLogoName(logosConfig.getLogoName());
        if(logosConfigList.size()>0){
            logosConfig.setUpdateTime(DateUtils.getNowDate());
            logosConfig.setUpdateBy(SecurityUtils.getUsername());
        }

        return logosConfigMapper.updateLogosConfig(logosConfig);
    }

    /**
     * 删除Logo图片存储信息
     *
     * @param id Logo图片存储主键
     * @return 结果
     */
    @Override
    public int deleteLogosConfigById(Long id)
    {
        LogosConfig logosConfig = logosConfigMapper.selectLogosConfigById(id);
        String ssUrl="";
        String os = System.getProperty("os.name").toLowerCase();
        if (os.contains("win")) {
            ssUrl=localFilePathWin;
        } else if (os.contains("nix") || os.contains("nux") || os.contains("mac")) {
            ssUrl=localFilePathLinux;;
        } else {
            throw new UnsupportedOperationException("Unsupported operating system: " + os);
        }
        File file = new File(ssUrl+"logo/"+logosConfig.getLogoName());
        // 检查文件是否存在
        if (file.exists()) {
            // 尝试删除文件
            file.delete();
        }
        return logosConfigMapper.deleteLogosConfigById(id);
    }

    /**
     * 根据Logo图片路径将Logo图片转换成字节
     * @param filePath
     * @return
     * @throws IOException
     */
    public String  processing(String filePath) throws IOException {
        File file = new File(filePath); // Logo图片的实际路径
        BufferedImage image = ImageIO.read(file);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, getFileExtension(filePath), baos);
        baos.flush();
        byte[] imageBytes = baos.toByteArray();
        baos.close();
        return Base64.getEncoder().encodeToString(imageBytes);
    }

    /**
     * 根据Logo图片名称或路径获取Logo图片后缀
     * @param fileName
     * @return
     */
    public String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return ""; // 如果文件名为空，返回空字符串
        }
        int dotIndex = fileName.lastIndexOf('.'); // 查找最后一个'.'的位置
        if (dotIndex == -1 || dotIndex == fileName.length() - 1) {
            return ""; // 如果没有找到'.'或者'.'在字符串末尾，说明没有扩展名
        }
        return fileName.substring(dotIndex + 1); // 返回'.'之后的部分，即文件扩展名
    }



}
