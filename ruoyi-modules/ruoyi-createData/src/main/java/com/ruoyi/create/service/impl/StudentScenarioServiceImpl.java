package com.ruoyi.create.service.impl;

import java.io.File;
import java.io.IOException;
import java.util.List;

import com.ruoyi.create.exception.CustomException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.StudentScenarioMapper;
import com.ruoyi.create.domain.StudentScenario;
import com.ruoyi.create.service.IStudentScenarioService;

import javax.annotation.Resource;

/**
 * 学生应用场景Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-11
 */
@Service
public class StudentScenarioServiceImpl implements IStudentScenarioService
{
    @Resource
    private StudentScenarioMapper studentScenarioMapper;

    /**
     * 查询学生应用场景
     *
     * @param id 学生应用场景主键
     * @return 学生应用场景
     */
    @Override
    public StudentScenario selectStudentScenarioById(Long id)
    {
        return studentScenarioMapper.selectStudentScenarioById(id);
    }
    @Override
    public StudentScenario selectStudentScenarioBgById(StudentScenario studentScenario)
    {
        return studentScenarioMapper.selectStudentScenarioBgById(studentScenario);
    }

    /**
     * 查询学生应用场景列表
     *
     * @param studentScenario 学生应用场景
     * @return 学生应用场景
     */
    @Override
    public List<StudentScenario> selectStudentScenarioList(StudentScenario studentScenario)
    {
        return studentScenarioMapper.selectStudentScenarioList(studentScenario);
    }

    /**
     * 新增学生应用场景
     *
     * @param studentScenario 学生应用场景
     * @return 结果
     */
    @Override
    public int insertStudentScenario(StudentScenario studentScenario)
    {
        return studentScenarioMapper.insertStudentScenario(studentScenario);
    }

    /**
     * 修改学生应用场景
     *
     * @param studentScenario 学生应用场景
     * @return 结果
     */
    @Override
    public int updateStudentScenario(StudentScenario studentScenario)
    {
        return studentScenarioMapper.updateStudentScenario(studentScenario);
    }

    /**
     * 批量删除学生应用场景
     *
     * @param ids 需要删除的学生应用场景主键
     * @return 结果
     */
    @Override
    public int deleteStudentScenarioByIds(Long[] ids)
    {
        return studentScenarioMapper.deleteStudentScenarioByIds(ids);
    }

    /**
     * 删除学生应用场景信息
     *
     * @param id 学生应用场景主键
     * @return 结果
     */
    @Override
    public int deleteStudentScenarioById(Long id)
    {
        return studentScenarioMapper.deleteStudentScenarioById(id);
    }

    @Override
    public int insertOrUpdateStudentScenario(StudentScenario studentScenario){
        //学生   更新 自己的应用场景 背景图删除原来的记录的图片
        StudentScenario haveScenario = studentScenarioMapper.selectStudentScenarioBgById(studentScenario);
        if (haveScenario != null && haveScenario.getImageUrl() != null && studentScenario.getImageUrl() != null){
            deleteFile(new File(haveScenario.getImageUrl()));
        }
        return studentScenarioMapper.insertOrUpdateStudentScenario(studentScenario);

    }

    @Override
    public void deleteImgByPath(String path) {
        deleteFile(new File(path));
    }


    // 文件删除方法
    private void deleteFile(File file) {
        try {
            if (file.exists() && !file.delete()) {
                throw new IOException("删除文件失败: " + file.getAbsolutePath());
            }
        } catch (Exception e) {
            // 记录异常日志，或者根据需求处理
            System.err.println("删除文件失败: " + e.getMessage());
            // 根据需要抛出自定义异常
            throw new CustomException("删除文件失败: " + file.getAbsolutePath(), e);
        }
    }

}
