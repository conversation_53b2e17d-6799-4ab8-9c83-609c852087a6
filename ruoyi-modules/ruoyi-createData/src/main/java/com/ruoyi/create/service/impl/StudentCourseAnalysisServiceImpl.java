package com.ruoyi.create.service.impl;


import com.ruoyi.create.Vo.StudentCourseAnalysisVo;
import com.ruoyi.create.service.IStudentCourseAnalysisService;

import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 生成学生学生课程分析统计接口实现类
 */
@Service
public class StudentCourseAnalysisServiceImpl implements IStudentCourseAnalysisService {
    /**
     * 生成学生单门课程分析统计表格
     *
     * @return exel地址集合
     */
    @Override
    public List<String> generateIndividualCourseStatistics() throws Exception {
        return Collections.emptyList();
    }

    /**
     * 学生所有课程分析统计表格
     *
     * @return exel地址集合
     */
    @Override
    public List<String> generateStudentCourseStatistics() throws Exception {
        return Collections.emptyList();
    }


//    /**
//     * 生成学生单门课程分析统计表格
//     *
//     * @return exel地址集合
//     */
//    @Override
//    public List<String> generateIndividualCourseStatistics() throws Exception {
//
////        ExcelToPDFByAspose.excel2pdf();
////        String fileName2 = "D:\\ruoyi\\uploadPath\\view\\班级课程分析统计.xlsx";
////        createTableClassCourseStatisticsByAspose(fileName2);
//        String fileName = "D:\\ruoyi\\uploadPath\\view\\学生课程分析统计.xlsx";
//        StudentCourseAnalysisVo courseAnalysisVo = new StudentCourseAnalysisVo();
//        // 姓名
//        courseAnalysisVo.setName("陌小杰");
//        // 课程名
//        courseAnalysisVo.setCourseId("城市管理学");
//        // 报告日期
//        courseAnalysisVo.setReportDate(new SimpleDateFormat("yyyy/MM/dd").format(new Date()));
//        // 总章数
//        courseAnalysisVo.setTotalChapterCount("20");
//        // 已完成章数
//        courseAnalysisVo.setCompletedChapterCount("16");
//        // 已完成章进度
//        courseAnalysisVo.setCompletedChapterProgress("80%");
//        // 平均完成进度
//        courseAnalysisVo.setAverageCompletionProgress("85%");
//        // 与班级平均进度比较
//        courseAnalysisVo.setComparisonWithClassAverageProgress("80%");
//        // 智能问答总次数
//        courseAnalysisVo.setTotalIntelligentQnACount("64");
//        // 平均每周问答次数
//        courseAnalysisVo.setAverageWeeklyQnACount("4");
//        // 与班级平均问答次数比较
//        courseAnalysisVo.setComparisonWithClassAverageQnACount("落后");
//        // 作业总数
//        courseAnalysisVo.setTotalHomeworkCount("32");
//        // 已提交次数
//        courseAnalysisVo.setSubmittedCount("30");
//        // 提交比例
//        courseAnalysisVo.setSubmissionRate("93.75%");
//        // 班级平均完成进度
//        courseAnalysisVo.setClassAverageHomeworkCompletion("95%");
//        // 与班级平均完成进度比较
//        courseAnalysisVo.setCompareWithClassAverageHomeworkCompletion("落后");
//        // 总题目数
//        courseAnalysisVo.setTotalQuestions("400");
//        // 正确题目数
//        courseAnalysisVo.setCorrectAnswersCount("360");
//        // 正确率
//        courseAnalysisVo.setAccuracyRate("90%");
//        // 班级平均正确率
//        courseAnalysisVo.setClassAverageAccuracyRate("95%");
//        // 与班级平均正确率比较
//        courseAnalysisVo.setCompareWithClassAverageAccuracyRate("落后");
//        // 易错点分布
//        courseAnalysisVo.setCommonMistakeDistribution("城市管理的概念和内涵特征，城市管理系统的作用和框架");
//        // 学习进度建议
//        courseAnalysisVo.setLearningProgressSuggestions("学生进度落后，建议调整学习计划，增加学习时间，必要时寻求教师或同学帮助");
//        // 提问频次建议
//        courseAnalysisVo.setQuestionFrequencySuggestions("学生提问次数低于班级平均值，建议调整学习计划，提高提问频率，及时提问");
//        // 作业正确率建议
//        courseAnalysisVo.setHomeworkAccuracySuggestions("作业正确率较低，针对错误率较高的知识点，建议学生复习相关教材，观看教学视频，或进行针对性练习");
//        // 学习策略建议
//        courseAnalysisVo.setLearningStrategySuggestions("建议学生采用主动学习策略，如小组讨论、思维导图等，提高学习效率。鼓励学生保持积极的学习态度，面对困难时寻求帮助，不放弃");
//        createTableStudentCourseAnalysisByAspose(fileName, courseAnalysisVo);
//        return Collections.emptyList();
//    }
//
////    public static void main(String[] args) throws Exception {
////        String fileName = "D:\\ruoyi\\uploadPath\\view\\学生课程分析统计.xlsx";
////        StudentCourseAnalysisVo courseAnalysisVo = new StudentCourseAnalysisVo();
////        // 姓名
////        courseAnalysisVo.setName("陌小杰");
////        // 课程名
////        courseAnalysisVo.setCourseId("城市管理学");
////        // 报告日期
////        courseAnalysisVo.setReportDate(new SimpleDateFormat("yyyy/MM/dd").format(new Date()));
////        // 总章数
////        courseAnalysisVo.setTotalChapterCount("20");
////        // 已完成章数
////        courseAnalysisVo.setCompletedChapterCount("16");
////        // 已完成章进度
////        courseAnalysisVo.setCompletedChapterProgress("80%");
////        // 平均完成进度
////        courseAnalysisVo.setAverageCompletionProgress("85%");
////        // 与班级平均进度比较
////        courseAnalysisVo.setComparisonWithClassAverageProgress("80%");
////        // 智能问答总次数
////        courseAnalysisVo.setTotalIntelligentQnACount("64");
////        // 平均每周问答次数
////        courseAnalysisVo.setAverageWeeklyQnACount("4");
////        // 与班级平均问答次数比较
////        courseAnalysisVo.setComparisonWithClassAverageQnACount("落后");
////        // 作业总数
////        courseAnalysisVo.setTotalHomeworkCount("32");
////        // 已提交次数
////        courseAnalysisVo.setSubmittedCount("30");
////        // 提交比例
////        courseAnalysisVo.setSubmissionRate("93.75%");
////        // 班级平均完成进度
////        courseAnalysisVo.setClassAverageHomeworkCompletion("95%");
////        // 与班级平均完成进度比较
////        courseAnalysisVo.setCompareWithClassAverageHomeworkCompletion("落后");
////        // 总题目数
////        courseAnalysisVo.setTotalQuestions("400");
////        // 正确题目数
////        courseAnalysisVo.setCorrectAnswersCount("360");
////        // 正确率
////        courseAnalysisVo.setAccuracyRate("90%");
////        // 班级平均正确率
////        courseAnalysisVo.setClassAverageAccuracyRate("95%");
////        // 与班级平均正确率比较
////        courseAnalysisVo.setCompareWithClassAverageAccuracyRate("落后");
////        // 易错点分布
////        courseAnalysisVo.setCommonMistakeDistribution("城市管理的概念和内涵特征，城市管理系统的作用和框架");
////        // 学习进度建议
////        courseAnalysisVo.setLearningProgressSuggestions("学生进度落后，建议调整学习计划，增加学习时间，必要时寻求教师或同学帮助");
////        // 提问频次建议
////        courseAnalysisVo.setQuestionFrequencySuggestions("学生提问次数低于班级平均值，建议调整学习计划，提高提问频率，及时提问");
////        // 作业正确率建议
////        courseAnalysisVo.setHomeworkAccuracySuggestions("作业正确率较低，针对错误率较高的知识点，建议学生复习相关教材，观看教学视频，或进行针对性练习");
////        // 学习策略建议
////        courseAnalysisVo.setLearningStrategySuggestions("建议学生采用主动学习策略，如小组讨论、思维导图等，提高学习效率。鼓励学生保持积极的学习态度，面对困难时寻求帮助，不放弃");
////        createTableStudentCourseAnalysisByAspose(fileName, courseAnalysisVo);
////    }
//
//    /**
//     * 学生所有课程分析统计表格
//     *
//     * @return exel地址集合
//     */
//    @Override
//    public List<String> generateStudentCourseStatistics() throws Exception {
//        return Collections.emptyList();
//    }
//
//
//    /**
//     * 创建班级课程分析统计表格
//     *
//     * @param fileName
//     * @return
//     * @throws Exception
//     */
//    private static String createTableClassCourseStatisticsByAspose(String fileName) throws Exception {
//        // Create a Workbook.
//        Workbook wb = new Workbook();
//        Worksheet worksheet = wb.getWorksheets().get(0);
//        Cells cells = worksheet.getCells();
//        worksheet.getCells().setStandardWidth(50.0f);
//        worksheet.getCells().setStandardWidth(25.0f);
//        // 合并单元格，合并 A1:C1
//        cells.merge(0, 0, 1, 3); // (起始行, 起始列, 行数, 列数)
//        // 设置大标题
//        Cell titleCell = cells.get(0, 0);
//        titleCell.setValue("班级课程分析统计");
//        // 设置标大题样式
//        Style titleStyle = titleCell.getStyle();
//        titleStyle.setTextWrapped(true);
//        Font font = titleStyle.getFont();
//        font.setSize(20);
//        titleStyle.setHorizontalAlignment(TextAlignmentType.CENTER); // 居中对齐
//        // 应用大标题样式
//        cells.get(0, 0).setStyle(titleStyle);
//        // Create an object for AutoFitterOptions
//        AutoFitterOptions options = new AutoFitterOptions();
//        options.setAutoFitMergedCells(true);
//        worksheet.autoFitRows(options);
//
//        cells.get(1, 0).setValue("一、班级基本信息");
//        cells.merge(1, 0, 2, 1);
//        cells.get(1, 1).setValue("班级名称");
//        cells.get(1, 2).setValue("管理学1班");
//        cells.get(2, 1).setValue("报告日期");
//        cells.get(2, 2).setValue(new SimpleDateFormat("yyyy/MM/dd").format(new Date()));
//
//
//        cells.get(3, 0).setValue("二、学习进度统计");
//        cells.merge(3, 0, 3, 1);
//        cells.get(3, 1).setValue("完成进度领先的学生比例");
//        cells.get(3, 2).setValue("60%");
//        cells.get(4, 1).setValue("完成进度持平的学生比例");
//        cells.get(4, 2).setValue("10%");
//        cells.get(5, 1).setValue("完成进度落后的学生比例%");
//        cells.get(5, 2).setValue("30%");
//
//        cells.get(6, 0).setValue("三、课后作业情况");
//        cells.merge(6, 0, 3, 1);
//        cells.get(6, 1).setValue("作业完成度高于90%的学生比例");
//        cells.get(6, 2).setValue("50%");
//        cells.get(7, 1).setValue("作业完成度高于80%的学生比例");
//        cells.get(7, 2).setValue("80%");
//        cells.get(8, 1).setValue("需要特别关注的学生(作业完成度或正确率较低)");
//        cells.get(8, 2).setValue("无");
//
//        cells.get(9, 0).setValue("四、问答频率与质量");
//        cells.merge(9, 0, 3, 1);
//        cells.get(9, 1).setValue("积极参与问答的学生比例");
//        cells.get(9, 2).setValue("100%");
//        cells.get(10, 1).setValue("回答质量较高的学生比例");
//        cells.get(10, 2).setValue("80%");
//        cells.get(11, 1).setValue("需要特别关注的学生(提问较少的学生)");
//        cells.get(11, 2).setValue("无");
//
//        cells.get(12, 0).setValue("五、总结与建议");
//        cells.merge(12, 0, 3, 1);
//        cells.get(12, 1).setValue("学习进度");
//        cells.get(12, 2).setValue("班级整体学习进度较为均衡，但仍有部分学生需要加快进度。建议教师关注落后学生的情况，提供必要的帮助和指导");
//        cells.get(13, 1).setValue("课后作业");
//        cells.get(13, 2).setValue("大部分学生的作业完成度和正确率较高，但仍有少数学生需要提高。建议教师针对这些学生进行个别辅导，帮助他们提高作业质量");
//        cells.get(14, 1).setValue("问答互动");
//        cells.get(14, 2).setValue("班级整体问答氛围较好，但仍有部分学生需要积极参与。建议教师鼓励学生多提问和回答问题，促进班级内部的交流和互动。同时，针对回答质量较低的学生进行个别指导，提高他们的问答能力");
//
//        setAllCellsFontAndAutoHeight(worksheet, "宋体");
//        // 添加表格边框
//        adjustCellBordersByAspose(wb);
//        wb.save(fileName);
//
//        XSSFWorkbook workbook = new XSSFWorkbook(new FileInputStream(fileName));
//        // 只保留第一个Sheet，删除其他Sheet
//        for (int i = workbook.getNumberOfSheets() - 1; i > 0; i--) {
//            workbook.removeSheetAt(i);
//        }
//        // 将更改后的工作簿写回文件
//        try (FileOutputStream out = new FileOutputStream(fileName)) {
//            workbook.write(out);
//        }
//        // 关闭工作簿
//        workbook.close();
//        System.out.println(fileName);
//        return fileName;
//
//    }
//
//
//    /**
//     * 生成学生单门课程分析统计表格方法
//     *
//     * @param fileName
//     * @return
//     * @throws Exception
//     */
//    public static String createTableStudentCourseAnalysisByAspose(String fileName) throws Exception {
//        // Create a Workbook.
//        Workbook wb = new Workbook();
//        Worksheet worksheet = wb.getWorksheets().get(0);
//
//        Cells cells = worksheet.getCells();
//        worksheet.getCells().setStandardWidth(50.0f);
//        worksheet.getCells().setStandardWidth(22.0f);
////        cells.setColumnWidth(0, 20f);
////        cells.setColumnWidth(1, 15f);
////        cells.setColumnWidth(2, 20f);
////        cells.setColumnWidth(2, 25.0f);
//        // 合并单元格，合并 A1:D1
//        cells.merge(0, 0, 1, 4); // (起始行, 起始列, 行数, 列数)
//        // 设置大标题
//        Cell titleCell = cells.get(0, 0);
//        titleCell.setValue("学生课程分析统计");
//
//        // 设置标大题样式
//        Style titleStyle = titleCell.getStyle();
//        titleStyle.setTextWrapped(true);
//        Font font = titleStyle.getFont();
//        font.setSize(20);
//        titleStyle.setHorizontalAlignment(TextAlignmentType.CENTER); // 居中对齐
//        // 应用大标题样式
//        cells.get(0, 0).setStyle(titleStyle);
//        // Create an object for AutoFitterOptions
//        AutoFitterOptions options = new AutoFitterOptions();
//        options.setAutoFitMergedCells(true);
//        worksheet.autoFitRows(options);
//
//
//        // 合并单元格，合并 A1:A5
//        cells.merge(1, 0, 3, 1); // (起始行, 起始列, 行数, 列数)
//        cells.get(1, 0).setValue("一、学生基本信息");
//        cells.get(1, 1).setValue("学生姓名");
//        cells.get(1, 2).setValue("陌小杰");
//        cells.merge(1, 2, 1, 2);
//        cells.get(2, 1).setValue("课程名称");
//        cells.get(2, 2).setValue("城市管理学");
//        cells.merge(2, 2, 1, 2);
//        cells.get(3, 1).setValue("报告日期");
//        cells.get(3, 2).setValue(new SimpleDateFormat("yyyy/MM/dd").format(new Date()));
//        cells.merge(3, 2, 1, 2);
//
//        // 合并单元格
//        cells.merge(4, 0, 8, 1);
//        cells.get(4, 0).setValue("二、学习进度分析");
//        cells.get(4, 1).setValue("课程进度概述");
//        cells.merge(4, 1, 3, 1);
//        cells.get(7, 1).setValue("学习速度评估");
//        cells.merge(7, 1, 2, 1);
//        cells.get(9, 1).setValue("提问频次评估");
//        cells.merge(9, 1, 3, 1);
//
//
//        cells.get(4, 2).setValue("总章数");
//        cells.get(4, 3).setValue("20");
//        cells.get(5, 2).setValue("已完成章节");
//        cells.get(5, 3).setValue("16");
//        cells.get(6, 2).setValue("当前已完成章节进度");
//        cells.get(6, 3).setValue("80%");
//        cells.get(7, 2).setValue("平均完成进度");
//        cells.get(7, 3).setValue("85%");
//        cells.get(8, 2).setValue("与班级平均进度比较");
//        cells.get(8, 3).setValue("落后%");
//        cells.get(9, 2).setValue("智能问答总次数");
//        cells.get(9, 3).setValue("64");
//        cells.get(10, 2).setValue("平均每周问答次数");
//        cells.get(10, 3).setValue("4");
//        cells.get(11, 2).setValue("与班级平均问答次数比较");
//        cells.get(11, 3).setValue("落后");
//
//
//        // 合并单元格
//        cells.merge(12, 0, 5, 1);
//        cells.get(12, 0).setValue("三、课后作业完成度分析");
//        cells.get(12, 1).setValue("作业提交情况");
//        cells.merge(12, 1, 5, 1);
//        cells.get(12, 2).setValue("总作业次数");
//        cells.get(12, 3).setValue("32");
//        cells.get(13, 2).setValue("已提交次数");
//        cells.get(13, 3).setValue("30");
//        cells.get(14, 2).setValue("提交比例");
//        cells.get(14, 3).setValue("93.75%");
//        cells.get(15, 2).setValue("班级平均作业完成度");
//        cells.get(15, 3).setValue("95%");
//        cells.get(16, 2).setValue("与班级平均作业完成度比较");
//        cells.get(16, 3).setValue("落后");
//
//
//        // 合并单元格
//        cells.merge(17, 0, 10, 1);
//        cells.get(17, 0).setValue("四、课后作业正确率分析");
//        cells.get(17, 1).setValue("整体正确率");
//        cells.merge(17, 1, 10, 1);
//        cells.get(17, 2).setValue("总题目数");
//        cells.get(17, 3).setValue("400");
//        cells.get(18, 2).setValue("正确题目数");
//        cells.get(18, 3).setValue("360");
//        cells.get(19, 2).setValue("正确率");
//        cells.get(19, 3).setValue("90%");
//        cells.get(20, 2).setValue("班级平均正确率");
//        cells.get(20, 3).setValue("95%");
//        cells.get(21, 2).setValue("与班级平均作业正确率比较");
//        cells.get(21, 3).setValue("落后");
//
//        cells.get(22, 1).setValue("知识点掌握情况");
//        cells.get(22, 2).setValue("易错点分布");
//        cells.get(22, 3).setValue("城市管理的概念和内涵特征，城市管理系统的作用和框架");
//        cells.get(23, 2).setValue("学习进度");
//        cells.get(23, 3).setValue("学生进度落后，建议调整学习计划，增加学习时间，必要时寻求教师或同学帮助");
//        cells.get(24, 2).setValue("提问频次");
//        cells.get(24, 3).setValue("学生提问次数低于班级平均值，建议调整学习计划，提高提问频率，及时提问");
//        cells.get(25, 2).setValue("作业正确率");
//        cells.get(25, 3).setValue("作业正确率较低，针对错误率较高的知识点，建议学生复习相关教材，观看教学视频，或进行针对性练习");
//        cells.get(26, 2).setValue("学习策略");
//        cells.get(26, 3).setValue("建议学生采用主动学习策略，如小组讨论、思维导图等，提高学习效率。鼓励学生保持积极的学习态度，面对困难时寻求帮助，不放弃");
//
////
////        cells.get(1, 1).setValue("学生姓名");
////        cells.get(1, 2).setValue("莫小姐");
////        cells.get(2, 1).setValue("课程名称");
////        cells.get(2, 2).setValue("城市管理学");
////        cells.get(3, 1).setValue("报告日期");
////        cells.get(3, 2).setValue("2024/10/14");
////
////
////        // 合并单元格，合并 A5:A12
////        cells.merge(4, 0, 8, 1); // (起始行, 起始列, 行数, 列数)
////        cells.get(4, 0).setValue("二、学习进度分析");
////
////        // 合并单元格
////        cells.merge(4, 1, 3, 1); // (起始行, 起始列, 行数, 列数)
////        cells.get(4, 1).setValue("课程进度概述");
////
////        // 合并单元格
////        cells.merge(7, 1, 2, 1); // (起始行, 起始列, 行数, 列数)
////        cells.get(7, 1).setValue("学习速度评估");
////
////        // 合并单元格
////        cells.merge(9, 1, 3, 1); // (起始行, 起始列, 行数, 列数)
////        cells.get(9, 1).setValue("提问频次评估");
////
//
//        setAllCellsFontAndAutoHeight(worksheet, "宋体");
//        // 添加表格边框
//        adjustCellBordersByAspose(wb);
//        wb.save(fileName);
//
//        XSSFWorkbook workbook = new XSSFWorkbook(new FileInputStream(fileName));
//        // 只保留第一个Sheet，删除其他Sheet
//        for (int i = workbook.getNumberOfSheets() - 1; i > 0; i--) {
//            workbook.removeSheetAt(i);
//        }
//        // 将更改后的工作簿写回文件
//        try (FileOutputStream out = new FileOutputStream(fileName)) {
//            workbook.write(out);
//        }
//        // 关闭工作簿
//        workbook.close();
//        System.out.println(fileName);
//        return fileName;
//    }
//
//
//    private static String createTableStudentCourseAnalysisByAspose(String fileName, StudentCourseAnalysisVo dataVo) throws Exception {
//        // Create a Workbook.
//        Workbook wb = new Workbook();
//        Worksheet worksheet = wb.getWorksheets().get(0);
//
//        Cells cells = worksheet.getCells();
//        worksheet.getCells().setStandardWidth(50.0f);
//        worksheet.getCells().setStandardWidth(22.0f);
//        // 合并单元格，合并 A1:D1
//        cells.merge(0, 0, 1, 4); // (起始行, 起始列, 行数, 列数)
//        // 设置大标题
//        Cell titleCell = cells.get(0, 0);
//        titleCell.setValue("学生课程分析统计");
//
//        // 设置标大题样式
//        Style titleStyle = titleCell.getStyle();
//        titleStyle.setTextWrapped(true);
//        Font font = titleStyle.getFont();
//        font.setSize(20);
//        titleStyle.setHorizontalAlignment(TextAlignmentType.CENTER); // 居中对齐
//        // 应用大标题样式
//        cells.get(0, 0).setStyle(titleStyle);
//        // Create an object for AutoFitterOptions
//        AutoFitterOptions options = new AutoFitterOptions();
//        options.setAutoFitMergedCells(true);
//        worksheet.autoFitRows(options);
//
//
//        // 合并单元格，合并 A1:A5
//        cells.merge(1, 0, 3, 1); // (起始行, 起始列, 行数, 列数)
//        cells.get(1, 0).setValue("一、学生基本信息");
//        cells.get(1, 1).setValue("学生姓名");
//        cells.get(1, 2).setValue(dataVo.getName());
//        cells.merge(1, 2, 1, 2);
//        cells.get(2, 1).setValue("课程名称");
//        cells.get(2, 2).setValue(dataVo.getCourseName());
//        cells.merge(2, 2, 1, 2);
//        cells.get(3, 1).setValue("报告日期");
//        cells.get(3, 2).setValue(dataVo.getReportDate());
//        cells.merge(3, 2, 1, 2);
//
//        // 合并单元格
//        cells.merge(4, 0, 8, 1);
//        cells.get(4, 0).setValue("二、学习进度分析");
//        cells.get(4, 1).setValue("课程进度概述");
//        cells.merge(4, 1, 3, 1);
//        cells.get(7, 1).setValue("学习速度评估");
//        cells.merge(7, 1, 2, 1);
//        cells.get(9, 1).setValue("提问频次评估");
//        cells.merge(9, 1, 3, 1);
//
//
//        cells.get(4, 2).setValue("总章数");
//        cells.get(4, 3).setValue(dataVo.getTotalChapterCount());
//        cells.get(5, 2).setValue("已完成章节");
//        cells.get(5, 3).setValue(dataVo.getCompletedChapterCount());
//        cells.get(6, 2).setValue("当前已完成章节进度");
//        cells.get(6, 3).setValue(dataVo.getCompletedChapterProgress());
//        cells.get(7, 2).setValue("平均完成进度");
//        cells.get(7, 3).setValue(dataVo.getAverageCompletionProgress());
//        cells.get(8, 2).setValue("与班级平均进度比较");
//        cells.get(8, 3).setValue(dataVo.getComparisonWithClassAverageProgress());
//        cells.get(9, 2).setValue("智能问答总次数");
//        cells.get(9, 3).setValue(dataVo.getTotalIntelligentQnACount());
//        cells.get(10, 2).setValue("平均每周问答次数");
//        cells.get(10, 3).setValue(dataVo.getAverageWeeklyQnACount());
//        cells.get(11, 2).setValue("与班级平均问答次数比较");
//        cells.get(11, 3).setValue(dataVo.getComparisonWithClassAverageQnACount());
//
//
//        // 合并单元格
//        cells.merge(12, 0, 5, 1);
//        cells.get(12, 0).setValue("三、课后作业完成度分析");
//        cells.get(12, 1).setValue("作业提交情况");
//        cells.merge(12, 1, 5, 1);
//        cells.get(12, 2).setValue("总作业次数");
//        cells.get(12, 3).setValue(dataVo.getTotalHomeworkCount());
//        cells.get(13, 2).setValue("已提交次数");
//        cells.get(13, 3).setValue(dataVo.getSubmittedCount());
//        cells.get(14, 2).setValue("提交比例");
//        cells.get(14, 3).setValue(dataVo.getSubmissionRate());
//        cells.get(15, 2).setValue("班级平均作业完成度");
//        cells.get(15, 3).setValue(dataVo.getClassAverageHomeworkCompletion());
//        cells.get(16, 2).setValue("与班级平均作业完成度比较");
//        cells.get(16, 3).setValue(dataVo.getCompareWithClassAverageHomeworkCompletion());
//
//
//        // 合并单元格
//        cells.merge(17, 0, 10, 1);
//        cells.get(17, 0).setValue("四、课后作业正确率分析");
//        cells.get(17, 1).setValue("整体正确率");
//        cells.merge(17, 1, 10, 1);
//        cells.get(17, 2).setValue("总题目数");
//        cells.get(17, 3).setValue(dataVo.getTotalQuestions());
//        cells.get(18, 2).setValue("正确题目数");
//        cells.get(18, 3).setValue(dataVo.getCorrectAnswersCount());
//        cells.get(19, 2).setValue("正确率");
//        cells.get(19, 3).setValue(dataVo.getAccuracyRate());
//        cells.get(20, 2).setValue("班级平均正确率");
//        cells.get(20, 3).setValue(dataVo.getClassAverageAccuracyRate());
//        cells.get(21, 2).setValue("与班级平均作业正确率比较");
//        cells.get(21, 3).setValue(dataVo.getCompareWithClassAverageAccuracyRate());
//
//        cells.get(22, 1).setValue("知识点掌握情况");
//        cells.get(22, 2).setValue("易错点分布");
//        cells.get(22, 3).setValue(dataVo.getCommonMistakeDistribution());
//        cells.get(23, 2).setValue("学习进度");
//        cells.get(23, 3).setValue(dataVo.getLearningProgressSuggestions());
//        cells.get(24, 2).setValue("提问频次");
//        cells.get(24, 3).setValue(dataVo.getQuestionFrequencySuggestions());
//        cells.get(25, 2).setValue("作业正确率");
//        cells.get(25, 3).setValue(dataVo.getHomeworkAccuracySuggestions());
//        cells.get(26, 2).setValue("学习策略");
//        cells.get(26, 3).setValue(dataVo.getLearningStrategySuggestions());
//
//
//        setAllCellsFontAndAutoHeight(worksheet, "宋体");
//        // 添加表格边框
//        adjustCellBordersByAspose(wb);
//        wb.save(fileName);
//
//        XSSFWorkbook workbook = new XSSFWorkbook(new FileInputStream(fileName));
//        // 只保留第一个Sheet，删除其他Sheet
//        for (int i = workbook.getNumberOfSheets() - 1; i > 0; i--) {
//            workbook.removeSheetAt(i);
//        }
//        // 将更改后的工作簿写回文件
//        try (FileOutputStream out = new FileOutputStream(fileName)) {
//            workbook.write(out);
//        }
//        // 关闭工作簿
//        workbook.close();
//        System.out.println(fileName);
//        return fileName;
//    }
//
//    public static void adjustCellBordersByAspose(Workbook wb) {
//        // 遍历所有工作表
//        for (int i = 0; i < wb.getWorksheets().getCount(); i++) {
//
//            Cells cells = wb.getWorksheets().get(i).getCells();
//
//            // 遍历合并单元格，删除边框
//            for (int j = 0; j < cells.getMergedCells().size(); j++) {
//                CellArea mergedCell = (CellArea) cells.getMergedCells().get(j);
//                for (int row = mergedCell.StartRow; row <= mergedCell.EndRow; row++) {
//                    for (int col = mergedCell.StartColumn; col <= mergedCell.EndColumn; col++) {
//                        Cell cell = cells.get(row, col);
//                        Style style = cell.getStyle();
//
//                        // 清除原有边框
//                        style.setBorder(BorderType.TOP_BORDER, CellBorderType.THIN, Color.getBlack());
//                        style.setBorder(BorderType.BOTTOM_BORDER, CellBorderType.THIN, Color.getBlack());
//                        style.setBorder(BorderType.LEFT_BORDER, CellBorderType.THIN, Color.getBlack());
//                        style.setBorder(BorderType.RIGHT_BORDER, CellBorderType.THIN, Color.getBlack());
//
//
//                        // 应用样式
//                        cell.setStyle(style);
//                    }
//                }
//            }
//            // 遍历每个单元格，删除边框并设置统一的边框
//            for (int row = 0; row <= cells.getMaxDataRow(); row++) {
//                for (int col = 0; col <= cells.getMaxDataColumn(); col++) {
//                    Cell cell = cells.get(row, col);
//                    Style style = cell.getStyle();
//
//                    // 清除原有边框
//                    style.setBorder(BorderType.TOP_BORDER, CellBorderType.THIN, Color.getBlack());
//                    style.setBorder(BorderType.BOTTOM_BORDER, CellBorderType.THIN, Color.getBlack());
//                    style.setBorder(BorderType.LEFT_BORDER, CellBorderType.THIN, Color.getBlack());
//                    style.setBorder(BorderType.RIGHT_BORDER, CellBorderType.THIN, Color.getBlack());
//
//                    // 应用样式
//                    cell.setStyle(style);
//                }
//            }
//
//            setFontStyleVerticallyCentered(cells);
//        }
//    }
//
//    /**
//     * 设置字体水平
//     *
//     * @param cells
//     */
//    public static void setFontStyleVerticallyCentered(Cells cells) {
//        // 设置所有单元格的垂直居中
//        for (int row = 0; row <= cells.getMaxDataRow(); row++) {
//            for (int col = 0; col <= cells.getMaxDataColumn(); col++) {
//                Cell cell = cells.get(row, col);
//                Style cellStyle = cell.getStyle();
////                cellStyle.setHorizontalAlignment(TextAlignmentType.CENTER);
//                cellStyle.setVerticalAlignment(TextAlignmentType.CENTER); // 垂直居中
//                cellStyle.setTextWrapped(true);
//                cell.setStyle(cellStyle);
//            }
//        }
//    }
//
//    /**
//     * 设置字体 设置自动行高
//     *
//     * @param sheet
//     */
//    public static void setAllCellsFontAndAutoHeight(Worksheet sheet, String useFont) throws Exception {
//        Cells cells = sheet.getCells();
//        int maxRow = cells.getMaxDataRow(); // 获取包含数据的最后一行
//        int maxCol = cells.getMaxDataColumn(); // 获取包含数据的最后一列
//
//
//        for (int row = 0; row <= maxRow; row++) {
//            for (int col = 0; col <= maxCol; col++) {
//                Cell cell = cells.get(row, col);
//                Style style = cell.getStyle();
//                Font font = style.getFont();
//                font.setName(useFont); // 设置字体为宋体
//                cell.setStyle(style); // 应用样式
//            }
//            // 自动调整行高
//            sheet.autoFitRow(row);
//        }
//    }
////    public static String createTableClassCourseStatistics(String fileName) throws Exception {
////        XSSFWorkbook workbook = new XSSFWorkbook();
////
////
////        XSSFSheet sheet = workbook.createSheet("Sheet1");
////        sheet.setDefaultColumnWidth(30);
////
////        sheet.createRow(0).createCell(0).setCellValue("班级课程分析统计");
////        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 2));
////
////
////        sheet.createRow(1).createCell(1).setCellValue("班级名称");
////        sheet.createRow(1).createCell(2).setCellValue("管理学1班");
////        sheet.addMergedRegion(new CellRangeAddress(2, 3, 1, 1));
////        sheet.createRow(2).createCell(1).setCellValue("报告日期");
////        sheet.createRow(2).createCell(2).setCellValue(new SimpleDateFormat("yyyy/MM/dd").format(new Date()));
////
////
////        sheet.createRow(1).createCell(1).setCellValue("二、课程学习情况");
////
////        sheet.createRow(3).createCell(0).setCellValue("二、学习进度统计");
////        sheet.addMergedRegion(new CellRangeAddress(3, 5, 0, 0));
////        sheet.createRow(6).createCell(0).setCellValue("三、课后作业情况");
////        sheet.addMergedRegion(new CellRangeAddress(6, 8, 0, 0));
////        sheet.createRow(9).createCell(0).setCellValue("四、问答频率与质量");
////        sheet.addMergedRegion(new CellRangeAddress(9, 11, 0, 0));
////        sheet.createRow(12).createCell(0).setCellValue("五、总结与建议");
////        sheet.addMergedRegion(new CellRangeAddress(12, 14, 0, 0));
////
////
////        adjustCellStyleByPoi(workbook, sheet);
////        setFontStyleCenteredByPoi(workbook, sheet);
////        File file = new File(fileName);
////        FileOutputStream fout = new FileOutputStream(file);
////
////        workbook.write(fout);
////        fout.close();
////
////        System.out.println(fileName);
////        return fileName;
////    }
////
////    public static void adjustCellStyleByPoi(XSSFWorkbook workbook, Sheet sheet) {
////        // 创建黑色细边框样式
////        CellStyle borderStyle = workbook.createCellStyle();
////        borderStyle.setBorderTop(BorderStyle.THIN);
////        borderStyle.setBorderBottom(BorderStyle.THIN);
////        borderStyle.setBorderLeft(BorderStyle.THIN);
////        borderStyle.setBorderRight(BorderStyle.THIN);
////        borderStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
////        borderStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
////        borderStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
////        borderStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
////
////        // 创建字体样式，设置为宋体，10号
////        XSSFFont font = workbook.createFont();
////        font.setFontName("宋体");
////        font.setFontHeightInPoints((short) 10); // 设置字号为10
////
////        // 设置单元格样式为字体样式，水平居中，垂直居中
////        CellStyle cellStyle = workbook.createCellStyle();
////        cellStyle.cloneStyleFrom(borderStyle); // 复制边框样式
////        cellStyle.setFont(font);
////        cellStyle.setAlignment(HorizontalAlignment.CENTER); // 水平居中
////        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
////
////        // 处理合并单元格区域，给所有合并区域内的单元格添加边框和样式
////        for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
////            CellRangeAddress region = sheet.getMergedRegion(i);
////            for (int rowNum = region.getFirstRow(); rowNum <= region.getLastRow(); rowNum++) {
////                Row row = sheet.getRow(rowNum);
////                if (row == null) {
////                    row = sheet.createRow(rowNum);
////                }
////                for (int colNum = region.getFirstColumn(); colNum <= region.getLastColumn(); colNum++) {
////                    org.apache.poi.ss.usermodel.Cell cell = row.getCell(colNum);
////                    if (cell == null) {
////                        cell = row.createCell(colNum);
////                    }
////                    cell.setCellStyle(cellStyle);  // 为合并区域的每个单元格设置样式
////                }
////            }
////        }
////
////        // 遍历所有单元格，给每个单元格添加样式
////        int maxRow = sheet.getLastRowNum();
////        for (int rowNum = 0; rowNum <= maxRow; rowNum++) {
////            Row row = sheet.getRow(rowNum);
////            if (row == null) {
////                row = sheet.createRow(rowNum);  // 确保行存在
////            }
////            // 动态获取该行的最大列数
////            int maxColumn = row.getLastCellNum();
////            if (maxColumn == -1) {
////                continue;  // 跳过空行
////            }
////            for (int colNum = 0; colNum < maxColumn; colNum++) {
////                org.apache.poi.ss.usermodel.Cell cell = row.getCell(colNum);
////                if (cell == null) {
////                    cell = row.createCell(colNum);  // 创建不存在的单元格
////                }
////                cell.setCellStyle(cellStyle);  // 设置单元格样式
////            }
////        }
////
////        // 单独设置第一行大标题
////        // 创建字体样式，设置为宋体，20号
////        XSSFFont titleFont = workbook.createFont();
////        titleFont.setFontName("宋体");
////        titleFont.setFontHeightInPoints((short) 20); // 设置字号为20
////        CellStyle titleStyle = workbook.createCellStyle();
////        titleStyle.cloneStyleFrom(sheet.getRow(0).getCell(0).getCellStyle());  // 复制现有的样式
////        titleStyle.setFont(titleFont);
////        sheet.getRow(0).getCell(0).setCellStyle(titleStyle);
////    }
////
////    /**
////     * poi 调整行高和自动换行
////     *
////     * @param workbook
////     * @throws Exception
////     */
////    public static void setFontStyleCenteredByPoi(XSSFWorkbook workbook, Sheet sheet) {
////        // 遍历所有单元格，基于现有样式添加自动换行
////        for (int rowNum = 0; rowNum < sheet.getLastRowNum(); rowNum++) {
////            Row row = sheet.getRow(rowNum);
////            if (row != null) {
////                for (int colNum = 0; colNum < row.getLastCellNum(); colNum++) {
////                    org.apache.poi.ss.usermodel.Cell cell = row.getCell(colNum);
////                    if (cell != null) {
////                        // 基于现有样式创建一个新的样式
////                        CellStyle newStyle = workbook.createCellStyle();
////                        newStyle.cloneStyleFrom(cell.getCellStyle());  // 复制现有的样式
////
////                        // 设置自动换行
////                        newStyle.setWrapText(true);
////
////                        // 应用新的样式
////                        cell.setCellStyle(newStyle);
////                    }
////                }
////            }
////        }
////
////        // 自动调整行高
////        for (int rowNum = 0; rowNum < sheet.getLastRowNum(); rowNum++) {
////            Row row = sheet.getRow(rowNum);
////            if (row != null) {
////                row.setHeight((short) -1);  // 设置行高为 -1，使行高度自动调整
////            }
////        }
////    }


}
