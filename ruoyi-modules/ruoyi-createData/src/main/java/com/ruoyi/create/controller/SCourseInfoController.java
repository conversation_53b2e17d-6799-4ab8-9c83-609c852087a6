package com.ruoyi.create.controller;

import java.util.List;
import java.io.IOException;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.SCourseInfo;
import com.ruoyi.create.service.ISCourseInfoService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 课程信息Controller
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
@RestController
@RequestMapping("/courseInfo")
public class SCourseInfoController extends BaseController
{
    @Resource
    private ISCourseInfoService sCourseInfoService;

    /**
     * 查询课程信息列表
     */
    @RequiresPermissions("create:courseInfo:list")
    @GetMapping("/list")
    public TableDataInfo list(SCourseInfo sCourseInfo)
    {
        startPage();
        List<SCourseInfo> list = sCourseInfoService.selectSCourseInfoList(sCourseInfo);
        return getDataTable(list);
    }

    /**
     * 导出课程信息列表
     */
    @RequiresPermissions("create:courseInfo:export")
    @Log(title = "课程信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SCourseInfo sCourseInfo)
    {
        List<SCourseInfo> list = sCourseInfoService.selectSCourseInfoList(sCourseInfo);
        ExcelUtil<SCourseInfo> util = new ExcelUtil<SCourseInfo>(SCourseInfo.class);
        util.exportExcel(response, list, "课程信息数据");
    }

    /**
     * 获取课程信息详细信息
     */
    @RequiresPermissions("create:courseInfo:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sCourseInfoService.selectSCourseInfoById(id));
    }

    /**
     * 新增课程信息
     */
    @RequiresPermissions("create:courseInfo:add")
    @Log(title = "课程信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SCourseInfo sCourseInfo)
    {
        return toAjax(sCourseInfoService.insertSCourseInfo(sCourseInfo));
    }

    /**
     * 修改课程信息
     */
    @RequiresPermissions("create:courseInfo:edit")
    @Log(title = "课程信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SCourseInfo sCourseInfo)
    {
        return toAjax(sCourseInfoService.updateSCourseInfo(sCourseInfo));
    }

    /**
     * 删除课程信息
     */
    @RequiresPermissions("create:courseInfo:remove")
    @Log(title = "课程信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sCourseInfoService.deleteSCourseInfoByIds(ids));
    }
}
