package com.ruoyi.create.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.create.domain.StudentEvaluation;
import com.ruoyi.create.service.StudentEvaluationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 学生测评Controller
 * 
 * <AUTHOR>
 * @date 2025-03-19
 */
@RestController
@RequestMapping("/evaluation")
public class StudentEvaluationController extends BaseController
{
    @Autowired
    private StudentEvaluationService studentEvaluationService;
    /**
     * 1.获取学生测评详细信息
     */
//    @RequiresPermissions("system:evaluation:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(studentEvaluationService.selectStudentEvaluationById(id));
    }

    /**
     * 2.查询某个学生的测评列表
     */
//    @RequiresPermissions("system:evaluation:list")
    @GetMapping(value = "list/{studentId}")
    public TableDataInfo list(String studentId)
    {
        startPage();
        List<StudentEvaluation> list = studentEvaluationService.selectStudentEvaluationList(studentId);
        return getDataTable(list);
    }

    /**
     * 导出学生测评列表
     */
    @RequiresPermissions("system:evaluation:export")
    @Log(title = "学生测评", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, String studentId)
    {
        List<StudentEvaluation> list = studentEvaluationService.selectStudentEvaluationList(studentId);
        ExcelUtil<StudentEvaluation> util = new ExcelUtil<StudentEvaluation>(StudentEvaluation.class);
        util.exportExcel(response, list, "学生测评数据");
    }



    /**
     * 新增学生测评
     */
//    @RequiresPermissions("system:evaluation:add")
    @Log(title = "学生测评", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StudentEvaluation StudentEvaluation)
    {
        return toAjax(studentEvaluationService.insertStudentEvaluation(StudentEvaluation));
    }

    /**
     * 修改学生测评
     */
    @RequiresPermissions("system:evaluation:edit")
    @Log(title = "学生测评", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody StudentEvaluation sStudentEvaluation)
    {
        return toAjax(studentEvaluationService.updateStudentEvaluation(sStudentEvaluation));
    }

    /**
     * 删除学生测评
     */
    @RequiresPermissions("system:evaluation:remove")
    @Log(title = "学生测评", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(studentEvaluationService.deleteStudentEvaluationByIds(ids));
    }
}
