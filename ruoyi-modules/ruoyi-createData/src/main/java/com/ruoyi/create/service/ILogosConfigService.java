package com.ruoyi.create.service;

import com.ruoyi.create.domain.LogosConfig;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * logo(学校andPPT)配置Service接口
 *  *
 *  * <AUTHOR>
 *  * @date 2024-09-05
 */
public interface ILogosConfigService {

    /**
     * 查询logo图片存储列表
     *
     * @param logosConfig logo图片存储
     * @return logo图片存储集合
     */
    public List<LogosConfig> selectLogosConfigList(LogosConfig logosConfig);

    /**
     * 上传logo图片配置列表
     *
     * @param file logo图片
     * @return 结果
     */
    public LogosConfig uploadLogosConfig(MultipartFile file);

    /**
     * 提交logo图片配置
     *
     * @param logosConfig logo图片存储
     * @return 结果
     */
    public int updateLogosConfigByLogoName(LogosConfig logosConfig);


    /**
     * 获取logo图片存储
     *
     * @param id logo图片存储主键
     * @return logo图片存储
     */
    public LogosConfig selectLogosConfigById(Long id);


    /**
     * 修改logo图片存储
     *
     * @param logosConfig logo图片存储
     * @return 结果
     */
    public int updateLogosConfig(LogosConfig logosConfig);

    /**
     * 删除logo图片存储信息
     *
     * @param id logo图片存储主键
     * @return 结果
     */
    public int deleteLogosConfigById(Long id);

    /**
     * 获取所有logo图片的路径
     *
     * @param logoPosition logo展示位置
     * @return 结果
     */
    public List<String> selectLogosConfigListAll(String logoPosition);

    /**
     * 获得登录人所在学校名称
     * @param username 登录人名称
     * @return 结果
     */
    public String selectUniversity(String username);
}
