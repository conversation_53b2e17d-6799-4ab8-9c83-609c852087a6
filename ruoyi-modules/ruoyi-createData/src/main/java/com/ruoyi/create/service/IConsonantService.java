package com.ruoyi.create.service;

import java.util.List;
import com.ruoyi.create.domain.Consonant;
import com.ruoyi.create.domain.PlatParam;
import com.ruoyi.create.domain.PptSpeechDraft;
import org.apache.ibatis.annotations.Param;

/**
 * 讲演稿声母韵母Service接口
 *
 * <AUTHOR>
 * @date 2024-08-26
 */
public interface IConsonantService
{
    /**
     * 查询讲演稿声母韵母
     *
     * @param id 讲演稿声母韵母主键
     * @return 讲演稿声母韵母
     */
    public Consonant selectConsonantById(Long id);

    /**
     * 查询讲演稿声母韵母列表
     *
     * @param consonant 讲演稿声母韵母
     * @return 讲演稿声母韵母集合
     */
    public List<Consonant> selectConsonantList(Consonant consonant);

    /**
     * 新增讲演稿声母韵母
     *
     * @param consonant 讲演稿声母韵母
     * @return 结果
     */
    public int insertConsonant(Consonant consonant);

    /**
     * 修改讲演稿声母韵母
     *
     * @param consonant 讲演稿声母韵母
     * @return 结果
     */
    public int updateConsonant(Consonant consonant);

    /**
     * 批量删除讲演稿声母韵母
     *
     * @param ids 需要删除的讲演稿声母韵母主键集合
     * @return 结果
     */
    public int deleteConsonantByIds(Long[] ids);

    /**
     * 删除讲演稿声母韵母信息
     *
     * @param id 讲演稿声母韵母主键
     * @return 结果
     */
    public int deleteConsonantById(Long id);
    public int deleteConsonantByPresationId(Long id);

    public Consonant getConsonant(PlatParam platParam);

    public String getConsonantStr(List<String> list);


    PlatParam getPlatParam(PlatParam platParam);

    List<PptSpeechDraft> getPptSpeechDraft(String pptSpeechDraftPath);

    /**
     * 更换讲演稿文件id
     * @param oldSpeechdraftId 旧的讲演稿文件id
     * @param newSpeechdraftId 新的讲演稿文件id
     * @return 结果
     */
    int updateSpeechdraftId(@Param("oldSpeechdraftId") String oldSpeechdraftId,
                            @Param("newSpeechdraftId") String newSpeechdraftId);
}
