package com.ruoyi.create.service;

import java.util.List;
import com.ruoyi.create.domain.StudentScenario;

/**
 * 学生应用场景Service接口
 *
 * <AUTHOR>
 * @date 2024-10-11
 */
public interface IStudentScenarioService
{
    /**
     * 查询学生应用场景
     *
     * @param id 学生应用场景主键
     * @return 学生应用场景
     */
    public StudentScenario selectStudentScenarioById(Long id);
    public StudentScenario selectStudentScenarioBgById(StudentScenario studentScenario);

    /**
     * 查询学生应用场景列表
     *
     * @param studentScenario 学生应用场景
     * @return 学生应用场景集合
     */
    public List<StudentScenario> selectStudentScenarioList(StudentScenario studentScenario);

    /**
     * 新增学生应用场景
     *
     * @param studentScenario 学生应用场景
     * @return 结果
     */
    public int insertStudentScenario(StudentScenario studentScenario);

    /**
     * 修改学生应用场景
     *
     * @param studentScenario 学生应用场景
     * @return 结果
     */
    public int updateStudentScenario(StudentScenario studentScenario);

    /**
     * 批量删除学生应用场景
     *
     * @param ids 需要删除的学生应用场景主键集合
     * @return 结果
     */
    public int deleteStudentScenarioByIds(Long[] ids);

    /**
     * 删除学生应用场景信息
     *
     * @param id 学生应用场景主键
     * @return 结果
     */
    public int deleteStudentScenarioById(Long id);


    public int insertOrUpdateStudentScenario(StudentScenario studentScenario);

    public void deleteImgByPath(String path);
}
