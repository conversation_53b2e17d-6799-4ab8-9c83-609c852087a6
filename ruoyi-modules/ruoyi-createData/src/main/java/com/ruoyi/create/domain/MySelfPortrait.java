package com.ruoyi.create.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.xml.soap.Text;
import java.time.LocalDateTime;

@TableName("s_self_portrait")
@Data
public class MySelfPortrait {
    @TableId
    private String studentId;
    private Integer laScore;
    private Integer laHomework;
    private Integer laMastery;
    private Integer laPerformance;
    private Integer laInterest;
    private Integer laTalent;
    private Integer laEvaluation;
    private Integer lbMorality;
    private Integer lbAesthetics;
    private Integer lbHealth;
    private Integer lbCooperation;
    private Integer lbLearning;
    private Float yuExperiment;
    private Float yuElective;
    private Float yuGeneral;
    private Float yuCore;
    private Float yuFoundation;
    private Float xuTotalCredits;
    private Float xuCompletedCredits;
    private String yuceFenxi; // 预测分析长文本
    private LocalDateTime statisticsTime;
    private String semester;

}