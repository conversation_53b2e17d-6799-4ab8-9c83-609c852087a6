package com.ruoyi.create.service;

import com.ruoyi.create.domain.UserVoiceType;

import java.util.List;

/**
 * 用户语音切换
 *
 * <AUTHOR> @create 2024-09-13-14:52
 */
public interface IUserVoiceTypeService {

    /**
     * 查询用户语音
     *
     * @param id 用户语音主键
     * @return 用户语音
     */
    public UserVoiceType selectUserVoiceTypeById(Long id);

    /**
     * 查询用户语音列表
     *
     * @param userVoiceType 用户语音
     * @return 用户语音集合
     */
    public List<UserVoiceType> selectUserVoiceTypeList(UserVoiceType userVoiceType);

    /**
     * 新增用户语音
     *
     * @param userVoiceType 用户语音
     * @return 结果
     */
    public int insertUserVoiceType(UserVoiceType userVoiceType);

    /**
     * 修改用户语音
     *
     * @param userVoiceType 用户语音
     * @return 结果
     */
    public int updateUserVoiceType(UserVoiceType userVoiceType);

    /**
     * 批量删除用户语音
     *
     * @param ids 需要删除的用户语音主键集合
     * @return 结果
     */
    public int deleteUserVoiceTypeByIds(Long[] ids);

    /**
     * 删除用户语音信息
     *
     * @param id 用户语音主键
     * @return 结果
     */
    public int deleteUserVoiceTypeById(Long id);

    /**
     * 根据登陆人存储语音种类
     *
     * @param userVoiceType 用户语音
     * @return 结果
     */
    public int updateUserVoiceTypeByStorage(UserVoiceType userVoiceType);

    public UserVoiceType selectUserVoiceTypeByUserName(String username);
}
