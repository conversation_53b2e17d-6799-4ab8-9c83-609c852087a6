package com.ruoyi.create.service;


import com.ruoyi.create.domain.StudentProblemSub;

import java.util.List;

/**
 * 学生回答记录Service接口
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
public interface IStudentProblemSubService
{
    /**
     * 查询学生回答记录
     *
     * @param id 学生回答记录主键
     * @return 学生回答记录
     */
    public StudentProblemSub selectStudentProblemSubById(Long id);

    /**
     * 查询学生回答记录列表
     *
     * @param studentProblemSub 学生回答记录
     * @return 学生回答记录集合
     */
    public List<StudentProblemSub> selectStudentProblemSubList(StudentProblemSub studentProblemSub);

    /**
     * 新增学生回答记录
     *
     * @param studentProblemSub 学生回答记录
     * @return 结果
     */
    public int insertStudentProblemSub(StudentProblemSub studentProblemSub);

    /**
     * 修改学生回答记录
     *
     * @param studentProblemSub 学生回答记录
     * @return 结果
     */
    public int updateStudentProblemSub(StudentProblemSub studentProblemSub);

    /**
     * 批量删除学生回答记录
     *
     * @param ids 需要删除的学生回答记录主键集合
     * @return 结果
     */
    public int deleteStudentProblemSubByIds(Long[] ids);

    /**
     * 删除学生回答记录信息
     *
     * @param id 学生回答记录主键
     * @return 结果
     */
    public int deleteStudentProblemSubById(Long id);
}
