package com.ruoyi.create.mapper;

import java.util.List;

import com.ruoyi.create.Vo.StudentAttendanceVo;
import com.ruoyi.create.domain.StudentAttendance;
import org.apache.ibatis.annotations.Mapper;

/**
 * 学生签到历史Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
@Mapper
public interface StudentAttendanceMapper
{
    /**
     * 查询学生签到历史
     *
     * @param id 学生签到历史主键
     * @return 学生签到历史
     */
    public StudentAttendance selectStudentAttendanceById(Long id);

    /**
     * 查询学生签到历史列表
     *
     * @param studentAttendance 学生签到历史
     * @return 学生签到历史集合
     */
    public List<StudentAttendance> selectStudentAttendanceList(StudentAttendance studentAttendance);

    /**
     * 新增学生签到历史
     *
     * @param studentAttendance 学生签到历史
     * @return 结果
     */
    public int insertStudentAttendance(StudentAttendance studentAttendance);

    /**
     * 修改学生签到历史
     *
     * @param studentAttendance 学生签到历史
     * @return 结果
     */
    public int updateStudentAttendance(StudentAttendance studentAttendance);

    /**
     * 删除学生签到历史
     *
     * @param id 学生签到历史主键
     * @return 结果
     */
    public int deleteStudentAttendanceById(Long id);

    /**
     * 批量删除学生签到历史
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStudentAttendanceByIds(Long[] ids);

    /**
     * 根据学生id查询学生签到历史
     * @param studentId
     * @return
     */
    List<StudentAttendanceVo> selectStudentAttendanceListByStudentId(String studentId);

    /**
     * 根据课程id和学生id查询学生签到历史
     * @param courseId
     * @param studentId
     * @return
     */
    List<StudentAttendance> selectStudentAttendanceByCourseIdAndStudentId(String courseId, String studentId);
}
