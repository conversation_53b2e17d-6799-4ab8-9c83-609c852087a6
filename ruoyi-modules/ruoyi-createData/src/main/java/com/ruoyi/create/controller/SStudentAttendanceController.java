package com.ruoyi.create.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.SStudentAttendance;
import com.ruoyi.create.service.ISStudentAttendanceService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 学生签到历史Controller
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
@RestController
@RequestMapping("/studentAttendance")
public class SStudentAttendanceController extends BaseController
{
    @Resource
    private ISStudentAttendanceService sStudentAttendanceService;

    /**
     * 查询学生签到历史列表
     */
    @RequiresPermissions("create:studentAttendance:list")
    @GetMapping("/list")
    public TableDataInfo list(SStudentAttendance sStudentAttendance)
    {
        startPage();
        List<SStudentAttendance> list = sStudentAttendanceService.selectSStudentAttendanceList(sStudentAttendance);
        return getDataTable(list);
    }

    /**
     * 导出学生签到历史列表
     */
    @RequiresPermissions("create:studentAttendance:export")
    @Log(title = "学生签到历史", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SStudentAttendance sStudentAttendance)
    {
        List<SStudentAttendance> list = sStudentAttendanceService.selectSStudentAttendanceList(sStudentAttendance);
        ExcelUtil<SStudentAttendance> util = new ExcelUtil<SStudentAttendance>(SStudentAttendance.class);
        util.exportExcel(response, list, "学生签到历史数据");
    }

    /**
     * 获取学生签到历史详细信息
     */
    @RequiresPermissions("create:studentAttendance:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sStudentAttendanceService.selectSStudentAttendanceById(id));
    }

    /**
     * 新增学生签到历史
     */
    @RequiresPermissions("create:studentAttendance:add")
    @Log(title = "学生签到历史", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SStudentAttendance sStudentAttendance)
    {
        return toAjax(sStudentAttendanceService.insertSStudentAttendance(sStudentAttendance));
    }

    /**
     * 修改学生签到历史
     */
    @RequiresPermissions("create:studentAttendance:edit")
    @Log(title = "学生签到历史", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SStudentAttendance sStudentAttendance)
    {
        return toAjax(sStudentAttendanceService.updateSStudentAttendance(sStudentAttendance));
    }

    /**
     * 删除学生签到历史
     */
    @RequiresPermissions("create:studentAttendance:remove")
    @Log(title = "学生签到历史", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sStudentAttendanceService.deleteSStudentAttendanceByIds(ids));
    }
}
