package com.ruoyi.create.service;

import java.util.List;

import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.create.domain.PromptOptimization;

/**
 * prompt模板优化Service接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface IPromptOptimizationService
{
    /**
     * 查询prompt模板优化
     *
     * @param id prompt模板优化主键
     * @return prompt模板优化
     */
    public PromptOptimization selectPromptOptimizationById(Long id);

    /**
     * 查询prompt模板优化列表
     *
     * @param promptOptimization prompt模板优化
     * @return prompt模板优化集合
     */
    public List<PromptOptimization> selectPromptOptimizationList(PromptOptimization promptOptimization);

    /**
     * 新增prompt模板优化
     *
     * @param promptOptimization prompt模板优化
     * @return 结果
     */
    public AjaxResult insertPromptOptimization(PromptOptimization promptOptimization);

    /**
     * 修改prompt模板优化
     *
     * @param promptOptimization prompt模板优化
     * @return 结果
     */
    public int updatePromptOptimization(PromptOptimization promptOptimization);

    /**
     * 批量删除prompt模板优化
     *
     * @param ids 需要删除的prompt模板优化主键集合
     * @return 结果
     */
    public int deletePromptOptimizationByIds(Long[] ids);

    /**
     * 删除prompt模板优化信息
     *
     * @param id prompt模板优化主键
     * @return 结果
     */
    public int deletePromptOptimizationById(Long id);

    /**
     * 获取prompt优化任务详情
     *
     * @param id prompt优化任务主键
     * @return prompt优化任务
     */
    public PromptOptimization getPromptInfo(Long id);
}
