package com.ruoyi.create.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.CourseKikeStep;
import com.ruoyi.create.service.ICourseKikeStepService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 课程讨论话题回复点赞Controller
 * 
 * <AUTHOR>
 * @date 2024-12-05
 */
@RestController
@RequestMapping("/kikeStep")
public class CourseKikeStepController extends BaseController
{
    @Autowired
    private ICourseKikeStepService courseKikeStepService;

    /**
     * 查询课程讨论话题回复点赞列表
     */
    @RequiresPermissions("create:step:list")
    @GetMapping("/list")
    public TableDataInfo list(CourseKikeStep courseKikeStep)
    {
        startPage();
        List<CourseKikeStep> list = courseKikeStepService.selectCourseKikeStepList(courseKikeStep);
        return getDataTable(list);
    }

    /**
     * 导出课程讨论话题回复点赞列表
     */
    @RequiresPermissions("create:step:export")
    @Log(title = "课程讨论话题回复点赞", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CourseKikeStep courseKikeStep)
    {
        List<CourseKikeStep> list = courseKikeStepService.selectCourseKikeStepList(courseKikeStep);
        ExcelUtil<CourseKikeStep> util = new ExcelUtil<CourseKikeStep>(CourseKikeStep.class);
        util.exportExcel(response, list, "课程讨论话题回复点赞数据");
    }

    /**
     * 获取课程讨论话题回复点赞详细信息
     */
    @RequiresPermissions("create:step:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(courseKikeStepService.selectCourseKikeStepById(id));
    }

    /**
     * 新增课程讨论话题回复点赞
     */
    @Log(title = "课程讨论话题回复点赞", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CourseKikeStep courseKikeStep)
    {
        return toAjax(courseKikeStepService.insertCourseKikeStep(courseKikeStep));
    }

    /**
     * 修改课程讨论话题回复点赞
     */
    @RequiresPermissions("create:step:edit")
    @Log(title = "课程讨论话题回复点赞", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CourseKikeStep courseKikeStep)
    {
        return toAjax(courseKikeStepService.updateCourseKikeStep(courseKikeStep));
    }

    /**
     * 删除课程讨论话题回复点赞
     */
    @Log(title = "课程讨论话题回复点赞", businessType = BusinessType.DELETE)
	@DeleteMapping("/{msgId}")
    public AjaxResult remove(@PathVariable Long msgId)
    {
        return toAjax(courseKikeStepService.deleteCourseKikeStepById(msgId));
    }
}
