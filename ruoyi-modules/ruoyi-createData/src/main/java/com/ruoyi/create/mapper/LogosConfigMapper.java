package com.ruoyi.create.mapper;

import com.ruoyi.create.domain.LogosConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * logo(学校andPPT)配置Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-05
 */
public interface LogosConfigMapper {

    /**
     * 查询图片存储列表
     *
     * @param logosConfig 图片存储
     * @return 图片存储集合
     */
    public List<LogosConfig> selectLogosConfigList(LogosConfig logosConfig);

    /**
     * 查询图片存储
     *
     * @param id 图片存储主键
     * @return 图片存储
     */
    public LogosConfig selectLogosConfigById(Long id);

    /**
     * 修改图片存储
     *
     * @param logosConfig 图片存储
     * @return 结果
     */
    public int updateLogosConfig(LogosConfig logosConfig);

    /**
     * 删除图片存储
     *
     * @param id 图片存储主键
     * @return 结果
     */
    public int deleteLogosConfigById(Long id);

    /**
     * 查询图片存储配置
     *
     * @param logoName 图片存储名称
     * @return 结果
     */
    public List<LogosConfig> selectLogosConfigByLogoName(@Param("logoName") String logoName);

    /**
     * 获取所有logo图片的路径
     *
     * @param logoPosition logo展示位置
     * @return 结果
     */
    public List<LogosConfig> selectLogosConfigListAll(@Param("logoPosition")String logoPosition,@Param("univerName")String univerName);

    /**
     * 增加logo图片配置
     *
     * @param logosConfig logo对象
     * @return 结果
     */
    public int insertLogosConfig(LogosConfig logosConfig);

    public List<LogosConfig> selectLogosConfigBySchool(@Param("logoPosition")String logoPosition,@Param("school") String school);
}