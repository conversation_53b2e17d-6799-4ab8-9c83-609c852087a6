package com.ruoyi.create.mapper;

import java.util.List;

import com.ruoyi.create.domain.StudentEvaluation;
import org.apache.ibatis.annotations.Mapper;


/**
 * 学生测评Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-19
 */
@Mapper
public interface StudentEvaluationMapper
{
    /**
     * 查询学生测评
     * 
     * @param id 学生测评主键
     * @return 学生测评
     */
    StudentEvaluation selectStudentEvaluationById(Long id);

    /**
     * 查询学生测评列表
     * 
     * @param 学生测评
     * @return 学生测评集合
     */
    List<StudentEvaluation> selectStudentEvaluationList(String studentId);

    /**
     * 新增学生测评
     * 
     * @param sStudentEvaluation 学生测评
     * @return 结果
     */
    int insertStudentEvaluation(StudentEvaluation sStudentEvaluation);

    /**
     * 修改学生测评
     * 
     * @param sStudentEvaluation 学生测评
     * @return 结果
     */
    int updateStudentEvaluation(StudentEvaluation sStudentEvaluation);

    /**
     * 删除学生测评
     * 
     * @param id 学生测评主键
     * @return 结果
     */
  int deleteStudentEvaluationById(String id);

    /**
     * 批量删除学生测评
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    //该抽象方法错误使用需要根据业务逻辑修改
    int deleteStudentEvaluationByIds(Long[] ids);
}
