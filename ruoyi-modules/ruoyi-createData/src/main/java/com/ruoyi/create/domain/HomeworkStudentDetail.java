package com.ruoyi.create.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

import java.util.List;

/**
 * 学生作业答案详情对象 s_homework_student_detail
 * 
 * <AUTHOR>
 * @date 2024-06-07
 */
@Data
@TableName(value = "s_homework_student_detail")
public class HomeworkStudentDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 表记录id */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 作业id */
    @Excel(name = "作业id")
    @TableField("hm_id")
    private Long hmId;

    /** 题目id */
    @Excel(name = "题目id")
    @TableField("question_id")
    private Long questionId;

    /** 用户id */
    @Excel(name = "用户id")
    @TableField("user_id")
    private Long userId;

    /** 用户的答案 */
    @Excel(name = "用户的答案")
    @TableField("user_answer")
    private String userAnswer;

    /** 正确答案 */
    @Excel(name = "正确答案")
    @TableField("correct_answer")
    private String correctAnswer;

    @TableField(exist = false)
    private String question;

    @TableField(exist = false)
    private String questionType;

    @TableField(exist = false)
    private  HomeworkQuestion homeworkQuestion;

    @TableField(exist = false)
    private List<HomeworkQuestionOption> homeworkQuestionOptionList;
}
