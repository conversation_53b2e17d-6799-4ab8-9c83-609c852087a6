package com.ruoyi.create.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.create.Vo.AmbitTeacherVo;
import com.ruoyi.create.domain.University;

/**
 * 学校信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
public interface UniversityMapper extends BaseMapper<University>
{
    /**
     * 查询学校信息
     *
     * @param id 学校信息主键
     * @return 学校信息
     */
    public University selectUniversityById(Long id);

    /**
     * 查询学校信息列表
     *
     * @param university 学校信息
     * @return 学校信息集合
     */
    public List<University> selectUniversityList(University university);

    /**
     * 新增学校信息
     *
     * @param university 学校信息
     * @return 结果
     */
    public int insertUniversity(University university);

    /**
     * 修改学校信息
     *
     * @param university 学校信息
     * @return 结果
     */
    public int updateUniversity(University university);

    /**
     * 删除学校信息
     *
     * @param id 学校信息主键
     * @return 结果
     */
    public int deleteUniversityById(Long id);

    /**
     * 批量删除学校信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUniversityByIds(Long[] ids);

    List<University> selectUniversityListAll();
    List<University> selectListAll();

    List<University> selectUniversityListallTeacher();

    University getUniversityInfo(University university);

    List<University> selectUniversityListByUniversityId(Long universityId);
    List<University> selectUniversityListById(Long universityId);

   /**
     * 根据学校名称查询学校信息
     */
    public List<University> selectUniversityListByUniverName(String univerName);


    /**
     * 根据课程名称查询教师信息
     */
    List<AmbitTeacherVo> selectAmbitTeacherVoListByCourseName(String courseName);
}
