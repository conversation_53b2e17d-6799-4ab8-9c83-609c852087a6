package com.ruoyi.create.mapper;


import com.ruoyi.create.domain.PresentationPath;
import java.util.List;

public interface PresentationPathMapper {
    /**
     * 查询ppt拆分
     *
     * @param id ppt拆分主键
     * @return ppt拆分
     */
    public PresentationPath selectPresentationPathById(Long id);

    /**
     * 查询ppt拆分列表
     *
     * @param presentationPath ppt拆分
     * @return ppt拆分集合
     */
    public List<PresentationPath> selectPresentationPathList(PresentationPath presentationPath);

    /**
     * 新增ppt拆分
     *
     * @param presentationPath ppt拆分
     * @return 结果
     */
    public int insertPresentationPath(PresentationPath presentationPath);

    /**
     * 修改ppt拆分
     *
     * @param presentationPath ppt拆分
     * @return 结果
     */
    public int updatePresentationPath(PresentationPath presentationPath);

    /**
     * 删除ppt拆分
     *
     * @param id ppt拆分主键
     * @return 结果
     */
    public int deletePresentationPathById(Long id);

    /**
     * 批量删除ppt拆分
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePresentationPathByIds(Long[] ids);
}
