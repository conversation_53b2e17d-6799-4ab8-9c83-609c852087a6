package com.ruoyi.create.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.domain.StudentPromotionVO;
import com.ruoyi.create.utils.UserUtils;
import com.ruoyi.system.api.domain.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.StudentPromotion;
import com.ruoyi.create.service.IStudentPromotionService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 提升课程推送Controller
 *
 * <AUTHOR>
 * @date 2024-08-13
 */
@RestController
@RequestMapping("/promotion")
public class StudentPromotionController extends BaseController
{
    @Autowired
    private IStudentPromotionService studentPromotionService;

    @Autowired
    private UserUtils userUtils;

    /**
     * 通过学生id查询提升课程推送列表
     */
    @RequiresPermissions("create:promotion:list")
    @RequestMapping ("/list")
    public TableDataInfo getInfoByStudentId(StudentPromotionVO studentPromotion)
    {
        startPage();
        SysUser sysUser = userUtils.getSysUser(SecurityUtils.getUserId());
        System.out.println(sysUser.getStudentId());
        studentPromotion.setStudentId(sysUser.getStudentId());
        List<StudentPromotionVO> list = studentPromotionService.selectStudentPromotionByStudentId(studentPromotion);
        return getDataTable(list);
    }


    /**
     * 新增修改学生学习推送表
     */
    @RequiresPermissions("create:promotion:add")
    @Log(title = "新增用户学习记录", businessType = BusinessType.INSERT)
    @PostMapping("/addStudentPromotion")
    public AjaxResult addStudentPromotion(@RequestBody StudentPromotion studentPromotion)
    {
        return AjaxResult.success(studentPromotionService.insertOrUpdateStudentPromotion(studentPromotion));
    }

    /**
     * 修改学生学习记录
     */
    @RequiresPermissions("create:promotion:edit")
    @Log(title = "修改学生学习记录", businessType = BusinessType.UPDATE)
    @PutMapping("/editStudentPromotion")
    public AjaxResult edit(@RequestBody StudentPromotion studentPromotion)
    {
        return toAjax(studentPromotionService.updateStudentPromotion(studentPromotion));
    }


    /**
     * 查询提升课程推送列表
     */
//    @RequiresPermissions("create:promotion:list")
//    @GetMapping("/list")
//    public TableDataInfo list(StudentPromotion studentPromotion)
//    {
//        startPage();
//        List<StudentPromotion> list = studentPromotionService.selectStudentPromotionList(studentPromotion);
//        return getDataTable(list);
//    }

    /**
     * 导出提升课程推送列表
     */
    @RequiresPermissions("create:promotion:export")
    @Log(title = "提升课程推送", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StudentPromotion studentPromotion)
    {
        List<StudentPromotion> list = studentPromotionService.selectStudentPromotionList(studentPromotion);
        ExcelUtil<StudentPromotion> util = new ExcelUtil<StudentPromotion>(StudentPromotion.class);
        util.exportExcel(response, list, "提升课程推送数据");
    }

    /**
     * 获取提升课程推送详细信息
     */
    @RequiresPermissions("create:promotion:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(studentPromotionService.selectStudentPromotionById(id));
    }

    /**
     * 新增提升课程推送
     */
    @RequiresPermissions("create:promotion:add")
    @Log(title = "提升课程推送", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StudentPromotion studentPromotion)
    {
        return toAjax(studentPromotionService.insertOrUpdateStudentPromotion(studentPromotion));
    }



    /**
     * 删除提升课程推送
     */
    @RequiresPermissions("create:promotion:remove")
    @Log(title = "提升课程推送", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(studentPromotionService.deleteStudentPromotionByIds(ids));
    }
}
