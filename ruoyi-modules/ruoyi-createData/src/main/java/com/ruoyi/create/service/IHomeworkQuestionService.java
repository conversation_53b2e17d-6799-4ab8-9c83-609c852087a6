package com.ruoyi.create.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.create.domain.HomeworkQuestion;

import java.util.List;

/**
 * 作业题目Service接口
 *
 * <AUTHOR>
 * @date 2024-06-07
 */
public interface IHomeworkQuestionService extends IService<HomeworkQuestion>
{
    /**
     * 查询作业题目
     *
     * @param id 作业题目主键
     * @return 作业题目
     */
    public HomeworkQuestion selectHomeworkQuestionById(Long id);

    /**
     * 查询作业题目列表
     *
     * @param homeworkQuestion 作业题目
     * @return 作业题目集合
     */
    public List<HomeworkQuestion> selectHomeworkQuestionList(HomeworkQuestion homeworkQuestion);

    /**
     * 新增作业题目
     *
     * @param homeworkQuestion 作业题目
     * @return 结果
     */
    public int insertHomeworkQuestion(HomeworkQuestion homeworkQuestion);

    /**
     * 修改作业题目
     *
     * @param homeworkQuestion 作业题目
     * @return 结果
     */
    public int updateHomeworkQuestion(HomeworkQuestion homeworkQuestion);

    /**
     * 批量删除作业题目
     *
     * @param ids 需要删除的作业题目主键集合
     * @return 结果
     */
    public int deleteHomeworkQuestionByIds(Long[] ids);

    /**
     * 删除作业题目信息
     *
     * @param id 作业题目主键
     * @return 结果
     */
    public int deleteHomeworkQuestionById(Long id);
}
