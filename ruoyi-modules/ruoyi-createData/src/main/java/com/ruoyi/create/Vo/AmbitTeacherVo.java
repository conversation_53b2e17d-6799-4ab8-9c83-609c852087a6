package com.ruoyi.create.Vo;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import com.ruoyi.create.domain.LiteratrueLinks;
import com.ruoyi.create.domain.LiteratrueNodesData;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 专业教师对象 s_ambit_teacher
 *
 * <AUTHOR>
 * @date 2024-08-29
 */
@Data
public class AmbitTeacherVo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 专业ID
     */
    @Excel(name = "专业ID")
    private Long majorId;

    private Long teacherId;

    /**
     * 学科ID
     */
    @Excel(name = "学科ID")
    private Long disciplineId;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String name;

    /**
     * 性别（0男 1女 2未知）
     */
    @Excel(name = "性别", readConverterExp = "0=男,1=女,2=未知")
    private String sex;

    /**
     * 职称
     */
    @Excel(name = "职称")
    private String title;

    /**
     * 学历
     */
    @Excel(name = "学历")
    private String education;

    /**
     * 研究方向
     */
    @Excel(name = "研究方向")
    private String researchDirection;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 课题组类型
     */
    private String researchType;



    /**
     * 学校id
     */
    private Long univerId;

    /**
     * 学校名称
     */
    private String univerName;
    private String majorName;

    /**
     * 学院id
     */
    private Long colleId;

    /**
     * 学院名称
     */
    private String colleName;

    private Long collegeId;
    private String collegeName;


    private String unionCoursesName;

    private Long unionCoursesId;

    private String teacherName;

    // 是否是负责人
    private String isPersonCharge;

    // 研究成果
    private String researchFindings;
    private List<String> researchFindingList;
    /**
     * 研究成果
     */
    private List<TeacherFindingsVo> teacherFindingsList;
}
