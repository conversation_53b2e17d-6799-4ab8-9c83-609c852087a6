package com.ruoyi.create.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.create.domain.CollegeInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.CollegeInfoMapper;
import com.ruoyi.create.service.ICollegeInfoService;

/**
 * 学院信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
@Service
public class CollegeInfoServiceImpl extends ServiceImpl<CollegeInfoMapper, CollegeInfo> implements ICollegeInfoService
{
    @Autowired
    private CollegeInfoMapper collegeInfoMapper;

    /**
     * 查询学院信息
     *
     * @param id 学院信息主键
     * @return 学院信息
     */
    @Override
    public CollegeInfo selectCollegeInfoById(Long id)
    {
        return collegeInfoMapper.selectCollegeInfoById(id);
    }

    /**
     * 查询学院信息列表
     *
     * @param collegeInfo 学院信息
     * @return 学院信息
     */
    @Override
    public List<CollegeInfo> selectCollegeInfoList(CollegeInfo collegeInfo)
    {
        return collegeInfoMapper.selectCollegeInfoList(collegeInfo);
    }

    /**
     * 新增学院信息
     *
     * @param collegeInfo 学院信息
     * @return 结果
     */
    @Override
    public int insertCollegeInfo(CollegeInfo collegeInfo)
    {
        collegeInfo.setCreateTime(DateUtils.getNowDate());
        return collegeInfoMapper.insertCollegeInfo(collegeInfo);
    }

    /**
     * 修改学院信息
     *
     * @param collegeInfo 学院信息
     * @return 结果
     */
    @Override
    public int updateCollegeInfo(CollegeInfo collegeInfo)
    {
        collegeInfo.setUpdateTime(DateUtils.getNowDate());
        return collegeInfoMapper.updateCollegeInfo(collegeInfo);
    }

    /**
     * 批量删除学院信息
     *
     * @param ids 需要删除的学院信息主键
     * @return 结果
     */
    @Override
    public int deleteCollegeInfoByIds(Long[] ids)
    {
        return collegeInfoMapper.deleteCollegeInfoByIds(ids);
    }

    /**
     * 删除学院信息信息
     *
     * @param id 学院信息主键
     * @return 结果
     */
    @Override
    public int deleteCollegeInfoById(Long id)
    {
        return collegeInfoMapper.deleteCollegeInfoById(id);
    }

    @Override
    public List<CollegeInfo> getAll() {
        return collegeInfoMapper.getAll();
    }
}
