package com.ruoyi.create.service;

import java.util.List;
import com.ruoyi.create.domain.KnowledgeRouting;

/**
 * 知识库路由配置Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-30
 */
public interface IKnowledgeRoutingService 
{
    /**
     * 查询知识库路由配置
     * 
     * @param id 知识库路由配置主键
     * @return 知识库路由配置
     */
    public KnowledgeRouting selectKnowledgeRoutingById(Long id);

    /**
     * 查询知识库路由配置列表
     * 
     * @param knowledgeRouting 知识库路由配置
     * @return 知识库路由配置集合
     */
    public List<KnowledgeRouting> selectKnowledgeRoutingList(KnowledgeRouting knowledgeRouting);

    /**
     * 新增知识库路由配置
     * 
     * @param knowledgeRouting 知识库路由配置
     * @return 结果
     */
    public int insertKnowledgeRouting(KnowledgeRouting knowledgeRouting);

    /**
     * 修改知识库路由配置
     * 
     * @param knowledgeRouting 知识库路由配置
     * @return 结果
     */
    public int updateKnowledgeRouting(KnowledgeRouting knowledgeRouting);

    /**
     * 批量删除知识库路由配置
     * 
     * @param ids 需要删除的知识库路由配置主键集合
     * @return 结果
     */
    public int deleteKnowledgeRoutingByIds(Long[] ids);

    /**
     * 删除知识库路由配置信息
     * 
     * @param id 知识库路由配置主键
     * @return 结果
     */
    public int deleteKnowledgeRoutingById(Long id);

    /**
     * 获取知识库路由配置详细信息
     */
    public List<KnowledgeRouting> selectKnowledgeByMenuRouting(String menuRouting);
}
