package com.ruoyi.create.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.Vo.UserVoiceVo;
import com.ruoyi.create.domain.UserVoice;
import com.ruoyi.create.mapper.UserVoiceMapper;
import com.ruoyi.create.service.IUserVoiceService;
import com.ruoyi.system.api.RemoteDictTypeService;
import com.ruoyi.system.api.domain.SysDictData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-09
 */
@Service
public class UserVoiceServiceImpl implements IUserVoiceService {

    @Autowired
    private UserVoiceMapper userVoiceMapper;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private RemoteDictTypeService remoteDictTypeService;

    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public UserVoice selectSUserVoiceById(Long id)
    {
        return userVoiceMapper.selectUserVoiceById(id);
    }

    @Override
    public UserVoice selectSUserVoiceById() {
        // 当前系统不存在当前用户
        if(Objects.isNull(tokenService.getLoginUser())){
            return null;
        }
        List<SysDictData> userVoiceChoose = remoteDictTypeService.dictTypeGetInfo("user_voice_choose", SecurityConstants.INNER);
        // 当前系统字典中没有设置使用的语音合成厂商
        if(Objects.isNull(userVoiceChoose)){
            throw new RuntimeException("请先设置系统默认发言人-请联系管理员");
        }
        // 获取系统使用的语音合成厂商
        SysDictData sysDictData = userVoiceChoose.get(0);
        String dictValue = sysDictData.getDictValue();
        UserVoice userVoice = userVoiceMapper.selectUserVoiceById(tokenService.getLoginUser().getUserid());
        // 当前系统用户没有选择过语音发言人 处理 -》 根据系统现在使用的是那个厂商返回固定的初始值
        if(Objects.isNull(userVoice)){
            // 返回初始化语音发言人
            return getSystemVoiceRoleInit(dictValue);
        }
        UserVoice vo = new UserVoice();
        if(dictValue.equals("xfSpark")){
            // 获取讯飞发音人
            List<SysDictData> sysXfVoiceRole = remoteDictTypeService.dictTypeGetInfo("sys_xf_voice_role", SecurityConstants.INNER);
            // 获取合法值
            Set<String> collect = sysXfVoiceRole.stream().map(SysDictData::getDictValue).collect(Collectors.toSet());
            String xfVoiceRoleId = userVoice.getXfVoiceRoleId();
            if(!collect.contains(xfVoiceRoleId)){
                xfVoiceRoleId = "xiaoyan";
            }
            vo.setVoiceRoleId(xfVoiceRoleId);
            vo.setVoicePitch(userVoice.getVoicePitch() * 10);
            vo.setVoiceSpeed(userVoice.getVoiceSpeed() * 10);
            vo.setVoiceVolume(userVoice.getVoiceVolume() * 10);
        }else if(dictValue.equals("baidu")){
            // 获取百度发音人
            List<SysDictData> sysVoiceRole = remoteDictTypeService.dictTypeGetInfo("sys_voice_role", SecurityConstants.INNER);
            // 获取合法值
            Set<String> collect = sysVoiceRole.stream().map(SysDictData::getDictValue).collect(Collectors.toSet());
            // 提取百度发言人用于判断其合法性
            String bdVoiceRoleId = userVoice.getBdVoiceRoleId();
            if(!collect.contains(bdVoiceRoleId)){
                bdVoiceRoleId = "1";
            }
            vo.setVoiceRoleId(bdVoiceRoleId);
            vo.setVoicePitch(userVoice.getVoicePitch());
            vo.setVoiceSpeed(userVoice.getVoiceSpeed());
            vo.setVoiceVolume(userVoice.getVoiceVolume());
        }else if(dictValue.equals("doubao")){
            // 获取豆包发音人
            List<SysDictData> sysVoiceRole = remoteDictTypeService.dictTypeGetInfo("sys_doubao_voice_role", SecurityConstants.INNER);
            Set<String> collect = sysVoiceRole.stream().map(SysDictData::getDictValue).collect(Collectors.toSet());
            String douBaoVoiceRoleId = userVoice.getDouBaoVoiceRoleId();
            if(!collect.contains(douBaoVoiceRoleId)){
                douBaoVoiceRoleId = "zh_female_shuangkuaisisi_moon_bigtts";
            }
            vo.setVoiceRoleId(douBaoVoiceRoleId);
            vo.setVoiceSpeed(userVoice.getVoiceSpeed() * 0.2);
        }
        vo.setUserPlayflag(userVoice.isUserPlayflag());
        vo.setId(userVoice.getId());
        vo.setUserId(userVoice.getUserId());
        vo.setCreateBy(userVoice.getCreateBy());
        return vo;
    }

    // 返回初始化语音发言人 参数 ： 当前系统使用的语音合成厂商
    private UserVoice getSystemVoiceRoleInit(String dictValue) {
        UserVoice userVoice = new UserVoice();
        if(dictValue.equals("xfSpark")){
            userVoice.setXfVoiceRoleId("xiaoyan");
            userVoice.setVoiceVolume(50);
            userVoice.setVoicePitch(50);
            userVoice.setVoiceSpeed(50.0);
        }else if(dictValue.equals("baidu")){
            userVoice.setBdVoiceRoleId("1");
            userVoice.setVoiceVolume(5);
            userVoice.setVoicePitch(5);
            userVoice.setVoiceSpeed(5.0);
        }else if(dictValue.equals("doubao")){
            userVoice.setDouBaoVoiceRoleId("zh_female_shuangkuaisisi_moon_bigtts");
            userVoice.setVoiceSpeed(1.0);
        }
        return userVoice;
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param userVoice 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<UserVoice> selectSUserVoiceList(UserVoice userVoice)
    {
        return userVoiceMapper.selectUserVoiceList(userVoice);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param userVoice 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertSUserVoice(UserVoice userVoice)
    {
        userVoice.setCreateTime(DateUtils.getNowDate());
        return userVoiceMapper.insertUserVoice(userVoice);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param userVoice 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateSUserVoice(UserVoice userVoice)
    {
        userVoice.setUpdateTime(DateUtils.getNowDate());
        return userVoiceMapper.updateUserVoice(userVoice);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteSUserVoiceByIds(Long[] ids)
    {
        return userVoiceMapper.deleteUserVoiceByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteSUserVoiceById(Long id)
    {
        return userVoiceMapper.deleteUserVoiceById(id);
    }

    /**
     * 保存用户发言人
     *
     * @param userVoice
     * @return
     */
    @Override
    public int saveUserVoice(UserVoice userVoice) {
        UserVoice user = userVoiceMapper.selectUserVoiceById(SecurityUtils.getUserId());
        UserVoice userV = new UserVoice();
        // 判断是否已经存在
        if(Objects.isNull(user)){
            userV.setUserId(SecurityUtils.getUserId());
            // 音调
            userV.setVoicePitch(userVoice.getVoicePitch());
            // 语速
            userV.setVoiceSpeed(userVoice.getVoiceSpeed());
            // 音量
            userV.setVoiceVolume(userVoice.getVoiceVolume());
            // 全局播放
            userV.setUserPlayflag(userVoice.getUserPlayflag());
            // 百度
            userV.setBdVoiceRoleId(userVoice.getBdVoiceRoleId());
            // 讯飞
            userV.setXfVoiceRoleId(userVoice.getXfVoiceRoleId());
            // 豆包
            userV.setDouBaoVoiceRoleId(userVoice.getDouBaoVoiceRoleId());
            userV.setCreateBy(SecurityUtils.getUsername());
            userV.setCreateTime(DateUtils.getNowDate());
            return userVoiceMapper.insertUserVoice(userV);
        }else {
            // 音调
            userV.setVoicePitch(userVoice.getVoicePitch());
            // 语速
            userV.setVoiceSpeed(userVoice.getVoiceSpeed());
            // 音量
            userV.setVoiceVolume(userVoice.getVoiceVolume());
            // 全局播放
            userV.setUserPlayflag(userVoice.getUserPlayflag());
            // baidu
            userV.setBdVoiceRoleId(userVoice.getBdVoiceRoleId());
            // xunfei
            userV.setXfVoiceRoleId(userVoice.getXfVoiceRoleId());
            // 豆包
            userV.setDouBaoVoiceRoleId(userVoice.getDouBaoVoiceRoleId());
            userV.setUserId(SecurityUtils.getUserId());
            userV.setUpdateBy(SecurityUtils.getUsername());
            userV.setUpdateTime(DateUtils.getNowDate());
            return userVoiceMapper.updateUserVoice(userV);
        }
    }

    @Override
    public String getSystemTTSChoose() {
        // 获取系统当前语音选择的值
        List<SysDictData> userVoiceChoose = remoteDictTypeService.dictTypeGetInfo("user_voice_choose", SecurityConstants.INNER);
        SysDictData sysDictData = userVoiceChoose.get(0);
        return sysDictData.getDictValue();
    }

    @Override
    public List<UserVoiceVo> list() {
        List<UserVoiceVo> list = new ArrayList<>();
        UserVoiceVo item1 = new UserVoiceVo();
        item1.setName("小美");
        item1.setValue(0);
        list.add(item1);
        UserVoiceVo item2 = new UserVoiceVo();
        item2.setName("丫丫");
        item2.setValue(4);
        list.add(item2);
        UserVoiceVo item3 = new UserVoiceVo();
        item3.setName("小宇");
        item3.setValue(1);
        list.add(item3);
        UserVoiceVo item4 = new UserVoiceVo();
        item4.setName("逍遥");
        item4.setValue(3);
        list.add(item4);

        return list;

    }
}
