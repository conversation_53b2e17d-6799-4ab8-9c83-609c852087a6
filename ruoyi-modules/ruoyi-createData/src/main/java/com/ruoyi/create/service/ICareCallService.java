package com.ruoyi.create.service;

import java.util.List;
import com.ruoyi.create.domain.CareCall;

/**
 * 服务调用统计Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
public interface ICareCallService 
{
    /**
     * 查询服务调用统计
     * 
     * @param id 服务调用统计主键
     * @return 服务调用统计
     */
    public CareCall selectCareCallById(Long id);

    /**
     * 查询服务调用统计列表
     * 
     * @param careCall 服务调用统计
     * @return 服务调用统计集合
     */
    public List<CareCall> selectCareCallList(CareCall careCall);

    /**
     * 新增服务调用统计
     * 
     * @param careCall 服务调用统计
     * @return 结果
     */
    public int insertCareCall(CareCall careCall);

    /**
     * 修改服务调用统计
     * 
     * @param careCall 服务调用统计
     * @return 结果
     */
    public int updateCareCall(CareCall careCall);

    /**
     * 批量删除服务调用统计
     * 
     * @param ids 需要删除的服务调用统计主键集合
     * @return 结果
     */
    public int deleteCareCallByIds(Long[] ids);

    /**
     * 删除服务调用统计信息
     * 
     * @param id 服务调用统计主键
     * @return 结果
     */
    public int deleteCareCallById(Long id);
}
