package com.ruoyi.create.service.impl;

import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.baidubce.appbuilder.base.exception.AppBuilderServerException;
import com.baidubce.appbuilder.console.appbuilderclient.AppBuilderClient;
import com.baidubce.appbuilder.model.appbuilderclient.AppBuilderClientIterator;
import com.baidubce.appbuilder.model.appbuilderclient.AppBuilderClientResult;
import com.baidubce.appbuilder.model.appbuilderclient.Event;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.baidu.api.BaiduApiService;
import com.ruoyi.baidu.api.dto.BaiduDto;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.config.CustomThreadPool;
import com.ruoyi.create.domain.*;
import com.ruoyi.create.dto.UserDto;
import com.ruoyi.create.event.Tevent;
import com.ruoyi.create.exception.CustomException;
import com.ruoyi.create.service.*;
import com.ruoyi.create.utils.Snowflake;
import com.ruoyi.system.api.ConfigService;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.domain.FileVo;
import com.ruoyi.system.api.domain.SysFile;
import com.ruoyi.system.api.domain.SysFileInfo;
import com.ruoyi.system.api.domain.TeventVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.poi.xslf.usermodel.XMLSlideShow;
import org.apache.poi.xslf.usermodel.XSLFSheet;
import org.apache.xmlbeans.XmlCursor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.PresentationMapper;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.namespace.QName;
import org.mozilla.universalchardet.UniversalDetector;

/**
 * 老师课程Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
@Slf4j
@Service
public class PresentationServiceImpl implements IPresentationService {

    //返回url地址 前缀
    private final static String targetUrl="http://127.0.0.1:9300/statics";
    //实际存储地址前缀
    private final static String newUrl="D:/ruoyi/uploadPath";
    //ppt分割存储的前缀
    private final static String slide="D:/ruoyi/uploadPath/ppt/slide";

    private final static String sourceBasePath = "D:/ruoyi/uploadPath/";
    private final static String targetBasePath = "D:/ruoyi/uploadPath/ppt/slide/";


    //    D:/ruoyi/uploadDataPath
    @Value("${file.path.file-path-win}")
    String localFilePathWin;
    //    /home/<USER>/uploadDataPath
    @Value("${file.path.filePathlinux}")
    String localFilePathLinux;
    @Resource
    private ConfigService configService;

    @Resource
    private BaiduApiService baiduApiService;
//    //    D:/ruoyi/uploadDataPath
//    @Value("${file.demo.file-path-win}")
//    String demoFilePathWin;
//    //    /home/<USER>/uploadDataPath
//    @Value("${file.demo.filePathlinux}")
//    String demoFilePathLinux;

    @Value("${file.temp}")
    String temp;

    @Resource
    private PresentationMapper presentationMapper;

    //文件上传
    @Resource
    private RemoteFileService remoteFileService;
    @Resource
    private IPresentationPathService presentationPathService;

    @Resource
    private ApplicationEventPublisher eventPublisher;

//    @Resource
//    private ISysFileInfoService sysFileInfoService;


    @Resource
    private IConsonantService consonantService;

    @Resource
    private ISpeechdraftProblemService speechdraftProblemService;
    @Resource
    private CustomThreadPool customThreadPool;

    /**
     * 查询老师课程
     *
     * @param id 老师课程主键
     * @return 老师课程
     */
    @Override
    public Presentation selectPresentationById(Long id)
    {
        return presentationMapper.selectPresentationById(id);
    }

    /**
     * 查询老师课程列表
     *
     * @param presentation 老师课程
     * @return 老师课程
     */
    @Override
    public List<Presentation> selectPresentationList(Presentation presentation) {
        return presentationMapper.selectPresentationList(presentation);
    }
    @Override
    public List<Presentation> selectPresentationListGroupByCourse(Presentation presentation){
        //如果是不是管理员只能查询对应学校的
        if (!SecurityUtils.isAdmin(SecurityUtils.getUserId())){
            Long universityId = SecurityUtils.getLoginUser().getSysUser().getUniversityId();
            presentation.setUniversityId(universityId);
        }
        return presentationMapper.selectPresentationListGroupByCourse(presentation);
    }

    /**
     * 新增老师课程
     *
     * @param presentation 老师课程
     * @return 结果
     */
    @Override
    public int insertPresentation(Presentation presentation) throws IOException, AppBuilderServerException {
        //雪花算法生成id给文件绑上busiid
        Snowflake snowflake = new Snowflake(1, 1);
        long id = snowflake.generateId();
        if(ObjectUtils.isNotEmpty(presentation.getPresentationFileId())){
            String fileIdStr = presentation.getPresentationFileId();
            Long[] fileIds = new Long[]{ Long.valueOf(fileIdStr.trim()) };
            remoteFileService.relationFile(fileIds, String.valueOf(id));
        }
        long id2 = snowflake.generateId();
        if(ObjectUtils.isNotEmpty(presentation.getSpeechdraftFileId())){
            String fileIdStr = presentation.getSpeechdraftFileId();
            Long[] fileIds = new Long[]{ Long.valueOf(fileIdStr.trim()) };
            remoteFileService.relationFile(fileIds, String.valueOf(id2));
        }
        presentation.setCreateTime(DateUtils.getNowDate());



        // 复制临时目录文件到目标目录  删除临时目录

        //相对路径
        String presentationPathHttp = presentation.getPresentationHttp();
        //            /createdPPtSlide/pptx/0/20240819/1724046483830129.pptx
        //String presentationTempPath="/temp"+presentationPath.substring(presentationPath.indexOf("/", presentationPath.indexOf("/") + 1));
//        临时文件路径
        String presentationTempPath=getTempPathByPathHttp(presentationPathHttp);
        //文件路径
        String presentationPath=presentation.getPresentationPath();

        //文件路径
        String speechdraftpath = presentation.getSpeechdraftpath();
        //相对路径
        String speechdraftpathHttp=getPathChangeHttp(speechdraftpath);
        //临时文件路径
        String speechdraftpathTemp=getTempPathByPathHttp(speechdraftpathHttp);

//        String os = System.getProperty("os.name").toLowerCase();
//        if (os.contains("win")) {
//            speechdraftpathHttp=speechdraftpath.replace(localFilePathWin,"");
//        } else if (os.contains("nix") || os.contains("nux") || os.contains("mac")) {
//            speechdraftpathHttp=speechdraftpath.replace(localFilePathLinux,"");
//        } else {
//            throw new UnsupportedOperationException("Unsupported operating system: " + os);
//        }


//        try {
//            int index2Num = presentationPathHttp.indexOf("/", presentationPathHttp.indexOf("/") + 1);
//            int index2Nums = speechdraftpathHttp.indexOf("/", speechdraftpathHttp.indexOf("/") + 1);
//            if (index2Num == -1) {
//                throw new IllegalArgumentException("Invalid path format: "+presentationPathHttp);
//            }
//            if (index2Nums == -1) {
//                throw new IllegalArgumentException("Invalid path format: "+speechdraftpathHttp);
//            }
//
//            String os = System.getProperty("os.name").toLowerCase();
//            if (os.contains("win")) {
//                presentationTempPath =localFilePathWin+"/temp"+presentationPathHttp.substring(index2Num);
//                presentationPath=localFilePathWin+presentationPathHttp;
//
//                speechdraftpathTemp=localFilePathWin+"/temp"+speechdraftpathHttp.substring(index2Nums);
//
//            } else if (os.contains("nix") || os.contains("nux") || os.contains("mac")) {
//                presentationTempPath =localFilePathLinux+"/temp"+presentationPathHttp.substring(index2Num);
//                presentationPath=localFilePathWin+presentationPathHttp;
//
//                speechdraftpathTemp=localFilePathLinux+"/temp"+speechdraftpathHttp.substring(index2Nums);
//
//            } else {
//                throw new UnsupportedOperationException("Unsupported operating system: " + os);
//            }
//
//            // 后续操作
//        } catch (StringIndexOutOfBoundsException e) {
//            // 捕获数组越界异常
//            log.error("Error processing the path: " + presentationPathHttp, e);
//            throw new CustomException("路径处理时发生异常", e);
//        } catch (IllegalArgumentException e) {
//            // 捕获非法路径格式异常
//            log.error(e.getMessage(), e);
//            throw new CustomException("无效的路径格式", e);
//        }


        //文件名字
        String presentationFileName=getFileName(presentationPath);
        //文件名字
        String speechdraftName=getFileName(speechdraftpath);

        // sourcepathOut.substring(0,sourcepathOut.lastIndexOf("/"))+"/"+presentationId;

        //最终目录
        String outputPath=getOutputPath(presentationPath, String.valueOf(presentation.getPresentationId()));


//
//        try {
//            int lastSlashIndex = presentationPath.lastIndexOf("/");
//            int speechdraftLastIndex = speechdraftpath.lastIndexOf("/");
//            if (lastSlashIndex == -1) {
//                throw new IllegalArgumentException("Invalid path format: " + presentationPath);
//            }
//            presentationFileName=presentationPath.substring(lastSlashIndex);
//            speechdraftName=speechdraftpath.substring(speechdraftLastIndex);
//            outputPath = presentationPath.substring(0, lastSlashIndex) + "/" + presentation.getPresentationId();
//            // 后续操作
//        } catch (StringIndexOutOfBoundsException e) {
//            // 捕获数组越界异常
//            log.error("Error processing the path: " + presentationPath, e);
//            throw new CustomException("路径处理时发生异常", e);
//        } catch (IllegalArgumentException e) {
//            // 捕获非法路径格式异常
//            log.error(e.getMessage(), e);
//            throw new CustomException("无效的路径格式", e);
//        }


        //
        presentationPath=outputPath+presentationFileName;
        //
        String speechdraftPath = outputPath + speechdraftName;


        //文件从presentationTempPath  -》 presentationPath
//        Path tempPath = Paths.get(presentationTempPath);
//        Path destinationPath = Paths.get(presentationPath);

        moveFile(presentationTempPath,presentationPath);
//        Path speechdraftTempPath = Paths.get(speechdraftpathTemp);
//        Path speechdraftDestinationPath = Paths.get(speechdraftPath);
        System.out.println("================"+speechdraftpathTemp+speechdraftPath);
        moveFile(speechdraftpathTemp,speechdraftPath);
        //转码为utf-8
        convertToUtf8(Paths.get(speechdraftPath));

//        try {
//            // 创建目标目录（如果不存在）
//            Files.createDirectories(destinationPath.getParent());
//            //
//            Files.createDirectories(speechdraftDestinationPath.getParent());
//            // 移动文件
//            Files.move(tempPath, destinationPath, StandardCopyOption.REPLACE_EXISTING);
//            //
//            Files.move(speechdraftTempPath, speechdraftDestinationPath, StandardCopyOption.REPLACE_EXISTING);
//            System.out.println("文件已成功移动到: " + presentationPath);
//            // 确保源文件已删除（通常文件移动后源文件已被删除，但此步骤是双重检查）
//            if (Files.exists(tempPath)) {
//                Files.delete(tempPath);
//                System.out.println("源文件已成功删除: " + presentationTempPath);
//            }
//            //
//            if (Files.exists(speechdraftTempPath)) {
//                Files.delete(speechdraftTempPath);
//                System.out.println("源文件已成功删除: " + speechdraftTempPath);
//            }
//        } catch (IOException e) {
//            System.err.println("移动文件时发生错误: " + e.getMessage());
//            e.printStackTrace();
//            throw new CustomException("移动文件时发生错误");
//        }


        //分割  处理

        //分割ppt耗时间  事件通知
        //eventPublisher.publishEvent(new Tevent(this,presentationPath,outputPath,"slide"));
        remoteFileService.publishEvent(TeventVo.builder().sourcePath(presentationPath).outputPath(outputPath).slidePrefix("slide").build());


        presentation.setPresentationHttp(getPathChangeHttp(presentationPath));
        presentation.setPresentationPath(presentationPath);
        presentation.setSpeechdraftpath(speechdraftPath);
        int rows = presentationMapper.insertPresentation(presentation);

        // 异步自定义线程池执行任务 检测视频页数
        Long generatedId = presentation.getId(); // 获取自动生成的id
        String path = presentation.getPresentationPath(); // 课件未拆分的路径
        customThreadPool.submitTask(() -> {
            checkAndRecordVideoPageIndexes(path, generatedId);
        });


        if (rows > 0){
            //文件上传成功
            String[] file = getFile(speechdraftPath);
            //TODO  对页拆分每一句  [。？！]  字符超过60

//            for (int i = 0; i < file.length; i++) {
//
//                String pageStr=file[i];
//                //获取每一页题目
//                SpeechdraftProblem speechdraftProblem = parseQuestion(file[i]);
//                if (!(speechdraftProblem.getProblem()==null) && !(speechdraftProblem.getProblem()=="") ){
//                    speechdraftProblem.setPresentationId(presentation.getPresentationId());
//                    speechdraftProblem.setIndex(Long.valueOf(i+1));
//                    String anwers = speechdraftProblem.getAnwers();
//                    if (anwers.contains("、")){
//                        speechdraftProblem.setRemark("1");
//                    }
//                    speechdraftProblemService.insertSpeechdraftProblem(speechdraftProblem);
//                }
//
//
//                QuestionRegex questionRegex = getQuestionRegex(pageStr);
//
//                pageStr=questionRegex.getTextWithoutQuestion();
//                pageStr=getNoStr(pageStr);
//                //System.out.println(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>"+pageStr);
//                List<PlatParam> platParamList=new ArrayList<>();
//                List<Consonant> consonantList=new ArrayList<>();
//                // 拆分每一页的文本段，按句号、分号和感叹号拆分
//                String[] sentences = pageStr.split("[。！；？]");
//                int buIndex=0;
//                int m=1;
//                for (int j = 0; j < sentences.length; j++) {
//                    PlatParam platParam = new PlatParam();
//                    platParam.setIndexPage(i + 1);  // 设置页码
//                    platParam.setPresentationFrom(String.valueOf(presentation.getPresentationId())); // 设置演讲稿来源
//
//                    String trimmedSentence = sentences[j].trim();
//                    //System.out.println("+++++++++++++++++++++++++++++++++++++ :"+trimmedSentence.length());
//                    // 如果句子长度超过60字，按逗号拆分
//
//                    if (trimmedSentence.length() > 60) {
//                        String[] parts = trimmedSentence.split("[,]");
//
//                        buIndex+=parts.length-1;
//                        for (int t = 0; t < parts.length; t++) {
//                            String trimmedPart = parts[t].trim();
//
//
//                            //System.out.println("-=-=-=-=-=---=-=-=-=-=-=-=-=-=-=-=-=-=-=-- :"+trimmedPart.length());
//                            while (trimmedPart.length() > 60) {
//                                StringBuilder subContent = new StringBuilder();
//                                subContent.append(trimmedPart, 0, 60);
//                                trimmedPart = trimmedPart.substring(60);
//
//                                PlatParam platParam3 = new PlatParam();
//                                platParam3.setIndexPage(i + 1);  // 设置页码
//                                platParam3.setPresentationFrom(String.valueOf(presentation.getPresentationId())); // 设置演讲稿来源
//                                ActionRegex actionRegex = getActionRegex(String.valueOf(subContent));
//                                platParam3.setMotion(actionRegex.getActionText());
//
//                                platParam3.setTxtSentence(String.valueOf(actionRegex.getTextWithoutAction())); // 设置文本
//                                platParam3.setBeforeAnalysisContent(actionRegex.getTextWithoutAction().trim());
//                                platParam3.setIndexSentence(j+t+m);
//                                m++;
//                                //System.out.println("{3}");
//                                //System.out.println(platParam3);
//                                //System.out.println("{3}");
//
////                                PlatParam platParamTo3 = getPlatParam(platParam3);
////                                getConsonant(platParamTo3);
//                                PlatParam platParamTo3 = consonantService.getPlatParam(platParam3);
//
//                                platParamList.add(platParamTo3);
//                                Consonant consonantSave3 = consonantService.getConsonant(platParamTo3);
//                                consonantList.add(consonantSave3);
////                               int consonantRows3 = consonantService.insertConsonant(consonantService.getConsonant(platParamTo3));
////
////                                if (consonantRows3 <= 0){
////                                    throw new CustomException("讲演稿声母韵母插入失败");
////                                }
//                            }
//
//
//                            PlatParam platParam2 = new PlatParam();
//                            platParam2.setIndexPage(i + 1);  // 设置页码
//                            platParam2.setPresentationFrom(String.valueOf(presentation.getPresentationId())); // 设置演讲稿来源
//                            ActionRegex actionRegex = getActionRegex(String.valueOf(trimmedPart));
//                            platParam2.setMotion(actionRegex.getActionText());
//
//                            platParam2.setTxtSentence(String.valueOf(actionRegex.getTextWithoutAction())); // 设置文本
//                            platParam2.setBeforeAnalysisContent(actionRegex.getTextWithoutAction().trim());
//                            platParam2.setIndexSentence(j+t+1+m-1);
//                            //System.out.println("{2}");
//                           // System.out.println(platParam2);
//                            //System.out.println("{2}");
//
//
////                            PlatParam platParamTo2 = getPlatParam(platParam2);
////                            getConsonant(platParamTo2);
//                            PlatParam platParamTo2 = consonantService.getPlatParam(platParam2);
//
//                            platParamList.add(platParamTo2);
//                            Consonant consonantSave2 = consonantService.getConsonant(platParamTo2);
//                            consonantList.add(consonantSave2);
////                            int consonantRows2 = consonantService.insertConsonant(consonantService.getConsonant(platParamTo2));
////
////                            if (consonantRows2 <= 0){
////                                throw new CustomException("讲演稿声母韵母插入失败");
////                            }
//
//                        }
//
//
//                    } else {
//                        ActionRegex actionRegex = getActionRegex(String.valueOf(trimmedSentence));
//                        platParam.setMotion(actionRegex.getActionText());
//
//                        platParam.setTxtSentence(String.valueOf(actionRegex.getTextWithoutAction())); // 设置文本
//                        platParam.setBeforeAnalysisContent(actionRegex.getTextWithoutAction().trim());
//                        System.out.println("buIndex"+buIndex);
//                        platParam.setIndexSentence(j + 1+buIndex+m-1);
//                        //System.out.println("{1}");
//                        //System.out.println(platParam);
//                        //System.out.println("{1}");
//
//
//
////                        PlatParam platParamTo = getPlatParam(platParam);
////                        getConsonant(platParamTo);
//                        PlatParam platParamTo = consonantService.getPlatParam(platParam);
//                        platParamList.add(platParamTo);
//                        Consonant consonantSave = consonantService.getConsonant(platParamTo);
//                        consonantList.add(consonantSave);
////                        int consonantRows = consonantService.insertConsonant(consonantService.getConsonant(platParamTo));
////
////                        if (consonantRows <= 0){
////                            throw new CustomException("讲演稿声母韵母插入失败");
////                        }
//                    }
//
//
//                }
//
//                int pt=0;
//                for (PlatParam p: platParamList) {
//                    System.out.println("=============="+i+"页+"+pt+"句话开始=============================");
//                    System.out.println(p);
//                    System.out.println("=============="+i+"页结束=============================");
//                }
//                Gson gson = new Gson();
//                String json = gson.toJson(platParamList);
//                String jsonC = gson.toJson(consonantList);
//
//                System.out.println("=============="+i+"页=============================");
//                System.out.println(json);
//                int consonantRows = consonantService.insertConsonant(Consonant.builder()
//                        .consonant(jsonC).indexPage((i+1)+"json").speechdraftId(String.valueOf(presentation.getPresentationId()))
//                        .build());
//
//                if (consonantRows <= 0){
//                    throw new CustomException("讲演稿声母韵母插入失败");
//                }
//                System.out.println("=============="+i+"页结束=============================");
//            }

            List<List<Consonant>> consonantList = getConsonantList(speechdraftPath, presentation.getPresentationId());
            for (int i = 0; i < consonantList.size(); i++){
                Gson gson = new Gson();
                String jsonC = gson.toJson(consonantList.get(i));
                int consonantRows = consonantService.insertConsonant(Consonant.builder()
                        .consonant(jsonC).indexPage((i+1)+"json").speechdraftId(String.valueOf(presentation.getPresentationId()))
                        .build());
                if (consonantRows <= 0){
                    throw new CustomException("讲演稿声母韵母插入失败");
                }
            }
        }


        return rows;
    }

    /**
     * Description: 记录ppt中的视频页面索引并更新数据库
     * <AUTHOR>
     * @since 下午6:21 2025/1/6
     * @param pptFilePath  检查并记录视频页索引
     * @param id id
     **/
    private void checkAndRecordVideoPageIndexes(String pptFilePath, Long id) {
        log.info("开始记录ppt中的视频页面索引并更新数据库");
        try (FileInputStream fis = new FileInputStream(pptFilePath)) {
            XMLSlideShow pptx = new XMLSlideShow(fis);
            String[] blipNS = {
                    "http://schemas.openxmlformats.org/drawingml/2006/main",
                    "http://schemas.openxmlformats.org/presentationml/2006/main"
            };

            int slideIndex = 1;  // 幻灯片索引
            Set<String> pageWithVideoIndexStrs = new HashSet<>();
            // 遍历每一张幻灯片
            for (XSLFSheet slide : pptx.getSlides()) {
                // 遍历命名空间
                for (String ns : blipNS) {
                    try {
                        XmlCursor picCur = slide.getXmlObject().newCursor();
                        picCur.selectPath("declare namespace p='" + ns + "' .//p:videoFile");

                        while (picCur.toNextSelection()) {
                            QName relName = new QName("http://schemas.openxmlformats.org/officeDocument/2006/relationships", "link");
                            String relId = picCur.getAttributeText(relName); // 获取嵌入资源ID
                            if (ObjectUtils.isNotEmpty(relId)) {
                                pageWithVideoIndexStrs.add(String.valueOf(slideIndex));
                                break;
                            }
                        }
                    } catch (Exception e) {
                        log.error("处理PPT文件时发生错误：", e);
                    }
                }
                slideIndex++;
            }
            // 集合转字符串
            String pageWithVideoIndexStr = StringUtils.join(pageWithVideoIndexStrs, ",");
            if (StringUtils.isNotBlank(pageWithVideoIndexStr)) {
                // 更新数据库信息
                Presentation presentation = new Presentation();
                presentation.setId(id);
                presentation.setVideoPageIndexes(pageWithVideoIndexStr);
                int i = presentationMapper.updatePresentationByPresentationId(presentation);
                if (i < 0) {
                    log.error("更新PPT信息失败");
                }
            }
            log.info("处理结束----带有视频的页面索引 --{}", pageWithVideoIndexStr);


        } catch (Exception e) {
            log.error("处理PPT文件时发生错误：", e);
        }


    }


    /**
     * 去除自己 定义要去除的符号后的文本
     * @param text
     * @return
     */
    public String getNoStr(String text){
        // 定义要去除的符号
        String cleanedText = text.replaceAll("[“”‘、：《》-]", "");
        cleanedText = cleanedText.replaceAll("@ask", "");
        // 输出结果
        //System.out.println("去除符号后的文本:\n" + cleanedText);
        return cleanedText;
    }

    /**
     * 获取 去除题目后的文本  源文本内容  仅题目
     * @param text
     * @return
     */
    public QuestionRegex getQuestionRegex(String text){
        // 正则匹配 @question:random_start 和 @question:random_end 标签中的内容
        String questionRegex = "@question:random_start(.*?)@question:random_end";

        Pattern pattern = Pattern.compile(questionRegex, Pattern.DOTALL);
        Matcher matcher = pattern.matcher(text);

//        // 定义要去除的符号
//        String cleanedText = text.replaceAll("[“”‘、\"\']", "");
//        // 输出结果
//        System.out.println("去除符号后的文本:\n" + cleanedText);


        String sourceText = text; // 源文本内容
        String questionText = ""; // 仅题目
        String textWithoutQuestion = text; // 去除题目后的文本

        // 查找匹配并提取题目内容
        if (matcher.find()) {
            questionText = matcher.group(1).trim(); // 获取题目内容
            textWithoutQuestion = text.replace(matcher.group(0), "").trim(); // 去除题目后的文本
        }

        // 输出结果
//        System.out.println("源文本内容:\n" + sourceText);
//        System.out.println("\n去除题目的文本:\n" + textWithoutQuestion);
//        System.out.println("\n仅包含题目的文本:\n" + questionText);

        return QuestionRegex.builder()
                .questionText(questionText)
                .sourceText(sourceText)
                .textWithoutQuestion(textWithoutQuestion)
                .build();
    }

    /**
     * 获取 去除动作标签后的文本  源文本内容  仅动作标签
     * @param text
     * @return
     */
    public ActionRegex getActionRegex(String text){
        String actionRegex = "@action:\\w+";
        Pattern pattern = Pattern.compile(actionRegex);
        Matcher matcher = pattern.matcher(text);

        StringBuilder actionTextBuilder = new StringBuilder(); // 用于保存所有标签
        List<String> tags = new ArrayList<>(); // 用于存储单个标签
        String textWithoutAction = text; // 初始化去除标签的文本

        // 查找并提取所有标签
        while (matcher.find()) {
            String action = matcher.group(0).trim();
            tags.add(action); // 添加到标签列表中
            actionTextBuilder.append(action).append(","); // 拼接标签并加上逗号
            // 去除标签后的文本
            textWithoutAction = textWithoutAction.replace(action, "").trim();
        }

        // 去除最后一个多余的逗号
        String actionText = actionTextBuilder.length() > 0
                ? actionTextBuilder.substring(0, actionTextBuilder.length() - 1)
                : "";

        // 输出结果
//        System.out.println("源文本内容:\n" + text);
//        System.out.println("\n动作标签 (用逗号分隔):\n" + actionText);
//        System.out.println("\n去除标签后的文本:\n" + textWithoutAction);
        return ActionRegex.builder()
                .actionText(actionText)
                .actionSourceText(text)
                .textWithoutAction(textWithoutAction)
                .build();
    }



    public static String[] getFile2(String path){
        // 文件路径
        String filePath = path;
        String[] split =null;
        try {
            // 读取文件内容，并去掉空格和空白行
            String content = Files.lines(Paths.get(filePath))
                    .map(String::trim) // 去掉每行的前后空白
                    .filter(line -> !line.isEmpty()) // 过滤掉空白行
                    .collect(Collectors.joining()); // 合并为一个字符串

            // 输出处理后的内容

            // 输出内容
            split = content.split("@page");

//            int i=1;
//            for (String s:split) {
//                System.out.println("第" + i + "页内容：");
//                System.out.println(s);
//                i++;
//            }

        } catch (IOException e) {
            e.printStackTrace();
        }
        return split;
    }

    public String[] getFile(String path){
        return remoteFileService.getFile(path);
    }

    public void moveFile2(String oldPath,String newPath){

        //文件从presentationTempPath  -》 presentationPath
        Path tempPath = Paths.get(oldPath);
        Path destinationPath = Paths.get(newPath);

        if (!new File(oldPath).exists()){
            return;
        }

        try {
            // 创建目标目录（如果不存在）
            Files.createDirectories(destinationPath.getParent());
            // 移动文件
            Files.move(tempPath, destinationPath, StandardCopyOption.REPLACE_EXISTING);
            System.out.println("文件已成功移动到: " + newPath);
            // 确保源文件已删除（通常文件移动后源文件已被删除，但此步骤是双重检查）
            if (Files.exists(tempPath)) {
                Files.delete(tempPath);
                System.out.println("源文件已成功删除: " + oldPath);
            }
        } catch (IOException e) {
            System.err.println("移动文件时发生错误: " + e.getMessage());
            e.printStackTrace();
            throw new CustomException("移动文件时发生错误");
        }


    }
    public void moveFile(String oldPath,String newPath){
        int i = remoteFileService.moveFile(TeventVo.builder().sourcePath(oldPath).outputPath(newPath).build());
        if ( i == 1){
            System.out.println("");
        }else {
            System.out.println("");
        }
    }


    //根据文件存储路径 获取相对路径
    public String getPathChangeHttp(String path){
        String presentationPathHttp="";
        String os = System.getProperty("os.name").toLowerCase();
        if (os.contains("win")) {
            presentationPathHttp=path.replace(localFilePathWin,"");
        } else if (os.contains("nix") || os.contains("nux") || os.contains("mac")) {
            presentationPathHttp=path.replace(localFilePathLinux,"");
        } else {
            throw new UnsupportedOperationException("Unsupported operating system: " + os);
        }
        return presentationPathHttp;
    }

    //根据相对路径获取 文件上传的临时目录
    public String getTempPathByPathHttp(String pathHttp){

        String presentationTempPath="";
        String os = System.getProperty("os.name").toLowerCase();
        try {
            int index2Num = pathHttp.indexOf("/", pathHttp.indexOf("/") + 1);

            if (index2Num == -1) {
                throw new IllegalArgumentException("Invalid path format: "+pathHttp);
            }
            if (os.contains("win")) {
                presentationTempPath =localFilePathWin+"/"+temp+pathHttp.substring(index2Num);
                // presentationTempPath =localFilePathWin+"/temp"+pathHttp.substring(index2Num);
            } else if (os.contains("nix") || os.contains("nux") || os.contains("mac")) {
                presentationTempPath =localFilePathLinux+"/"+temp+pathHttp.substring(index2Num);
                //presentationTempPath =localFilePathLinux+"/temp"+pathHttp.substring(index2Num);
            } else {
                throw new UnsupportedOperationException("Unsupported operating system: " + os);
            }
            // 后续操作
        } catch (StringIndexOutOfBoundsException e) {
            // 捕获数组越界异常
            log.error("Error processing the path: " + pathHttp, e);
            throw new CustomException("路径处理时发生异常", e);
        } catch (IllegalArgumentException e) {
            // 捕获非法路径格式异常
            log.error(e.getMessage(), e);
            throw new CustomException("无效的路径格式", e);
        }

        return presentationTempPath;
    }

    //根据文件路径 获取文件名
    public String getFileName(String path){
        String fileName="";
        try {
            int lastSlashIndex = path.lastIndexOf("/");
            if (lastSlashIndex == -1) {
                throw new IllegalArgumentException("Invalid path format: " + path);
            }
            fileName=path.substring(lastSlashIndex);
            // 后续操作
        } catch (StringIndexOutOfBoundsException e) {
            // 捕获数组越界异常
            log.error("Error processing the path: " + path, e);
            throw new CustomException("路径处理时发生异常", e);
        } catch (IllegalArgumentException e) {
            // 捕获非法路径格式异常
            log.error(e.getMessage(), e);
            throw new CustomException("无效的路径格式", e);
        }
        return fileName;
    }

    //根据文件存储路径 获取最终目录路径
    public String getOutputPath(String path,String presentationId){
        String outputPath="";
        try {
            int lastSlashIndex = path.lastIndexOf("/");
            if (lastSlashIndex == -1) {
                throw new IllegalArgumentException("Invalid path format: " + path);
            }
            outputPath = path.substring(0, lastSlashIndex) + "/" + presentationId;
            // 后续操作
        } catch (StringIndexOutOfBoundsException e) {
            // 捕获数组越界异常
            log.error("Error processing the path: " + path, e);
            throw new CustomException("路径处理时发生异常", e);
        } catch (IllegalArgumentException e) {
            // 捕获非法路径格式异常
            log.error(e.getMessage(), e);
            throw new CustomException("无效的路径格式", e);
        }
        return outputPath;
    }



    /**
     * 修改老师课程
     *
     * @param presentation 老师课程
     * @return 结果
     */
    @Override
    public int updatePresentation(Presentation presentation) throws IOException, AppBuilderServerException {



        presentation.setUpdateTime(DateUtils.getNowDate());
        Long presentationId = presentationMapper.selectPresentationById(presentation.getId()).getPresentationId();
        Long oldPresentationId = presentationId;
        Long presentationId1 = presentation.getPresentationId();
        String presentationPath1 = presentationMapper.selectPresentationById(presentation.getId()).getPresentationPath();
        String oldSpeechdraftpath = presentationMapper.selectPresentationById(presentation.getId()).getSpeechdraftpath();
        String outputPath = null;
        if (presentation.getPresentationId() != null) {
            Long presentationIdOld = presentationId;
            String presentationPathOld = presentationPath1;
            presentationId = presentation.getPresentationId();
            outputPath = "";
            if (!presentationIdOld.equals(presentationId)) {
                //   新的课件id  删除原来的文件夹

                //删除原有的声母韵母
                //consonantService.deleteConsonantByPresationId(presentationIdOld);
                //删除原有的课件题
                //speechdraftProblemService.deleteSpeechdraftProblemBypresentationId(presentationIdOld);

                try {
                    int lastSlashIndex = presentationPathOld.lastIndexOf("/");
                    if (lastSlashIndex == -1) {
                        throw new IllegalArgumentException("Invalid path format: " + presentationPathOld);
                    }
                    outputPath = presentationPathOld.substring(0, lastSlashIndex);
                    // 后续操作
                } catch (StringIndexOutOfBoundsException e) {
                    // 捕获数组越界异常
                    log.error("Error processing the path: " + presentationPathOld, e);
                    throw new CustomException("路径处理时发生异常", e);
                } catch (IllegalArgumentException e) {
                    // 捕获非法路径格式异常
                    log.error(e.getMessage(), e);
                    throw new CustomException("无效的路径格式", e);
                }
//                deleteDirectory(new File(outputPath));
                //deleteDirectory(outputPath);
            }


        }


        String pid = "/" + presentationId1 + "/";
        //更新ppt
        if (presentation.getPresentationPath() != null && !presentation.getPresentationPath().contains(pid)) {
            String path = presentation.getPresentationPath();

            String presentationPath = getOutputPath(path, String.valueOf(presentationId1));
            String outdir = presentationPath;
            presentationPath = presentationPath + getFileName(path);


            presentation.setPresentationHttp(getPathChangeHttp(path));

            String tempPathByPathHttp = getTempPathByPathHttp(getPathChangeHttp(path));
            presentation.setPresentationPath(presentationPath);

            moveFile(tempPathByPathHttp, presentationPath);
//            eventPublisher.publishEvent(new Tevent(this,presentationPath,outdir,"slide"));
            remoteFileService.publishEvent(TeventVo.builder().sourcePath(presentationPath).outputPath(outdir).slidePrefix("slide").build());

        }

        //jyg
        if (presentation.getSpeechdraftpath() != null && !presentation.getSpeechdraftpath().contains(oldSpeechdraftpath)) {
            String path = presentation.getSpeechdraftpath();
            String pathPpt = presentation.getPresentationPath();
            if (pathPpt.contains(pid)) {
                pathPpt = pathPpt.replace(pid, "/");
            }

            String speechdraftPath = getOutputPath(pathPpt, String.valueOf(presentationId1));
            speechdraftPath = speechdraftPath + getFileName(path);

            String tempSpeechdraftPathHttp = getTempPathByPathHttp(getPathChangeHttp(path));
            presentation.setSpeechdraftpath(speechdraftPath);

            moveFile(tempSpeechdraftPathHttp,speechdraftPath);
            //jyg转化为utf8
            convertToUtf8(Paths.get(speechdraftPath));

            //删除原有的声母韵母
            consonantService.deleteConsonantByPresationId(presentationId1);

            //删除原有的课件题
            speechdraftProblemService.deleteSpeechdraftProblemBypresentationId(presentationId1);

            String[] file = getFile(speechdraftPath);
            //TODO  对页拆分每一句  [。？！]  字符超过60

//            for (int i = 0; i < file.length; i++) {
//
//                String pageStr=file[i];
//                //获取每一页题目
//                SpeechdraftProblem speechdraftProblem = parseQuestion(file[i]);
//                if (!(speechdraftProblem.getProblem()==null) && !(speechdraftProblem.getProblem()=="") ){
//                    speechdraftProblem.setPresentationId(presentation.getPresentationId());
//                    speechdraftProblem.setIndex(Long.valueOf(i+1));
//                    String anwers = speechdraftProblem.getAnwers();
//                    if (anwers.contains("、")){
//                        speechdraftProblem.setRemark("1");
//                    }
//                    speechdraftProblemService.insertSpeechdraftProblem(speechdraftProblem);
//                }
//
//
//                QuestionRegex questionRegex = getQuestionRegex(pageStr);
//
//                pageStr=questionRegex.getTextWithoutQuestion();
//                pageStr=getNoStr(pageStr);
//                //System.out.println(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>"+pageStr);
//                List<PlatParam> platParamList=new ArrayList<>();
//                List<Consonant> consonantList=new ArrayList<>();
//                // 拆分每一页的文本段，按句号、分号和感叹号拆分
//                String[] sentences = pageStr.split("[。！；？]");
//                int buIndex=0;
//                int m=1;
//                for (int j = 0; j < sentences.length; j++) {
//                    PlatParam platParam = new PlatParam();
//                    platParam.setIndexPage(i + 1);  // 设置页码
//                    platParam.setPresentationFrom(String.valueOf(presentation.getPresentationId())); // 设置演讲稿来源
//
//                    String trimmedSentence = sentences[j].trim();
//                    //System.out.println("+++++++++++++++++++++++++++++++++++++ :"+trimmedSentence.length());
//                    // 如果句子长度超过60字，按逗号拆分
//
//                    if (trimmedSentence.length() > 60) {
//                        String[] parts = trimmedSentence.split("[,]");
//
//                        buIndex+=parts.length-1;
//                        for (int t = 0; t < parts.length; t++) {
//                            String trimmedPart = parts[t].trim();
//
//
//                            //System.out.println("-=-=-=-=-=---=-=-=-=-=-=-=-=-=-=-=-=-=-=-- :"+trimmedPart.length());
//                            while (trimmedPart.length() > 60) {
//                                StringBuilder subContent = new StringBuilder();
//                                subContent.append(trimmedPart, 0, 60);
//                                trimmedPart = trimmedPart.substring(60);
//
//                                PlatParam platParam3 = new PlatParam();
//                                platParam3.setIndexPage(i + 1);  // 设置页码
//                                platParam3.setPresentationFrom(String.valueOf(presentation.getPresentationId())); // 设置演讲稿来源
//                                ActionRegex actionRegex = getActionRegex(String.valueOf(subContent));
//                                platParam3.setMotion(actionRegex.getActionText());
//
//                                platParam3.setTxtSentence(String.valueOf(actionRegex.getTextWithoutAction())); // 设置文本
//                                platParam3.setBeforeAnalysisContent(actionRegex.getTextWithoutAction().trim());
//                                platParam3.setIndexSentence(j+t+m);
//                                m++;
//                                //System.out.println("{3}");
//                                //System.out.println(platParam3);
//                                //System.out.println("{3}");
//
////                                PlatParam platParamTo3 = getPlatParam(platParam3);
////                                getConsonant(platParamTo3);
//                                PlatParam platParamTo3 = consonantService.getPlatParam(platParam3);
//                                platParamList.add(platParamTo3);
//                                Consonant consonantSave3 = consonantService.getConsonant(platParamTo3);
//                                consonantList.add(consonantSave3);
////                                int consonantRows3 = consonantService.insertConsonant(consonantService.getConsonant(platParamTo3));
////
////                                if (consonantRows3 <= 0){
////                                    throw new CustomException("讲演稿声母韵母插入失败");
////                                }
//                            }
//
//
//                            PlatParam platParam2 = new PlatParam();
//                            platParam2.setIndexPage(i + 1);  // 设置页码
//                            platParam2.setPresentationFrom(String.valueOf(presentation.getPresentationId())); // 设置演讲稿来源
//                            ActionRegex actionRegex = getActionRegex(String.valueOf(trimmedPart));
//                            platParam2.setMotion(actionRegex.getActionText());
//
//                            platParam2.setTxtSentence(String.valueOf(actionRegex.getTextWithoutAction())); // 设置文本
//                            platParam2.setBeforeAnalysisContent(actionRegex.getTextWithoutAction().trim());
//                            platParam2.setIndexSentence(j+t+1+m-1);
//                            //System.out.println("{2}");
//                            // System.out.println(platParam2);
//                            //System.out.println("{2}");
//
//
////                            PlatParam platParamTo2 = getPlatParam(platParam2);
////                            getConsonant(platParamTo2);
//                            PlatParam platParamTo2 = consonantService.getPlatParam(platParam2);
//                            platParamList.add(platParamTo2);
//                            Consonant consonantSave2 = consonantService.getConsonant(platParamTo2);
//                            consonantList.add(consonantSave2);
////                            int consonantRows2 = consonantService.insertConsonant(consonantService.getConsonant(platParamTo2));
////
////                            if (consonantRows2 <= 0){
////                                throw new CustomException("讲演稿声母韵母插入失败");
////                            }
//
//                        }
//
//
//                    } else {
//                        ActionRegex actionRegex = getActionRegex(String.valueOf(trimmedSentence));
//                        platParam.setMotion(actionRegex.getActionText());
//
//                        platParam.setTxtSentence(String.valueOf(actionRegex.getTextWithoutAction())); // 设置文本
//                        platParam.setBeforeAnalysisContent(actionRegex.getTextWithoutAction().trim());
//                        System.out.println("buIndex"+buIndex);
//                        platParam.setIndexSentence(j + 1+buIndex+m-1);
//                        //System.out.println("{1}");
//                        //System.out.println(platParam);
//                        //System.out.println("{1}");
//
//
//
////                        PlatParam platParamTo = getPlatParam(platParam);
////                        getConsonant(platParamTo);
//                        PlatParam platParamTo = consonantService.getPlatParam(platParam);
//                        platParamList.add(platParamTo);
//                        Consonant consonantSave = consonantService.getConsonant(platParamTo);
//                        consonantList.add(consonantSave);
////                        int consonantRows = consonantService.insertConsonant(consonantService.getConsonant(platParamTo));
////
////                        if (consonantRows <= 0){
////                            throw new CustomException("讲演稿声母韵母插入失败");
////                        }
//                    }
//
//
//                }
//                int pt=0;
//                for (PlatParam p: platParamList) {
//                    System.out.println("=============="+i+"页+"+pt+"句话开始=============================");
//                    System.out.println(p);
//                    pt++;
//                    System.out.println("=============="+i+"页结束=============================");
//                }
//                Gson gson = new Gson();
//                String json = gson.toJson(platParamList);
//                String jsonC = gson.toJson(consonantList);
//
//                System.out.println("=============="+i+"页============================");
//                System.out.println(json);
//                int consonantRows = consonantService.insertConsonant(Consonant.builder()
//                        .consonant(jsonC).indexPage((i+1)+"json").speechdraftId(String.valueOf(presentation.getPresentationId()))
//                        .build());
//
//                if (consonantRows <= 0){
//                    throw new CustomException("讲演稿声母韵母插入失败");
//                }
//                System.out.println("=============="+i+"页结束=============================");
//            }

            //用的最终的speechdraftPath和页面提交的presentationId
            List<List<Consonant>> consonantList = getConsonantList(speechdraftPath, presentationId1);
            for (int i = 0; i < consonantList.size(); i++) {
                Gson gson = new Gson();
                String jsonC = gson.toJson(consonantList.get(i));
                int consonantRows = consonantService.insertConsonant(Consonant.builder()
                        .consonant(jsonC).indexPage((i + 1) + "json").speechdraftId(String.valueOf(presentation.getPresentationId()))
                        .build());
                if (consonantRows <= 0) {
                    throw new CustomException("讲演稿声母韵母插入失败");
                }
            }

        }
        //只更新ppt，讲演稿不变的情况
        if (presentation.getSpeechdraftpath() != null && presentation.getSpeechdraftpath().contains(oldSpeechdraftpath)) {

            int consonantRows = consonantService.updateSpeechdraftId(String.valueOf(oldPresentationId), String.valueOf(presentationId1));
            if (consonantRows < 0) {
                throw new CustomException("讲演稿声母韵母更新失败");
            }
            String path = presentation.getSpeechdraftpath();
            String pathPpt = presentation.getPresentationPath();
            String tempPath = pathPpt.substring(0, pathPpt.lastIndexOf("/"));

            String speechdraftPath = tempPath + getFileName(path);

            presentation.setSpeechdraftpath(speechdraftPath);
            try {
                Path sourcePath = Paths.get(oldSpeechdraftpath);
                Path targetPath = Paths.get(speechdraftPath);

                // 确保目标目录存在（创建父级目录）
                Files.createDirectories(targetPath.getParent());

                // 移动文件（如已存在则覆盖）
                Files.move(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);

                System.out.println("文件已成功从 " + oldSpeechdraftpath + " 移动到 " + speechdraftPath);
            } catch (Exception e) {
                System.err.println("文件移动失败：");
                e.printStackTrace();
            }
        }

        presentation.setPresentationHttp(getPathChangeHttp(presentation.getPresentationPath()));
        //最后删除旧文件夹
        if (outputPath != null) {
            try {
                deleteDirectory(outputPath);
            } catch (Exception e) {
                log.error("删除旧文件夹失败：" + outputPath, e);
            }
        }
        return presentationMapper.updatePresentation(presentation);
    }


    private List<List<Consonant>> getConsonantList(String speechdraftPath,Long presentationId) throws IOException, AppBuilderServerException {

        List<List<Consonant>> fileLists=new ArrayList<>();
        String[] file = getFile(speechdraftPath);
        for (int i = 0; i < file.length; i++) {
            String pageStr=file[i];
            pageStr = getProblem(pageStr, presentationId, i);

            //@pause(n)   拆  第几句话有这个标识  标识数字多少
            List<Paragraph> paragraphs = splitTextAndExtractPause(pageStr); //分割文字
            if (paragraphs.size() >= 1 && paragraphs.get(0).getPause() >=0 ){
                //根据 pause 分成页码

                List<Consonant> fileListsTwo=new ArrayList<>();
                for (Paragraph paragraph: paragraphs) {
                    List<Consonant> fileListsPo = getConsonantList(paragraph.getText(), presentationId, i);
                    // 计算当前段落的总数，并更新 sentence 累计值
                    int size = fileListsPo.size();
                    // 如果段落中有 pause 信息，设置相关属性
                    if (paragraph.getPause() != -1 && size > 0) {
                        // 确保数组索引不越界
                        Consonant lastConsonant = fileListsPo.get(size - 1);
                        lastConsonant.setHavePause(true);
                        lastConsonant.setTimeOut(paragraph.getPause());
                    }

                    // 将当前段落的 Consonant 列表内容合并到总列表中
                    fileListsTwo.addAll(fileListsPo);
                }
                int indexp=1;
                for (Consonant c:fileListsTwo) {
                    c.setIndexSentence(String.valueOf(indexp));
                    indexp++;
                }
                fileLists.add(fileListsTwo);
            }else {
                List<Consonant> fileListsTh = getConsonantList(pageStr, presentationId, i);
                fileLists.add(fileListsTh);
            }

            //把生成的这一页数据 合成

        }
        return fileLists;
    }
    private List<Paragraph> splitTextAndExtractPause(String text) {
        List<Paragraph> result = new ArrayList<>();
        String regex = "@pause\\((\\d+)\\)"; // 匹配 @pause(n)
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(regex);
        java.util.regex.Matcher matcher = pattern.matcher(text);

        int lastIndex = 0;
        while (matcher.find()) {
            // 获取段落内容
            String paragraphText = text.substring(lastIndex, matcher.start()).trim();
            if (!paragraphText.isEmpty()) {
                // 提取 pause(n) 中的数字
                int pauseValue = Integer.parseInt(matcher.group(1));
                result.add(new Paragraph(paragraphText, pauseValue));
            }
            lastIndex = matcher.end();
        }

        // 处理最后一个段落
        if (lastIndex < text.length()) {
            String lastParagraph = text.substring(lastIndex).trim();
            if (!lastParagraph.isEmpty()) {
                result.add(new Paragraph(lastParagraph, -1)); // 最后一段没有 pause，默认为 0
            }
        }

        return result;
    }


    private String getProblem(String pageStr,Long presentationId,int i) throws IOException, AppBuilderServerException {
        SpeechdraftProblem speechdraftProblem = parseQuestion(pageStr);
        if (!(speechdraftProblem.getProblem()==null) && !(speechdraftProblem.getProblem()=="") ){
            speechdraftProblem.setPresentationId(presentationId);
            speechdraftProblem.setIndex(Long.valueOf(i+1));
            String anwers = speechdraftProblem.getAnwers();
            if (anwers.contains("、")){
                speechdraftProblem.setRemark("1");
            }
            speechdraftProblemService.insertSpeechdraftProblem(speechdraftProblem);
        }

        //判断需要生成几个随机题目
        int occurrences = countOccurrences(pageStr, "@ask");
        //生成随机题目
        if (occurrences>0) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    generateRandom(occurrences,presentationId,i);
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }, Executors.newSingleThreadExecutor());

        }
        //获取每一页题目
        QuestionRegex questionRegex = getQuestionRegex(pageStr);

        pageStr=questionRegex.getTextWithoutQuestion();
        pageStr=getNoStr(pageStr);
        return pageStr;
    }
    private void generateRandom(int occurrences,Long presentationId,int i) throws IOException, AppBuilderServerException {
        //查询相关知识点
        Presentation presentation = presentationMapper.selectPresentationByPresentationId(presentationId);
        List<String> stringList = presentationMapper.selectKnowledgeByPresentation(presentation);
        if (stringList.size()>0){
            if (stringList.size()>occurrences){
                removeRandomlyUntilSize(stringList, occurrences);
            }
            String a = "根据以下知识点生成每个知识点的单选题,每个知识点生成一个,不要省略,题目和题目之间都间隔一行"+stringList.toString()+"问题答案简洁，请在问题前带上“题目：”，不要解析，让第一个选项紧跟在“选项：”后面，而其他的" +
                    "选项各自独占一行，格式如：\"问题：世界上最高的山是？\",\"选项：A.泰山,\"换" +
                    "行\"B.华山,\"换行\"C.喜马拉雅山,\"换行\"D.印度恒河,并给出答案，格式如：“答" +
                    "案：C”";

            List<Messages> messagesList = new ArrayList<>();
            Messages messages = new Messages();
            messages.setRole("user");
            messages.setContent(a.replaceAll("\\r\\n|\\r|\\n", ""));
            messagesList.add(messages);
            String result = knowledgeBase(messagesList).replaceAll("\\*", "");
            String[] entries = result.split("(?=题目：)");
            List<SpeechdraftProblem> speechdraftProblemArrayList = new ArrayList<>();
            for (String entry : entries) {
                SpeechdraftProblem speechdraftProblem1 = parseEntry(entry);
                if(speechdraftProblem1!=null
                        &&speechdraftProblem1.getProblem()!=null&&!"".equals(speechdraftProblem1.getProblem())
                        &&speechdraftProblem1.getOptions()!=null&&!"".equals(speechdraftProblem1.getOptions())
                        &&speechdraftProblem1.getAnwers()!=null&&!"".equals(speechdraftProblem1.getAnwers())){
                    speechdraftProblem1.setPresentationId(presentationId);
                    speechdraftProblem1.setIndex(Long.valueOf(i+1));
                    speechdraftProblem1.setCreateTime(DateUtils.getNowDate());
                    speechdraftProblemArrayList.add(speechdraftProblem1);
                }
            }
            if (speechdraftProblemArrayList.size()>occurrences){
                removeRandomlyUntilSize(speechdraftProblemArrayList, occurrences);
            }
            if (speechdraftProblemArrayList.size()>0) {
                speechdraftProblemService.insertSpeechdraftProblemList(speechdraftProblemArrayList);
            }
        }
    }

    public  <T> void removeRandomlyUntilSize(List<T> list, int targetSize) {
        Random rand = new Random();
        while (list.size() > targetSize) {
            // 生成一个介于0（包括）到列表当前大小（不包括）之间的随机整数
            int indexToRemove = rand.nextInt(list.size());
            // 移除指定索引处的元素
            list.remove(indexToRemove);
        }
    }
    private   SpeechdraftProblem parseEntry(String entry) {
        SpeechdraftProblem speechdraftProblem  = new SpeechdraftProblem();

        // 去掉开头的“输出：”字样，如果存在的话
        if (entry.startsWith("输出：")) {
            entry = entry.substring(3);
        }
        entry = entry.replaceAll("\n+", "\n");

        Pattern pattern = Pattern.compile(
                "题目：(.*?)\\n" +  // 题目
                        "选项：(?:\\n?)(A\\.?.*?)(?:\\n|,)(B\\.?.*?)(?:\\n|,)(C\\.?.*?)(?:\\n|,)(D\\.?.*?)\\n?" +  // 选项
                        "答案：(.*)",   // 答案
                Pattern.DOTALL  // 允许.匹配包括换行符在内的任何字符
        );

        Matcher matcher = pattern.matcher(entry);

        if (matcher.find()) {
            String question = matcher.group(1).trim();
            String optionA =  matcher.group(2).trim();
            String optionB = matcher.group(3).trim();
            String optionC =  matcher.group(4).trim();
            String optionD =  matcher.group(5).trim();
            String answer =   matcher.group(6).trim();

            speechdraftProblem.setProblem(question);
            speechdraftProblem.setOptions(optionA+","+optionB+","+optionC+","+optionD);

            if (answer != null && !answer.isEmpty()) {
                speechdraftProblem.setAnwers(""+answer.charAt(0));
            }

        } else {
            return null;
        }
        return speechdraftProblem;
    }


    public   String knowledgeBase(List<Messages> messagesList) throws IOException, AppBuilderServerException {
        String appid = configService.getConfigKey2("appidHomework", SecurityConstants.INNER).get("msg").toString();
        String secretkey = configService.getConfigKey2("secretkey", SecurityConstants.INNER).get("msg").toString();
        BaiduDto baiduDto = new BaiduDto();
        baiduDto.setQuery(messagesList.toString());
        baiduDto.setAppid(appid);
        baiduDto.setSecretkey(secretkey);
        return baiduApiService.knowledgeBase(baiduDto,SecurityConstants.INNER);
    }

    public static Map<String, Object> getDataset(String query, String appid, String secretkey) throws IOException, AppBuilderServerException {
        Map<String, Object> map = new HashMap<String, Object>();
        System.setProperty("APPBUILDER_TOKEN", secretkey);
        AppBuilderClient builder = new AppBuilderClient(appid);
        String conversationId = builder.createConversation();
        // 输入query
        AppBuilderClientIterator itor = builder.run(query, conversationId, null, false);
        AppBuilderClientResult response = new AppBuilderClientResult();
        StringBuilder answer = new StringBuilder();
        while (itor.hasNext()) {
            response = itor.next();
            answer.append(response.getAnswer());
            for (Event event : response.getEvents()) {
                switch (event.getContentType()) {
                    case "rag":
                        List<Object> references = (List<Object>) event.getDetail().get("references");
                        for (Object reference : references) {
                            ReferenceDetail ragDetail = com.baidubce.appbuilder.base.utils.json.JsonUtils.deserialize(com.baidubce.appbuilder.base.utils.json.JsonUtils.serialize(reference), ReferenceDetail.class);
                            map.put("ragDetail", ragDetail);
                        }
                        break;
                    default:
                }
            }
        }
        map.put("answer", answer);
        return map;
    }

    /**
     * 判断字符串中包含几个特定的字符
     * @param text
     * @param target
     * @return
     */
    public  int countOccurrences(String text, String target) {
        if (target.isEmpty()) return 0;  // 如果目标是空字符串，则直接返回0

        int count = 0;
        int fromIndex = 0;
        while ((fromIndex = text.indexOf(target, fromIndex)) != -1) {
            count++;
            fromIndex += target.length();  // 移动到上次找到的子串之后的位置
        }
        return count;
    }


    private List<Consonant> getConsonantList(String pageStr,Long presentationId,int i){
        //对action标签错误的问题进行处理
        String newPageStr=fixAction(pageStr);
        pageStr=newPageStr;
        //System.out.println(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>"+pageStr);
        List<PlatParam> platParamList=new ArrayList<>();
        List<Consonant> consonantList=new ArrayList<>();
        // 拆分每一页的文本段，按句号、分号和感叹号拆分
        String[] sentences = pageStr.split("[。！；？?!;]");
        int buIndex=0;
        int m=1;
        //进入循环每一文本段
        for (int j = 0; j < sentences.length; j++) {
            PlatParam platParam = new PlatParam();
            platParam.setIndexPage(i + 1);  // 设置页码
            platParam.setPresentationFrom(String.valueOf(presentationId)); // 设置演讲稿来源

            String trimmedSentence = sentences[j].trim();
            //System.out.println("+++++++++++++++++++++++++++++++++++++ :"+trimmedSentence.length());
            // 如果句子长度超过60字，按逗号拆分

            if (trimmedSentence.length() > 60) {
                String[] parts = trimmedSentence.split("[，,]");

                buIndex+=parts.length-1;
                for (int t = 0; t < parts.length; t++) {
                    String trimmedPart = parts[t].trim();


                    //System.out.println("-=-=-=-=-=---=-=-=-=-=-=-=-=-=-=-=-=-=-=-- :"+trimmedPart.length());
//                    while (trimmedPart.length() > 60) {
//                        StringBuilder subContent = new StringBuilder();
//                        subContent.append(trimmedPart, 0, 60);
//                        trimmedPart = trimmedPart.substring(60);
//
//                        PlatParam platParam3 = new PlatParam();
//                        platParam3.setIndexPage(i + 1);  // 设置页码
//                        platParam3.setPresentationFrom(String.valueOf(presentationId)); // 设置演讲稿来源
//                        ActionRegex actionRegex = getActionRegex(String.valueOf(subContent));
//                        platParam3.setMotion(actionRegex.getActionText());
//
//                        platParam3.setTxtSentence(String.valueOf(actionRegex.getTextWithoutAction())); // 设置文本
//                        platParam3.setBeforeAnalysisContent(actionRegex.getTextWithoutAction().trim());
//                        platParam3.setIndexSentence(j+t+m);
//                        m++;
//
////                                PlatParam platParamTo3 = getPlatParam(platParam3);
////                                getConsonant(platParamTo3);
//                        PlatParam platParamTo3 = consonantService.getPlatParam(platParam3);
//                        platParamList.add(platParamTo3);
//                        Consonant consonantSave3 = consonantService.getConsonant(platParamTo3);
//                        consonantList.add(consonantSave3);
////                                int consonantRows3 = consonantService.insertConsonant(consonantService.getConsonant(platParamTo3));
////
////                                if (consonantRows3 <= 0){
////                                    throw new CustomException("讲演稿声母韵母插入失败");
////                                }
//                    }

                    if(trimmedPart.length() > 60){
                        List<String> strings = splitText(trimmedPart);
                        for (String string : strings) {
                            PlatParam platParam3 = new PlatParam();
                            platParam3.setIndexPage(i + 1);  // 设置页码
                            platParam3.setPresentationFrom(String.valueOf(presentationId)); // 设置演讲稿来源
                            ActionRegex actionRegex = getActionRegex(String.valueOf(string));
                            platParam3.setMotion(actionRegex.getActionText());

                            platParam3.setTxtSentence(String.valueOf(actionRegex.getTextWithoutAction())); // 设置文本
                            platParam3.setBeforeAnalysisContent(actionRegex.getTextWithoutAction().trim());
                            platParam3.setIndexSentence(j+t+m);
                            m++;

//                                PlatParam platParamTo3 = getPlatParam(platParam3);
//                                getConsonant(platParamTo3);
                            PlatParam platParamTo3 = consonantService.getPlatParam(platParam3);
                            platParamList.add(platParamTo3);
                            Consonant consonantSave3 = consonantService.getConsonant(platParamTo3);
                            consonantList.add(consonantSave3);
                        }
                    }else {
                        PlatParam platParam2 = new PlatParam();
                        platParam2.setIndexPage(i + 1);  // 设置页码
                        platParam2.setPresentationFrom(String.valueOf(presentationId)); // 设置演讲稿来源
                        ActionRegex actionRegex = getActionRegex(String.valueOf(trimmedPart));
                        platParam2.setMotion(actionRegex.getActionText());

                        platParam2.setTxtSentence(String.valueOf(actionRegex.getTextWithoutAction())); // 设置文本
                        platParam2.setBeforeAnalysisContent(actionRegex.getTextWithoutAction().trim());
                        platParam2.setIndexSentence(j+t+1+m-1);
//                            PlatParam platParamTo2 = getPlatParam(platParam2);
//                            getConsonant(platParamTo2);

                        PlatParam platParamTo2 = consonantService.getPlatParam(platParam2);
                        platParamList.add(platParamTo2);
                        Consonant consonantSave2 = consonantService.getConsonant(platParamTo2);
                        consonantList.add(consonantSave2);
//                            int consonantRows2 = consonantService.insertConsonant(consonantService.getConsonant(platParamTo2));
//
//                            if (consonantRows2 <= 0){
//                                throw new CustomException("讲演稿声母韵母插入失败");
//                            }

                    }
                }
            } else {
                ActionRegex actionRegex = getActionRegex(String.valueOf(trimmedSentence));
                platParam.setMotion(actionRegex.getActionText());

                platParam.setTxtSentence(String.valueOf(actionRegex.getTextWithoutAction())); // 设置文本
                platParam.setBeforeAnalysisContent(actionRegex.getTextWithoutAction().trim());
                System.out.println("buIndex"+buIndex);
                platParam.setIndexSentence(j + 1+buIndex+m-1);
//                        PlatParam platParamTo = getPlatParam(platParam);
//                        getConsonant(platParamTo);
                PlatParam platParamTo = consonantService.getPlatParam(platParam);
                platParamList.add(platParamTo);
                Consonant consonantSave = consonantService.getConsonant(platParamTo);
                consonantList.add(consonantSave);
//                        int consonantRows = consonantService.insertConsonant(consonantService.getConsonant(platParamTo));
//
//                        if (consonantRows <= 0){
//                            throw new CustomException("讲演稿声母韵母插入失败");
//                        }
            }


        }

        return consonantList;
    }

    //    public static void main(String[] args) {
//        System.out.println(new PresentationServiceImpl().fixAction("@action:spread_hands。《国家突发公共事件总体应急预案》对突发公共事件的分类情况如表10-1所示。@action:spread_hands。提问：请根据表10-1的分类，借助大模型列举1-2个案例,同学们自行思考政府在事件中扮演了什么角色？@action:spread_hands"));
//        //System.out.println(new PresentationServiceImpl().isEndMarks('张'));
//    }
    private String fixAction(String pageStr) {
        // 定义一个正则表达式来匹配整个 @action: 开头的标签
        Pattern actionPattern = Pattern.compile("@action:\\w+");

        // 创建一个 Matcher 对象来匹配文本
        Matcher matcher = actionPattern.matcher(pageStr);

        //被删除掉的字符数量
        int i=0;

        //创建一个String类型的数组来
        while (matcher.find()) {

            int startIndex = matcher.start();
            int endIndex = matcher.end();


            try {
                // 检查前一个字符存在
                if(startIndex-1-i!=-1){
                    //如果前一个是终结符
                    if(isEndMarks(pageStr.charAt(startIndex-1-i))){
                        //报错说明在句尾
                        pageStr.charAt(endIndex-i);
                    }
                }



                //如果@action:后面不为空

                try {
                    //检查后面是不是有终结符
                    if(isEndMarks(pageStr.charAt(endIndex-i))){
                        //检查@action:前面是否为空
                        //报错说明在句首
                        pageStr.charAt(startIndex-1-i);
                    }

                    //检查前后是否都有终结符，如果都有就删除后面的终结符，把@action:移动到前面
                    if(isEndMarks(pageStr.charAt(startIndex-1-i))&&isEndMarks(pageStr.charAt(endIndex-i))){
                        //先储存终结符
                        char c = pageStr.charAt(startIndex-1-i);
                        //如果@action:后面已经为空
                        for (int j = startIndex-i; j < endIndex-i; j++){
                            StringBuilder sb = new StringBuilder(pageStr);
                            sb.setCharAt(j - 1, pageStr.charAt(j)); // 修改 j-1 位置的字符为 j 位置的字符
                            pageStr = sb.toString(); // 转回字符串
                        }
                        //拼上终结符
                        StringBuilder sb = new StringBuilder(pageStr);
                        sb.setCharAt(endIndex-1-i, c);
                        pageStr = sb.toString();
                        //删掉最后一个终结符
                        pageStr =  pageStr.substring(0, endIndex-i) +  pageStr.substring(endIndex-i + 1);
                        i++;
                    }


                } catch (Exception e) {

                    //删掉最后后面的终止符
                    pageStr =  pageStr.substring(0, endIndex-i) +  pageStr.substring(endIndex-i + 1);
                    i++;


                }
            } catch (Exception e) {
                //先储存终结符
                char c = pageStr.charAt(startIndex-1-i);
                //如果@action:后面已经为空
                for (int j = startIndex-i; j < endIndex-i; j++){
                    StringBuilder sb = new StringBuilder(pageStr);
                    sb.setCharAt(j - 1, pageStr.charAt(j)); // 修改 j-1 位置的字符为 j 位置的字符
                    pageStr = sb.toString(); // 转回字符串
                }
                //拼上终结符
                StringBuilder sb = new StringBuilder(pageStr);
                sb.setCharAt(endIndex-1-i, c);
                pageStr = sb.toString();

            }


        }
        return pageStr;
    }


    private boolean isEndMarks(char taggerChar){
        // 定义包含所有终结符的字符数组
        char[] endMarks = {'。', '！', '；', '？', '!', '?', ';', ','};
        boolean isEndMark = false;
        for (char mark : endMarks) {
            if (taggerChar == mark) {
                isEndMark = true;
                break; // 如果找到匹配的符号，立即跳出循环
            }
        }
        return isEndMark;
    }

    //------------------------------getConsonantList2---------------------------------------
    /**
     *
     * @param speechdraftPath 讲演稿路径
     * @param presentationId 对应课件id
     * @return
     */
    public List<List<Consonant>> getConsonantList2(String speechdraftPath, Long presentationId) {
        List<List<Consonant>> fileLists = new ArrayList<>();
        String[] pages = getFile(speechdraftPath);//每一页处理 转为数组

        for (int i = 0; i < pages.length; i++) {
            String pageContent = pages[i];

            // 处理并插入题目
            SpeechdraftProblem speechdraftProblem = parseQuestion(pageContent);
            handleSpeechdraftProblem(speechdraftProblem, presentationId, i);

            // 提取并处理句子
            List<Consonant> consonantList = processPageContent(pageContent, i + 1, presentationId);
            fileLists.add(consonantList);
        }

        return fileLists;
    }

    /**
     *
     * @param problem  提取出来的题目对象
     * @param presentationId 课件id
     * @param pageIndex 对应的第几页
     */
    private void handleSpeechdraftProblem(SpeechdraftProblem problem, Long presentationId, int pageIndex) {
        if (problem.getProblem() != null && !problem.getProblem().isEmpty()) {
            problem.setPresentationId(presentationId);
            problem.setIndex((long) (pageIndex + 1));
            if (problem.getAnwers().contains("、")) {
                problem.setRemark("1");
            }
            speechdraftProblemService.insertSpeechdraftProblem(problem);
        }
    }

    /**
     * 每一页文本  --> List<Consonant>
     * @param pageContent 每一页文本
     * @param pageIndex 第几页
     * @param presentationId 对应的课件id
     * @return
     */
    private List<Consonant> processPageContent(String pageContent, int pageIndex, Long presentationId) {
        List<Consonant> consonantList = new ArrayList<>();
        String contentWithoutQuestion = getQuestionRegex(pageContent).getTextWithoutQuestion();
        contentWithoutQuestion = getNoStr(contentWithoutQuestion);

        String[] sentences = contentWithoutQuestion.split("[。！；？]");
        int sentenceIndex = 1;

        for (String sentence : sentences) {
            sentence = sentence.trim();
            if (sentence.length() > 60) {
                String[] parts = sentence.split(",");
                for (String part : parts) {
                    part = part.trim();
                    sentenceIndex = processLongSentence(part, pageIndex, presentationId, consonantList, sentenceIndex);
                }
            } else {
                sentenceIndex = processShortSentence(sentence, pageIndex, presentationId, consonantList, sentenceIndex);
            }
        }

        return consonantList;
    }

    /**
     * 对 文本通过 。？！拆分超过60
     * @param sentencePart
     * @param pageIndex
     * @param presentationId
     * @param consonantList
     * @param sentenceIndex
     * @return
     */
    private int processLongSentence(String sentencePart, int pageIndex, Long presentationId, List<Consonant> consonantList, int sentenceIndex) {
        while (sentencePart.length() > 60) {
            String subContent = sentencePart.substring(0, 60);
            sentencePart = sentencePart.substring(60);

            sentenceIndex = createAndAddConsonant(subContent, pageIndex, presentationId, consonantList, sentenceIndex);
        }

        if (!sentencePart.isEmpty()) {
            sentenceIndex = createAndAddConsonant(sentencePart, pageIndex, presentationId, consonantList, sentenceIndex);
        }

        return sentenceIndex;
    }

    private int processLongSentence2(String sentencePart, int pageIndex, Long presentationId, List<Consonant> consonantList, int sentenceIndex) {
        // 用来存放每一个符合条件的子内容
        List<String> segments = new ArrayList<>();

        // 将 sentencePart 按 60 个字符切分
        while (sentencePart.length() > 60) {
            segments.add(sentencePart.substring(0, 60));
            sentencePart = sentencePart.substring(60);
        }

        // 检查最后的剩余部分是否过短（例如仅有 1-2 个字符）
        if (sentencePart.length() <= 1 && !segments.isEmpty()) {
            // 将剩余字符并入到前一个段落
            String lastSegment = segments.remove(segments.size() - 1);
            segments.add(lastSegment + sentencePart);
        } else if (!sentencePart.isEmpty()) {
            // 如果剩余部分超过 2 个字符，作为单独段落添加
            segments.add(sentencePart);
        }

        // 处理所有分段
        for (String segment : segments) {
            sentenceIndex = createAndAddConsonant(segment, pageIndex, presentationId, consonantList, sentenceIndex);
        }

        return sentenceIndex;
    }

    /**
     * 如果文本通过 。？！拆分不超过60
     * @param sentence
     * @param pageIndex
     * @param presentationId
     * @param consonantList
     * @param sentenceIndex
     * @return
     */
    private int processShortSentence(String sentence, int pageIndex, Long presentationId, List<Consonant> consonantList, int sentenceIndex) {
        return createAndAddConsonant(sentence, pageIndex, presentationId, consonantList, sentenceIndex);
    }

    /**
     *
     * @param content 不超过60字符文本
     * @param pageIndex 第几页
     * @param presentationId 对应课件id
     * @param consonantList 被添加到的list
     * @param sentenceIndex 第几句话
     * @return sentenceIndex++
     */
    private int createAndAddConsonant(String content, int pageIndex, Long presentationId, List<Consonant> consonantList, int sentenceIndex) {
        PlatParam platParam = new PlatParam();
        platParam.setIndexPage(pageIndex);
        platParam.setPresentationFrom(String.valueOf(presentationId));

        ActionRegex actionRegex = getActionRegex(content);
        platParam.setMotion(actionRegex.getActionText());
        platParam.setTxtSentence(actionRegex.getTextWithoutAction());
        platParam.setBeforeAnalysisContent(actionRegex.getTextWithoutAction().trim());
        platParam.setIndexSentence(sentenceIndex++);

        PlatParam platParamResult = consonantService.getPlatParam(platParam);
        Consonant consonantResult = consonantService.getConsonant(platParamResult);

        consonantList.add(consonantResult);
        return sentenceIndex;
    }


    //------------------------------getConsonantList2---------------------------------------





    /**
     * 批量删除老师课程
     *
     * @param ids 需要删除的老师课程主键
     * @return 结果
     */
    @Override
    //@Transactional(rollbackFor = Exception.class)
    public int deletePresentationByIds(Long[] ids)
    {
        //还有问题  前端根据课程删除  则对应的课程所有课件都要删除  现在是主键id删除 则删除一个课程的其中课件 （前端需要对同一课程不同课件的列表展示）
        //删除具体文件
        //未分割  分割文件夹

        //通过要删除的id找到s_presentation中的文件唯一id
        for (Long id : ids) {
            //根据id获得信息
            Presentation presentation2 = presentationMapper.selectPresentationById(id);
            //拿出文件唯一id
            String PresentationfileObjectNameStr=presentation2.getPresentationFileId();
            String SpeechdraftfileObjectNameStr=presentation2.getSpeechdraftFileId();
            //使用文件唯一id删除文件
            deleteFileInfoByFileObjectNameStr(PresentationfileObjectNameStr);
            deleteFileInfoByFileObjectNameStr(SpeechdraftfileObjectNameStr);

        }


        try {
            for (Long id : ids) {
                Presentation presentation = presentationMapper.selectPresentationById(id);
                Long presentationId = presentation.getPresentationId();
                // 未分割文件路径
                String presentationPath = presentation.getPresentationPath();
                // 文件夹路径
                String presentationSlidePath = presentationPath.substring(0, presentationPath.lastIndexOf("/")) ;
                // 删除文件和文件夹
                //File presentationFile = new File(presentationPath);
                deleteFile(presentationPath);

                //课件id的文件夹  全都删除了
                //File presentationSlideFile = new File(presentationSlidePath);
                deleteDirectory(presentationSlidePath);

                //删除关联表信息
//                int i = presentationMapper.delStudentStudyRecordByPId(presentationId);
//                if(i>0 ? false:true){
//                    throw new CustomException("delStudentStudyRecordByPId删除异常");
//                }
//                int j = presentationMapper.delStudentPromotionByPId(presentationId);
//                if(j>0 ? false:true){
//                    throw new CustomException("delStudentPromotionByPId删除异常");
//                }
                //删除讲演稿的声母韵母
                int i = consonantService.deleteConsonantByPresationId(presentationId);
//                if(i>0 ? false:true){
//                    throw new CustomException("deleteConsonantByPresationId异常");
//                }

                int j = speechdraftProblemService.deleteSpeechdraftProblemBypresentationId(presentationId);
//                if(j>0 ? false:true){
//                    throw new CustomException("deleteSpeechdraftProblemBypresentationId异常");
//                }

            }
        } catch (Exception e) {
            throw new CustomException("删除文件失败: " + e.getMessage(), e);
        }

        return presentationMapper.deletePresentationByIds(ids);
    }

    private void deleteFileInfoByFileObjectNameStr(String PresentationfileObjectNameStr) {
        SysFileInfo  sysFileInfo   = remoteFileService.getFileInfo(PresentationfileObjectNameStr);
        Long Fileid = sysFileInfo.getId();
        String FilebusiId = sysFileInfo.getBusiId();
        remoteFileService.deleteFile(FilebusiId);
        remoteFileService.delete(Fileid);
    }


    // 文件删除方法
    private void deleteFile2(File file) {
        try {
            if (file.exists() && !file.delete()) {
                throw new IOException("删除文件失败: " + file.getAbsolutePath());
            }
        } catch (Exception e) {
            // 记录异常日志，或者根据需求处理
            System.err.println("删除文件失败: " + e.getMessage());
            // 根据需要抛出自定义异常
            throw new CustomException("删除文件失败: " + file.getAbsolutePath(), e);
        }
    }
    // 文件删除方法
    private void deleteFile(String path) {
        remoteFileService.deleteFilep(path);
    }



    // 目录删除方法
    private void deleteDirectory2(File directory) {
        if (directory.exists() && directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File f : files) {
                    deleteFile2(f);
                }
            }
            try {
                if (!directory.delete()) {
                    throw new IOException("删除文件夹失败: " + directory.getAbsolutePath());
                }
            } catch (Exception e) {
                // 记录异常日志，或者根据需求处理
                System.err.println("删除文件夹失败: " + e.getMessage());
                // 根据需要抛出自定义异常
                throw new CustomException("删除文件夹失败: " + directory.getAbsolutePath(), e);
            }
        }
    }
    // 目录删除方法
    private void deleteDirectory(String path) {
        remoteFileService.deleteDirectory(path);
    }


    /**
     * 删除老师课程信息
     *
     * @param id 老师课程主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deletePresentationById(Long id) {
        Presentation presentation = presentationMapper.selectPresentationById(id);
        Long presentationId = presentation.getPresentationId();
        // 未分割文件路径
        String presentationPath = presentation.getPresentationPath();
        // 文件夹路径
        String presentationSlidePath = presentationPath.substring(0, presentationPath.lastIndexOf("/")) + "/" + presentationId;
        // 删除文件和文件夹
        // File presentationFile = new File(presentationPath);
        deleteFile(presentationPath);

        //File presentationSlideFile = new File(presentationSlidePath);
        deleteDirectory(presentationSlidePath);

//        int i = presentationMapper.delStudentStudyRecordByPId(presentationId);
//        if(i>0 ? false:true){
//            throw new CustomException("delStudentStudyRecordByPId删除异常");
//        }
//        int j = presentationMapper.delStudentPromotionByPId(presentationId);
//        if(j>0 ? false:true){
//            throw new CustomException("delStudentPromotionByPId删除异常");
//        }

        //删除讲演稿的声母韵母
        int i = consonantService.deleteConsonantByPresationId(presentationId);
//        if(i>0 ? false:true){
//            throw new CustomException("deleteConsonantByPresationId异常");
//        }
        System.out.println("=================================="+presentationId);
        //删除原有的课件题
        int j = speechdraftProblemService.deleteSpeechdraftProblemBypresentationId(presentationId);
//        if(j>0 ? false:true){
//            throw new CustomException("deleteSpeechdraftProblemBypresentationId异常");
//        }

        return presentationMapper.deletePresentationById(id);
    }

    @Override
    public int selectUByT(Long id) {
        return selectUByT(id);
    }

    @Override
    public int selectCByT(Long id) {
        return selectCByT(id);
    }

    @Override
    public UserDto selectUserByUserName(String username) {
        List<UserDto> userDtos = presentationMapper.selectUserByUserName(username);
        if (userDtos.size() > 1){
            throw new CustomException("当前登录用户出现重复，请检查数据");
        }
        return userDtos.get(0);
    }

    @Override
    public List<String> selectUserRoleKeyByUserName(String username) {
        return presentationMapper.selectUserRoleKeyByUserName(username);
    }


//    @Override
//    public Map<String, Object> uploadPPt(MultipartFile file, String modeltype) {
//        //临时上传//
//        //临时返回个 modeltype是临时
//
//        FileVo fileVo = remoteFileService.uploadFile(file, modeltype);
//
//
//
//        if (fileVo==null){
//            throw new CustomException("上传ppt文件过大");
//        }
//
//
//        String fileName = fileVo.getName().replace(".pptx","");
//        //1723704258958456
//        // Long fileId = fileVo.getId();
//        //   D:/ruoyi/uploadDataPath    \\createdPPtSlide\\pptx\\0\\20240815\\1723704258958456.pptx
//        //   /home/<USER>/uploadDataPath  \\createdPPtSlide\\pptx\\0\\20240815\\1723704258958456.pptx
//        String path = fileVo.getPreview();
//        int presentationId=getRandom();
//        int pptSize =0;
//
//        String sourcePath=path.replace("\\","/");
//
//        //
//        String sourcepathOut=path.replace("\\","/");
//
//        //    /createdPPtSlide/pptx/0/20240815/1723704258958456.pptx
//        //String pathX=path.substring(path.indexOf("\\")).replace("\\","/");
//
//
//        String os = System.getProperty("os.name").toLowerCase();
//        if (os.contains("win")) {
//            //sourcePath =localFilePathWin+pathX;
//            pptSize=presentationPathService.getPPTSize(new File(path));
//            sourcePath=path.replace(localFilePathWin,"").replace("\\","/");
//        } else if (os.contains("nix") || os.contains("nux") || os.contains("mac")) {
//            //sourcePath =localFilePathLinux+pathX;
//            pptSize=presentationPathService.getPPTSize(new File(path));
//            sourcePath=path.replace(localFilePathLinux,"").replace("\\","/");
//        } else {
//            throw new UnsupportedOperationException("Unsupported operating system: " + os);
//        }
//
//
//        //D:/ruoyi/uploadDataPath/createdPPtSlide/pptx/0/20240816/1723800157510848.pptx
//
//
//        String outputPath="";// sourcepathOut.substring(0,sourcepathOut.lastIndexOf("/"))+"/"+presentationId;
//        try {
//            int lastSlashIndex = sourcepathOut.lastIndexOf("/");
//            if (lastSlashIndex == -1) {
//                throw new IllegalArgumentException("Invalid path format: " + sourcepathOut);
//            }
//            outputPath = sourcepathOut.substring(0, lastSlashIndex) + "/" + presentationId;
//            // 后续操作
//        } catch (StringIndexOutOfBoundsException e) {
//            // 捕获数组越界异常
//            log.error("Error processing the path: " + sourcepathOut, e);
//            throw new CustomException("路径处理时发生异常", e);
//        } catch (IllegalArgumentException e) {
//            // 捕获非法路径格式异常
//            log.error(e.getMessage(), e);
//            throw new CustomException("无效的路径格式", e);
//        }
//
//        //ppt页数
//        // int pptSize = presentationPathService.getPPTSize(new File(sourcePath));
//
//
//        //分割ppt耗时间  事件通知
//        eventPublisher.publishEvent(new Tevent(this,sourcepathOut,outputPath,"slide"));
//
//
//        Map<String,Object> resultMap=new HashMap<>();
//        resultMap.put("presentationId",presentationId);
//        resultMap.put("presentationPath",sourcepathOut);
//        resultMap.put("presentationHttp",sourcePath);
//        resultMap.put("pptAllPage",pptSize);
//        resultMap.put("fileName",fileName);
//        resultMap.put("fileid",fileVo.getId());
//        return resultMap;
//
//    }




//    @Override
//    public Map<String, Object> uploadPPtOld(MultipartFile file) {
//        //把ppt做成多个存储
//        //课件id  返回
//        R<SysFile> upload = remoteFileService.upload(file);
//        System.out.println(upload.getData());
//        //File pptPath=new File(upload.getData().getUrl());
//        //ppt 拆分多个ppt
//        //url  http://127.0.0.1:9300/statics/2024/08/13/春季教育_20240813102421A001.pptx
////        D:/ruoyi/uploadPath/2024/08/13/春季教育_20240813102421A001.pptx
//        //http://127.0.0.1:9300/statics/  createdPPtSlide/pptx/0/20240815/1723704110233057.pptx
//        String path=upload.getData().getUrl();
//        String fileName=upload.getData().getName().replace(".pptx","");
//        //ppt文件存储的
//        String sourcePath=path.replace(targetUrl,newUrl);
//        // ppt 来源    D:/ruoyi/uploadPath/2024/08/13/春季教育_20240813102421A001.pptx
//        // ppt slide输出目录    D:/ruoyi/uploadPath/ppt/slide/2024/08/13/春季教育_20240813102421A001
//        // ppt slide的前缀
//        //String sourcePath = "D:/ruoyi/uploadPath/2024/08/13/春季教育_20240813102421A001.pptx";
//        // 定义源路径和目标路径的基础部分
//        // 替换路径
//        //String outputPath = sourcePath.replace(sourceBasePath, targetBasePath).replace(".pptx", "");
//        //生成课件id  建议再加上用户信息 id
//        int presentationId=getRandom();
//        //"D:/ruoyi/uploadPath/2024/08/13/春季教育_20240813102421A001.pptx   ->D:/ruoyi/uploadPath/2024/08/13/ + presentationId
//        String outputPath = sourcePath.substring(0,sourcePath.lastIndexOf("/"))+"/"+presentationId;
//        //路径处理
//        int pptSize = presentationPathService.getPPTSize(new File(sourcePath));
//        //这里时间久
//        //List<String> list = presentationPathService.processPPT(new File(sourcePath), outputPath, "slide");
//        eventPublisher.publishEvent(new Tevent(this,sourcePath,outputPath,"slide"));
//        // int presentationId = presentationPathService.insertPresentationPath(list);
//        Map<String,Object> resultMap=new HashMap<>();
//        resultMap.put("presentationId",presentationId);
//        resultMap.put("sourcePath",sourcePath);
//        resultMap.put("pptAllPage",pptSize);
//        resultMap.put("fileName",fileName);
//
//        //至少返回课件id
//        return resultMap;
//    }




//    @Override
//    public Map<String, Object> uploadSpeechDraftOld(MultipartFile file) {
//        R<SysFile> upload = remoteFileService.upload(file);
//        //  System.out.println(upload.getData());
//        //name url
//        File filePath=new File(upload.getData().getUrl());
//        //讲演稿  处理  添加动作标签
//
//        String path=upload.getData().getUrl();
//        String newPath=path.replace(targetUrl,newUrl);
//
//        Map<String,Object> resultMap=new HashMap<>();
//        resultMap.put("SpeechDraftName",upload.getData().getName());
//        resultMap.put("SpeechDraftPath",newPath);
//
//        return resultMap;
//    }


    @Override
    public String selectNameById(String id) {
        return presentationMapper.selectNameById(id);
    }


    public int getRandom(){
        Random random = new Random();
        int min = 1; // 最小值，确保生成的值大于零
        int max = Integer.MAX_VALUE; // 最大值
        int randomInt = random.nextInt(max - min + 1) + min;
        //System.out.println("随机生成的正整数: " + randomInt);
        return randomInt;
    }

    public void getTxt(HttpServletResponse response,Presentation presentation){
        //提交后的  文件
        readFile(response,presentation.getSpeechdraftpath());
    }

    public void getTxt2(HttpServletResponse response,String speechdraftpath){

        //传来的路径  获取临时文件路径
        String pathChangeHttp = getPathChangeHttp(speechdraftpath);
        String tempPathByPathHttp = getTempPathByPathHttp(pathChangeHttp);

        //临时文件
        readFile(response,tempPathByPathHttp);
    }

    @Override
    public Boolean handleSubmit(Presentation presentation) {

        //文件处于 临时目录
        if (presentation.getId() == null ){

            System.out.println("==============================="+"进入");
            try {
                System.out.println("=========================="+presentation.getSpeechdraftpath());
                //临时文件夹修改
                String speechdraftpath = presentation.getSpeechdraftpath();
                String pathChangeHttp = getPathChangeHttp(speechdraftpath);
                String tempPathByPathHttp = getTempPathByPathHttp(pathChangeHttp);

                String content = presentation.getSpeechdraftFileTxt();
                System.out.println("++++++++++============="+tempPathByPathHttp);
                Files.write(Paths.get(tempPathByPathHttp), content.getBytes(StandardCharsets.UTF_8));

            } catch (IOException e) {
                e.printStackTrace();
                throw new CustomException(e.getMessage());
            }
            return true;
        }

        //文件已提交数据库
        Presentation presentation1 = presentationMapper.selectPresentationById(presentation.getId());

        // 处理接收到的文本
        System.out.println(presentation.getSpeechdraftFileTxt());
        System.out.println(presentation.getSpeechdraftpath());


        String path=presentation.getSpeechdraftpath();

        if (!presentation1.getSpeechdraftpath().equals(presentation.getSpeechdraftpath())) {
            path=presentation1.getSpeechdraftpath();
        }
//            String speechdraftPath=getOutputPath(path, String.valueOf(presentationId));
//            speechdraftPath=speechdraftPath+getFileName(path);

        try {
//            FileWriter fileWriter = new FileWriter(speechdraftPath);
//            fileWriter.write(presentation.getSpeechdraftFileTxt());
//            fileWriter.flush(); // 刷新缓冲区，确保内容写入文件

            String content = presentation.getSpeechdraftFileTxt();
            Files.write(Paths.get(path), content.getBytes(StandardCharsets.UTF_8));


            consonantService.deleteConsonantByPresationId(presentation1.getPresentationId());
            //删除原有的课件题
            speechdraftProblemService.deleteSpeechdraftProblemBypresentationId(presentation1.getPresentationId());

            //saveConsonant(path,String.valueOf(presentation.getPresentationId()));
            //文件上传成功
            String[] file = getFile(path);
            //
//            for (int i=0;i<file.length;i++){
//                PlatParam platParam =new PlatParam();
//                platParam.setIndexPage(i+1);//第几页
//                platParam.setPresentationFrom(String.valueOf(presentation1.getPresentationId()));//课件id
//                platParam.setBeforeAnalysisContent(file[i]);//文本
////                    PlatParam platParam = PlatParam.builder()
////                            .indexPage(i+1)
////                            .presentationFrom(String.valueOf(presentation1.getPresentationId()))
////                            .beforeAnalysisContent(file[i])
////                            .build();
//
//                //TODO
//                SpeechdraftProblem speechdraftProblem = parseQuestion(file[i]);
//
//                if (!(speechdraftProblem.getProblem()==null) && !(speechdraftProblem.getProblem()=="") ){
//                    speechdraftProblem.setPresentationId(presentation1.getPresentationId());
//                    speechdraftProblem.setIndex(Long.valueOf(i+1));
//                    String anwers = speechdraftProblem.getAnwers();
//                    if (anwers.contains("、")){
//                        speechdraftProblem.setRemark("1");
//                    }
//                    speechdraftProblemService.insertSpeechdraftProblem(speechdraftProblem);
//                }
//
//
//                //处理声韵母
//                PlatParam platParamTo = consonantService.getPlatParam(platParam);
//
//                int consonantRows = consonantService.insertConsonant(consonantService.getConsonant(platParamTo));
//
//                if (consonantRows <= 0){
//                    throw new CustomException("讲演稿声母韵母插入失败");
//                }
//            }

            List<List<Consonant>> consonantList = getConsonantList(path, presentation1.getPresentationId());
            for (int i = 0; i < consonantList.size(); i++){
                Gson gson = new Gson();
                String jsonC = gson.toJson(consonantList.get(i));
                int consonantRows = consonantService.insertConsonant(Consonant.builder()
                        .consonant(jsonC).indexPage((i+1)+"json").speechdraftId(String.valueOf(presentation1.getPresentationId()))
                        .build());
                if (consonantRows <= 0){
                    throw new CustomException("讲演稿声母韵母插入失败");
                }
            }


        } catch (IOException | AppBuilderServerException e) {
            e.printStackTrace();
            throw new CustomException(e.getMessage());
        }
        return true;

    }

//    @Override
//    public void getTxtDemo(HttpServletResponse response) {
//        //示例文件路径？？
//        String demoFilePath = getDemoFilePath();
//        readFile(response,demoFilePath);
//    }


    public void readFile(HttpServletResponse response,String filepath){
        //OutputStream OutputStreamWriter FileOutputStream BufferedWriter
        FileInputStream fileInputStream=null;
        OutputStream outputStream=null;

        try{
            File file = new File(filepath);
            if (!file.exists()){
                return;
            }

            // === 新增：自动识别文件编码 ===
            String detectedCharset = detectFileEncoding(file);
            if (detectedCharset == null) {
                detectedCharset = "UTF-8"; // 默认兜底
            }

            // === 设置响应头，带自动识别的编码 ===
            response.setContentType("text/plain; charset=" + detectedCharset);

            fileInputStream=new FileInputStream(file);
            byte[] bytes = new byte[1024];
            outputStream=response.getOutputStream();

            int len=0;
            while ( (len=fileInputStream.read(bytes) )!= -1){
                outputStream.write(bytes,0,len);
            }
            outputStream.flush();
        }catch (Exception e){
            log.error("读取文件异常",e);
            throw new CustomException("读取文件异常");
        }finally {

            if (outputStream != null){
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("IO异常",e);
                    e.printStackTrace();
                }
            }
            if (fileInputStream != null){
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    log.error("IO异常",e);
                    e.printStackTrace();
                }
            }
        }
    }

    // 内联的自动编码识别方法
    private String detectFileEncoding(File file) {
        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] buf = new byte[4096];
            UniversalDetector detector = new UniversalDetector(null);
            int nread;
            while ((nread = fis.read(buf)) > 0 && !detector.isDone()) {
                detector.handleData(buf, 0, nread);
            }
            detector.dataEnd();
            return detector.getDetectedCharset();
        } catch (IOException e) {
            log.error("自动检测编码失败", e);
            return null;
        }
    }

    @Override
    public void download(HttpServletRequest request, HttpServletResponse response,Long fileId,int remark) {
        //   String demoFilePath = getDemoFilePath();

        Presentation presentation = presentationMapper.selectPresentationByFileId(String.valueOf(fileId));

        if (remark==1){
            String presentationPath = presentation.getPresentationPath();
            String fileName = getFileName(presentationPath);

            response.setContentType("application/x-msdownload; charset=UTF-8");
            if (request.getHeader("User-Agent").toLowerCase().indexOf("msie") > 0){
                try {
                    fileName= URLEncoder.encode(fileName,"UTF-8");
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                    throw new CustomException("编码错误转换");
                }
            }else {
                //  fileName= new String(fileName.getBytes(StandardCharsets.UTF-8));
                try {
                    fileName=new String(fileName.getBytes("UTF-8"),"ISO8859-1");
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                    throw new CustomException("编码错误转换");
                }
            }
            response.setHeader("Content-Disposition","attachment;filename=\""+fileName+"\"");

            readFile(response,presentationPath);
        }else {
            String speechdraftpath = presentation.getSpeechdraftpath();
            String fileName = getFileName(speechdraftpath);
            response.setContentType("application/x-msdownload; charset=UTF-8");
            if (request.getHeader("User-Agent").toLowerCase().indexOf("msie") > 0){
                try {
                    fileName= URLEncoder.encode(fileName,"UTF-8");
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                    throw new CustomException("编码错误转换");
                }
            }else {
                //  fileName= new String(fileName.getBytes(StandardCharsets.UTF-8));
                try {
                    fileName=new String(fileName.getBytes("UTF-8"),"ISO8859-1");
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                    throw new CustomException("编码错误转换");
                }
            }
            response.setHeader("Content-Disposition","attachment;filename=\""+fileName+"\"");

            readFile(response,speechdraftpath);
        }






    }

//    private String getDemoFilePath(){
//        String demoFilePath="";
//        String os = System.getProperty("os.name").toLowerCase();
//        if (os.contains("win")) {
//            demoFilePath=demoFilePathWin;
//        } else if (os.contains("nix") || os.contains("nux") || os.contains("mac")) {
//            demoFilePath=demoFilePathLinux;
//        } else {
//            throw new UnsupportedOperationException("Unsupported operating system: " + os);
//        }
//        return demoFilePath;
//    }



    //讲演稿路径 课件id   ··保存声母韵母和题目
    public void saveConsonant(String path,String presentationId) throws IOException, AppBuilderServerException {
        //文件上传成功
        String[] file = getFile(path);
        //
//        for (int i=0;i<file.length;i++){
//            PlatParam platParam =new PlatParam();
//            platParam.setIndexPage(i+1);
//            platParam.setPresentationFrom(presentationId);
//            platParam.setBeforeAnalysisContent(file[i]);
//
//
//
//            SpeechdraftProblem speechdraftProblem = parseQuestion(file[i]);
//
//            if (!(speechdraftProblem.getProblem()==null) && !(speechdraftProblem.getProblem()=="") ){
//                speechdraftProblem.setPresentationId(Long.valueOf(presentationId));
//                speechdraftProblem.setIndex(Long.valueOf(i+1));
//                String anwers = speechdraftProblem.getAnwers();
//                if (anwers.contains("、")){
//                    speechdraftProblem.setRemark("1");
//                }
//                speechdraftProblemService.insertSpeechdraftProblem(speechdraftProblem);
//            }
//
//
//            PlatParam platParamTo = consonantService.getPlatParam(platParam);
//
//            int consonantRows = consonantService.insertConsonant(consonantService.getConsonant(platParamTo));
//
//            if (consonantRows <= 0){
//                throw new CustomException("讲演稿声母韵母插入失败");
//            }
//        }
        List<List<Consonant>> consonantList = getConsonantList(path, Long.valueOf(presentationId));
        for (int i = 0; i < consonantList.size(); i++){
            Gson gson = new Gson();
            String jsonC = gson.toJson(consonantList.get(i));
            int consonantRows = consonantService.insertConsonant(Consonant.builder()
                    .consonant(jsonC).indexPage((i+1)+"json").speechdraftId(presentationId)
                    .build());
            if (consonantRows <= 0){
                throw new CustomException("讲演稿声母韵母插入失败");
            }
        }
    }

    @Override
    public List<KnowledgeBaseFile> selectTxtIdByCourse(KnowledgeBaseFile knowledgeBaseFile) {
        return presentationMapper.selectTxtIdByCourse(knowledgeBaseFile);
    }

    @Override
    public List<String> selectChapterById(Long id) {
        return presentationMapper.selectChapterById(id);
    }




    /**
     *  //解析讲演稿题目
     * @param text
     * @return
     */
    public  SpeechdraftProblem parseQuestion(String text) {
        Pattern pattern = Pattern.compile("@question:random_start(.*?)@question:random_end", Pattern.DOTALL | Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(text);
        SpeechdraftProblem questions = new SpeechdraftProblem();
        while (matcher.find()) {
            String questionBlock = matcher.group(1).trim();


            // 提取题目
            Pattern questionPattern = Pattern.compile("^题目：(.*?)选项：", Pattern.DOTALL | Pattern.CASE_INSENSITIVE);
            Matcher questionMatcher = questionPattern.matcher(questionBlock);
            if (questionMatcher.find()) {
                questions.setProblem(questionMatcher.group(1).trim());
            }

            // 提取选项
            /*int optionsStart = questionBlock.indexOf("选项：") + "选项：".length();
            int optionsEnd = questionBlock.indexOf("答案：");
            String optionsText = questionBlock.substring(optionsStart, optionsEnd).trim();
            List<String> options = new ArrayList<>();
            String[] splitOptions = optionsText.split(",\\s*");
            for (int i = 0; i < splitOptions.length; i++) {
                String option = splitOptions[i].trim();
                options.add(option);
            }
            q.setOptions(options);*/
            // 提取选项
            int optionsStart = text.indexOf("选项：") + "选项：".length();
            int optionsEnd = text.indexOf("答案：");
            String optionsText = text.substring(optionsStart, optionsEnd).trim();
            questions.setOptions(optionsText);

            // 提取答案
            Pattern answerPattern = Pattern.compile("(?i)答案：(.*)", Pattern.CASE_INSENSITIVE);
            Matcher answerMatcher = answerPattern.matcher(questionBlock);
            if (answerMatcher.find()) {
                questions.setAnwers(answerMatcher.group(1).trim());
            }

        }

        return questions;
    }

    public static List<String> splitText(String text) {
        List<String> result = new ArrayList<>();
        StringBuilder currentLine = new StringBuilder();
        int currentLength = 0;
        StringBuilder currentWord = new StringBuilder();

        for (char c : text.toCharArray()) {
            // 判断字符类型（中文或英文）
            boolean isCJK = Character.UnicodeBlock.of(c) == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS;

            currentWord.append(c);

            // 中文直接作为分隔点，英文需要空格分隔
            if (isCJK || c == ' ' || c == '\t') {
                if (currentLength + currentWord.length() > 60) {
                    result.add(currentLine.toString().trim());
                    currentLine.setLength(0);
                    currentLength = 0;
                }
                currentLine.append(currentWord);
                currentLength += currentWord.length();
                currentWord.setLength(0);
            }
        }

        // 处理最后一个单词
        if (currentWord.length() > 0) {
            if (currentLength + currentWord.length() > 60) {
                result.add(currentLine.toString().trim());
                currentLine.setLength(0);
            }
            currentLine.append(currentWord);
        }

        if (currentLine.length() > 0) {
            String lastLine = currentLine.toString().trim();
            currentLine.setLength(0);

            // 检查最后一行是否包含五个或更少的单词或汉字
            int wordCount = lastLine.split("\\s+").length;
            int charCount = 0;
            for (char c : lastLine.toCharArray()) {
                if (Character.UnicodeBlock.of(c) == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS) {
                    charCount++;
                }
            }

            if (wordCount <= 5 || charCount <= 5) {
                if (!result.isEmpty()) {
                    result.set(result.size() - 1, result.get(result.size() - 1) + " " + lastLine);
                } else {
                    result.add(lastLine);
                }
            } else {
                result.add(lastLine);
            }
        }

        return result;
    }
    /**
     * 如果源文件不是 UTF-8，则转码为 UTF-8 并就地覆盖。
     *
     * @return true = 做过转码；false = 文件原本已是 UTF-8
     */
    public static boolean convertToUtf8(Path src) throws IOException {
        // 探测原编码
        String detected = detectFileEncoding(src);
        if (detected == null) detected = "UTF-8";
        if ("UTF-8".equalsIgnoreCase(detected)) {
            return false;  // 已是 UTF-8，直接返回
        }

        //  读出 → 写回（覆盖）
        Path temp = Files.createTempFile("utf8_", ".tmp");
        try (BufferedReader  br = Files.newBufferedReader(src, Charset.forName(detected));
             BufferedWriter  bw = Files.newBufferedWriter(temp, StandardCharsets.UTF_8)) {

            char[] buf = new char[4096];
            int len;
            while ((len = br.read(buf)) != -1) {
                bw.write(buf, 0, len);
            }
        }

        //  原子替换
        Files.move(temp, src, StandardCopyOption.REPLACE_EXISTING);
        return true;

    }

    private static String detectFileEncoding(Path src) {


        try (FileInputStream fis = new FileInputStream(src.toFile())) {
            byte[] buf = new byte[4096];
            UniversalDetector detector = new UniversalDetector(null);
            int nread;
            while ((nread = fis.read(buf)) > 0 && !detector.isDone()) {
                detector.handleData(buf, 0, nread);
            }
            detector.dataEnd();
            return detector.getDetectedCharset();
        } catch (IOException e) {
            log.error("自动检测编码失败", e);
            return null;
        }

    }


}
