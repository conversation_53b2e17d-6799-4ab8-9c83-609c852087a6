package com.ruoyi.create.service.impl;

import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.create.domain.PresentationPath;
import com.ruoyi.create.exception.CustomException;
import com.ruoyi.create.mapper.PresentationMapper;
import com.ruoyi.create.mapper.PresentationPathMapper;
import com.ruoyi.create.service.IPresentationPathService;
import com.spire.presentation.FileFormat;
import com.spire.presentation.Presentation;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.openxml4j.opc.PackagePart;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.sl.usermodel.PictureData;
import org.apache.poi.xslf.usermodel.*;
import org.apache.xmlbeans.XmlCursor;
import org.openxmlformats.schemas.drawingml.x2006.main.CTRegularTextRun;
import org.openxmlformats.schemas.drawingml.x2006.main.CTSolidColorFillProperties;
import org.openxmlformats.schemas.drawingml.x2006.main.CTTextCharacterProperties;
import org.openxmlformats.schemas.drawingml.x2006.main.impl.CTTextFieldImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.xml.namespace.QName;
import java.awt.*;
import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

import org.apache.xmlbeans.XmlCursor;

@Slf4j
@Service
public class PresentationPathServiceImpl implements IPresentationPathService {

    @Resource
    private PresentationPathMapper presentationPathMapper;

    @Resource
    private PresentationMapper presentationMapper;
    /**
     * 给定ppt课件file   输出文件目录   对ppt每一页拆分成独立ppt
     *
     * @param pptFile     课件file
     * @param outputDir   输出文件目录
     * @param slidePrefix 独立ppt 前缀
     * @return 返回拆分ppt的path  list
     * @throws IOException 异常上抛
     */
    // @Override
//    public  List<String> processPPT(File pptFile, String outputDir,String slidePrefix) throws IOException {
//        try (FileInputStream fis = new FileInputStream(pptFile);
//             XMLSlideShow ppt = new XMLSlideShow(fis)) {
//
//            // 获取原始 PPT 的宽度和高度
//            Dimension pgSize = ppt.getPageSize();
//
//            // 遍历每一页
//            List<XSLFSlide> slides = ppt.getSlides();
//            List<String> pathList = new ArrayList<>();
//
//            for (int i = 0; i < slides.size(); i++) {
//                XSLFSlide slide = slides.get(i);
//
//                // 创建新的Presentation对象
//                XMLSlideShow newPpt = new XMLSlideShow();
//                // 设置新的 PPT 尺寸
//                newPpt.setPageSize(pgSize);
//
//                // 使用原始 PPT 的布局
//                XSLFSlideMaster slideMaster = ppt.getSlideMasters().get(0);
//                XSLFSlideLayout layout = slide.getSlideLayout();
//
//                XSLFSlide newSlide = newPpt.createSlide(layout);
//                newSlide.importContent(slide);
//
//                File directory = new File(outputDir);
//                // 判断目录是否存在
//                if (!directory.exists()) {
//                    // 目录不存在，尝试创建目录
//                    boolean created = directory.mkdirs(); // mkdirs() 方法会创建所有必要的父目录
//                    if (created) {
//                        System.out.println("目录已创建: " + outputDir);
//                    } else {
//                        System.out.println("目录创建失败: " + outputDir);
//                        throw new CustomException("目录创建失败");
//                    }
//                }
//
//                // 保存新的PPT文件
//                try (FileOutputStream fos = new FileOutputStream(new File(outputDir, slidePrefix + "_" + (i + 1) + ".pptx"))) {
//                    newPpt.write(fos);
//                }
//                // 添加到路径列表
//                pathList.add(outputDir + "/" + slidePrefix + "_" + (i + 1) + ".pptx");
//            }
//            return pathList;
//        }
//
//    }


//    poi的 ppt分割   部分不兼容 视频获取不到
    @Override
    public List<String> processPPT(File pptFile, String outputDir, String slidePrefix) throws IOException {
        try (FileInputStream fis = new FileInputStream(pptFile);
             XMLSlideShow oldPpt = new XMLSlideShow(fis)) {

            // 获取原始 PPT 的宽度和高度
            Dimension pgSize = oldPpt.getPageSize();

            // 遍历每一页
            List<XSLFSlide> oldSlides = oldPpt.getSlides();
            List<String> pathList = new ArrayList<>();
            for (int i = 0; i < oldSlides.size(); i++) {
                XSLFSlide oldSlide = oldSlides.get(i);
                // 创建新的Presentation对象
                XMLSlideShow newPpt = new XMLSlideShow();
                // 设置新的 PPT 尺寸
                newPpt.setPageSize(pgSize);
                // 使用原始 PPT 的布局
                XSLFSlideLayout layout = oldSlide.getSlideLayout();
                //布局

                XSLFSlide newSlide = newPpt.createSlide();
//
                //复制这些内容到新的
                newSlide.importContent(oldSlide);

                List<XSLFShape> shapes = layout.getShapes();

                for (XSLFShape shape : shapes) {
                    if (shape instanceof XSLFPictureShape) {
                        System.out.println("shape instanceof XSLFPictureShape");
                        XSLFPictureShape pictureShape = (XSLFPictureShape) shape;
                        XSLFPictureData pictureData = pictureShape.getPictureData();
                        Rectangle2D anchor = pictureShape.getAnchor();
                        byte[] data = pictureData.getData();
//                        String tmpPath = "D:/Ppt/demo1/" + i + pictureData.getFileName();
//                        FileOutputStream fileOutputStream = new FileOutputStream(tmpPath);
//                        fileOutputStream.write(data);
//                        fileOutputStream.close();

                        if (data != null && data.length > 0) {
                            System.out.println("data != null && data.length > 0");

                            // FileInputStream fileInputStream = new FileInputStream(tmpPath);

                            XSLFPictureData xslfPictureData = newPpt.addPicture(data, PictureData.PictureType.JPEG);
                            XSLFPictureShape picture = newSlide.getSlideLayout().createPicture(xslfPictureData);
                            picture.setAnchor(anchor);
                            break;
                        }

                    } else if (shape instanceof XSLFGroupShape) {
                        System.out.println("shape instanceof XSLFGroupShape");
                        XSLFGroupShape groupShape = (XSLFGroupShape) shape;
                        List<XSLFShape> shapes1 = groupShape.getShapes();
                        for (XSLFShape xslfShape : shapes1) {
                            if (xslfShape instanceof XSLFPictureShape) {
                                System.out.println("xslfShape instanceof XSLFPictureShape");
                                XSLFPictureShape pictureShape = (XSLFPictureShape) xslfShape;
                                XSLFPictureData pictureData = pictureShape.getPictureData();
                                Rectangle2D anchor = pictureShape.getAnchor();
                                byte[] data = pictureData.getData();
//                                String tmpPath = "D:/Ppt/demo1/" + i + pictureData.getFileName();
//                                FileOutputStream fileOutputStream = new FileOutputStream(tmpPath);
//                                fileOutputStream.write(data);
//                                fileOutputStream.close();

                                if (data != null && data.length > 0) {
                                    System.out.println("data != null && data.length > 0");
                                    //  FileInputStream fileInputStream = new FileInputStream(tmpPath);

                                    XSLFPictureData xslfPictureData = newPpt.addPicture(data, PictureData.PictureType.JPEG);
                                    XSLFPictureShape picture = newSlide.getSlideLayout().createPicture(xslfPictureData);
                                    picture.setAnchor(anchor);
                                    break;
                                }

                            }
                        }

                    }
                }


                File directory = new File(outputDir);
                // 判断目录是否存在
                if (!directory.exists()) {
                    // 目录不存在，尝试创建目录
                    boolean created = directory.mkdirs(); // mkdirs() 方法会创建所有必要的父目录
                    if (created) {
                        System.out.println("目录已创建: " + outputDir);
                    } else {
                        System.out.println("目录创建失败: " + outputDir);
                        return null;
                    }
                }
                // 保存新的PPT文件
                try (FileOutputStream fos = new FileOutputStream(new File(outputDir, slidePrefix + "_" + (i + 1) + ".pptx"))) {
                    newPpt.write(fos);
                }
                // 添加到路径列表
                pathList.add(outputDir + "/" + slidePrefix + "_" + (i + 1) + ".pptx");
            }
            return pathList;
        }

    }


    @Override
    public List<String> processPPT(String pptFile, String outputDir, String slidePrefix) throws Exception {
        // return splide(pptFile,outputDir,slidePrefix);
        return splidePpt(pptFile, outputDir, slidePrefix);
    }


    //文件复制  去除页  文件大  ppt可能不兼容   office需要修复才可以打开
    public List<String> splide(String pptFile, String outputDir, String prefix) {
        List<String> list = new ArrayList<>();
        try (FileInputStream fis = new FileInputStream(new File(pptFile));
             XMLSlideShow oldPpt = new XMLSlideShow(fis)) {
            int size = oldPpt.getSlides().size();

            for (int i = 0; i < size; i++) {
                String targer = outputDir + "/" + prefix + "_" + (i + 1) + ".pptx";
                list.add(targer);
                delNumPpt(pptFile, targer, i + 1);
            }

        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return list;
    }

    //spire 的ppt分割  读取poi的ppt不兼容
    public List<String> splidePpt(String pptFile, String outputDir, String prefix) throws Exception {

//        generateSingleImagesFromPPT2(pptFile, outputDir, "false");
//        // 处理ppt文字字体
//        dealPptFontsToImg(pptFile, "宋体");
        // 检查ppt中的每页是否含有视频并记录数据库
        Long presentationId = Long.valueOf(outputDir.substring(outputDir.lastIndexOf("/") + 1));

        checkAndRecordVideoPageIndexes(pptFile, presentationId);

        List<String> list = new ArrayList<>();
        //加载测试文档1
        Presentation ppt1 = new Presentation();
        System.out.println("============================" + pptFile);
        ppt1.loadFromFile(pptFile);
        //遍历文档1
        for (int i = 0; i < ppt1.getSlides().getCount(); i++) {

            //新建一个PPT文档，并移除默认生成的第一页幻灯片
            Presentation newppt = new Presentation();
            newppt.getSlides().removeAt(0);

            //将每一页添加到新建的文档，并保存
            newppt.getSlides().append(ppt1.getSlides().get(i));
            String format = outputDir + "/" + prefix + String.format("_%1$s.pptx", (i + 1));
            list.add(format);
            newppt.saveToFile(format, FileFormat.PPTX_2013);
            newppt.dispose();
            // 处理单页ppt生成图片
            dealPpToImg(format);
        }
        ppt1.dispose();
        return list;

    }

    /**
     * @description: 处理ppt中的每页是否含有视频并记录数据库
     * @author: zhaoTianQi
     * @date: 2024/11/12 17:22
     * @param: pptFilePath
     * @param presentationId
     * @return: void
     **/
    private void checkAndRecordVideoPageIndexes(String pptFilePath, Long presentationId) {
        try (FileInputStream fis = new FileInputStream(pptFilePath)) {
            XMLSlideShow pptx = new XMLSlideShow(fis);
            String[] blipNS = {
                    "http://schemas.openxmlformats.org/drawingml/2006/main",
                    "http://schemas.openxmlformats.org/presentationml/2006/main"
            };

            int slideIndex = 1;  // 幻灯片索引
            Set<String> pageWithVideoIndexStrs = new HashSet<>();
            // 遍历每一张幻灯片
            for (XSLFSheet slide : pptx.getSlides()) {
                // 遍历命名空间
                for (String ns : blipNS) {
                    try {
                        XmlCursor picCur = slide.getXmlObject().newCursor();
                        picCur.selectPath("declare namespace p='" + ns + "' .//p:videoFile");

                        while (picCur.toNextSelection()) {
                            QName relName = new QName("http://schemas.openxmlformats.org/officeDocument/2006/relationships", "link");
                            String relId = picCur.getAttributeText(relName); // 获取嵌入资源ID
                            if (ObjectUtils.isNotEmpty(relId)) {
                                pageWithVideoIndexStrs.add(String.valueOf(slideIndex));
                                break;
                            }
                        }
                    } catch (Exception e) {
                        log.error("处理PPT文件时发生错误：", e);
                    }
                }
                slideIndex++;
            }
            // 集合转字符串
            String pageWithVideoIndexStr = StringUtils.join(pageWithVideoIndexStrs, ",");
            // 更新数据库信息
            com.ruoyi.create.domain.Presentation presentation = new com.ruoyi.create.domain.Presentation();
            presentation.setPresentationId(presentationId);
            presentation.setVideoPageIndexes(pageWithVideoIndexStr);
            int i = presentationMapper.updatePresentationByPresentationId(presentation);
            if (i < 0) {
                log.error("更新PPT信息失败");
            }

        } catch (Exception e) {
            log.error("处理PPT文件时发生错误：", e);
        }


    }

    private static void dealPpToImg(String path) throws Exception {
        //load an example PPTX file
        Presentation presentation = new Presentation();
        presentation.loadFromFile(path);
        //loop through the slides
        for (int j = 0; j < presentation.getSlides().getCount(); j++) {
            //save each slide as a BufferedImage
            BufferedImage image = presentation.getSlides().get(j).saveAsImage();
            //save BufferedImage as PNG file format
            String fileName = path.replace(".pptx", ".jpg");
            ImageIO.write(image, "PNG", new File(fileName));
        }
        ThumbnailGenerator(path);
        presentation.dispose();
    }
    private static void ThumbnailGenerator(String format) throws Exception {
        String originalFilePath = format.replace(".pptx", ".jpg");
        String thumbnailFilePath =format.replace(".pptx", "WH.jpg");

        try {
            Thumbnails.of(originalFilePath)
                    .size(1247, 720) // 最大宽度和高度
                    .keepAspectRatio(true) // 保持宽高比
                    .toFile(thumbnailFilePath); // 保存缩略图
            System.out.println("缩略图已生成: " + thumbnailFilePath);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    public List<String> generateSingleImagesFromPPT2(String pptPath, String outputDir, String highImageQuality) {
        return generateSingleImagesFromPPTWithRetries(pptPath, outputDir, highImageQuality, 0);  // 初始调用重试次数为0
    }

    private List<String> generateSingleImagesFromPPTWithRetries(String pptPath, String outputDir, String highImageQuality, int retryCount) {
        if (StringUtils.isBlank(highImageQuality)) {
            highImageQuality = "false";
        }

        dealPptFontsToImg(pptPath, "宋体");

        if (StringUtils.isBlank(pptPath)) throw new IllegalArgumentException("PPT 路径不能为空.");
        if (StringUtils.isBlank(outputDir)) throw new IllegalArgumentException("输出目录不能为空.");

        List<String> resultImages = new ArrayList<>();
        ZipSecureFile.setMinInflateRatio(0.001);  // 允许更高的压缩比

        try (FileInputStream fis = new FileInputStream(pptPath);
             XMLSlideShow ppt = new XMLSlideShow(fis)) {

            Dimension pgsize = ppt.getPageSize();
            int slideCount = ppt.getSlides().size();
            double scale = "true".equals(highImageQuality) ? 4.0 : 2.0;

            int scaledWidth = Math.max((int) (pgsize.width * scale), pgsize.width);
            int scaledHeight = Math.max((int) (pgsize.height * scale), pgsize.height);

            List<CompletableFuture<String>> futures = new ArrayList<>();
            AtomicInteger failureCount = new AtomicInteger(0);  // 记录失败的次数

            for (int i = 0; i < slideCount; i++) {
                int slideIndex = i;
                String finalHighImageQuality = highImageQuality;
                CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                    return retryGenerateSlideImage(ppt, slideIndex, scaledWidth, scaledHeight, scale, outputDir, finalHighImageQuality, 0);
                }, new ThreadPoolExecutor(5, 10, 60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(200)));

                futures.add(future);
            }

            // 等待所有任务完成并收集结果
            CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            allOf.join();  // Blocks until all are complete

            // 收集所有有效的结果
            for (CompletableFuture<String> future : futures) {
                try {
                    String result = future.get();
                    if (result != null) {
                        resultImages.add(result);
                    }
                } catch (Exception e) {
                    log.error("获取生成图片的路径时出错: {}", e.getMessage());
                }
            }
            futures.clear();

            // 如果有生成失败的图片，并且已经尝试3次，那么重新生成整个PPT的图片
            if (failureCount.get() > 0 && retryCount < 3) {
                log.warn("有图片生成失败，尝试重新生成所有图片，当前是第 {} 次重试", retryCount + 1);
                return generateSingleImagesFromPPTWithRetries(pptPath, outputDir, highImageQuality, retryCount + 1);  // 递归重试生成所有图片
            }

        } catch (IOException e) {
            log.error("读取PPT文件失败: {}", e.getMessage());
        }

        return resultImages;  // 返回图片地址集合
    }

    private String retryGenerateSlideImage(XMLSlideShow ppt, int slideIndex, int scaledWidth, int scaledHeight, double scale, String outputDir, String highImageQuality, int attempt) {
        try {
            // 调用单张幻灯片的生成方法
            return generateSlideImage(ppt, slideIndex, scaledWidth, scaledHeight, scale, outputDir, highImageQuality);
        } catch (Exception e) {
            log.error("第 {} 次尝试生成幻灯片 {} 的图像失败: {}", attempt + 1, slideIndex + 1, e.getMessage());
            if (attempt < 2) {  // 如果尝试次数小于3次，继续尝试
                return retryGenerateSlideImage(ppt, slideIndex, scaledWidth, scaledHeight, scale, outputDir, highImageQuality, attempt + 1);
            } else {
                log.error("幻灯片 {} 的图像生成连续失败 3 次.", slideIndex + 1);
                return null;  // 返回 null 表示最终失败
            }
        }
    }

    private String generateSlideImage(XMLSlideShow ppt, int slideIndex, int scaledWidth, int scaledHeight, double scale, String outputDir, String highImageQuality) throws IOException {
        BufferedImage slideImage = new BufferedImage(scaledWidth, scaledHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D slideGraphics = slideImage.createGraphics();

        dealHighImageQuality(highImageQuality, slideGraphics);
        slideGraphics.setPaint(Color.white);
        slideGraphics.fillRect(0, 0, scaledWidth, scaledHeight);
        slideGraphics.scale(scale, scale);
        ppt.getSlides().get(slideIndex).draw(slideGraphics);
        slideGraphics.dispose();

        // Add sequence number
        Graphics2D g = slideImage.createGraphics();
        g.setColor(Color.BLACK);
        g.setFont(new Font("Arial", Font.BOLD, (int) (10 * scale)));
        String slideNumber = "sdyr-Slide " + (slideIndex + 1);
        int textWidth = g.getFontMetrics().stringWidth(slideNumber);
        int textHeight = g.getFontMetrics().getHeight();
        int x = scaledWidth - textWidth - 10;
        int y = scaledHeight - textHeight + 5;
        g.drawString(slideNumber, x, y);
        g.dispose();

        // Save image to output directory
        String outputPath = String.format("%s/slide_%d.jpg", outputDir, slideIndex + 1);
        ImageIO.write(slideImage, "jpg", new File(outputPath));
        slideImage.flush();  // Release BufferedImage memory

        return outputPath;  // Return the path
    }

//    public List<String> generateSingleImagesFromPPT2(String pptPath, String outputDir, String highImageQuality) {
//        if (StringUtils.isBlank(highImageQuality)) {
//            highImageQuality = "false";
//        }
//
//        dealPptFontsToImg(pptPath, "宋体");
//
//        if (StringUtils.isBlank(pptPath)) throw new IllegalArgumentException("PPT 路径不能为空.");
//        if (StringUtils.isBlank(outputDir)) throw new IllegalArgumentException("输出目录不能为空.");
//
//        List<String> resultImages = new ArrayList<>();
//
//        ZipSecureFile.setMinInflateRatio(0.001);  // 允许更高的压缩比
//        try (FileInputStream fis = new FileInputStream(pptPath);
//             XMLSlideShow ppt = new XMLSlideShow(fis)) {
//
//            Dimension pgsize = ppt.getPageSize();
//            int slideCount = ppt.getSlides().size();
//            double scale = "true".equals(highImageQuality) ? 4.0 : 2.0;
//
//            int scaledWidth = Math.max((int) (pgsize.width * scale), pgsize.width);
//            int scaledHeight = Math.max((int) (pgsize.height * scale), pgsize.height);
//
//            List<CompletableFuture<String>> futures = new ArrayList<>();
//
//            for (int i = 0; i < slideCount; i++) {
//                int slideIndex = i;
//                String finalHighImageQuality = highImageQuality;
//                CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
//                    try {
//                        BufferedImage slideImage = new BufferedImage(scaledWidth, scaledHeight, BufferedImage.TYPE_INT_RGB);
//                        Graphics2D slideGraphics = slideImage.createGraphics();
//
//                        dealHighImageQuality(finalHighImageQuality, slideGraphics);
//                        slideGraphics.setPaint(Color.white);
//                        slideGraphics.fillRect(0, 0, scaledWidth, scaledHeight);
//                        slideGraphics.scale(scale, scale);
//                        ppt.getSlides().get(slideIndex).draw(slideGraphics);
//                        slideGraphics.dispose();
//
//                        // Add sequence number
//                        Graphics2D g = slideImage.createGraphics();
//                        g.setColor(Color.BLACK);
//                        g.setFont(new Font("Arial", Font.BOLD, (int) (10 * scale)));
//                        String slideNumber = "sdyr-Slide " + (slideIndex + 1);
//                        int textWidth = g.getFontMetrics().stringWidth(slideNumber);
//                        int textHeight = g.getFontMetrics().getHeight();
//                        int x = scaledWidth - textWidth - 10;
//                        int y = scaledHeight - textHeight + 5;
//                        g.drawString(slideNumber, x, y);
//                        g.dispose();
//
//                        // Save image to output directory
//                        String outputPath = String.format("%s/slide_%d.jpg", outputDir, slideIndex + 1);
//                        ImageIO.write(slideImage, "jpg", new File(outputPath));
//                        slideImage.flush();  // Release BufferedImage memory
//
//                        return outputPath;  // Return the path
//                    } catch (Exception e) {
//                        log.error("生成幻灯片 {} 的图像失败: {}", slideIndex + 1, e.getMessage());
//                        return null;  // Return null on failure
//                    }
//                }, new ThreadPoolExecutor(5, 10, 60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(200)));
//
//                futures.add(future);
//            }
//
//            // 等待所有任务完成并收集结果
//            CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
//            allOf.join();  // Blocks until all are complete
//
//            // 收集所有有效的结果
//            for (CompletableFuture<String> future : futures) {
//                try {
//                    String result = future.get();
//                    if (result != null) {
//                        resultImages.add(result);
//                    }
//                } catch (Exception e) {
//                    log.error("获取生成图片的路径时出错: {}", e.getMessage());
//                }
//            }
//            futures.clear();
//        } catch (IOException e) {
//            log.error("读取PPT文件失败: {}", e.getMessage());
//        }
//
//        return resultImages;  // 返回图片地址集合
//    }

    private void dealHighImageQuality(String highImageQuality, Graphics2D slideGraphics) {
        if ("true".equals(highImageQuality)) {
            // 开启抗锯齿
            slideGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            // 设置渲染质量为高质量
            slideGraphics.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            // 开启文本抗锯齿
            slideGraphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            // 设置插值类型为双三次插值
            slideGraphics.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
            // 启用颜色平滑
            slideGraphics.setRenderingHint(RenderingHints.KEY_COLOR_RENDERING, RenderingHints.VALUE_COLOR_RENDER_QUALITY);
            // 启用形状几何学的子像素调整
            slideGraphics.setRenderingHint(RenderingHints.KEY_STROKE_CONTROL, RenderingHints.VALUE_STROKE_PURE);
            // 启用全场景抗锯齿
            slideGraphics.setRenderingHint(RenderingHints.KEY_FRACTIONALMETRICS, RenderingHints.VALUE_FRACTIONALMETRICS_ON);
        } else {
            // 开启抗锯齿
            slideGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            // 开启文本抗锯齿
            slideGraphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            // 启用全场景抗锯齿
            slideGraphics.setRenderingHint(RenderingHints.KEY_FRACTIONALMETRICS, RenderingHints.VALUE_FRACTIONALMETRICS_ON);
        }

        slideGraphics.setPaint(Color.white);
    }

    public void dealPptFontsToImg(String filePath, String useFont) {
        if (StringUtils.isBlank(useFont)) {
            useFont = "黑体";
        }
        ZipSecureFile.setMinInflateRatio(0.001);  // 允许更高的压缩比
        try (XMLSlideShow ppt = new XMLSlideShow(Files.newInputStream(Paths.get(filePath)))) {
            List<XSLFSlide> slides = ppt.getSlides();
            try {
                for (XSLFSlide slide : slides) {
                    List<XSLFShape> shapes = slide.getShapes();
                    List<XSLFShape> layoutShapes = slide.getSlideLayout().getShapes();
                    dealPptFontsToImg(shapes, useFont);
                    dealPptFontsToImg(layoutShapes, useFont);

                }
                slides.clear();
            } catch (Exception e) {
                e.printStackTrace();
            }


            // 保存回原来的文件
            try (FileOutputStream out = new FileOutputStream(filePath)) {
                ppt.write(out);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void dealPptFontsToImg(List<? extends XSLFShape> shapes, String useFont) {
        for (XSLFShape shape : shapes) {
            if (shape instanceof XSLFTextShape) {
                XSLFTextShape textShape = (XSLFTextShape) shape;
                for (XSLFTextParagraph paragraph : textShape.getTextParagraphs()) {
                    for (XSLFTextRun textRun : paragraph.getTextRuns()) {

                        // 处理不同类型的文本元素
                        if (textRun.getXmlObject() instanceof CTRegularTextRun) {
                            CTRegularTextRun xmlObject = (CTRegularTextRun) textRun.getXmlObject();
                            CTTextCharacterProperties rPr = xmlObject.getRPr();
                            if (rPr != null) {
                                // 设置字体
                                if (rPr.isSetLatin()) {
                                    rPr.getLatin().setTypeface(useFont);
                                } else {
                                    rPr.addNewLatin().setTypeface(useFont);
                                }
                                if (rPr.isSetEa()) {
                                    rPr.getEa().setTypeface(useFont);
                                } else {
                                    rPr.addNewEa().setTypeface(useFont);
                                }
                                if (rPr.isSetCs()) {
                                    rPr.getCs().setTypeface(useFont);
                                }
                                if (rPr.isSetSolidFill()) {
                                    CTSolidColorFillProperties solidFill = rPr.getSolidFill();
                                }

                                if (rPr.isSetCs()) {
                                    rPr.getCs().setTypeface(useFont);
                                }

                            }
                        } else if (textRun.getXmlObject() instanceof CTTextFieldImpl) {
                        }
                    }
                }
            } else if (shape instanceof XSLFGroupShape) {
                XSLFGroupShape groupShape = (XSLFGroupShape) shape;
                // 递归处理组中的形状
                dealPptFontsToImg(groupShape.getShapes(), useFont);
            }
        }
    }


    public void delNumPpt(String inputFile, String outputFile, Integer num) {

        // 保留的幻灯片页码（基于1的索引，即第一页为1）
        Set<Integer> pagesToKeep = new HashSet<>();
        pagesToKeep.add(num);  // 保留第一页
        //pagesToKeep.add(3);  // 保留第三页

        try (FileInputStream fis = new FileInputStream(inputFile);
             XMLSlideShow ppt = new XMLSlideShow(fis)) {

            // 获取所有幻灯片
            List<XSLFSlide> slides = ppt.getSlides();

            // 从后往前遍历幻灯片并删除未指定保留的幻灯片
            for (int i = slides.size(); i > 0; i--) {
                if (!pagesToKeep.contains(i)) {
                    ppt.removeSlide(i - 1); // 注意索引是从0开始的，所以需要减1
                }
            }

            // 保存修改后的PPT文件
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                ppt.write(fos);
            }

            System.out.println("指定页码的幻灯片已保留，新的PPT已保存。");

        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    public int getPPTSize(File pptFile) {
        int pptSize = 0;
        try (FileInputStream fis = new FileInputStream(pptFile);
             XMLSlideShow ppt = new XMLSlideShow(fis)) {
            // 遍历每一页
            List<XSLFSlide> slides = ppt.getSlides();
            pptSize = slides.size();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            log.info(e.getMessage());
            throw new CustomException(e.getMessage());
        } catch (IOException e) {
            e.printStackTrace();
            log.info(e.getMessage());
            throw new CustomException(e.getMessage());
        }
        return pptSize;
    }


    //已经弃用  不单独存储分割ppt路径
    @Override
    public int insertPresentationPath(List<String> pathList) {
        //暂时  生成存储id  生成课件id
        int presentationId = getRandom();
        //数据库是否有
        List<PresentationPath> presentationPaths = presentationPathMapper.selectPresentationPathList(PresentationPath.builder().sPresentaationId((long) presentationId).build());
        if (presentationPaths.size() > 0 || presentationPaths != null) {
            presentationId = getRandom();
        }


        for (String path : pathList) {
            presentationPathMapper
                    .insertPresentationPath(
                            PresentationPath.builder()
                                    .sPresentationPath(path)
                                    .sPresentaationId(Long.valueOf(presentationId))
                                    .build()
                    );
        }
        return presentationId;
    }


    private int getRandom() {
        Random random = new Random();
        int min = 1; // 最小值，确保生成的值大于零
        int max = Integer.MAX_VALUE; // 最大值
        int randomInt = random.nextInt(max - min + 1) + min;
        //System.out.println("随机生成的正整数: " + randomInt);
        return randomInt;
    }


    /**
     * 查询ppt拆分
     *
     * @param id ppt拆分主键
     * @return ppt拆分
     */
    @Override
    public PresentationPath selectPresentationPathById(Long id) {
        return presentationPathMapper.selectPresentationPathById(id);
    }

    /**
     * 查询ppt拆分列表
     *
     * @param presentationPath ppt拆分
     * @return ppt拆分
     */
    @Override
    public List<PresentationPath> selectPresentationPathList(PresentationPath presentationPath) {
        return presentationPathMapper.selectPresentationPathList(presentationPath);
    }

    /**
     * 新增ppt拆分
     *
     * @param presentationPath ppt拆分
     * @return 结果
     */
    @Override
    public int insertPresentationPath(PresentationPath presentationPath) {
        return presentationPathMapper.insertPresentationPath(presentationPath);
    }

    /**
     * 修改ppt拆分
     *
     * @param presentationPath ppt拆分
     * @return 结果
     */
    @Override
    public int updatePresentationPath(PresentationPath presentationPath) {
        return presentationPathMapper.updatePresentationPath(presentationPath);
    }

    /**
     * 批量删除ppt拆分
     *
     * @param ids 需要删除的ppt拆分主键
     * @return 结果
     */
    @Override
    public int deletePresentationPathByIds(Long[] ids) {
        return presentationPathMapper.deletePresentationPathByIds(ids);
    }

    /**
     * 删除ppt拆分信息
     *
     * @param id ppt拆分主键
     * @return 结果
     */
    @Override
    public int deletePresentationPathById(Long id) {
        return presentationPathMapper.deletePresentationPathById(id);
    }


}
