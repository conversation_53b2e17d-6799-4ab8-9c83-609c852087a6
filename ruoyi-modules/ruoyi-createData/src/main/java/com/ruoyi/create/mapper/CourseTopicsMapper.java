package com.ruoyi.create.mapper;

import java.util.List;
import com.ruoyi.create.domain.CourseTopics;

/**
 * 课程讨论话题Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
public interface CourseTopicsMapper
{
    /**
     * 查询课程讨论话题
     *
     * @param id 课程讨论话题主键
     * @return 课程讨论话题
     */
    public CourseTopics selectCourseTopicsById(Long id);

    /**
     * 查询课程讨论话题列表
     *
     * @param courseTopics 课程讨论话题
     * @return 课程讨论话题集合
     */
    public List<CourseTopics> selectCourseTopicsList(CourseTopics courseTopics);

    /**
     * 新增课程讨论话题
     *
     * @param courseTopics 课程讨论话题
     * @return 结果
     */
    public int insertCourseTopics(CourseTopics courseTopics);

    /**
     * 修改课程讨论话题
     *
     * @param courseTopics 课程讨论话题
     * @return 结果
     */
    public int updateCourseTopics(CourseTopics courseTopics);

    /**
     * 删除课程讨论话题
     *
     * @param id 课程讨论话题主键
     * @return 结果
     */
    public int deleteCourseTopicsById(Long id);

    /**
     * 批量删除课程讨论话题
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCourseTopicsByIds(Long[] ids);

    /**是管理员 查询全部的没有限制**/
    List<CourseTopics> listAllTopics(CourseTopics courseTopics);
    /**是教师 查询部分讨论**/
    List<CourseTopics> listAllTopicsForTeacher(CourseTopics courseTopics);
    /**是学生 查询部分讨论**/
    List<CourseTopics> listAllTopicsForStudent(CourseTopics courseTopics);

    List<CourseTopics> selectCourseTopicsList2(CourseTopics courseTopics);

}
