package com.ruoyi.create.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.SMessage;
import com.ruoyi.create.domain.SMessage1;
import com.ruoyi.create.service.ISMessageService;
import com.ruoyi.create.service.ISMessageService1;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 留言信息Controller
 * 
 * <AUTHOR>
 * @date 2024-06-05
 */
@RestController
@RequestMapping("/message1")
public class SMessageController1 extends BaseController
{
    @Resource
    private ISMessageService1 sMessageService;

    /**
     * 查询留言信息列表
     */
    @RequiresPermissions("system:message:list")
    @GetMapping("/list")
    public TableDataInfo list(SMessage1 sMessage)
    {
        startPage();
        List<SMessage1> list = sMessageService.selectSMessageList(sMessage);
        return getDataTable(list);
    }

    /**
     * 导出留言信息列表
     */
    @RequiresPermissions("system:message:export")
    @Log(title = "留言信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SMessage1 sMessage)
    {
        List<SMessage1> list = sMessageService.selectSMessageList(sMessage);
        ExcelUtil<SMessage1> util = new ExcelUtil<SMessage1>(SMessage1.class);
        util.exportExcel(response, list, "留言信息数据");
    }

    /**
     * 获取留言信息详细信息
     */
    @RequiresPermissions("system:message:query")
    @GetMapping(value = "/{msgId}")
    public AjaxResult getInfo(@PathVariable("msgId") Long msgId)
    {
        return success(sMessageService.selectSMessageByMsgId(msgId));
    }

    /**
     * 新增留言信息
     */
    @RequiresPermissions("system:message:add")
    @Log(title = "留言信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SMessage1 sMessage)
    {

        return toAjax(sMessageService.insertSMessage(sMessage));
    }

    /**
     * 修改留言信息
     */
    @RequiresPermissions("system:message:edit")
    @Log(title = "留言信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SMessage1 sMessage)
    {
        return toAjax(sMessageService.updateSMessage(sMessage));
    }

    /**
     * 删除留言信息
     */
    @RequiresPermissions("system:message:remove")
    @Log(title = "留言信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{msgIds}")
    public AjaxResult remove(@PathVariable Long[] msgIds)
    {
        return toAjax(sMessageService.deleteSMessageByMsgIds(msgIds));
    }
}
