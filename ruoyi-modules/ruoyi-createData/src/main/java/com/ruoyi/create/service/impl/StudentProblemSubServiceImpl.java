package com.ruoyi.create.service.impl;


import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.create.domain.StudentProblemSub;
import com.ruoyi.create.mapper.StudentProblemSubMapper;
import com.ruoyi.create.service.IStudentProblemSubService;
import org.springframework.stereotype.Service;

import java.util.List;
import javax.annotation.Resource;

/**
 * 学生回答记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
@Service
public class StudentProblemSubServiceImpl implements IStudentProblemSubService
{
    @Resource
    private StudentProblemSubMapper studentProblemSubMapper;

    /**
     * 查询学生回答记录
     *
     * @param id 学生回答记录主键
     * @return 学生回答记录
     */
    @Override
    public StudentProblemSub selectStudentProblemSubById(Long id)
    {
        return studentProblemSubMapper.selectStudentProblemSubById(id);
    }

    /**
     * 查询学生回答记录列表
     *
     * @param studentProblemSub 学生回答记录
     * @return 学生回答记录
     */
    @Override
    public List<StudentProblemSub> selectStudentProblemSubList(StudentProblemSub studentProblemSub)
    {
        return studentProblemSubMapper.selectStudentProblemSubList(studentProblemSub);
    }

    /**
     * 新增学生回答记录
     *
     * @param studentProblemSub 学生回答记录
     * @return 结果
     */
    @Override
    public int insertStudentProblemSub(StudentProblemSub studentProblemSub)
    {
        studentProblemSub.setCreateTime(DateUtils.getNowDate());
        return studentProblemSubMapper.insertStudentProblemSub(studentProblemSub);
    }

    /**
     * 修改学生回答记录
     *
     * @param studentProblemSub 学生回答记录
     * @return 结果
     */
    @Override
    public int updateStudentProblemSub(StudentProblemSub studentProblemSub)
    {
        return studentProblemSubMapper.updateStudentProblemSub(studentProblemSub);
    }

    /**
     * 批量删除学生回答记录
     *
     * @param ids 需要删除的学生回答记录主键
     * @return 结果
     */
    @Override
    public int deleteStudentProblemSubByIds(Long[] ids)
    {
        return studentProblemSubMapper.deleteStudentProblemSubByIds(ids);
    }

    /**
     * 删除学生回答记录信息
     *
     * @param id 学生回答记录主键
     * @return 结果
     */
    @Override
    public int deleteStudentProblemSubById(Long id)
    {
        return studentProblemSubMapper.deleteStudentProblemSubById(id);
    }
}
