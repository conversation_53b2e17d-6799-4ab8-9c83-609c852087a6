package com.ruoyi.create.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.CareResourceMapper;
import com.ruoyi.create.domain.CareResource;
import com.ruoyi.create.service.ICareResourceService;

/**
 * 服务资源配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
@Service
public class CareResourceServiceImpl implements ICareResourceService 
{
    @Autowired
    private CareResourceMapper careResourceMapper;

    /**
     * 查询服务资源配置
     * 
     * @param id 服务资源配置主键
     * @return 服务资源配置
     */
    @Override
    public CareResource selectCareResourceById(Long id)
    {
        return careResourceMapper.selectCareResourceById(id);
    }

    /**
     * 查询服务资源配置列表
     * 
     * @param careResource 服务资源配置
     * @return 服务资源配置
     */
    @Override
    public List<CareResource> selectCareResourceList(CareResource careResource)
    {
        return careResourceMapper.selectCareResourceList(careResource);
    }

    /**
     * 新增服务资源配置
     * 
     * @param careResource 服务资源配置
     * @return 结果
     */
    @Override
    public int insertCareResource(CareResource careResource)
    {
        return careResourceMapper.insertCareResource(careResource);
    }

    /**
     * 修改服务资源配置
     * 
     * @param careResource 服务资源配置
     * @return 结果
     */
    @Override
    public int updateCareResource(CareResource careResource)
    {
        return careResourceMapper.updateCareResource(careResource);
    }

    /**
     * 批量删除服务资源配置
     * 
     * @param ids 需要删除的服务资源配置主键
     * @return 结果
     */
    @Override
    public int deleteCareResourceByIds(Long[] ids)
    {
        return careResourceMapper.deleteCareResourceByIds(ids);
    }

    /**
     * 删除服务资源配置信息
     * 
     * @param id 服务资源配置主键
     * @return 结果
     */
    @Override
    public int deleteCareResourceById(Long id)
    {
        return careResourceMapper.deleteCareResourceById(id);
    }
}
