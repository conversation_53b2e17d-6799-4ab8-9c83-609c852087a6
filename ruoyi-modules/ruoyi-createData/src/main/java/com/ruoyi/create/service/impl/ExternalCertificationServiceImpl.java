package com.ruoyi.create.service.impl;


import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.domain.ExternalCertification;
import com.ruoyi.create.domain.execl.*;
import com.ruoyi.create.domain.execl.Class;
import com.ruoyi.create.mapper.ExeclStuAndTeaSaveMapper;
import com.ruoyi.create.mapper.ExternalCertificationMapper;
import com.ruoyi.create.service.IExternalCertificationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class ExternalCertificationServiceImpl implements IExternalCertificationService {

    @Resource
    private ExternalCertificationMapper externalCertificationMapper;


    //1、认证： 教师、 姓名 工号 输入学校、院系 不需比对直接认证。
    // 学生角色，姓名 学号 输入学校、院系、专业、班级，不需比对直接认证。
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int add(ExternalCertification externalCertification) {
        externalCertification.setCreateBy(SecurityUtils.getUsername());
        externalCertification.setCreateTime(new Date());
        getUniver(externalCertification);
        getColl(externalCertification);

        if (externalCertification.getIsStuOrTea().equals(1)){
            getMaj(externalCertification);
            getCla(externalCertification);
//            String studentId = externalCertification.getStudentId();
            List<Long> longs = externalCertificationMapper.selectStuByStuId(externalCertification.getStudentId());
            if (longs.size()==0 || longs==null){
                externalCertificationMapper.insertOrUpdateS(externalCertification);
                externalCertificationMapper.insertOrUpdateUS(externalCertification);
                externalCertificationMapper.insertUserRoleS(externalCertification.getUserId());
            }else {
                throw new RuntimeException("请重新输入教工号或学号");
            }


        }else {
//            String teacherId = externalCertification.getTeacherId();
            List<Long> longs = externalCertificationMapper.selectTeaByTeaId(externalCertification.getTeacherId());
            if (longs.size()==0 || longs==null){
                externalCertificationMapper.insertOrUpdateT(externalCertification);
                externalCertificationMapper.insertOrUpdateUT(externalCertification);
                externalCertificationMapper.insertUserRoleT(externalCertification.getUserId());
            }else {
                throw new RuntimeException("请重新输入教工号或学号");
            }
        }

        return 0;
    }

    @Override
    public AjaxResult checkStuOrTea(ExternalCertification externalCertification) {
        if (externalCertification.getIsStuOrTea().equals(1)){
            List<Long> longs = externalCertificationMapper.selectStuByStuId(externalCertification.getStudentId());
            if (longs.size()==0 || longs==null){
                return AjaxResult.success();
            }else {
                return new AjaxResult(300,"学号重复，请重新输入学号");
            }
        }else {
            List<Long> longs = externalCertificationMapper.selectTeaByTeaId(externalCertification.getTeacherId());
            if (longs.size()==0 || longs==null){
                return AjaxResult.success();
            }else {
                return new AjaxResult(300,"教工号重复，请重新输入教工号");
            }
        }
    }



    public  void getUniver(ExternalCertification externalCertification){

        List<Long> univers = externalCertificationMapper.selectUniverIdByName(externalCertification.getUniverName());
        if (univers.size()==0 || univers==null){
            Univer build = new Univer();
            build.setUniverName(externalCertification.getUniverName());
            build.setCreateBy(SecurityUtils.getUsername());
            build.setCreateTime(new Date());
            externalCertificationMapper.insertUniver(build);
            externalCertification.setUniverId(build.getId());
        }else {
            externalCertification.setUniverId(univers.get(0));
        }
    }
    public  void getColl(ExternalCertification externalCertification){

        List<Long> collIds = externalCertificationMapper.selectCollIdByName(externalCertification);
        if (collIds.size()==0 || collIds==null){
            Coll build = new Coll();
            build.setColleName(externalCertification.getColleName());
            build.setUniverId(externalCertification.getUniverId());
            build.setCreateBy(SecurityUtils.getUsername());
            build.setCreateTime(new Date());
            externalCertificationMapper.insertColl(build);
            externalCertification.setColleId(build.getId());
        }else {
            externalCertification.setColleId(collIds.get(0));
        }
    }

    public  void getMaj(ExternalCertification externalCertification){

        List<Long> majIds = externalCertificationMapper.selectMajIdByName(externalCertification);
        if (majIds.size()==0 || majIds==null){
            Major build = Major.builder()
                    .majorName(externalCertification.getMajorName())
                    .univerId(externalCertification.getUniverId())
                    .colleId(externalCertification.getColleId())
                    .createBy(SecurityUtils.getUsername())
                    .createTime(new Date())
                    .build();
            externalCertificationMapper.insertMajor(build);
            externalCertification.setMajorId(build.getId());
        }else {
            externalCertification.setMajorId(majIds.get(0));
        }
    }

    public  void getCla(ExternalCertification externalCertification){

        List<Long> claIds = externalCertificationMapper.selectClaIdByName(externalCertification);
        if (claIds.size()==0 || claIds==null){
            Class build = Class.builder()
                    .className(externalCertification.getClassName())
                    .univerId(externalCertification.getUniverId())
                    .colleId(externalCertification.getColleId())
                    .majorId(externalCertification.getMajorId())
                    .createBy(SecurityUtils.getUsername())
                    .createTime(new Date())
                    .build();
            externalCertificationMapper.insertCla(build);
            externalCertification.setClassId(build.getId());
        }else {
            externalCertification.setClassId(claIds.get(0));
        }
    }
}
