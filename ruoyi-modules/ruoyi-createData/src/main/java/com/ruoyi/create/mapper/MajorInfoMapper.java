package com.ruoyi.create.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.create.domain.MajorInfo;

/**
 * 专业信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
public interface MajorInfoMapper extends BaseMapper<MajorInfo>
{
    /**
     * 查询专业信息
     *
     * @param id 专业信息主键
     * @return 专业信息
     */
    public MajorInfo selectMajorInfoById(Long id);

    /**
     * 查询专业信息列表
     *
     * @param majorInfo 专业信息
     * @return 专业信息集合
     */
    public List<MajorInfo> selectMajorInfoList(MajorInfo majorInfo);

    /**
     * 新增专业信息
     *
     * @param majorInfo 专业信息
     * @return 结果
     */
    public int insertMajorInfo(MajorInfo majorInfo);

    /**
     * 修改专业信息
     *
     * @param majorInfo 专业信息
     * @return 结果
     */
    public int updateMajorInfo(MajorInfo majorInfo);

    /**
     * 删除专业信息
     *
     * @param id 专业信息主键
     * @return 结果
     */
    public int deleteMajorInfoById(Long id);

    /**
     * 批量删除专业信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMajorInfoByIds(Long[] ids);
}
