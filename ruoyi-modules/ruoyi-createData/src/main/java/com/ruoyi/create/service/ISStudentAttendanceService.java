package com.ruoyi.create.service;

import com.ruoyi.create.domain.SStudentAttendance;

import java.util.List;

/**
 * 学生签到历史Service接口
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
public interface ISStudentAttendanceService
{
    /**
     * 查询学生签到历史
     *
     * @param id 学生签到历史主键
     * @return 学生签到历史
     */
    public SStudentAttendance selectSStudentAttendanceById(Long id);

    /**
     * 查询学生签到历史列表
     *
     * @param sStudentAttendance 学生签到历史
     * @return 学生签到历史集合
     */
    public List<SStudentAttendance> selectSStudentAttendanceList(SStudentAttendance sStudentAttendance);

    /**
     * 新增学生签到历史
     *
     * @param sStudentAttendance 学生签到历史
     * @return 结果
     */
    public int insertSStudentAttendance(SStudentAttendance sStudentAttendance);

    /**
     * 修改学生签到历史
     *
     * @param sStudentAttendance 学生签到历史
     * @return 结果
     */
    public int updateSStudentAttendance(SStudentAttendance sStudentAttendance);

    /**
     * 批量删除学生签到历史
     *
     * @param ids 需要删除的学生签到历史主键集合
     * @return 结果
     */
    public int deleteSStudentAttendanceByIds(Long[] ids);

    /**
     * 删除学生签到历史信息
     *
     * @param id 学生签到历史主键
     * @return 结果
     */
    public int deleteSStudentAttendanceById(Long id);
}
