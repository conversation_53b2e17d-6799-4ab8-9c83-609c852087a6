package com.ruoyi.create.mapper;

import java.util.List;
import com.ruoyi.create.domain.ClassStudentCourseReport;
import org.apache.ibatis.annotations.Param;

/**
 * 课程班级信息统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-10
 */
public interface ClassStudentCourseReportMapper 
{
    /**
     * 查询课程班级信息统计
     * 
     * @param id 课程班级信息统计主键
     * @return 课程班级信息统计
     */
    public ClassStudentCourseReport selectClassStudentCourseReportById(Long id);

    /**
     * 查询课程班级信息统计列表
     * 
     * @param classStudentCourseReport 课程班级信息统计
     * @return 课程班级信息统计集合
     */
    public List<ClassStudentCourseReport> selectClassStudentCourseReportList(ClassStudentCourseReport classStudentCourseReport);

    /**
     * 新增课程班级信息统计
     * 
     * @param classStudentCourseReport 课程班级信息统计
     * @return 结果
     */
    public int insertClassStudentCourseReport(ClassStudentCourseReport classStudentCourseReport);

    /**
     * 修改课程班级信息统计
     * 
     * @param classStudentCourseReport 课程班级信息统计
     * @return 结果
     */
    public int updateClassStudentCourseReport(ClassStudentCourseReport classStudentCourseReport);

    /**
     * 删除课程班级信息统计
     * 
     * @param id 课程班级信息统计主键
     * @return 结果
     */
    public int deleteClassStudentCourseReportById(Long id);

    /**
     * 批量删除课程班级信息统计
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteClassStudentCourseReportByIds(Long[] ids);
	
	ClassStudentCourseReport getSingleCourseAnalysis(ClassStudentCourseReport courseReport);

	List<ClassStudentCourseReport> selectCourseReportInList(@Param("collectStudentId") List<String> collectStudentId);

	ClassStudentCourseReport getClassCourseAnalysis(ClassStudentCourseReport courseReport);
}
