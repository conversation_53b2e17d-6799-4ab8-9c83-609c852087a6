package com.ruoyi.create.service.impl;

import com.ruoyi.create.domain.KnowledgeNumber;
import com.ruoyi.create.mapper.KnowledgeNumberMapper;
import com.ruoyi.create.mapper.MappingKnowledgeMapper;
import com.ruoyi.create.service.MappingKnowledgeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
@Service
public class MappingKnowledgeServiceImpl implements MappingKnowledgeService {

    @Autowired
    private MappingKnowledgeMapper mappingKnowledgeMapper;
    @Autowired
    private KnowledgeNumberMapper knowledgeNumberMapper;
    @Override
    public void CountMappingKnowledge() {
        //定义时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        //去计算s_textbook_keyword_analysis中的具体知识点数
        int number =mappingKnowledgeMapper.CountMappingKnowledge();
        //然后拿出s_knowledge_number中的数据
        List<KnowledgeNumber> knowledgeNumber=knowledgeNumberMapper.selectKnowledgeNumber();
        //对比其中最大的月份是不是今天这个月
        for (KnowledgeNumber s:knowledgeNumber){
            //如果是今天这个月，那么就把这个月的数据改成最新的知识点数
            if (s.getYearMonth().equals(LocalDate.now().format(formatter))){
                s.setKnowledgeNumber(number);
                knowledgeNumberMapper.postKnowledgeNumber(s);
                return;
            }
        }
        //如果没返回代表没有这个月，那么就新增这个月数据
        KnowledgeNumber knowledgeNumber2 =new KnowledgeNumber();
        knowledgeNumber2.setKnowledgeNumber(number);
        knowledgeNumber2.setYearMonth(LocalDate.now().format(formatter));
        knowledgeNumberMapper.insertKnowledgeNumber(knowledgeNumber2);
        //并且删除掉最小的月份数据
        KnowledgeNumber oldestYearMonth = new KnowledgeNumber();
        oldestYearMonth.setYearMonth(LocalDate.now().format(formatter));
        for (KnowledgeNumber s:knowledgeNumber){

        if (s.getYearMonth().compareTo(oldestYearMonth.getYearMonth())<0){

            oldestYearMonth.setYearMonth(s.getYearMonth());

        }

        }
        knowledgeNumberMapper.deleteKnowledgeNumberByYearMonth(oldestYearMonth.getYearMonth());

    }
}

