package com.ruoyi.create.mapper;


import com.ruoyi.create.domain.ImageStorage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 图片存储Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-06
 */
public interface ImageStorageMapper 
{
    /**
     * 查询图片存储
     * 
     * @param id 图片存储主键
     * @return 图片存储
     */
    public ImageStorage selectImageStorageById(Long id);

    /**
     * 查询图片存储列表
     * 
     * @param imageStorage 图片存储
     * @return 图片存储集合
     */
    public List<ImageStorage> selectImageStorageList(ImageStorage imageStorage);

    /**
     * 新增图片存储
     * 
     * @param imageStorage 图片存储
     * @return 结果
     */
    public int insertImageStorage(ImageStorage imageStorage);

    /**
     * 修改图片存储
     * 
     * @param imageStorage 图片存储
     * @return 结果
     */
    public int updateImageStorage(ImageStorage imageStorage);

    /**
     * 删除图片存储
     * 
     * @param id 图片存储主键
     * @return 结果
     */
    public int deleteImageStorageById(Long id);

    /**
     * 批量删除图片存储
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteImageStorageByIds(Long[] ids);

    public int countImageStorage(@Param("imageNumber") String imageNumber);

    ImageStorage selectImageStorageByImageNumber(@Param("imageNumber")String imageStorage);
}
