package com.ruoyi.create.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.CareCall;
import com.ruoyi.create.service.ICareCallService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 服务调用统计Controller
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
@RestController
@RequestMapping("/call")
public class CareCallController extends BaseController
{
    @Autowired
    private ICareCallService careCallService;

    /**
     * 查询服务调用统计列表
     */
    @RequiresPermissions("create:call:list")
    @GetMapping("/list")
    public TableDataInfo list(CareCall careCall)
    {
        startPage();
        List<CareCall> list = careCallService.selectCareCallList(careCall);
        return getDataTable(list);
    }

    /**
     * 导出服务调用统计列表
     */
    @RequiresPermissions("create:call:export")
    @Log(title = "服务调用统计", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CareCall careCall)
    {
        List<CareCall> list = careCallService.selectCareCallList(careCall);
        ExcelUtil<CareCall> util = new ExcelUtil<CareCall>(CareCall.class);
        util.exportExcel(response, list, "服务调用统计数据");
    }

    /**
     * 获取服务调用统计详细信息
     */
    @RequiresPermissions("create:call:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(careCallService.selectCareCallById(id));
    }

    /**
     * 新增服务调用统计
     */
    @RequiresPermissions("create:call:add")
    @Log(title = "服务调用统计", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CareCall careCall)
    {
        return toAjax(careCallService.insertCareCall(careCall));
    }

    /**
     * 修改服务调用统计
     */
    @RequiresPermissions("create:call:edit")
    @Log(title = "服务调用统计", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CareCall careCall)
    {
        return toAjax(careCallService.updateCareCall(careCall));
    }

    /**
     * 删除服务调用统计
     */
    @RequiresPermissions("create:call:remove")
    @Log(title = "服务调用统计", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(careCallService.deleteCareCallByIds(ids));
    }
}
