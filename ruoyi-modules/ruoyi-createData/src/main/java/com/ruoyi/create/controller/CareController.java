package com.ruoyi.create.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.Care;
import com.ruoyi.create.service.ICareService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 服务Controller
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
@RestController
@RequestMapping("/care")
public class CareController extends BaseController
{
    @Autowired
    private ICareService careService;

    /**
     * 查询服务列表
     */
    @RequiresPermissions("create:care:list")
    @GetMapping("/list")
    public TableDataInfo list(Care care)
    {
        startPage();
        List<Care> list = careService.selectCareList(care);
        return getDataTable(list);
    }

    /**
     * 导出服务列表
     */
    @RequiresPermissions("create:care:export")
    @Log(title = "服务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Care care)
    {
        List<Care> list = careService.selectCareList(care);
        ExcelUtil<Care> util = new ExcelUtil<Care>(Care.class);
        util.exportExcel(response, list, "服务数据");
    }

    /**
     * 获取服务详细信息
     */
    @RequiresPermissions("create:care:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(careService.selectCareById(id));
    }

    /**
     * 新增服务
     */
    @RequiresPermissions("create:care:add")
    @Log(title = "服务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Care care)
    {
        return toAjax(careService.insertCare(care));
    }

    /**
     * 修改服务
     */
    @RequiresPermissions("create:care:edit")
    @Log(title = "服务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Care care)
    {
        return toAjax(careService.updateCare(care));
    }

    /**
     * 删除服务
     */
    @RequiresPermissions("create:care:remove")
    @Log(title = "服务", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(careService.deleteCareByIds(ids));
    }
}
