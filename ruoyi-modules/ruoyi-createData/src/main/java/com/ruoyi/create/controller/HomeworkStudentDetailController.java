package com.ruoyi.create.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.create.domain.Homework;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.HomeworkStudentDetail;
import com.ruoyi.create.service.IHomeworkStudentDetailService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 学生作业答案详情Controller
 * 
 * <AUTHOR>
 * @date 2024-06-07
 */
@RestController
@RequestMapping("/detail")
public class HomeworkStudentDetailController extends BaseController
{
    @Autowired
    private IHomeworkStudentDetailService homeworkStudentDetailService;

    /**
     * 查询学生作业答案详情列表
     */
    @RequiresPermissions("create:detail:list")
    @GetMapping("/list")
    public TableDataInfo list(HomeworkStudentDetail homeworkStudentDetail)
    {
        startPage();
        List<HomeworkStudentDetail> list = homeworkStudentDetailService.selectHomeworkStudentDetailList(homeworkStudentDetail);
        return getDataTable(list);
    }

    /**
     * 导出学生作业答案详情列表
     */
    @RequiresPermissions("create:detail:export")
    @Log(title = "学生作业答案详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HomeworkStudentDetail homeworkStudentDetail)
    {
        List<HomeworkStudentDetail> list = homeworkStudentDetailService.selectHomeworkStudentDetailList(homeworkStudentDetail);
        ExcelUtil<HomeworkStudentDetail> util = new ExcelUtil<HomeworkStudentDetail>(HomeworkStudentDetail.class);
        util.exportExcel(response, list, "学生作业答案详情数据");
    }

    /**
     * 获取学生作业答案详情详细信息
     */
    @RequiresPermissions("create:detail:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(homeworkStudentDetailService.selectHomeworkStudentDetailById(id));
    }

    /**
     * 新增学生作业答案详情
     */
    @RequiresPermissions("create:detail:add")
    @Log(title = "学生作业答案详情", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HomeworkStudentDetail homeworkStudentDetail)
    {
        return toAjax(homeworkStudentDetailService.insertHomeworkStudentDetail(homeworkStudentDetail));
    }

    /**
     * 新增学生作业答案详情
     */
    @RequiresPermissions("create:detail:add")
    @Log(title = "学生作业答案详情", businessType = BusinessType.INSERT)
    @PostMapping("/addDetailList")
    public AjaxResult addDetailList(@RequestBody List<HomeworkStudentDetail> homeworkStudentDetail)
    {
        return homeworkStudentDetailService.insertHomeworkStudentDetailList(homeworkStudentDetail);
    }
    /**
     * 修改学生作业答案详情
     */
    @RequiresPermissions("create:detail:edit")
    @Log(title = "学生作业答案详情", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HomeworkStudentDetail homeworkStudentDetail)
    {
        return toAjax(homeworkStudentDetailService.updateHomeworkStudentDetail(homeworkStudentDetail));
    }

    /**
     * 删除学生作业答案详情
     */
    @RequiresPermissions("create:detail:remove")
    @Log(title = "学生作业答案详情", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(homeworkStudentDetailService.deleteHomeworkStudentDetailByIds(ids));
    }
    /**
     * 查询学生作业答案详情列表
     */
    @RequiresPermissions("create:detail:list")
    @PostMapping("/all")
    public AjaxResult selectHomeworkStudentDetailAll(@RequestBody HomeworkStudentDetail homeworkStudentDetail)
    {
        List<HomeworkStudentDetail> list = homeworkStudentDetailService.selectHomeworkStudentDetailListAll(homeworkStudentDetail);
        return success(list);
    }
}
