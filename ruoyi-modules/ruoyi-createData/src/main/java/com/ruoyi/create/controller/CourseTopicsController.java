package com.ruoyi.create.controller;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.create.service.ICourseTopicsService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.create.domain.CourseTopics;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 课程讨论话题Controller
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
@RestController
@RequestMapping("/courseTopics")
public class CourseTopicsController extends BaseController {
    @Resource
    private ICourseTopicsService courseTopicsService;

    /**
     * 查询课程讨论话题列表
     */
    @GetMapping("/list")
    public TableDataInfo list(CourseTopics courseTopics) {
        startPage();
        List<CourseTopics> list = courseTopicsService.selectCourseTopicsList(courseTopics);
        return getDataTable(list);
    }

    @GetMapping("/list2")
    public TableDataInfo list2(CourseTopics courseTopics) {
        startPage();
        List<CourseTopics> list = courseTopicsService.selectCourseTopicsListCondition(courseTopics);
        return getDataTable(list);
    }

    /**
     * 导出课程讨论话题列表
     */
    @Log(title = "课程讨论话题", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CourseTopics courseTopics) {
        List<CourseTopics> list = courseTopicsService.selectCourseTopicsList(courseTopics);
        ExcelUtil<CourseTopics> util = new ExcelUtil<CourseTopics>(CourseTopics.class);
        util.exportExcel(response, list, "课程讨论话题数据");
    }

    /**
     * 获取课程讨论话题详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(courseTopicsService.selectCourseTopicsById(id));
    }


    /**
     * 新增课程讨论话题
     */
    @Log(title = "课程讨论话题", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CourseTopics courseTopics) {
        return toAjax(courseTopicsService.insertCourseTopics(courseTopics));
    }

    /**
     * 修改课程讨论话题
     */
    @Log(title = "课程讨论话题", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CourseTopics courseTopics) {
        return toAjax(courseTopicsService.updateCourseTopics(courseTopics));
    }

    /**
     * 删除课程讨论话题
     */
    @Log(title = "课程讨论话题", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(courseTopicsService.deleteCourseTopicsAndReplaceByIds(ids));
    }
}
