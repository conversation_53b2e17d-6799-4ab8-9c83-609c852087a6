package com.ruoyi.create.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.domain.*;
import com.ruoyi.create.mapper.ClassInfoMapper;
import com.ruoyi.create.mapper.SCourseClassMapper;
import com.ruoyi.create.mapper.StudentSignMapper;
import com.ruoyi.create.service.ISCourseClassService;
import com.ruoyi.create.utils.Snowflake;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.StudentInfo;
import com.ruoyi.system.api.model.LoginUser;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 课程班级关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
@Service
public class SCourseClassServiceImpl implements ISCourseClassService
{
    @Resource
    private SCourseClassMapper sCourseClassMapper;

    @Resource
    private StudentSignMapper studentSignMapper;

    @Resource
    private RemoteUserService remoteUserService;




    /**
     * 查询课程班级关联
     *
     * @param id 课程班级关联主键
     * @return 课程班级关联
     */
    @Override
    public SCourseClass selectSCourseClassById(Long id)
    {
        return sCourseClassMapper.selectSCourseClassById(id);
    }

    /**
     * 查询课程班级关联列表
     *
     * @param attendanceParam 课程班级关联
     * @return 课程班级关联
     */
    @Override
    public List<AttendanceParam> selectSCourseClassList(AttendanceParam attendanceParam)
    {
        return sCourseClassMapper.selectSCourseClassList(attendanceParam);
    }

    /**
     * 发起签到
     *
     * @param attendanceParam 发起签到表
     * @return 结果
     */
    @Override
    public int initiateAttendance(AttendanceParam attendanceParam)
    {
        R<LoginUser> userAndRole = remoteUserService.getUserAndRole(SecurityUtils.getUserId(), SecurityConstants.INNER);
        LoginUser data2 = userAndRole.getData();
        Snowflake snowflake = new Snowflake(1, 1);
        long id = snowflake.generateId();
        //工号
        String jobId = data2.getSysUser().getJobId();
        attendanceParam.setTeacherId(jobId);
        attendanceParam.setId(id);
        String signType = sCourseClassMapper.selectSignType(attendanceParam);
        attendanceParam.setCourseType(signType);
        attendanceParam.setIsAttendance("1");
        attendanceParam.setStartTime(attendanceParam.getStartTime());
        attendanceParam.setEndTime(calculateEndTime(attendanceParam.getStartTime(),attendanceParam.getValidity()));
        attendanceParam.setCreateTime(DateUtils.getNowDate());
        attendanceParam.setCreateBy(SecurityUtils.getUsername());
        List<String> signClassList = attendanceParam.getSignClassList();
        if(signClassList!=null && !"".equals(signClassList)){
            for(int i=0;i<signClassList.size();i++){
                attendanceParam.setSignClass(signClassList.get(i));
                sCourseClassMapper.insertStartSignInSession(attendanceParam);
                StudentSign studentSign = new StudentSign();
                studentSign.setTeacherId(attendanceParam.getTeacherId());
                studentSign.setSignClass(attendanceParam.getSignClass());
                studentSign.setCourseName(attendanceParam.getCourseName());
                studentSign.setTerm(attendanceParam.getTerm());
                studentSign.setCourseType(attendanceParam.getCourseType());
                studentSign.setStartTime(attendanceParam.getStartTime());
                studentSign.setEndTime(attendanceParam.getEndTime());
                // 查询在此班上课的所有学生
                List<CourseManagement> StudentList = studentSignMapper.selectStudentIdList(studentSign);
                if(StudentList!=null){
                    for(int k=0;k<StudentList.size();k++){
                        // 根据学号查询班级
                        String studentId = StudentList.get(k).getStudentId();
                        String className = studentSignMapper.selectClassByStudentId(studentId);
                        if(className.equals(attendanceParam.getSignClass())){
                            studentSign.setStudentId(StudentList.get(k).getStudentId());
                            studentSign.setSignId(id);
                            studentSignMapper.insertStudentInfo(studentSign);
                        }
                    }
                }
            }
        }
        return 1;
    }

    // 计算结束时间，返回 Date 类型
    public static String calculateEndTime(String startTimeStr, int duration) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            Date startTime = sdf.parse(startTimeStr);
            Date endTime = new Date(startTime.getTime() + duration * 60 * 1000);

            String endTimeStr = sdf.format(endTime);
            return endTimeStr;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }
}
