package com.ruoyi.create.service.impl;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.domain.UserVoiceType;
import com.ruoyi.create.mapper.UserVoiceTypeMapper;
import com.ruoyi.create.service.IUserVoiceTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 用户语音切换
 * 
 * <AUTHOR> @create 2024-09-13-14:57
 */
@Service
public class UserVoiceTypeServiceImpl  implements IUserVoiceTypeService {

    @Autowired
    private UserVoiceTypeMapper userVoiceTypeMapper;

    /**
     * 查询用户语音
     *
     * @param id 用户语音主键
     * @return 用户语音
     */
    @Override
    public UserVoiceType selectUserVoiceTypeById(Long id)
    {
        return userVoiceTypeMapper.selectUserVoiceTypeById(id);
    }

    /**
     * 查询用户语音列表
     *
     * @param userVoiceType 用户语音
     * @return 用户语音
     */
    @Override
    public List<UserVoiceType> selectUserVoiceTypeList(UserVoiceType userVoiceType)
    {
        return userVoiceTypeMapper.selectUserVoiceTypeList(userVoiceType);
    }

    /**
     * 新增用户语音
     *
     * @param userVoiceType 用户语音
     * @return 结果
     */
    @Override
    public int insertUserVoiceType(UserVoiceType userVoiceType)
    {
        userVoiceType.setCreateTime(DateUtils.getNowDate());
        return userVoiceTypeMapper.insertUserVoiceType(userVoiceType);
    }

    /**
     * 修改用户语音
     *
     * @param userVoiceType 用户语音
     * @return 结果
     */
    @Override
    public int updateUserVoiceType(UserVoiceType userVoiceType)
    {
        userVoiceType.setUpdateTime(DateUtils.getNowDate());
        return userVoiceTypeMapper.updateUserVoiceType(userVoiceType);
    }

    /**
     * 批量删除用户语音
     *
     * @param ids 需要删除的用户语音主键
     * @return 结果
     */
    @Override
    public int deleteUserVoiceTypeByIds(Long[] ids)
    {
        return userVoiceTypeMapper.deleteUserVoiceTypeByIds(ids);
    }

    /**
     * 删除用户语音信息
     *
     * @param id 用户语音主键
     * @return 结果
     */
    @Override
    public int deleteUserVoiceTypeById(Long id)
    {
        return userVoiceTypeMapper.deleteUserVoiceTypeById(id);
    }

    /**
     * 根据登陆人存储语音种类
     *
     * @param userVoiceType 用户语音
     * @return 结果
     */
    @Override
    @Transactional
    public int updateUserVoiceTypeByStorage(UserVoiceType userVoiceType) {

        UserVoiceType userVoiceType1 = userVoiceTypeMapper.selectUserVoiceTypeByUserName(SecurityUtils.getUsername());
        if(userVoiceType1!=null){
            userVoiceType1.setVoiceType(userVoiceType.getVoiceType());
            return userVoiceTypeMapper.updateUserVoiceTypeByUserNameTmp(userVoiceType1.getUserName(),userVoiceType1.getVoiceType());
        }else {
            userVoiceType.setUserName(SecurityUtils.getUsername());
            return userVoiceTypeMapper.insertUserVoiceType(userVoiceType);
        }
    }

    @Override
    public UserVoiceType selectUserVoiceTypeByUserName(String username) {
        UserVoiceType userVoiceType = userVoiceTypeMapper.selectUserVoiceTypeByUserName(username);

        return userVoiceType;
    }

}
