package com.ruoyi.create.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.create.domain.HomeworkQuestionReq;
import com.ruoyi.create.mapper.HomeworkQuestionReqMapper;
import com.ruoyi.create.service.IHomeworkQuestionReqService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 作业题型要求明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@Service
public class HomeworkQuestionReqServiceImpl extends ServiceImpl<HomeworkQuestionReqMapper, HomeworkQuestionReq> implements IHomeworkQuestionReqService
{
    @Autowired
    private HomeworkQuestionReqMapper homeworkQuestionReqMapper;

    /**
     * 查询作业题型要求明细
     *
     * @param id 作业题型要求明细主键
     * @return 作业题型要求明细
     */
    @Override
    public HomeworkQuestionReq selectHomeworkQuestionReqById(Long id)
    {
        return homeworkQuestionReqMapper.selectHomeworkQuestionReqById(id);
    }

    /**
     * 查询作业题型要求明细列表
     *
     * @param homeworkQuestionReq 作业题型要求明细
     * @return 作业题型要求明细
     */
    @Override
    public List<HomeworkQuestionReq> selectHomeworkQuestionReqList(HomeworkQuestionReq homeworkQuestionReq)
    {
        return homeworkQuestionReqMapper.selectHomeworkQuestionReqList(homeworkQuestionReq);
    }

    /**
     * 新增作业题型要求明细
     *
     * @param homeworkQuestionReq 作业题型要求明细
     * @return 结果
     */
    @Override
    public int insertHomeworkQuestionReq(HomeworkQuestionReq homeworkQuestionReq)
    {
        homeworkQuestionReq.setCreateTime(DateUtils.getNowDate());
        return homeworkQuestionReqMapper.insertHomeworkQuestionReq(homeworkQuestionReq);
    }

    /**
     * 修改作业题型要求明细
     *
     * @param homeworkQuestionReq 作业题型要求明细
     * @return 结果
     */
    @Override
    public int updateHomeworkQuestionReq(HomeworkQuestionReq homeworkQuestionReq)
    {
        homeworkQuestionReq.setUpdateTime(DateUtils.getNowDate());
        return homeworkQuestionReqMapper.updateHomeworkQuestionReq(homeworkQuestionReq);
    }

    /**
     * 批量删除作业题型要求明细
     *
     * @param ids 需要删除的作业题型要求明细主键
     * @return 结果
     */
    @Override
    public int deleteHomeworkQuestionReqByIds(Long[] ids)
    {
        return homeworkQuestionReqMapper.deleteHomeworkQuestionReqByIds(ids);
    }

    /**
     * 删除作业题型要求明细信息
     *
     * @param id 作业题型要求明细主键
     * @return 结果
     */
    @Override
    public int deleteHomeworkQuestionReqById(Long id)
    {
        return homeworkQuestionReqMapper.deleteHomeworkQuestionReqById(id);
    }
}
