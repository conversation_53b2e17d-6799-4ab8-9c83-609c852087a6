package com.ruoyi.create.service.impl;

import java.util.List;

import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.CourseKikeStepMapper;
import com.ruoyi.create.domain.CourseKikeStep;
import com.ruoyi.create.service.ICourseKikeStepService;

/**
 * 课程讨论话题回复点赞Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-05
 */
@Service
public class CourseKikeStepServiceImpl implements ICourseKikeStepService 
{
    @Autowired
    private CourseKikeStepMapper courseKikeStepMapper;

    /**
     * 查询课程讨论话题回复点赞
     * 
     * @param id 课程讨论话题回复点赞主键
     * @return 课程讨论话题回复点赞
     */
    @Override
    public CourseKikeStep selectCourseKikeStepById(Long id)
    {
        return courseKikeStepMapper.selectCourseKikeStepById(id);
    }

    /**
     * 查询课程讨论话题回复点赞列表
     * 
     * @param courseKikeStep 课程讨论话题回复点赞
     * @return 课程讨论话题回复点赞
     */
    @Override
    public List<CourseKikeStep> selectCourseKikeStepList(CourseKikeStep courseKikeStep)
    {
        return courseKikeStepMapper.selectCourseKikeStepList(courseKikeStep);
    }

    /**
     * 新增课程讨论话题回复点赞
     * 
     * @param courseKikeStep 课程讨论话题回复点赞
     * @return 结果
     */
    @Override
    public int insertCourseKikeStep(CourseKikeStep courseKikeStep)
    {
        courseKikeStep.setCreateBy(SecurityUtils.getUsername());
        courseKikeStepMapper.deleteCourseKikeStep(courseKikeStep);
        return courseKikeStepMapper.insertCourseKikeStep(courseKikeStep);
    }

    /**
     * 修改课程讨论话题回复点赞
     * 
     * @param courseKikeStep 课程讨论话题回复点赞
     * @return 结果
     */
    @Override
    public int updateCourseKikeStep(CourseKikeStep courseKikeStep)
    {
        return courseKikeStepMapper.updateCourseKikeStep(courseKikeStep);
    }

    /**
     * 批量删除课程讨论话题回复点赞
     * 
     * @param ids 需要删除的课程讨论话题回复点赞主键
     * @return 结果
     */
    @Override
    public int deleteCourseKikeStepByIds(Long[] ids)
    {
        return courseKikeStepMapper.deleteCourseKikeStepByIds(ids);
    }

    /**
     * 删除课程讨论话题回复点赞信息
     * 
     * @param msgId 课程讨论话题回复点赞主键
     * @return 结果
     */
    @Override
    public int deleteCourseKikeStepById(Long msgId)
    {
        CourseKikeStep courseKikeStep = new CourseKikeStep();
        courseKikeStep.setMsgId(msgId);
        courseKikeStep.setCreateBy(SecurityUtils.getUsername());
        return courseKikeStepMapper.deleteCourseKikeStep(courseKikeStep);
    }
}
