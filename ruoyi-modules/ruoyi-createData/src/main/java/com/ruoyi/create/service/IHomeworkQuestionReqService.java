package com.ruoyi.create.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.create.domain.HomeworkQuestionReq;

import java.util.List;

/**
 * 作业题型要求明细Service接口
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
public interface IHomeworkQuestionReqService extends IService<HomeworkQuestionReq>
{
    /**
     * 查询作业题型要求明细
     *
     * @param id 作业题型要求明细主键
     * @return 作业题型要求明细
     */
    public HomeworkQuestionReq selectHomeworkQuestionReqById(Long id);

    /**
     * 查询作业题型要求明细列表
     *
     * @param homeworkQuestionReq 作业题型要求明细
     * @return 作业题型要求明细集合
     */
    public List<HomeworkQuestionReq> selectHomeworkQuestionReqList(HomeworkQuestionReq homeworkQuestionReq);

    /**
     * 新增作业题型要求明细
     *
     * @param homeworkQuestionReq 作业题型要求明细
     * @return 结果
     */
    public int insertHomeworkQuestionReq(HomeworkQuestionReq homeworkQuestionReq);

    /**
     * 修改作业题型要求明细
     *
     * @param homeworkQuestionReq 作业题型要求明细
     * @return 结果
     */
    public int updateHomeworkQuestionReq(HomeworkQuestionReq homeworkQuestionReq);

    /**
     * 批量删除作业题型要求明细
     *
     * @param ids 需要删除的作业题型要求明细主键集合
     * @return 结果
     */
    public int deleteHomeworkQuestionReqByIds(Long[] ids);

    /**
     * 删除作业题型要求明细信息
     *
     * @param id 作业题型要求明细主键
     * @return 结果
     */
    public int deleteHomeworkQuestionReqById(Long id);
}
