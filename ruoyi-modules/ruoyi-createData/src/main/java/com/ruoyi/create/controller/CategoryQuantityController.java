package com.ruoyi.create.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.CategoryQuantity;
import com.ruoyi.create.service.ICategoryQuantityService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 文献整理-类别统计Controller
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@RestController
@RequestMapping("/quantity")
public class CategoryQuantityController extends BaseController
{
    @Autowired
    private ICategoryQuantityService categoryQuantityService;

    /**
     * 查询文献整理-类别统计列表
     */
    @RequiresPermissions("create:quantity:list")
    @GetMapping("/list")
    public TableDataInfo list(CategoryQuantity categoryQuantity)
    {
        startPage();
        List<CategoryQuantity> list = categoryQuantityService.selectCategoryQuantityList(categoryQuantity);
        return getDataTable(list);
    }

    /**
     * 导出文献整理-类别统计列表
     */
    @RequiresPermissions("create:quantity:export")
    @Log(title = "文献整理-类别统计", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CategoryQuantity categoryQuantity)
    {
        List<CategoryQuantity> list = categoryQuantityService.selectCategoryQuantityList(categoryQuantity);
        ExcelUtil<CategoryQuantity> util = new ExcelUtil<CategoryQuantity>(CategoryQuantity.class);
        util.exportExcel(response, list, "文献整理-类别统计数据");
    }

    /**
     * 获取文献整理-类别统计详细信息
     */
    @RequiresPermissions("create:quantity:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(categoryQuantityService.selectCategoryQuantityById(id));
    }

    /**
     * 新增文献整理-类别统计
     */
    @RequiresPermissions("create:quantity:add")
    @Log(title = "文献整理-类别统计", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CategoryQuantity categoryQuantity)
    {
        return toAjax(categoryQuantityService.insertCategoryQuantity(categoryQuantity));
    }

    /**
     * 修改文献整理-类别统计
     */
    @RequiresPermissions("create:quantity:edit")
    @Log(title = "文献整理-类别统计", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CategoryQuantity categoryQuantity)
    {
        return toAjax(categoryQuantityService.updateCategoryQuantity(categoryQuantity));
    }

    /**
     * 删除文献整理-类别统计
     */
    @RequiresPermissions("create:quantity:remove")
    @Log(title = "文献整理-类别统计", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(categoryQuantityService.deleteCategoryQuantityByIds(ids));
    }

    /**
     * 查询文献类别名称、数量
     */
    @RequiresPermissions("create:quantity:list")
    @PostMapping("/getCount")
    public AjaxResult selectDataSetAll(String title)
    {
        List<CategoryQuantity> list = categoryQuantityService.selectCategoryCount(title);
        return success(list);
    }
}
