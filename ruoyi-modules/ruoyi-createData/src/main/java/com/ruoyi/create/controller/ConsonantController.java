package com.ruoyi.create.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.create.domain.PlatParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.Consonant;
import com.ruoyi.create.service.IConsonantService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 讲演稿声母韵母Controller
 *
 * <AUTHOR>
 * @date 2024-08-26
 */
@RestController
@RequestMapping("/consonant")
public class ConsonantController extends BaseController
{
    @Autowired
    private IConsonantService consonantService;



    /**
     * 查询讲演稿声母韵母列表
     */
    //@RequiresPermissions("create:consonant:list")
    @GetMapping("/list")
    public TableDataInfo list(Consonant consonant)
    {
        startPage();
        List<Consonant> list = consonantService.selectConsonantList(consonant);
        return getDataTable(list);
    }

    /**
     * 导出讲演稿声母韵母列表
     */
    //@RequiresPermissions("create:consonant:export")
    @Log(title = "讲演稿声母韵母", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Consonant consonant)
    {
        List<Consonant> list = consonantService.selectConsonantList(consonant);
        ExcelUtil<Consonant> util = new ExcelUtil<Consonant>(Consonant.class);
        util.exportExcel(response, list, "讲演稿声母韵母数据");
    }

    /**
     * 获取讲演稿声母韵母详细信息
     */
    //@RequiresPermissions("create:consonant:query")
    @GetMapping(value = "/info/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(consonantService.selectConsonantById(id));
    }

    /**
     * 新增讲演稿声母韵母
     */
    //@RequiresPermissions("create:consonant:add")
    @Log(title = "讲演稿声母韵母", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody Consonant consonant)
    {
        return toAjax(consonantService.insertConsonant(consonant));
    }

    /**
     * 修改讲演稿声母韵母
     */
    //@RequiresPermissions("create:consonant:edit")
    @Log(title = "讲演稿声母韵母", businessType = BusinessType.UPDATE)
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody Consonant consonant)
    {
        return toAjax(consonantService.updateConsonant(consonant));
    }

    /**
     * 删除讲演稿声母韵母
     */
    //@RequiresPermissions("create:consonant:remove")
    @Log(title = "讲演稿声母韵母", businessType = BusinessType.DELETE)
	@DeleteMapping("/del/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(consonantService.deleteConsonantByIds(ids));
    }


    // @RequiresPermissions("create:studyrecord:list")
    @Log(title = "获取Consonant", businessType = BusinessType.INSERT)
    @PostMapping("/getConsonant")
    public AjaxResult getDigitalHuman(@RequestBody PlatParam platParam)
    {
        return AjaxResult.success(consonantService.getConsonant(platParam));
    }


    //@RequiresPermissions("create:consonant:add")
    @Log(title = "讲演稿声母韵母", businessType = BusinessType.INSERT)
    @PostMapping("/addConsonant")
    public AjaxResult getAndAdd(@RequestBody PlatParam platParam)
    {
        PlatParam platParamTo = consonantService.getPlatParam(platParam);
        return toAjax(consonantService.insertConsonant(consonantService.getConsonant(platParamTo)));
    }

    @Log(title = "讲演稿声母韵母", businessType = BusinessType.INSERT)
    @GetMapping("/ceshi")
    public AjaxResult ceshi(String speechdraftPath)
    {

        return success(consonantService.getPptSpeechDraft(speechdraftPath));
    }

}
