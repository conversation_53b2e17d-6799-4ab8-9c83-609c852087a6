package com.ruoyi.create.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.AttendanceParam;
import com.ruoyi.create.domain.SCourseClass;
import com.ruoyi.create.service.ISCourseClassService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 课程班级关联Controller
 *
 * <AUTHOR>
 * @date 2024-08-23
 */
@RestController
@RequestMapping("/courseClass")
public class SCourseClassController extends BaseController
{
    @Resource
    private ISCourseClassService sCourseClassService;

    /**
     * 查询教师发起签到列表
     */
//    @RequiresPermissions("create:courseClass:list")
    @GetMapping("/list")
    public TableDataInfo list(AttendanceParam attendanceParam)
    {
        startPage();
        List<AttendanceParam> list = sCourseClassService.selectSCourseClassList(attendanceParam);
        return getDataTable(list);
    }

    /**
     * 导出教师发起签到列表
     */
//    @RequiresPermissions("create:courseClass:export")
    @Log(title = "导出教师发起签到列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AttendanceParam attendanceParam)
    {
        List<AttendanceParam> list = sCourseClassService.selectSCourseClassList(attendanceParam);
        ExcelUtil<AttendanceParam> util = new ExcelUtil<AttendanceParam>(AttendanceParam.class);
        util.exportExcel(response, list, "教师发起签到列表");
    }

    /**
     * 获取教师发起签到详细信息
     */
//    @RequiresPermissions("create:courseClass:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sCourseClassService.selectSCourseClassById(id));
    }

    /**
     * 教师发起签到
     */
//    @RequiresPermissions("create:studentAttendance:initiateAttendance")
    @Log(title = "教师发起签到", businessType = BusinessType.OTHER)
    @PostMapping("/initiateAttendance")
    public AjaxResult initiateAttendance(@RequestBody AttendanceParam attendanceParam)
    {
        return toAjax(sCourseClassService.initiateAttendance(attendanceParam));
    }

}
