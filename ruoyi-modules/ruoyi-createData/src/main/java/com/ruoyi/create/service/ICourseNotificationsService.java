package com.ruoyi.create.service;

import java.util.List;
import com.ruoyi.create.domain.CourseNotifications;

/**
 * 课程通知Service接口
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
public interface ICourseNotificationsService
{
    /**
     * 查询课程通知
     *
     * @param id 课程通知主键
     * @return 课程通知
     */
    public CourseNotifications selectCourseNotificationsById(Long id);
    List<CourseNotifications> selectCourseNotificationsList2(CourseNotifications courseNotifications);
    /**
     * 查询课程通知列表
     *
     * @param courseNotifications 课程通知
     * @return 课程通知集合
     */
    public List<CourseNotifications> selectCourseNotificationsList(CourseNotifications courseNotifications);

    /**
     * 新增课程通知
     *
     * @param courseNotifications 课程通知
     * @return 结果
     */
    public int insertCourseNotifications(CourseNotifications courseNotifications);

    /**
     * 修改课程通知
     *
     * @param courseNotifications 课程通知
     * @return 结果
     */
    public int updateCourseNotifications(CourseNotifications courseNotifications);

    /**
     * 批量删除课程通知
     *
     * @param ids 需要删除的课程通知主键集合
     * @return 结果
     */
    public int deleteCourseNotificationsByIds(Long[] ids);

    /**
     * 删除课程通知信息
     *
     * @param id 课程通知主键
     * @return 结果
     */
    public int deleteCourseNotificationsById(Long id);


}
