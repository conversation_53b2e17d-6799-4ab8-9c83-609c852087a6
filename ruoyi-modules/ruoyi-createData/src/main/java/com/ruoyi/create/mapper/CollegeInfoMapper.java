package com.ruoyi.create.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.create.domain.CollegeInfo;

/**
 * 学院信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
public interface CollegeInfoMapper extends BaseMapper<CollegeInfo>
{
    /**
     * 查询学院信息
     *
     * @param id 学院信息主键
     * @return 学院信息
     */
    public CollegeInfo selectCollegeInfoById(Long id);

    /**
     * 查询学院信息列表
     *
     * @param collegeInfo 学院信息
     * @return 学院信息集合
     */
    public List<CollegeInfo> selectCollegeInfoList(CollegeInfo collegeInfo);

    /**
     * 新增学院信息
     *
     * @param collegeInfo 学院信息
     * @return 结果
     */
    public int insertCollegeInfo(CollegeInfo collegeInfo);

    /**
     * 修改学院信息
     *
     * @param collegeInfo 学院信息
     * @return 结果
     */
    public int updateCollegeInfo(CollegeInfo collegeInfo);

    /**
     * 删除学院信息
     *
     * @param id 学院信息主键
     * @return 结果
     */
    public int deleteCollegeInfoById(Long id);

    /**
     * 批量删除学院信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCollegeInfoByIds(Long[] ids);

    List<CollegeInfo> selectCollegeInfoAll();

    List<CollegeInfo> getAll();
}
