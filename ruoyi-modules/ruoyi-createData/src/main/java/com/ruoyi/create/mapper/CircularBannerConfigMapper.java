package com.ruoyi.create.mapper;

import com.ruoyi.create.domain.CircularBannerConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 学校轮播图配置Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-04
 */
public interface CircularBannerConfigMapper {

    /**
     * 查询学校轮播图配置列表
     *
     * @param circularBannerConfig 学校轮播图配置
     * @return 学校轮播图配置集合
     */
    public List<CircularBannerConfig> selectCircularBannerConfigList(CircularBannerConfig circularBannerConfig);

    /**
     * 查询学校轮播图配置
     *
     * @param id 学校轮播图配置主键
     * @return 学校轮播图配置
     */
    public CircularBannerConfig selectCircularBannerConfigById(Long id);

    /**
     * 修改学校轮播图配置
     *
     * @param circularBannerConfig 学校轮播图配置
     * @return 结果
     */
    public int updateCircularBannerConfig(CircularBannerConfig circularBannerConfig);

    /**
     * 删除学校轮播图配置
     *
     * @param id 学校轮播图配置主键
     * @return 结果
     */
    public int deleteCircularBannerConfigById(Long id);

    /**
     * 查询学校轮播图配置
     *
     * @param fileName 学校轮播图名称
     * @return 结果
     */
    public List<CircularBannerConfig> selectCircularBannerConfigByFileName(@Param("fileName") String fileName);

    /**
     * 增加学校轮播图配置
     *
     * @param circularBannerConfig 学校轮播图对象
     * @return 结果
     */
    public int insertCircularBannerConfig(CircularBannerConfig circularBannerConfig);

    /**
     * 获取所有图片的路径
     *
     * @return 结果
     */
    public List<CircularBannerConfig> selectCircularBannerConfigListAll(@Param("univerName") String univerName);

    /**
     * 获取所有图片的路径
     * @param userName 登录人名称
     * @return 结果
     */
    public String selectUniversity(@Param("userName") String userName);

    public List<CircularBannerConfig> selectCircularBannerConfigBySchool(@Param("school") String school);
}
