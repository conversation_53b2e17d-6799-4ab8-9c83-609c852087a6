package com.ruoyi.create.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.create.domain.PromptOptimization;

/**
 * prompt模板优化Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface PromptOptimizationMapper extends BaseMapper<PromptOptimization>
{
    /**
     * 查询prompt模板优化
     *
     * @param id prompt模板优化主键
     * @return prompt模板优化
     */
    public PromptOptimization selectPromptOptimizationById(Long id);

    /**
     * 查询prompt模板优化列表
     *
     * @param promptOptimization prompt模板优化
     * @return prompt模板优化集合
     */
    public List<PromptOptimization> selectPromptOptimizationList(PromptOptimization promptOptimization);

    /**
     * 新增prompt模板优化
     *
     * @param promptOptimization prompt模板优化
     * @return 结果
     */
    public int insertPromptOptimization(PromptOptimization promptOptimization);

    /**
     * 修改prompt模板优化
     *
     * @param promptOptimization prompt模板优化
     * @return 结果
     */
    public int updatePromptOptimization(PromptOptimization promptOptimization);

    /**
     * 删除prompt模板优化
     *
     * @param id prompt模板优化主键
     * @return 结果
     */
    public int deletePromptOptimizationById(Long id);

    /**
     * 批量删除prompt模板优化
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePromptOptimizationByIds(Long[] ids);
}
