package com.ruoyi.create.service.impl;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.Vo.StudentAttendanceVo;
import com.ruoyi.create.domain.*;
import com.ruoyi.create.mapper.StudentSignMapper;
import com.ruoyi.create.service.IStudentSignService;
import com.ruoyi.create.utils.UserUtils;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 拼音字符与口型动作对应关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
@Service
public class StudentSignServiceImpl implements IStudentSignService
{
    @Resource
    private StudentSignMapper studentSignMapper;

    @Autowired
    private UserUtils userUtils;

    @Resource
    private RemoteUserService remoteUserService;

    @Value("${logo.path.file-path-win}")
    String localFilePathWin;// D:/ruoyi/uploadDataPath

    @Value("${logo.path.file-path-linux}")
    String localFilePathLinux;// /home/<USER>/uploadDataPath

    @Override
    public StudentSign selectStudentSignById(Long id)
    {
        return studentSignMapper.selectStudentSignById(id);
    }

    @Override
    public List<StudentSign> selectStudentSignList(StudentSign studentSign)
    {

        List<StudentSign> studentSignList = studentSignMapper.selectStudentSignList(studentSign);
        AttendanceParam attendanceParam = studentSignMapper.selectById(studentSign.getId());

        R<LoginUser> userAndRole = remoteUserService.getUserAndRole(SecurityUtils.getUserId(), SecurityConstants.INNER);
        LoginUser data2 = userAndRole.getData();
        //工号
        String jobId = data2.getSysUser().getJobId();
        studentSign.setTeacherId(jobId);
        studentSign.setStartTime(attendanceParam.getStartTime());
        studentSign.setEndTime(attendanceParam.getEndTime());
        // 根据id去查询s_sign_in_sessions表中的数据
        List<StudentSign> studentSignList1 = new ArrayList<StudentSign>();


        String isAttendance = studentSignMapper.selectIsAttendanceById(studentSign.getId());
        if(!"1".equals(isAttendance)){
            // 查询在此班上课的所有学生
            List<CourseManagement> StudentList = studentSignMapper.selectStudentIdList(studentSign);
            if(StudentList!=null){
                for(int i=0;i<StudentList.size();i++){
                    StudentSign studentSign1 = new StudentSign();
                    studentSign1.setStudentId(StudentList.get(i).getStudentId());
                    studentSign1.setTeacherId(jobId);
                    studentSign1.setSignClass(studentSign.getSignClass());
                    studentSign1.setCourseName(StudentList.get(i).getCourseName());
                    studentSign1.setCourseType(studentSign.getCourseType());
                    studentSign1.setTerm(StudentList.get(i).getTerm());
                    studentSignList1.add(studentSign1);
                }
            }
//            studentSignMapper.deleteStudentInfo(studentSign);
//            studentSignMapper.insertStudentInfo(studentSignList1);
        }
        for(int i=0; i<studentSignList.size();i++){
            List<StudentSign> lists = studentSignMapper.selectBystudentId(studentSignList.get(i).getStudentId());
            studentSignList.get(i).setSignType(attendanceParam.getSignType());
            studentSignList.get(i).setStudentName(lists.get(0).getStudentName());
            studentSignList.get(i).setSex(lists.get(0).getSex());
        }
        return studentSignList;
    }

    @Override
    public List<StudentSign> selectStudentAttendanceList(StudentSign studentSign) {
        List<StudentSign> studentSignList = studentSignMapper.selectStudentAttendanceListByStudentId(studentSign);

        //使用studentSign找出签到id而不是使用开始和结束日期来判断

        for (int i = 0; i < studentSignList.size(); i++) {
            //根据studentSign去找出签到的id
            String signType=studentSignMapper.selectSignTypeBySignId(studentSignList.get(i));
            studentSignList.get(i).setSignType(signType);
        }


//        for(int i=0;i<studentSignList.size();i++){
//          String signType = studentSignMapper.selectSignType(studentSignList.get(i));
//            studentSignList.get(i).setSignType(signType);
//       }
        return studentSignList;
    }

    @Override
    public int updateStudentAttendance(StudentSign studentSign) {
        String studentId = userUtils.getSysUser(SecurityUtils.getUserId()).getStudentId();
        // 获取当前系统时间
        LocalDateTime currentDateTime = LocalDateTime.now();
        // 定义格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 格式化时间
        String formattedDateTime = currentDateTime.format(formatter);
        studentSign.setSignTime(formattedDateTime);
        studentSign.setIsSign("1");
        studentSign.setStudentId(studentId);
        if(studentSign.getPhotoPath()==null || studentSign.getPhotoPath()== ""){
            studentSign.setPhotoPath("");
            studentSign.setPhotoName("");
        }else{
            File file = new File(studentSign.getPhotoPath().trim());
            studentSign.setPhotoName(file.getName());
        }
        return studentSignMapper.updateStudentAttendance(studentSign);
    }

    @Override
    public StudentSign uploadjpg(MultipartFile file) {
        try {
            StudentSign studentSign = new StudentSign();
            long uniqueID = System.currentTimeMillis();
            // 获取上传的文件名 photo1.jpg
            String fileName = file.getOriginalFilename();
            fileName = uniqueID+"-"+fileName;
            String os = System.getProperty("os.name").toLowerCase();
            String speechdraftpath = "";
            if (os.contains("win")) {
                speechdraftpath = localFilePathWin;
            } else if (os.contains("nix") || os.contains("nux") || os.contains("mac")) {
                speechdraftpath = localFilePathLinux;;
            } else {
                throw new UnsupportedOperationException("Unsupported operating system: " + os);
            }
            String filePath = speechdraftpath+"xsqdpz";
            // 检查并创建目录
            Path uploadPath = Paths.get(filePath);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }
            // 保存文件到指定路径
            Path filePath1 = uploadPath.resolve(fileName);
            Files.write(filePath1, file.getBytes());
            studentSign.setPhotoPath(filePath);
            studentSign.setPhotoName(fileName);
            return studentSign;
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }



}
