package com.ruoyi.create.controller;

import java.util.List;
import java.io.IOException;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.CourseNotifications;
import com.ruoyi.create.service.ICourseNotificationsService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 课程通知Controller
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
@RestController
@RequestMapping("/courseNotifications")
public class CourseNotificationsController extends BaseController {
    @Resource
    private ICourseNotificationsService courseNotificationsService;

    /**
     * 查询课程通知列表
     */
    @GetMapping("/list")
    public TableDataInfo list(CourseNotifications courseNotifications) {
        startPage();
        List<CourseNotifications> list = courseNotificationsService.selectCourseNotificationsList(courseNotifications);
        return getDataTable(list);
    }

    @GetMapping("/list2")
    public TableDataInfo list2(CourseNotifications courseNotifications) {
        startPage();
        List<CourseNotifications> list = courseNotificationsService.selectCourseNotificationsList2(courseNotifications);
        return getDataTable(list);
    }

    /**
     * 导出课程通知列表
     */
    @Log(title = "课程通知", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CourseNotifications courseNotifications) {
        List<CourseNotifications> list = courseNotificationsService.selectCourseNotificationsList(courseNotifications);
        ExcelUtil<CourseNotifications> util = new ExcelUtil<CourseNotifications>(CourseNotifications.class);
        util.exportExcel(response, list, "课程通知数据");
    }

    /**
     * 获取课程通知详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(courseNotificationsService.selectCourseNotificationsById(id));
    }

    /**
     * 新增课程通知
     */
    @Log(title = "课程通知", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CourseNotifications courseNotifications) {
        return toAjax(courseNotificationsService.insertCourseNotifications(courseNotifications));
    }

    /**
     * 修改课程通知
     */
    @Log(title = "课程通知", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CourseNotifications courseNotifications) {
        return toAjax(courseNotificationsService.updateCourseNotifications(courseNotifications));
    }

    /**
     * 删除课程通知
     */
    @Log(title = "课程通知", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(courseNotificationsService.deleteCourseNotificationsByIds(ids));
    }
}
