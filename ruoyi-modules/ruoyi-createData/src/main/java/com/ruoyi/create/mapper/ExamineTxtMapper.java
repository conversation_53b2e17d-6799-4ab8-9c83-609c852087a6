package com.ruoyi.create.mapper;

import java.util.List;
import com.ruoyi.create.domain.ExamineTxt;

/**
 * 课件审核建议Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-19
 */
public interface ExamineTxtMapper
{
    /**
     * 查询课件审核建议
     *
     * @param id 课件审核建议主键
     * @return 课件审核建议
     */
    public ExamineTxt selectExamineTxtById(Long id);
    public String selectSuggestionByPId(Long id);
    /**
     * 查询课件审核建议列表
     *
     * @param examineTxt 课件审核建议
     * @return 课件审核建议集合
     */
    public List<ExamineTxt> selectExamineTxtList(ExamineTxt examineTxt);

    /**
     * 新增课件审核建议
     *
     * @param examineTxt 课件审核建议
     * @return 结果
     */
    public int insertExamineTxt(ExamineTxt examineTxt);

    /**
     * 修改课件审核建议
     *
     * @param examineTxt 课件审核建议
     * @return 结果
     */
    public int updateExamineTxt(ExamineTxt examineTxt);

    /**
     * 删除课件审核建议
     *
     * @param id 课件审核建议主键
     * @return 结果
     */
    public int deleteExamineTxtById(Long id);
    public int deleteExamineTxtByPId(Long id);
    /**
     * 批量删除课件审核建议
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExamineTxtByIds(Long[] ids);
}
