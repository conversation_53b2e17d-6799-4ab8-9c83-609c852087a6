package com.ruoyi.create.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.ExamineTxt;
import com.ruoyi.create.service.IExamineTxtService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 课件审核建议Controller
 *
 * <AUTHOR>
 * @date 2024-09-19
 */
@RestController
@RequestMapping("/examine")
public class ExamineTxtController extends BaseController
{
    @Autowired
    private IExamineTxtService examineTxtService;

    /**
     * 查询课件审核建议列表
     */
    @RequiresPermissions("create:examine:list")
    @GetMapping("/list")
    public TableDataInfo list(ExamineTxt examineTxt) {
        startPage();
        List<ExamineTxt> list = examineTxtService.selectExamineTxtList(examineTxt);
        return getDataTable(list);
    }


    /**
     * 获取课件审核建议详细信息
     */
    @RequiresPermissions("create:examine:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(examineTxtService.selectExamineTxtById(id));
    }

    /**
     * 新增课件审核建议
     */
    @RequiresPermissions("teach:materials:check")//teach:materials:check
    @Log(title = "课件审核建议", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody ExamineTxt examineTxt) {
        return toAjax(examineTxtService.insertExamineTxt(examineTxt));
    }

    /**
     * 修改课件审核建议
     */
    @RequiresPermissions("create:examine:edit")
    @Log(title = "课件审核建议", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ExamineTxt examineTxt) {
        return toAjax(examineTxtService.updateExamineTxt(examineTxt));
    }

    /**
     * 删除课件审核建议
     */
    @RequiresPermissions("create:examine:remove")
    @Log(title = "课件审核建议", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(examineTxtService.deleteExamineTxtByIds(ids));
    }
}
