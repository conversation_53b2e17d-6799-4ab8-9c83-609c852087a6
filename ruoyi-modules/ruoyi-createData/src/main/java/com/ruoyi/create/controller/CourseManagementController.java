package com.ruoyi.create.controller;

import cn.idev.excel.FastExcel;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.ServletUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.domain.ClassInfo;
import com.ruoyi.create.domain.CourseManagement;
import com.ruoyi.create.domain.StudentCourse;
import com.ruoyi.create.dto.CourseManagementDto;
import com.ruoyi.create.service.ICourseManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.ruoyi.common.core.web.page.TableSupport.PAGE_NUM;
import static com.ruoyi.common.core.web.page.TableSupport.PAGE_SIZE;

/**
 * 课程管理Controller
 *
 * <AUTHOR>
 * @create 2024-10-11
 */
@RestController
@RequestMapping("/courseManagement")
public class CourseManagementController extends BaseController {


    @Autowired
    private ICourseManagementService courseManagementService;

    // 列标题与别名的映射关系
    private static final Map<String, String> HEADER_ALIAS_MAP = new HashMap<String, String>() {{
        put("student_id", "学号");
    }};

    /**
     * 查询课程管理列表
     */
//    @RequiresPermissions("system:courseManagement:list")
    @GetMapping("/list")
    public TableDataInfo list1(CourseManagement courseManagement) {
        if (ServletUtils.getParameter(PAGE_NUM) != null && ServletUtils.getParameter(PAGE_SIZE) != null) {
            startPage();
        }
        List<CourseManagement> list = courseManagementService.selectCourseManagementList(courseManagement);
        return getDataTable(list);
    }

    /**
     * 查询课程管理列表（小程序）
     */
//    @RequiresPermissions("system:courseManagement:list")
    @GetMapping("/xcxList")
    public TableDataInfo xcxList(CourseManagement courseManagement) {
        List<CourseManagement> list = courseManagementService.selectCourseManagementList2(courseManagement);
        return getDataTable(list);
    }


    /**
     * 查询课程管理列表（小程序）
     */
//    @RequiresPermissions("system:courseManagement:list")
    @GetMapping("/courseNameList")
    public TableDataInfo courseNameList(CourseManagement courseManagement) {
        List<CourseManagement> list = courseManagementService.selectCourseManagementList3(courseManagement);
        //对list列表进行去重，如果courseName已经存在就不添加
//        for(int i=0;i<list.size();i++){
//            for(int j=i+1;j<list.size();j++){
//                if(list.get(i).getCourseName().equals(list.get(j).getCourseName())){
//                    list.remove(j);
//                    j--;
//                }
//            }
//        }

        return getDataTable(list);
    }


    /**
     * 查询课程所在班级（小程序）
     */
//    @RequiresPermissions("system:courseManagement:list")
    @GetMapping("/xcxcxkcszbj")
    public TableDataInfo xcxList(String courseName) {
        List<ClassInfo> list = courseManagementService.selectCourseManagementByCourseName(courseName);
        if(list!=null){
            for(int i=0;i<list.size();i++){
                Long id = courseManagementService.selectIdByClassName(courseName,list.get(i).getClassName());
                list.get(i).setId(id);
            }
        }
        return getDataTable(list);
    }

    /**
     * 查询课程管理列表（全）
     */
//    @RequiresPermissions("system:courseManagement:list")
    @GetMapping("/listDetails")
    public TableDataInfo list(CourseManagement courseManagement) {
        if (ServletUtils.getParameter(PAGE_NUM) != null && ServletUtils.getParameter(PAGE_SIZE) != null) {
            startPage();
        }
        List<CourseManagement> list = courseManagementService.selectCourseManagementList1(courseManagement);
        return getDataTable(list);
    }

    /**
     * 导出课程管理列表
     */
//    @RequiresPermissions("system:courseManagement:export")
    @Log(title = "课程管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CourseManagement courseManagement) {
        List<CourseManagement> list = courseManagementService.selectCourseManagementList(courseManagement);
        ExcelUtil<CourseManagement> util = new ExcelUtil<CourseManagement>(CourseManagement.class);
        util.exportExcel(response, list, "课程管理数据");
    }

    /**
     * 获取课程管理详细信息
     */
//    @RequiresPermissions("system:courseManagement:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(courseManagementService.selectCourseManagementById(id));
    }

    /**
     * 新增课程管理
     */
//    @RequiresPermissions("system:courseManagement:add")
    @Log(title = "课程管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CourseManagement courseManagement) {
        return toAjax(courseManagementService.insertCourseManagement(courseManagement));
    }

    /**
     * 新增课程
     */
    @Log(title = "课程管理", businessType = BusinessType.INSERT)
    @PostMapping("/addCourseName")
    public AjaxResult addCourseName(@RequestBody StudentCourse studentCourse) throws Exception {
        //如果是不是管理员新增时需要有对应学校的id
        if (!SecurityUtils.isAdmin(SecurityUtils.getUserId())){
            Long universityId = SecurityUtils.getLoginUser().getSysUser().getUniversityId();
            studentCourse.setUniversityId(universityId);
        }
        return toAjax(courseManagementService.insertCourseName(studentCourse));
    }
    /**
     * 删除课程
     */
    @Log(title = "课程管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/deleteCourseName")
    public AjaxResult deleteCourseName(@RequestBody StudentCourse studentCourse) {
        return toAjax(courseManagementService.deleteCourseName(studentCourse));
    }

    /**
     * 修改课程管理
     */
//    @RequiresPermissions("system:courseManagement:edit")
    @Log(title = "课程管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CourseManagement courseManagement) {
        return toAjax(courseManagementService.updateCourseManagement(courseManagement));
    }

    /**
     * 删除课程管理(内层)
     */

//    @RequiresPermissions("system:courseManagement:remove")
    @Log(title = "课程管理", businessType = BusinessType.DELETE)
    @DeleteMapping
    public AjaxResult remove(CourseManagement courseManagement) {
        return toAjax(courseManagementService.deleteCourseManagement(courseManagement));
    }

    /**
     * 删除课程管理(外层)
     */
//    @RequiresPermissions("system:courseManagement:remove")
    @Log(title = "课程管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/del")
    public AjaxResult wremove(CourseManagement courseManagement)
    {
        return toAjax(courseManagementService.deleteCourseManagement1(courseManagement));
    }

    /**
     * 获取课程管理详细信息
     */
//    @RequiresPermissions("system:courseManagement:all")
    @GetMapping("/all")
    public AjaxResult getAll() {
        return success(courseManagementService.getAll());
    }

    /**
     * 上传课程管理表配置
     */
    @PostMapping("/upload")
    public List<String> uploadFile(@RequestParam("file") MultipartFile file) throws IOException {
        courseManagementService.uploadCourseManagement(file);

	    // 直接读取为 Map 列表
	    List<Map<Integer, String>> list = FastExcel.read(file.getInputStream()).sheet().doReadSync();

	    // 处理读取的数据列表
	    List<String> xhList = new ArrayList<>();
	    for (Map<Integer, String> data : list) {
		    xhList.add(data.get(0));
	    }

//        List<Map<String, String>> dataList = new ArrayList<>();
//        // 读取 Excel 文件
//        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
//            Sheet sheet = workbook.getSheetAt(0); // 读取第一张表
//            Row headerRow = sheet.getRow(0); // 获取标题行
//            // 构建标题与别名的映射表
//            List<String> headerAliases = new ArrayList<>();
//            for (int i = 0; i < headerRow.getPhysicalNumberOfCells(); i++) {
//                String originalHeader = headerRow.getCell(i).getStringCellValue();
//                String alias = HEADER_ALIAS_MAP.getOrDefault(originalHeader, originalHeader);
//                headerAliases.add(alias);
//            }
//            // 遍历表格数据行
//            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
//                Row row = sheet.getRow(i);
//                if (row != null) {
//                    Map<String, String> dataMap = new HashMap<>();
//                    for (int j = 0; j < row.getPhysicalNumberOfCells(); j++) {
//                        String alias = headerAliases.get(j);
//                        String value = row.getCell(j) != null ? row.getCell(j).toString() : "";
//                        dataMap.put(alias, value);
//                    }
//                    dataList.add(dataMap);
//                }
//            }
//        }
//        List<String> xhList = new ArrayList<>();
//        for (Map<String, String> row : dataList) {
//            xhList.add(row.get("学号"));
//        }
        return xhList;
    }

    /**
     * 判断登录人是老师还是学生
     */
    @GetMapping("/loginPerson")
    public AjaxResult loginPerson() {
        return AjaxResult.success(courseManagementService.IsTeacherOrStudent());
    }

    /**
     * 根据登录的学生查询所有课程
     */
    @GetMapping("/xskcList")
    public TableDataInfo xskcList() {
        if (ServletUtils.getParameter(PAGE_NUM) != null && ServletUtils.getParameter(PAGE_SIZE) != null) {
            startPage();
        }
        List<CourseManagement> list = courseManagementService.selectStudentCourseList();
        if(list !=null){
            for(int i=0;i<list.size();i++){
                String teacherName = courseManagementService.selectNameById(list.get(i).getTeacherId());
                list.get(i).setTeacherName(teacherName);
            }
        }
        return getDataTable(list);
    }

    /**
     * 课程统计
     */
    @PostMapping("/courseStatistics")
    public R<?> getCourseStatistics(@RequestBody CourseManagementDto courseManagementDto) {
        return R.ok(courseManagementService.getCourseStatistics(courseManagementDto));
    }
}
