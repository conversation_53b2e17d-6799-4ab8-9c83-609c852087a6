package com.ruoyi.create.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.MajorInfo;
import com.ruoyi.create.service.IMajorInfoService;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;

/**
 * 专业信息Controller
 * 
 * <AUTHOR>
 * @date 2024-06-11
 */
@RestController
@RequestMapping("/majorInfo")
public class MajorInfoController extends BaseController
{
    @Autowired
    private IMajorInfoService majorInfoService;

    /**
     * 查询专业信息列表
     */
    @RequiresPermissions("system:majorInfo:list")
    @GetMapping("/list")
    public TableDataInfo list(MajorInfo majorInfo)
    {
        startPage();
        List<MajorInfo> list = majorInfoService.selectMajorInfoList(majorInfo);
        return getDataTable(list);
    }

    /**
     * 导出专业信息列表
     */
    @RequiresPermissions("system:majorInfo:export")
    @Log(title = "专业信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MajorInfo majorInfo)
    {
        List<MajorInfo> list = majorInfoService.selectMajorInfoList(majorInfo);
        ExcelUtil<MajorInfo> util = new ExcelUtil<MajorInfo>(MajorInfo.class);
        util.exportExcel(response, list, "专业信息数据");
    }

    /**
     * 获取专业信息详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(majorInfoService.selectMajorInfoById(id));
    }

    /**
     * 新增专业信息
     */
    @RequiresPermissions("system:majorInfo:add")
    @Log(title = "专业信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MajorInfo majorInfo)
    {
        return toAjax(majorInfoService.insertMajorInfo(majorInfo));
    }

    /**
     * 修改专业信息
     */
    @RequiresPermissions("system:majorInfo:edit")
    @Log(title = "专业信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MajorInfo majorInfo)
    {
        return toAjax(majorInfoService.updateMajorInfo(majorInfo));
    }

    /**
     * 删除专业信息
     */
    @RequiresPermissions("system:majorInfo:remove")
    @Log(title = "专业信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(majorInfoService.deleteMajorInfoByIds(ids));
    }


    /**
     * 查询专业信息列表
     */
    @GetMapping("/majorInfoList")
    public List<MajorInfo> getMajorInfoList(MajorInfo majorInfo)
    {
        return majorInfoService.selectMajorInfoList(majorInfo);
    }
}
