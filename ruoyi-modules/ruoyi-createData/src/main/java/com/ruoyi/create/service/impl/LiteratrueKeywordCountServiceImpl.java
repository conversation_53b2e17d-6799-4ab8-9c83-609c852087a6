package com.ruoyi.create.service.impl;

import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.create.domain.LiteratrueKeywordCount;
import com.ruoyi.create.domain.LiteratrueLinks;
import com.ruoyi.create.domain.LiteratrueNodesData;
import com.ruoyi.create.mapper.LiteratrueKeywordCountMapper;
import com.ruoyi.create.service.ILiteratrueKeywordCountService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 文献整理-关键词统计Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-18
 */
@Service
public class LiteratrueKeywordCountServiceImpl implements ILiteratrueKeywordCountService
{
    @Autowired
    private LiteratrueKeywordCountMapper literatrueKeywordCountMapper;

    /**
     * 查询文献整理-关键词统计
     * 
     * @param id 文献整理-关键词统计主键
     * @return 文献整理-关键词统计
     */
    @Override
    public LiteratrueKeywordCount selectLiteratrueKeywordCountById(Long id)
    {
        return literatrueKeywordCountMapper.selectLiteratrueKeywordCountById(id);
    }

    /**
     * 查询文献整理-关键词统计列表
     * 
     * @param literatrueKeywordCount 文献整理-关键词统计
     * @return 文献整理-关键词统计
     */
    @Override
    public List<LiteratrueKeywordCount> selectLiteratrueKeywordCountList(LiteratrueKeywordCount literatrueKeywordCount)
    {
        return literatrueKeywordCountMapper.selectLiteratrueKeywordCountList(literatrueKeywordCount);
    }

    /**
     * 新增文献整理-关键词统计
     * 
     * @param literatrueKeywordCount 文献整理-关键词统计
     * @return 结果
     */
    @Override
    public int insertLiteratrueKeywordCount(LiteratrueKeywordCount literatrueKeywordCount)
    {
        return literatrueKeywordCountMapper.insertLiteratrueKeywordCount(literatrueKeywordCount);
    }

    /**
     * 修改文献整理-关键词统计
     * 
     * @param literatrueKeywordCount 文献整理-关键词统计
     * @return 结果
     */
    @Override
    public int updateLiteratrueKeywordCount(LiteratrueKeywordCount literatrueKeywordCount)
    {
        return literatrueKeywordCountMapper.updateLiteratrueKeywordCount(literatrueKeywordCount);
    }

    /**
     * 批量删除文献整理-关键词统计
     * 
     * @param ids 需要删除的文献整理-关键词统计主键
     * @return 结果
     */
    @Override
    public int deleteLiteratrueKeywordCountByIds(Long[] ids)
    {
        return literatrueKeywordCountMapper.deleteLiteratrueKeywordCountByIds(ids);
    }

    /**
     * 删除文献整理-关键词统计信息
     * 
     * @param id 文献整理-关键词统计主键
     * @return 结果
     */
    @Override
    public int deleteLiteratrueKeywordCountById(Long id)
    {
        return literatrueKeywordCountMapper.deleteLiteratrueKeywordCountById(id);
    }

    @Override
    public AjaxResult selectGroupLiteratrueKeywordCountList(LiteratrueKeywordCount literatrueKeywordCount) {
        // 设置默认关键词
        if (StringUtils.isBlank(literatrueKeywordCount.getKeyword())) {
            literatrueKeywordCount.setKeyword("养老服务");
        }
        // 查询机构
        literatrueKeywordCount.setCategory("研究机构");
        List<LiteratrueKeywordCount> literatrueKeywordCountsInstitutions = literatrueKeywordCountMapper.selectGroupLiteratrueKeywordCountList(literatrueKeywordCount);
        // 查询作者
        literatrueKeywordCount.setCategory("高影响力作者");
        List<LiteratrueKeywordCount> literatrueKeywordCountsAuthors = literatrueKeywordCountMapper.selectGroupLiteratrueKeywordCountList(literatrueKeywordCount);
        // 查询主题
        literatrueKeywordCount.setCategory("相关主题");
        List<LiteratrueKeywordCount> literatrueKeywordCountsTopics = literatrueKeywordCountMapper.selectGroupLiteratrueKeywordCountListBySubject(literatrueKeywordCount);



        // 初始化节点数据列表
        List<LiteratrueNodesData> literatrueNodesDataList = new ArrayList<>();
        // 添加顶级节点
        literatrueNodesDataList.add(createLiteratrueNodesData(0L, 1L, literatrueKeywordCount.getKeyword()));
        literatrueNodesDataList.add(createLiteratrueNodesData(1L, 2L, "相关主题"));
        literatrueNodesDataList.add(createLiteratrueNodesData(2L, 2L, "高影响力作者"));
        literatrueNodesDataList.add(createLiteratrueNodesData(3L, 2L, "研究机构"));

        // 初始化连接数据列表
        List<LiteratrueLinks> literatrueLinksList = new ArrayList<>();

        // 添加节点和链接
        addNodesAndLinks(literatrueKeywordCountsInstitutions, 3L, 3L, literatrueNodesDataList, literatrueLinksList);
        addNodesAndLinks(literatrueKeywordCountsAuthors, 2L, 4L, literatrueNodesDataList, literatrueLinksList);
        addNodesAndLinks(literatrueKeywordCountsTopics, 1L, 5L, literatrueNodesDataList, literatrueLinksList);

        // 添加源节点链接
        addSourceNodeLinks(literatrueLinksList);

        // 创建 LiteratrueKeywordCount1 对象并设置属性
        LiteratrueKeywordCount literatrueKeywordCount1 = new LiteratrueKeywordCount();
        literatrueKeywordCount1.setLiteratrueLinks(literatrueLinksList);
        literatrueKeywordCount1.setLiteratrueNodesData(literatrueNodesDataList);
        if(literatrueKeywordCountsInstitutions.isEmpty()&&literatrueKeywordCountsAuthors.isEmpty()&&
                literatrueKeywordCountsTopics.isEmpty()){
         return AjaxResult.error("此关键词，未查询到相关数据。");
        }
        return AjaxResult.success(literatrueKeywordCount1);
    }

    //方法用于创建 LiteratrueNodesData 对象
    private LiteratrueNodesData createLiteratrueNodesData(Long id, Long gradeFlag, String name) {
        LiteratrueNodesData literatrueNodesData = new LiteratrueNodesData();
        literatrueNodesData.setId(id);
        literatrueNodesData.setName(name);
        literatrueNodesData.setGradeFlag(gradeFlag);
        return literatrueNodesData;
    }

    //方法用于添加节点和连接到列表中
    private void addNodesAndLinks(List<LiteratrueKeywordCount> keywordCounts, Long sourceId, Long gradeFlag,
                                  List<LiteratrueNodesData> nodesDataList, List<LiteratrueLinks> linksList) {
        for (LiteratrueKeywordCount keywordCount : keywordCounts) {
            LiteratrueNodesData literatrueNodesData = createLiteratrueNodesData(keywordCount.getId(), gradeFlag, keywordCount.getKeyword());
            literatrueNodesData.setCount(keywordCount.getCount());
            nodesDataList.add(literatrueNodesData);

            LiteratrueLinks literatrueLinks = new LiteratrueLinks();
            literatrueLinks.setSource(sourceId);
            literatrueLinks.setTarget(keywordCount.getId());
            linksList.add(literatrueLinks);
        }
    }

    //方法用于添加源节点链接
    private void addSourceNodeLinks(List<LiteratrueLinks> linksList) {
        Long sourceNodeId = 0L;
        List<Long> targetNodeIds = Collections.unmodifiableList(Arrays.asList(1L, 2L, 3L)); // 固定连接的节点ID
        for (Long targetNodeId : targetNodeIds) {
            LiteratrueLinks literatrueLinks = new LiteratrueLinks();
            literatrueLinks.setSource(sourceNodeId);
            literatrueLinks.setTarget(targetNodeId);
            linksList.add(literatrueLinks);
        }
    }

}
