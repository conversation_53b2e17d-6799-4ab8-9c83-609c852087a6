package com.ruoyi.create.controller;

import com.github.pagehelper.PageInfo;
import com.ruoyi.common.core.constant.HttpStatus;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.domain.LogosConfig;
import com.ruoyi.create.mapper.CircularBannerConfigMapper;
import com.ruoyi.create.mapper.LogosConfigMapper;
import com.ruoyi.create.service.ILogosConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * logo(学校andPPT)配置Controller
 *
 * <AUTHOR>
 * @date 2024-09-05
 */
@RestController
@RequestMapping("/logosconfig")
public class LogosConfigController extends BaseController {

    @Value("${logo.path.file-path-win}")
    String localFilePathWin;// D:/ruoyi/uploadDataPath

    @Value("${logo.path.file-path-linux}")
    String localFilePathLinux;// /home/<USER>/uploadDataPath

    @Autowired
    private ILogosConfigService logosConfigService;

    @Resource
    private CircularBannerConfigMapper circularBannerConfigMapper;

    @Resource
    private LogosConfigMapper logosConfigMapper;

    /**
     * 查询logo图片存储列表
     */
    @GetMapping("/list")
    public TableDataInfo list(LogosConfig logosConfig)
    {

        String univerName = circularBannerConfigMapper.selectUniversity(SecurityUtils.getUsername());
        logosConfig.setSchool(univerName);

        startPage();
        List<LogosConfig> list = logosConfigService.selectLogosConfigList(logosConfig);

        List<LogosConfig> logosConfigList = logosConfigMapper.selectLogosConfigList(logosConfig);
        List<LogosConfig> cList = new ArrayList<LogosConfig>();
        for (int i=0;i<logosConfigList.size();i++) {

            LogosConfig c = logosConfigList.get(i);
            String ssUrl="";
            String imagename=c.getLogoName();
            String os = System.getProperty("os.name").toLowerCase();
            if (os.contains("win")) {
                ssUrl=localFilePathWin+"logo";
            } else if (os.contains("nix") || os.contains("nux") || os.contains("mac")) {
                ssUrl=localFilePathLinux+"logo";
            } else {
                throw new UnsupportedOperationException("Unsupported operating system: " + os);
            }
            int lastSlashIndex = c.getLogoPath().lastIndexOf("/");
            String lastDirectoryName = ssUrl.substring(lastSlashIndex);

            String imageUrl=lastDirectoryName+"/"+imagename;
            List<Map<String, String>> presentationFileList = new ArrayList<>();
            // 创建第一个文件的Map
            Map<String, String> file1 = new HashMap<>();
            file1.put("name", imagename != null ? imagename : "");
            file1.put("url", imageUrl != null ? imageUrl : "");
            presentationFileList.add(file1);
            cList.add(
                    LogosConfig.builder().id(c.getId())
                            .logoName(c.getLogoName())
                            .logoId(c.getLogoId())
                            .title(c.getTitle())
                            .logoPath(c.getLogoPath())
                            .logoPosition(c.getLogoPosition())
                            .description(c.getDescription())
                            .school(c.getSchool())
                            .createBy(c.getCreateBy())
                            .createTime(c.getCreateTime())
                            .updateBy(c.getUpdateBy())
                            .updateTime(c.getUpdateTime())
                            .presentationFileList(presentationFileList)
                            .build()
            );
        }
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setRows(cList);
        rspData.setMsg("查询成功");
        rspData.setTotal(new PageInfo(cList).getTotal());
        return rspData;
    }

    /**
     * 上传logo图片配置
     */
    @PostMapping("/upload")
    public @ResponseBody LogosConfig upload(@RequestParam("file") MultipartFile file) {
        return logosConfigService.uploadLogosConfig(file);
    }

    /**
     * 提交logo图片存储
     */
    @Log(title = "logo图片存储", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LogosConfig logosConfig) {
        return toAjax(logosConfigService.updateLogosConfigByLogoName(logosConfig));
    }

    /**
     * 获取所有logo图片的路径
     */
    @GetMapping("/listAll/{logoPosition}")
    public List<String> listAll(@PathVariable("logoPosition") String logoPosition)
    {
        return logosConfigService.selectLogosConfigListAll(logoPosition);
    }


    /**
     * 获取logo图片存储详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(logosConfigService.selectLogosConfigById(id));
    }

    /**
     * 修改logo图片存储
     */
    @Log(title = "logo图片存储", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LogosConfig logosConfig)
    {
        return toAjax(logosConfigService.updateLogosConfig(logosConfig));
    }

    /**
     * 删除logo图片存储
     */
    @Log(title = "logo图片存储", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id)
    {
        return toAjax(logosConfigService.deleteLogosConfigById(id));
    }

    /**
     * 导出logo图片存储列表
     */
    @Log(title = "logo图片存储", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LogosConfig logosConfig)
    {
        List<LogosConfig> list = logosConfigService.selectLogosConfigList(logosConfig);
        ExcelUtil<LogosConfig> util = new ExcelUtil<LogosConfig>(LogosConfig.class);
        util.exportExcel(response, list, "logo图片存储数据");
    }

    /**
     *  获得登录人所在学校名称
     */
    @GetMapping("/selUniverName")
    public AjaxResult getUniverName()
    {
        return success(logosConfigService.selectUniversity(SecurityUtils.getUsername()));
    }

}
