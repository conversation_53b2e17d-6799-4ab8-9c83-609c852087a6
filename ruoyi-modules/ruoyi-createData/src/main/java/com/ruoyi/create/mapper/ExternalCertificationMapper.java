package com.ruoyi.create.mapper;

import com.ruoyi.create.domain.ExternalCertification;
import com.ruoyi.create.domain.execl.*;
import com.ruoyi.create.domain.execl.Class;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ExternalCertificationMapper {

    public List<Long> selectUniverIdByName(String univerName);
    public List<Long> selectCollIdByName(ExternalCertification externalCertification);
    public List<Long> selectMajIdByName(ExternalCertification externalCertification);
    public List<Long> selectClaIdByName(ExternalCertification externalCertification);

    public int insertCla(Class c);
    public int insertMajor(Major m);
    public int insertColl(Coll coll);
    public int insertUniver(Univer univer);


    public List<Long> selectStuByStuId(String studentId);
    public List<Long> selectTeaByTeaId(String teacherId);

    public int insertOrUpdateUS(ExternalCertification externalCertification);
    public int insertOrUpdateUT(ExternalCertification externalCertification);

    public int insertOrUpdateS(ExternalCertification list);
    public int insertOrUpdateT(ExternalCertification list);

    public int insertUserRoleS(Long userId);
    public int insertUserRoleT(Long userId);

}
