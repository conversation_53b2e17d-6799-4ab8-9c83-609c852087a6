package com.ruoyi.create.service.impl;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.domain.*;
import com.ruoyi.create.mapper.CourseKikeStepMapper;
import com.ruoyi.create.mapper.CourseTopicsRepliesMapper;
import com.ruoyi.create.utils.Snowflake;
import com.ruoyi.system.api.RemoteUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.TopicsReplyMapper;
import com.ruoyi.create.service.ITopicsReplyService;

/**
 * 课程留言与回复关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-04
 */
@Service
public class TopicsReplyServiceImpl implements ITopicsReplyService 
{
    @Autowired
    private TopicsReplyMapper topicsReplyMapper;

    @Autowired
    private RemoteUserService userService;

    @Autowired
    private CourseTopicsRepliesMapper courseTopicsRepliesMapper;

    @Autowired
    private CourseKikeStepMapper courseKikeStepMapper;

    /**
     * 查询课程留言与回复关联
     * 
     * @param id 课程留言与回复关联主键
     * @return 课程留言与回复关联
     */
    @Override
    public TopicsReply selectTopicsReplyById(Long id)
    {
        return topicsReplyMapper.selectTopicsReplyById(id);
    }

    /**
     * 查询课程留言与回复关联列表
     * 
     * @param topicsReply 课程留言与回复关联
     * @return 课程留言与回复关联
     */
    @Override
    public List<TopicsReply> selectTopicsReplyList(TopicsReply topicsReply)
    {
        return topicsReplyMapper.selectTopicsReplyList(topicsReply);
    }

    /**
     * 新增课程留言与回复关联
     * 
     * @param topicsReply 课程留言与回复关联
     * @return 结果
     */
    @Override
    public int insertTopicsReply(TopicsReply topicsReply)
    {
        //当前回复是否为
        Snowflake snowflakereply = new Snowflake(1, 1);
        long replyId = snowflakereply.generateId();
        topicsReply.setReplyId(replyId);
        topicsReply.setReplyUserid(SecurityUtils.getUserId());
        AjaxResult ajaxResult =userService.getInfo(SecurityUtils.getUserId(), SecurityConstants.INNER);
        Map linkedHashMap = (LinkedHashMap) ajaxResult.get("data");
        topicsReply.setReplyUserName(String.valueOf(linkedHashMap.get("nickName")));
        topicsReply.setCreateTime(DateUtils.getNowDate());
        return topicsReplyMapper.insertTopicsReply(topicsReply);
    }

    /**
     * 修改课程留言与回复关联
     * 
     * @param topicsReply 课程留言与回复关联
     * @return 结果
     */
    @Override
    public int updateTopicsReply(TopicsReply topicsReply)
    {
        return topicsReplyMapper.updateTopicsReply(topicsReply);
    }

    /**
     * 批量删除课程留言与回复关联
     * 
     * @param ids 需要删除的课程留言与回复关联主键
     * @return 结果
     */
    @Override
    public int deleteTopicsReplyByIds(Long[] ids)
    {
        return topicsReplyMapper.deleteTopicsReplyByIds(ids);
    }

    /**
     * 删除课程留言与回复关联信息
     * 
     * @param id 课程留言与回复关联主键
     * @return 结果
     */
    @Override
    public int deleteTopicsReplyById(Long id)
    {
        return topicsReplyMapper.deleteTopicsReplyById(id);
    }

    @Override
    public List<CourseTopicsReplies> selectTopicsReplyAll(Long msgId) {
        CourseTopicsReplies courseTopicsReplies = new CourseTopicsReplies();
        courseTopicsReplies.setMsgId(msgId);
        // 获取权限
        long uid = SecurityUtils.getUserId();
        AjaxResult ajaxResult = userService.getInfo(SecurityUtils.getUserId(), SecurityConstants.INNER);
        List<Integer> userRoleid = (ArrayList) ajaxResult.get("roleIds");
        List<CourseTopicsReplies> courseTopicsRepliesList = courseTopicsRepliesMapper.selectCourseTopicsRepliesList(courseTopicsReplies);
        for (CourseTopicsReplies itemsCourseTopicsReplies : courseTopicsRepliesList) {
            List<TopicsReply>  topicsReplyList = topicsReplyMapper.selectTopicsReplyAll(msgId);
            boolean isAdmin = userRoleid.contains(1) || userRoleid.contains(105);
            if (isAdmin || itemsCourseTopicsReplies.getMsgUserId() == uid) {
                itemsCourseTopicsReplies.setDeleteFlag(true);
                topicsReplyList.forEach(itemSMsgReply -> itemSMsgReply.setDeleteFlag(true));
            } else {
                itemsCourseTopicsReplies.setDeleteFlag(false);
                topicsReplyList.stream()
                        .filter(s -> s.getReplyUserid() == uid)
                        .forEach(s -> s.setDeleteFlag(true));
            }
            itemsCourseTopicsReplies.setTopicsReplyList(topicsReplyList);
            //点赞数量
            int coutKike =  courseKikeStepMapper.countCourseKike(msgId);
            //点踩数量
            int coutStep = courseKikeStepMapper.countCourseStep(msgId);
            //当前用户赞踩
            CourseKikeStep courseKikeStep = new CourseKikeStep();
            courseKikeStep.setMsgId(msgId);
            courseKikeStep.setCreateBy(SecurityUtils.getUsername());
            String  kikeStep=  courseKikeStepMapper.selectCourseKikeStepByUserName(courseKikeStep);
            itemsCourseTopicsReplies.setCoutKike(coutKike);
            itemsCourseTopicsReplies.setCoutStep(coutStep);
            if (kikeStep!=null){
                itemsCourseTopicsReplies.setKikeStep(kikeStep);
            }
        }
        return courseTopicsRepliesList;
    }

    @Override
    public int deleteTopicsReplylyIds(Long[] replyIds) {
        return topicsReplyMapper.deleteTopicsReplylyIds(replyIds);
    }
}
