package com.ruoyi.create.controller;

import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.security.annotation.RequiresPermissions;
import com.ruoyi.create.domain.LiteratrueKeywordCount;
import com.ruoyi.create.service.ILiteratrueKeywordCountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.ListIterator;

/**
 * 文献整理-关键词统计Controller
 * 
 * <AUTHOR>
 * @date 2024-07-18
 */
@RestController
@RequestMapping("/literatruecount")
public class LiteratrueKeywordCountController extends BaseController
{
    @Autowired
    private ILiteratrueKeywordCountService literatrueKeywordCountService;

    /**
     * 查询文献整理-关键词统计列表
     */
    @RequiresPermissions("create:literatruecount:list")
    @GetMapping("/list")
    public TableDataInfo list(LiteratrueKeywordCount literatrueKeywordCount)
    {
        startPage();
        List<LiteratrueKeywordCount> list = literatrueKeywordCountService.selectLiteratrueKeywordCountList(literatrueKeywordCount);
        return getDataTable(list);
    }

    /**
     * 导出文献整理-关键词统计列表
     */
    @RequiresPermissions("create:literatruecount:export")
    @Log(title = "文献整理-关键词统计", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LiteratrueKeywordCount literatrueKeywordCount)
    {
        List<LiteratrueKeywordCount> list = literatrueKeywordCountService.selectLiteratrueKeywordCountList(literatrueKeywordCount);
        ExcelUtil<LiteratrueKeywordCount> util = new ExcelUtil<LiteratrueKeywordCount>(LiteratrueKeywordCount.class);
        util.exportExcel(response, list, "文献整理-关键词统计数据");
    }

    /**
     * 获取文献整理-关键词统计详细信息
     */
    @RequiresPermissions("create:literatruecount:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(literatrueKeywordCountService.selectLiteratrueKeywordCountById(id));
    }

    /**
     * 新增文献整理-关键词统计
     */
    @RequiresPermissions("create:literatruecount:add")
    @Log(title = "文献整理-关键词统计", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LiteratrueKeywordCount literatrueKeywordCount)
    {
        return toAjax(literatrueKeywordCountService.insertLiteratrueKeywordCount(literatrueKeywordCount));
    }

    /**
     * 修改文献整理-关键词统计
     */
    @RequiresPermissions("create:literatruecount:edit")
    @Log(title = "文献整理-关键词统计", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LiteratrueKeywordCount literatrueKeywordCount)
    {
        return toAjax(literatrueKeywordCountService.updateLiteratrueKeywordCount(literatrueKeywordCount));
    }

    /**
     * 删除文献整理-关键词统计
     */
    @RequiresPermissions("create:literatruecount:remove")
    @Log(title = "文献整理-关键词统计", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(literatrueKeywordCountService.deleteLiteratrueKeywordCountByIds(ids));
    }
    /**
     * 查询文献整理-关键词统计列表
     */
    @RequiresPermissions("create:literatruecount:list")
    @PostMapping("/all")
    public AjaxResult selectDataSetAll(@RequestBody LiteratrueKeywordCount literatrueKeywordCount)
    {
        AjaxResult list = literatrueKeywordCountService.selectGroupLiteratrueKeywordCountList(literatrueKeywordCount);
        return list;
    }


}
