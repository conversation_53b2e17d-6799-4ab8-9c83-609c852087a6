package com.ruoyi.create.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.create.domain.Homework;

import java.util.List;

/**
 * 作业信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
public interface HomeworkMapper extends BaseMapper<Homework>
{
    /**
     * 查询作业信息
     *
     * @param id 作业信息主键
     * @return 作业信息
     */
    public Homework selectHomeworkById(Long id);

    /**
     * 查询作业信息列表
     *
     * @param homework 作业信息
     * @return 作业信息集合
     */
    public List<Homework> selectHomeworkList(Homework homework);

    /**
     * 新增作业信息
     *
     * @param homework 作业信息
     * @return 结果
     */
    public int insertHomework(Homework homework);

    /**
     * 修改作业信息
     *
     * @param homework 作业信息
     * @return 结果
     */
    public int updateHomework(Homework homework);

    /**
     * 删除作业信息
     *
     * @param id 作业信息主键
     * @return 结果
     */
    public int deleteHomeworkById(Long id);

    /**
     * 批量删除作业信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHomeworkByIds(Long[] ids);
}
