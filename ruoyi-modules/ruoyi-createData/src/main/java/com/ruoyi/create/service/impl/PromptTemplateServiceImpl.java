package com.ruoyi.create.service.impl;

import java.util.List;
import java.util.Set;

import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.create.mapper.PromptTemplateMapper;
import com.ruoyi.create.domain.PromptTemplate;
import com.ruoyi.create.service.IPromptTemplateService;

import javax.annotation.Resource;

/**
 * prompt模板Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
@Service
public class PromptTemplateServiceImpl implements IPromptTemplateService {
    @Resource
    private PromptTemplateMapper promptTemplateMapper;

    /**
     * 查询prompt模板
     *
     * @param id prompt模板主键
     * @return prompt模板
     */
    @Override
    public PromptTemplate selectPromptTemplateById(Long id) {
        return promptTemplateMapper.selectPromptTemplateById(id);
    }

    /**
     * 查询prompt模板列表
     *
     * @param promptTemplate prompt模板
     * @return prompt模板
     */
    @Override
    public List<PromptTemplate> selectPromptTemplateList(PromptTemplate promptTemplate) {
        Set<String> roles = SecurityUtils.getLoginUser().getRoles();
        //管理员可以查看所有
        if (roles.contains("admin") || roles.contains("administration")) {
            promptTemplate.setCode(1);
        } else {
            promptTemplate.setCode(2);
        }
        promptTemplate.setCreateBy(SecurityUtils.getUsername());
        return promptTemplateMapper.selectPromptTemplateList(promptTemplate);
    }

    /**
     * 新增prompt模板
     *
     * @param promptTemplate prompt模板
     * @return 结果
     */
    @Override
    public int insertPromptTemplate(PromptTemplate promptTemplate) {
        promptTemplate.setCreateBy(SecurityUtils.getUsername());
        promptTemplate.setCreateTime(DateUtils.getNowDate());
        return promptTemplateMapper.insertPromptTemplate(promptTemplate);
    }

    /**
     * 修改prompt模板
     *
     * @param promptTemplate prompt模板
     * @return 结果
     */
    @Override
    public int updatePromptTemplate(PromptTemplate promptTemplate) {
        PromptTemplate promptTemplate1 = promptTemplateMapper.selectPromptTemplateById(promptTemplate.getId());
        if (!SecurityUtils.getUsername().equals(promptTemplate1.getCreateBy())) {
            throw new SecurityException("无此操作权限");
        }
        promptTemplate.setUpdateBy(SecurityUtils.getUsername());
        promptTemplate.setUpdateTime(DateUtils.getNowDate());
        return promptTemplateMapper.updatePromptTemplate(promptTemplate);
    }

    /**
     * 批量删除prompt模板
     *
     * @param ids 需要删除的prompt模板主键
     * @return 结果
     */
    @Override
    public int deletePromptTemplateByIds(Long[] ids) {
        List<PromptTemplate> promptTemplateList = promptTemplateMapper.selectPromptTemplateByIds(ids);
        for (PromptTemplate promptTemplate : promptTemplateList) {
            if (!SecurityUtils.getUsername().equals(promptTemplate.getCreateBy())) {
                throw new SecurityException("无此操作权限");
            }
        }
        return promptTemplateMapper.deletePromptTemplateByIds(ids);
    }

    /**
     * 删除prompt模板信息
     *
     * @param id prompt模板主键
     * @return 结果
     */
    @Override
    public int deletePromptTemplateById(Long id) {
        return promptTemplateMapper.deletePromptTemplateById(id);
    }

    @Override
    public List<PromptTemplate> selectPromptAll() {
        String username = SecurityUtils.getUsername();
        return promptTemplateMapper.selectPromptAll(username);
    }
}
