package com.ruoyi.create.controller;

import java.io.IOException;

import java.util.*;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baidubce.appbuilder.base.exception.AppBuilderServerException;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.core.constant.HttpStatus;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.Vo.KnowledgeBaseFileVo;
import com.ruoyi.create.Vo.PresentationVo;
import com.ruoyi.create.domain.*;
import com.ruoyi.create.dto.PresentationDto;
import com.ruoyi.create.dto.UserDto;
import com.ruoyi.create.exception.CustomException;
import com.ruoyi.create.mapper.PresentationMapper;
import com.ruoyi.create.service.*;
import com.ruoyi.system.api.RemoteFileService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.log.annotation.Log;
import com.ruoyi.common.log.enums.BusinessType;
import com.ruoyi.common.core.web.controller.BaseController;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 老师课程Controller
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
@Slf4j
@RestController
@RequestMapping("/presentation")
public class PresentationController extends BaseController
{
    //返回url地址 前缀
    private final static String targetUrl="http://127.0.0.1:9300/statics";
    //实际存储地址前缀
    private final static String newUrl="D:/ruoyi/uploadPath";
    //ppt分割存储的前缀
    private final static String slide="D:/ruoyi/uploadPath/ppt/slide";

    private final static String sourceBasePath = "D:/ruoyi/uploadPath/";
    private final static String targetBasePath = "D:/ruoyi/uploadPath/ppt/slide/";

    //http://***************:9210
    @Value("${file.domain}")
    private String domain;
    //http://***************:9210/statics +
    //  /statics
    @Value("${file.path.prefix}")
    private String prefix;

    //    D:/ruoyi/uploadDataPath
    @Value("${file.path.file-path-win}")
    String localFilePathWin;
    //    /home/<USER>/uploadDataPath
    @Value("${file.path.filePathlinux}")
    String localFilePathLinux;

    //文件上传
    @Resource
    private RemoteFileService remoteFileService;

    //presentation 的逻辑处理层
    @Resource
    private IPresentationService presentationService;

    @Resource
    private IPresentationPathService presentationPathService;
    //学校 学院 专业
    @Resource
    private IUniversityService universityService;
    @Resource
    private ICollegeInfoService collegeInfoService;
    @Resource
    private IMajorInfoService majorInfoService;

    @Resource
    private IExamineTxtService examineTxtService;

    @Resource
    private ApplicationEventPublisher eventPublisher;



    @Resource
    private PresentationMapper presentationMapper;






    @GetMapping("/getPresentationList")
    public TableDataInfo getPresentationList(Presentation presentation){
        String username = SecurityUtils.getUsername();
        List<String> roleKey = presentationService.selectUserRoleKeyByUserName(username);

        if (roleKey.contains("teacher")&& roleKey.size()==1){
            presentation.setCreateUser(SecurityUtils.getUsername());
        }



        UserDto userDto = presentationService.selectUserByUserName(username);
        if(roleKey.contains("admin")){
            System.out.println("白名单");
        }else if(roleKey.contains("apiece")) {
            if (userDto.getUniversityId() == null ){
                System.out.println("登陆人学校");
            }
            presentation.setSchool(userDto.getUniversityId());
        }else{
            if (userDto.getUniversityId() == null || userDto.getCollegeId() == null || userDto.getMajorId() == null){
                System.out.println("登陆人学校学院专业部分为空");
            }
            presentation.setSchool(userDto.getUniversityId());
        }


        startPage();
        List<Presentation> list = presentationService.selectPresentationList(presentation);

        List<PresentationVo> presentationsVo =new ArrayList<>();



        for (Presentation p : list) {
            CollegeInfo collegeInfo = collegeInfoService.selectCollegeInfoById(Long.valueOf(p.getCollege()));
            MajorInfo majorInfo = majorInfoService.selectMajorInfoById(Long.valueOf(p.getMajor()));
            University university = universityService.selectUniversityById(Long.valueOf(p.getSchool()));
            String suggestion="";
            if (p.getIsExamine()==2){
                suggestion = examineTxtService.selectSuggestionByPId(p.getId());
            }

            Long[] affiliatedUnit=new Long[3];

            if (p.getSchool() != "0"){
                affiliatedUnit[0]= Long.valueOf(p.getSchool());
            }
            if (p.getCollege() != "0"){
                affiliatedUnit[1]=Long.valueOf(p.getCollege());
            }
            if (p.getMajor() != "0"){
                affiliatedUnit[2]=Long.valueOf(p.getMajor());
            }


            if (p.getCreateUser().equals(username) || username.equals("admin")){
                p.setAuthority(0);
            }else {
                p.setAuthority(1);
            }
            String txtFileName ="无";
            if (StringUtils.isNotBlank(p.getTextBookId())) {
                txtFileName = presentationMapper.selectFileNameById(Long.valueOf(p.getTextBookId()));
            }


            String chapter = p.getChapter();
            if (StringUtils.isEmpty(chapter)) {
                chapter = "无";
            }

            presentationsVo.add(
                    PresentationVo.builder()
                            .id(p.getId())
                            .course(p.getCourse() != null ? p.getCourse() : "")
                            .college(p.getCollege() != null ? p.getCollege() : "")
                            .collageName(collegeInfo != null && collegeInfo.getColleName() != null ? collegeInfo.getColleName() : "")
                            .collegeInfo(collegeInfo)
                            .school(p.getSchool() != null ? p.getSchool() : "")
                            .shoolName(university != null && university.getUniverName() != null ? university.getUniverName() : "")
                            .university(university)
                            .major(p.getMajor() != null ? p.getMajor() : "")
                            .majorName(majorInfo != null && majorInfo.getMajorName() != null ? majorInfo.getMajorName() : "")
                            .majorInfo(majorInfo)
                            .presentationName(p.getPresentationName() != null ? p.getPresentationName() : "")
                            .presentationStatus(p.getPresentationStatus() != null ? p.getPresentationStatus() : "")
                            .presentationId(p.getPresentationId())
                            .createUser(p.getCreateUser() != null ? p.getCreateUser() : "")
                            .createTime(p.getCreateTime())
                            .updateBy(p.getUpdateBy() != null ? p.getUpdateBy() : "")
                            .updateTime(p.getUpdateTime())
                            .speechdraftpath(p.getSpeechdraftpath() != null ? p.getSpeechdraftpath() : "")
                            .presentationHttp(p.getPresentationHttp() != null ? p.getPresentationHttp() : "")
                            .presentationAllpage(p.getPresentationAllpage())
                            .presentationPath(p.getPresentationPath() != null ? p.getPresentationPath() : "")
                            .presentationFileId(p.getPresentationFileId() != null ? p.getPresentationFileId() : "")
                            .speechdraftFileId(p.getSpeechdraftFileId() != null ? p.getSpeechdraftFileId() : "")
                            .affiliatedUnit(affiliatedUnit)
                            .remark(p.getRemark())
                            .isExamine(p.getIsExamine())
                            .suggestion(suggestion)
                            .authority(p.getAuthority())
                            .chapter(chapter)
                            .textBookId(p.getTextBookId())
                            .textBookName(txtFileName)
                            .build()
            );
        }


        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setRows(presentationsVo);
        rspData.setMsg("查询成功");
        rspData.setTotal(new PageInfo(list).getTotal());

        return rspData;
    }

    @GetMapping("/getPresentationListGroupByCourse")
    public TableDataInfo getPresentationListGroupByCourse(Presentation presentation){
        String username = SecurityUtils.getUsername();
        List<String> roleKey = presentationService.selectUserRoleKeyByUserName(username);
        boolean isAdmin = roleKey.contains("admin");
        boolean isTeacher = roleKey.contains("teacher");
        boolean isApiece = roleKey.contains("apiece");


        UserDto userDto = presentationService.selectUserByUserName(username);
        //超级管理员不受任何限制
        if (!isAdmin) {
            // 所有非admin 身份都受限
            // 只要是teacher不管有没有apiece，也必须限制只能查自己创建
            if (isTeacher) {
                presentation.setCreateUser(username);
            }

            if (isApiece) {
                // apiece可以查本校
                if (userDto.getUniversityId() != null) {
                    presentation.setSchool(userDto.getUniversityId());
                }
            } else {
                // 普通用户受school+college+major限制
                if (userDto.getUniversityId() != null) {
                    presentation.setSchool(userDto.getUniversityId());
                }
                if (userDto.getCollegeId() != null) {
                    presentation.setCollege(userDto.getCollegeId());
                }
                if (userDto.getMajorId() != null) {
                    presentation.setMajor(userDto.getMajorId());
                }
            }
        }

        startPage();

        List<Presentation> list = presentationService.selectPresentationListGroupByCourse(presentation);

        List<PresentationVo> presentationsVo =new ArrayList<>();





        for (Presentation p : list) {
            CollegeInfo collegeInfo = collegeInfoService.selectCollegeInfoById(Long.valueOf(p.getCollege()));
            MajorInfo majorInfo = majorInfoService.selectMajorInfoById(Long.valueOf(p.getMajor()));
            University university = universityService.selectUniversityById(Long.valueOf(p.getSchool()));


            Long[] affiliatedUnit=new Long[3];

            if (p.getSchool() != "0"){
                affiliatedUnit[0]= Long.valueOf(p.getSchool());
            }
            if (p.getCollege() != "0"){
                affiliatedUnit[1]=Long.valueOf(p.getCollege());
            }
            if (p.getMajor() != "0"){
                affiliatedUnit[2]=Long.valueOf(p.getMajor());
            }


            if (p.getCreateUser().equals(username)){
                p.setAuthority(0);
            }else {
                p.setAuthority(1);
            }


            presentationsVo.add(
                    PresentationVo.builder()
                            .id(p.getId())
                            .course(p.getCourse() != null ? p.getCourse() : "")
                            .college(p.getCollege() != null ? p.getCollege() : "")
                            .collageName(collegeInfo != null && collegeInfo.getColleName() != null ? collegeInfo.getColleName() : "")
                            .school(p.getSchool() != null ? p.getSchool() : "")
                            .shoolName(university != null && university.getUniverName() != null ? university.getUniverName() : "")
                            .major(p.getMajor() != null ? p.getMajor() : "")
                            .majorName(majorInfo != null && majorInfo.getMajorName() != null ? majorInfo.getMajorName() : "")
                            .createUser(p.getCreateUser() != null ? p.getCreateUser() : "")
                            .createTime(p.getCreateTime())
                            .updateBy(p.getUpdateBy() != null ? p.getUpdateBy() : "")
                            .updateTime(p.getUpdateTime())
                            .remark(p.getRemark())
                            .authority(p.getAuthority())
                            .isAllianceCourse(p.getIsAllianceCourse())
                            .build()
            );
        }


        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setRows(presentationsVo);
        rspData.setMsg("查询成功");
        rspData.setTotal(new PageInfo(list).getTotal());

        return rspData;
    }





//    @ApiOperation("单个文件上传")
//    @PostMapping("/uploadtemp")
//    public AjaxResult uploadtemp(@RequestParam("file") MultipartFile file, @RequestParam String modeltype) {
//        return AjaxResult.success(presentationService.uploadPPtTemp(file,modeltype));
//    }




//    @PostMapping("/uploadold")
//    public AjaxResult uploadOld(@RequestPart(value = "file") MultipartFile file) throws IOException {
//        return success(presentationService.uploadPPtOld(file));
//    }




//    //讲演稿
//    @PostMapping("/uploadPathold")
//    public AjaxResult uploadSpeechDraftOld(@RequestPart(value = "file") MultipartFile file){
//        return success(presentationService.uploadSpeechDraftOld(file));
//    }




//
//    /**
//     * 查询老师课程列表
//     */
//    //@RequiresPermissions("create:presentation:list")
//    @GetMapping("/list")
//    public TableDataInfo list(@RequestBody Presentation presentation)
//    {
//        String username = SecurityUtils.getUsername();
//        List<String> roleKey = presentationService.selectUserRoleKeyByUserName(username);
//
//        if (roleKey.contains("teacher")&& roleKey.size()==1){
//            presentation.setCreateUser(SecurityUtils.getUsername());
//        }
//        UserDto userDto = presentationService.selectUserByUserName(username);
//        if(roleKey.contains("admin")){
//            System.out.println("白名单");
//        }else if(roleKey.contains("apiece")) {
//            if (userDto.getUniversityId() == null ){
//                System.out.println("登陆人学校");
//            }
//            presentation.setSchool(userDto.getUniversityId());
//        }else{
//            if (userDto.getUniversityId() == null || userDto.getCollegeId() == null || userDto.getMajorId() == null){
//                System.out.println("登陆人学校学院专业部分为空");
//            }
//            presentation.setSchool(userDto.getUniversityId());
//        }
//
//        startPage();
//        presentation.setCreateUser(SecurityUtils.getUsername());
//
//        List<Presentation> list = presentationService.selectPresentationList(presentation);
//        List<PresentationVo> presentationsVo =new ArrayList<>();
//        for (Presentation p : list) {
//            CollegeInfo collegeInfo = collegeInfoService.selectCollegeInfoById(Long.valueOf(p.getCollege()));
//            MajorInfo majorInfo = majorInfoService.selectMajorInfoById(Long.valueOf(p.getMajor()));
//            University university = universityService.selectUniversityById(Long.valueOf(p.getSchool()));
//            String suggestion="";
//            if (p.getIsExamine()==2){
//                suggestion = examineTxtService.selectSuggestionByPId(p.getId());
//            }
//            presentationsVo.add(
//                    PresentationVo.builder()
//                            .id(p.getId())
//                            .course(p.getCourse() != null ? p.getCourse() : "")
//                            .college(p.getCollege() != null ? p.getCollege() : "")
//                            .collageName(collegeInfo != null && collegeInfo.getColleName() != null ? collegeInfo.getColleName() : "")
//                            .collegeInfo(collegeInfo)
//                            .school(p.getSchool() != null ? p.getSchool() : "")
//                            .shoolName(university != null && university.getUniverName() != null ? university.getUniverName() : "")
//                            .university(university)
//                            .major(p.getMajor() != null ? p.getMajor() : "")
//                            .majorName(majorInfo != null && majorInfo.getMajorName() != null ? majorInfo.getMajorName() : "")
//                            .majorInfo(majorInfo)
//                            .presentationName(p.getPresentationName() != null ? p.getPresentationName() : "")
//                            .presentationStatus(p.getPresentationStatus() != null ? p.getPresentationStatus() : "")
//                            .presentationId(p.getPresentationId())
//                            .createUser(p.getCreateUser() != null ? p.getCreateUser() : "")
//                            .createTime(p.getCreateTime())
//                            .updateBy(p.getUpdateBy() != null ? p.getUpdateBy() : "")
//                            .updateTime(p.getUpdateTime())
//                            .speechdraftpath(p.getSpeechdraftpath() != null ? p.getSpeechdraftpath() : "")
//                            .presentationHttp(p.getPresentationHttp() != null ? p.getPresentationHttp() : "")
//                            .presentationAllpage(p.getPresentationAllpage())
//                            .presentationPath(p.getPresentationPath() != null ? p.getPresentationPath() : "")
//                            .presentationFileId(p.getPresentationFileId() != null ? p.getPresentationFileId() : "")
//                            .speechdraftFileId(p.getSpeechdraftFileId() != null ? p.getSpeechdraftFileId() : "")
//                            .affiliatedUnit(p.getAffiliatedUnit())
//                            .remark(p.getRemark())
//                            .isExamine(p.getIsExamine())
//                            .suggestion(suggestion)
//                            .chapter(p.getChapter())
//                            .textBookId(p.getTextBookId())
//                            .build()
//            );
//        }
//
//        TableDataInfo rspData = new TableDataInfo();
//        rspData.setCode(HttpStatus.SUCCESS);
//        rspData.setRows(presentationsVo);
//        rspData.setMsg("查询成功");
//        rspData.setTotal(new PageInfo(list).getTotal());
//
//        return rspData;
//    }
//
//    /**
//     * 导出老师课程列表
//     */
//    //@RequiresPermissions("create:presentation:export")
//    @Log(title = "老师课程", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, Presentation presentation)
//    {
//        List<Presentation> list = presentationService.selectPresentationList(presentation);
//        ExcelUtil<Presentation> util = new ExcelUtil<Presentation>(Presentation.class);
//        util.exportExcel(response, list, "老师课程数据");
//    }
//


    @GetMapping(value = "/getUser")
    public AjaxResult getUser(String username) {
        return success(presentationService.selectUserByUserName(username));
    }
    /**
     * 获取老师课程详细信息
     */
    //@RequiresPermissions("create:presentation:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {

        Presentation p = presentationService.selectPresentationById(id);

        University university = universityService.selectUniversityById(Long.valueOf(p.getSchool()));
        CollegeInfo collegeInfo = collegeInfoService.selectCollegeInfoById(Long.valueOf(p.getCollege()));
        MajorInfo majorInfo = majorInfoService.selectMajorInfoById(Long.valueOf(p.getMajor()));
        String suggestion="";
        if (p.getIsExamine()==2){
            suggestion = examineTxtService.selectSuggestionByPId(p.getId());
        }

        Long[] affiliatedUnit=new Long[3];

        if (p.getSchool() != "0"){
            affiliatedUnit[0]= Long.valueOf(p.getSchool());
        }
        if (p.getCollege() != "0"){
            affiliatedUnit[1]=Long.valueOf(p.getCollege());
        }
        if (p.getMajor() != "0"){
            affiliatedUnit[2]=Long.valueOf(p.getMajor());
        }


//               Long[] affiliatedUnit=null;
//        if (p.getMajor() != "0"){
//            affiliatedUnit=new Long[3];
//            affiliatedUnit[0]= Long.valueOf(p.getSchool());
//            affiliatedUnit[1]=Long.valueOf(p.getCollege());
//            affiliatedUnit[2]=Long.valueOf(p.getMajor());
//        }else {
//            affiliatedUnit=new Long[2];
//            affiliatedUnit[0]= Long.valueOf(p.getSchool());
//            affiliatedUnit[1]=Long.valueOf(p.getCollege());
//        }


        //http://192.168.101.229:9210/statics
        //String urlFix=domain+prefix;

//        String pptUrl=domain+"/presentation/downloadPresentation/"+p.getPresentationFileId();
        String pptUrl=p.getPresentationFileId();
//        String pptUrl="http://127.0.0.1:9300/statics"+p.getPresentationHttp();
        String pptname=p.getPresentationName();


        String ssUrl=p.getPresentationFileId();
//        tring ssUrl=domain+"/presentation/downloadSpeech/"+p.getPresentationFileId();;

//        String os = System.getProperty("os.name").toLowerCase();
//        if (os.contains("win")) {
//            ssUrl=p.getSpeechdraftpath().replace(localFilePathWin,urlFix);
////            ssUrl=p.getSpeechdraftpath().replace("D:/ruoyi/uploadDataPath","http://127.0.0.1:9300/statics");
//        } else if (os.contains("nix") || os.contains("nux") || os.contains("mac")) {
//            ssUrl=p.getSpeechdraftpath().replace(localFilePathLinux,urlFix);
////            ssUrl=p.getSpeechdraftpath().replace("/home/<USER>/uploadDataPath","http://127.0.0.1:9300/statics");
//        } else {
//            throw new UnsupportedOperationException("Unsupported operating system: " + os);
//        }

        String ssname=presentationService.selectNameById(p.getSpeechdraftFileId());





        List<Map<String, String>> presentationFileList = new ArrayList<>();
        // 创建第一个文件的Map
        Map<String, String> file1 = new HashMap<>();
        file1.put("name", pptname != null ? pptname : "");
        file1.put("url", pptUrl != null ? pptUrl : "");
        presentationFileList.add(file1);


        List<Map<String, String>> speechdraftFileList = new ArrayList<>();
        Map<String, String> file2 = new HashMap<>();
        file2.put("name", ssname != null ? ssname : "");
        file2.put("url", ssUrl != null ? ssUrl : "");
        // 将文件添加到列表中
        speechdraftFileList.add(file2);

        String txtFileName ="无";
        if (StringUtils.isNotBlank(p.getTextBookId())) {
            txtFileName = presentationMapper.selectFileNameById(Long.valueOf(p.getTextBookId()));
        }


        String chapter = p.getChapter();
        if (StringUtils.isEmpty(chapter)) {
            chapter = "无";
        }

        return success(PresentationVo.builder()
                .id(p.getId())
                .course(p.getCourse() != null ? p.getCourse() : "")
                .college(p.getCollege() != null ? p.getCollege() : "")
                .collageName(collegeInfo != null && collegeInfo.getColleName() != null ? collegeInfo.getColleName() : "")
                .collegeInfo(collegeInfo)
                .school(p.getSchool() != null ? p.getSchool() : "")
                .shoolName(university != null && university.getUniverName() != null ? university.getUniverName() : "")
                .university(university)
                .major(p.getMajor() != null ? p.getMajor() : "")
                .majorName(majorInfo != null && majorInfo.getMajorName() != null ? majorInfo.getMajorName() : "")
                .majorInfo(majorInfo)
                .presentationName(p.getPresentationName() != null ? p.getPresentationName() : "")
                .presentationStatus(p.getPresentationStatus() != null ? p.getPresentationStatus() : "")
                .presentationId(p.getPresentationId())
                .createUser(p.getCreateUser() != null ? p.getCreateUser() : "")
                .createTime(p.getCreateTime())
                .updateBy(p.getUpdateBy() != null ? p.getUpdateBy() : "")
                .updateTime(p.getUpdateTime())
                .speechdraftpath(p.getSpeechdraftpath() != null ? p.getSpeechdraftpath() : "")
                .presentationHttp(p.getPresentationHttp() != null ? p.getPresentationHttp() : "")
                .presentationAllpage(p.getPresentationAllpage())
                .presentationPath(p.getPresentationPath() != null ? p.getPresentationPath() : "")
                .presentationFileId(p.getPresentationFileId() != null ? p.getPresentationFileId() : "")
                .speechdraftFileId(p.getSpeechdraftFileId() != null ? p.getSpeechdraftFileId() : "")
                .affiliatedUnit(p.getAffiliatedUnit() != null ? p.getAffiliatedUnit() :affiliatedUnit)
                .presentationFileList(presentationFileList)
                .speechdraftFileList(speechdraftFileList)
                .remark(p.getRemark())
                .isExamine(p.getIsExamine())
                .suggestion(suggestion)
                .chapter(chapter)
                .textBookId(p.getTextBookId())
                .textBookName(txtFileName)
                .build());
    }

    /**
     * 新增老师课程
     */
    //@RequiresPermissions("create:presentation:add")
    @Log(title = "老师课程", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody Presentation presentation) throws IOException, AppBuilderServerException {



        //如果是不是管理员要加入对应学校
        if (!SecurityUtils.isAdmin(SecurityUtils.getUserId())){
            Long universityId = SecurityUtils.getLoginUser().getSysUser().getUniversityId();
            presentation.setUniversityId(universityId);
        }
//        Long[] affiliatedUnit = presentation.getAffiliatedUnit();
//        presentation.setSchool(String.valueOf(affiliatedUnit != null && affiliatedUnit.length > 0 ? affiliatedUnit[0] : 0)); //
//        presentation.setCollege(String.valueOf(affiliatedUnit != null && affiliatedUnit.length > 1 ? affiliatedUnit[1] : 0)); //
//        presentation.setMajor(String.valueOf(affiliatedUnit != null && affiliatedUnit.length > 2 ? affiliatedUnit[2] : 0)); //

        ////获取当前用户
        presentation.setCreateUser(SecurityUtils.getUsername());
        presentation.setCreateTime(new Date());

        presentation.setIsExamine(1);

        //当前登录的人的 学校 学院 专业
        String username = SecurityUtils.getUsername();
        List<String> roleKey = presentationService.selectUserRoleKeyByUserName(username);
        UserDto userDto = presentationService.selectUserByUserName(username);
        if(roleKey.contains("admin")){
            System.out.println("白名单");
        }else if(roleKey.contains("apiece")) {
            if (userDto.getUniversityId() == null ){
                System.out.println("登陆人学校");
            }
            presentation.setSchool(userDto.getUniversityId());
        }else{
            if (userDto.getUniversityId() == null || userDto.getCollegeId() == null || userDto.getMajorId() == null){
                System.out.println("登陆人学校学院专业部分为空");
            }
            presentation.setSchool(userDto.getUniversityId());
        }

        //presentation.setCollege(userDto.getCollegeId()); //
        //存
        String major = presentation.getMajor();
        if (major=="" || major==null){
            throw new CustomException("专业不能为空");
        }
        presentation.setMajor(major); //
        MajorInfo majorInfo = majorInfoService.selectMajorInfoById(Long.valueOf(major));
        presentation.setCollege(String.valueOf(majorInfo.getColleId()));

//        Long[] affiliatedUnit2 =new Long[3];
//        affiliatedUnit2[0]= Long.valueOf(userDto.getUniversityId());
//        affiliatedUnit2[1]= Long.valueOf(userDto.getCollegeId());
//        affiliatedUnit2[2]= Long.valueOf(userDto.getMajorId());

        ////获取当前用户




        //System.out.println("====================================="+SecurityUtils.getUsername());
	        int rows = presentationService.insertPresentation(presentation);

        return  rows > 0 ? AjaxResult.success(presentation) : AjaxResult.error();
    }





    /**
     * 修改老师课程
     */
    //@RequiresPermissions("create:presentation:edit")
    @Log(title = "老师课程", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public AjaxResult edit(@RequestBody Presentation presentation) throws IOException, AppBuilderServerException {
        //获取当前用户
        presentation.setUpdateBy(SecurityUtils.getUsername());

        presentation.setIsExamine(1);
        examineTxtService.deleteExamineTxtByPId(presentation.getId());

        int rows =  presentationService.updatePresentation(presentation);
        return  rows > 0 ? AjaxResult.success() : AjaxResult.error();
    }




    /**
     * 删除老师课程
     */
    //@RequiresPermissions("create:presentation:remove")
    @Log(title = "老师课程", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        //删除文件
        //删除关联表
        return toAjax(presentationService.deletePresentationByIds(ids));
    }

    //@RequiresPermissions("create:presentation:edit")
    @Log(title = "老师讲演稿")
    @GetMapping("/txt")
    public AjaxResult getTxt(HttpServletResponse response,
                             @RequestBody Presentation presentation){
        presentationService.getTxt(response,presentation);
        return success();
    }

    @Log(title = "老师讲演稿")
    @GetMapping("/txt2")
    public AjaxResult getTxt2(HttpServletResponse response,
                              String speechdraftpath,Long id){

        //存数据库的
        if (id!=null){
            Presentation presentation = presentationService.selectPresentationById(id);
            presentationService.getTxt(response,presentation);
            return success();
        }else {
            // System.out.println("==============================="+speechdraftpath);
            //不带id是临时文件夹  使用传来的路径
            presentationService.getTxt2(response,speechdraftpath);
            return success();
        }

    }



    //@RequiresPermissions("create:presentation:edit")
    @Log(title = "老师讲演稿")
    @RequestMapping("/submit")
    public AjaxResult handleSubmit(@RequestBody Presentation presentation) {

        if (presentationService.handleSubmit(presentation)) {
            return success();
        }else {
            return error();
        }

    }


    /**
     * 查询专业信息列表
     */
    //@RequiresPermissions("system:majorInfo:list")
    @GetMapping("/majorlist")
    public AjaxResult list(MajorInfo majorInfo)
    {
        //startPage();
//         如果需要查询登陆人的学院下的专业
        String username = SecurityUtils.getUsername();
        List<String> roleKey = presentationService.selectUserRoleKeyByUserName(username);
        UserDto userDto = presentationService.selectUserByUserName(username);
        if(roleKey.contains("admin")){
            System.out.println("白名单");
        }else if(roleKey.contains("apiece")) {
            if (userDto.getUniversityId() == null ){
                System.out.println("登陆人学校");
            }
            majorInfo.setUniverId(Long.valueOf(userDto.getUniversityId()));
        }else{
            if (userDto.getUniversityId() == null || userDto.getCollegeId() == null || userDto.getMajorId() == null){
                System.out.println("登陆人学校学院专业部分为空");
            }
            majorInfo.setUniverId(Long.valueOf(userDto.getUniversityId()));
        }
        //majorInfo.setColleId(Long.valueOf(userDto.getCollegeId()));

        List<MajorInfo> list = majorInfoService.selectMajorInfoList(majorInfo);
        return success(list);
    }

//
//    @Log(title = "老师讲演稿示例")
//    @GetMapping("/txtDemo")
//    public AjaxResult getTxtDemo(HttpServletResponse response){
//        presentationService.getTxtDemo(response);
//        return success();
//    }
//
//    @Log(title = "老师讲演稿示例下载")
//    @GetMapping("/txtDemoDownload")
//    public AjaxResult getTxtDemoDownload(HttpServletRequest request,HttpServletResponse response){
//        presentationService.download(request,response);
//        return success();
//    }

    //@RequiresPermissions("create:presentation:edit")
    @Log(title = "老师讲演稿")
    @RequestMapping("/saveConsonant")
    public AjaxResult saveConsonant(@RequestBody Presentation presentation) throws IOException, AppBuilderServerException {

        presentationService.saveConsonant(presentation.getSpeechdraftpath(), String.valueOf(presentation.getPresentationId()));
        return success();
    }

    @Log(title = "下载")
    @GetMapping("/downloadPresentation/{fileId}")
    public void downloadPresentation(HttpServletRequest request,HttpServletResponse response,@PathVariable("fileId") Long fileId){
        presentationService.download(request,response,fileId,1);

    }
    @Log(title = "下载")
    @GetMapping("/downloadSpeech/{fileId}")
    public void downloadSpeech(HttpServletRequest request,HttpServletResponse response,@PathVariable("fileId") Long fileId){
        presentationService.download(request,response,fileId,0);

    }

    /**
     * 课程名 专业 必传
     * 根据课程名 专业 获取教材
     * @param presentation
     * @return
     */
    @GetMapping("/knowledge")
    public AjaxResult knowledge(PresentationDto presentation){
        if (presentation.getMajor()==null || presentation.getCourse()==null){
            return success();
        }
        List<KnowledgeBaseFile> knowledgeBaseFileList = presentationService.selectTxtIdByCourse(KnowledgeBaseFile.builder()
                .courseName(presentation.getCourse()).majorId(Long.valueOf(presentation.getMajor()))
                .build());
        List<KnowledgeBaseFileVo> knowledgeBaseFileVoList =new ArrayList<>();

        for (KnowledgeBaseFile k:knowledgeBaseFileList) {
            List<String> chapterList = presentationService.selectChapterById(k.getId());

            knowledgeBaseFileVoList.add(KnowledgeBaseFileVo.builder()
                    .id(k.getId())
                    .fileName(k.getFileName())
                    .courseName(k.getCourseName())
                    .chapterList(chapterList)
                    .build());
        }

        return success(knowledgeBaseFileVoList);
    }



}
