package com.ruoyi.create.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.baidu.api.BaiduApiService;
import com.ruoyi.baidu.api.dto.BaiduDto;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.create.domain.ApplicationScenario;
import com.ruoyi.create.domain.ApplicationScenarioKnowledgeBase;
import com.ruoyi.create.domain.NacosClient;
import com.ruoyi.create.domain.StudentScenario;
import com.ruoyi.create.domain.execl.Major;
import com.ruoyi.create.dto.UserDto;
import com.ruoyi.create.exception.CustomException;
import com.ruoyi.create.mapper.ApplicationScenarioMapper;
import com.ruoyi.create.service.IApplicationScenarioService;
import com.ruoyi.create.service.IStudentScenarioService;
import com.ruoyi.create.utils.Snowflake;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysRole;
import com.ruoyi.create.mapper.ApplicationScenarioKnowledgeBaseMapper;
import com.ruoyi.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ListableBeanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.ruoyi.common.core.utils.PageUtils.startPage;

/**
 * 应用场景Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-10
 */
@Slf4j
@Service
public class ApplicationScenarioServiceImpl implements IApplicationScenarioService
{
    @Resource
    private ApplicationScenarioMapper applicationScenarioMapper;
    @Resource
    private IStudentScenarioService studentScenarioService;
    @Resource
    private RemoteUserService remoteUserService;
    @Autowired
    private ListableBeanFactory listableBeanFactory;
    @Resource
    private BaiduApiService baiduApiService;
    @Autowired
    private NacosClient nacosClient;
    @Autowired
    private ApplicationScenarioKnowledgeBaseMapper applicationScenarioKnowledgeBaseMapper;


    /**
     * 查询应用场景
     *
     * @param id 应用场景主键
     * @return 应用场景
     */
    @Override
    public ApplicationScenario selectApplicationScenarioById(Long id)
    {
        return applicationScenarioMapper.selectApplicationScenarioById(id);
    }

    @Override
    public void selectApplicationScenarioImageById(Long id, HttpServletResponse response) {
        String imageUrl =applicationScenarioMapper.selectApplicationScenarioById(id).getImagePath();
        String username = SecurityUtils.getUsername();
        UserDto userDto = selectUserByUserName(username);

        if(userDto.getStudentId() != null && userDto.getStudentId() != ""){
            StudentScenario studentScenario = studentScenarioService.selectStudentScenarioBgById(StudentScenario.builder().scenario(String.valueOf(id)).studentId(Long.valueOf(userDto.getStudentId())).build());
            if (studentScenario!=null && studentScenario.getImageUrl()!=null && studentScenario.getImageUrl() != ""){
                imageUrl =studentScenario.getImageUrl();
            }
        }
        readFile(response,imageUrl);
    }

    @Override
    public String selectApplicationScenarioImagePathById(Long id) {
        String imagePath =applicationScenarioMapper.selectApplicationScenarioById(id).getImagePath();
        String studentId = SecurityUtils.getLoginUser().getSysUser().getStudentId();
        if(studentId != null && !studentId.isEmpty()){
            StudentScenario studentScenario = studentScenarioService.selectStudentScenarioBgById(StudentScenario.builder().scenario(String.valueOf(id)).studentId(Long.valueOf(studentId)).build());
            if (studentScenario!=null && studentScenario.getImageUrl()!=null && !Objects.equals(studentScenario.getImageUrl(), "")){
                imagePath =studentScenario.getImageUrl();
            }
        }
        return imagePath;
    }

    public void readFile(HttpServletResponse response, String filepath) {
        File file = new File(filepath);
        if (!file.exists()) {
            log.warn("文件不存在: " + filepath);
            return;
        }

        try (FileInputStream fileInputStream = new FileInputStream(file);
             OutputStream outputStream = response.getOutputStream()) {

            // 根据文件扩展名或者文件内容自动设置 Content-Type
            String contentType = Files.probeContentType(Paths.get(filepath));
            if (contentType == null) {
                // 根据文件扩展名手动设置一些常见的Content-Type
                if (filepath.endsWith(".txt")) {
                    contentType = "text/plain; charset=UTF-8";
                } else if (filepath.endsWith(".html")) {
                    contentType = "text/html; charset=UTF-8";
                } else if (filepath.endsWith(".pdf")) {
                    contentType = "application/pdf";
                } else if (filepath.endsWith(".jpg") || filepath.endsWith(".jpeg")) {
                    contentType = "image/jpeg";
                } else if (filepath.endsWith(".png")) {
                    contentType = "image/png";
                } else {
                    contentType = "application/octet-stream";  // 默认类型，适合任意二进制文件
                }
            }

            // 设置响应的内容类型
            response.setContentType(contentType);

            byte[] bytes = new byte[4096];
            int len;
            while ((len = fileInputStream.read(bytes)) != -1) {
                outputStream.write(bytes, 0, len);
            }
            outputStream.flush();

        } catch (Exception e) {
            log.error("读取文件异常", e);
            throw new CustomException("读取文件异常");
        }
    }

    /**
     * 查询应用场景列表
     *
     * @param applicationScenario 应用场景
     * @return 应用场景
     */
    @Override
    public List<ApplicationScenario> selectApplicationScenarioList(ApplicationScenario applicationScenario) {
        R<LoginUser> userAndRole = remoteUserService.getUserAndRole(SecurityUtils.getUserId(), SecurityConstants.INNER);
        LoginUser loginUser = userAndRole.getData();
        String userName = loginUser.getSysUser().getUserName();
        String jobId = loginUser.getSysUser().getJobId();
        Long majorId = loginUser.getSysUser().getMajorId();
        Long universityId = loginUser.getSysUser().getUniversityId();

        // 统一角色判断
        boolean isSuperAdmin = loginUser.getSysUser().isAdmin();
        boolean isTeacher = loginUser.getSysUser().getRoles().stream()
                .anyMatch(role -> "教师".equals(role.getRoleName()) && role.isFlag());
        boolean isAdmin = SecurityUtils.isAdmin(SecurityUtils.getUserId());

        // 非管理员，限定学校
        if (!isAdmin) {
            applicationScenario.setUniversityId(universityId);
        }

        // 超级管理员查全部
        if (isSuperAdmin) {
            applicationScenario.setCreateBy("");
            return applicationScenarioMapper.selectApplicationScenarioList(applicationScenario);
        }

        // 教师角色，查课程组
        if (isTeacher) {
            List<String> userNameList = applicationScenarioMapper.selectUserNameByJobId(jobId,universityId);
            if (userNameList != null && !userNameList.isEmpty()) {
                startPage();
                return applicationScenarioMapper.selectApplicationScenarioListByUserNames(userNameList);
            }
            // 没有课程组，查自己
            applicationScenario.setCreateBy(userName);
            return applicationScenarioMapper.selectApplicationScenarioList(applicationScenario);
        }

        // 普通教师/学生，查自己或本专业
        ApplicationScenario scenarioByMajor = new ApplicationScenario();
        scenarioByMajor.setMajor(String.valueOf(majorId));
        return applicationScenarioMapper.selectApplicationScenarioList(scenarioByMajor);
    }

    /**
     * 新增应用场景
     *
     * @param applicationScenario 应用场景
     * @return 结果
     */
    @Override
    public int insertApplicationScenario(ApplicationScenario applicationScenario)
    {
        applicationScenario.setCreateTime(DateUtils.getNowDate());
        return applicationScenarioMapper.insertApplicationScenario(applicationScenario);
    }

    /**
     * 修改应用场景
     *
     * @param applicationScenario 应用场景
     * @return 结果
     */
    @Override
    public int updateApplicationScenario(ApplicationScenario applicationScenario)
    {
        applicationScenario.setUpdateTime(DateUtils.getNowDate());
        ApplicationScenario old = applicationScenarioMapper.selectApplicationScenarioById(applicationScenario.getId());
        if (old.getImagePath() != null && applicationScenario.getImagePath() != null){
            deleteFile(new File(old.getImagePath()));
        }
        return applicationScenarioMapper.updateApplicationScenario(applicationScenario);
    }

    /**
     * 批量删除应用场景
     *
     * @param ids 需要删除的应用场景主键
     * @return 结果
     */
    @Override
    public int deleteApplicationScenarioByIds(Long[] ids)
    {
        for (Long id:ids) {
            ApplicationScenario applicationScenario = applicationScenarioMapper.selectApplicationScenarioById(id);
            if (applicationScenario.getImagePath() != null){
                deleteFile(new File(applicationScenario.getImagePath()));
            }
            //删除之前找出对应的知识库来删除

            Long Id =applicationScenario.getId();
            List<ApplicationScenarioKnowledgeBase> list = applicationScenarioKnowledgeBaseMapper.selectList(
                    new LambdaQueryWrapper<ApplicationScenarioKnowledgeBase>()
                            .eq(ApplicationScenarioKnowledgeBase::getApplicationScenarioId, Id)
            );
            if (list != null && !list.isEmpty()) {
                ApplicationScenarioKnowledgeBase applicationScenarioKnowledgeBase = list.get(0);
                BaiduDto baiduDto = new BaiduDto();
                String secretkey = nacosClient.getSecretkey();
//                String secretkey ="bce-v3/ALTAK-gG168dtOeRpl6DZkMKkbU/0fd2c04dd65715bb17783db4cd0d9039a610b345";
                baiduDto.setSecretkey(secretkey);
                baiduDto.setDatasetId(applicationScenarioKnowledgeBase.getKbId());
                baiduApiService.delKnowledgeBase(baiduDto, SecurityConstants.INNER);
            }
        }



        return applicationScenarioMapper.deleteApplicationScenarioByIds(ids);
    }

    /**
     * 删除应用场景信息
     *
     * @param id 应用场景主键
     * @return 结果
     */
    @Override
    public int deleteApplicationScenarioById(Long id)
    {
        ApplicationScenario applicationScenario = applicationScenarioMapper.selectApplicationScenarioById(id);
        if (applicationScenario.getImagePath() != null){
            deleteFile(new File(applicationScenario.getImagePath()));
        }
        return applicationScenarioMapper.deleteApplicationScenarioById(id);
    }

    @Override
    public UserDto selectUserByUserName(String username) {
        List<UserDto> userDtos = applicationScenarioMapper.selectUserByUserName(username);
        if (userDtos.size() > 1){
            throw new CustomException("当前登录用户出现重复，请检查数据");
        }
        return userDtos.get(0);
    }

    @Override
    public List<String> selectUserRoleKeyByUserName(String username) {
        return applicationScenarioMapper.selectUserRoleKeyByUserName(username);
    }

    @Override
    public List<Major> selectMajorList() {
        return applicationScenarioMapper.selectMajorListAll();
    }

    @Override
    public int selectApplicationScenarioByNameAndMajor(String applicationName, String major) {
        return applicationScenarioMapper.selectApplicationScenarioByNameAndMajor(applicationName,major);
    }


    // 文件删除方法
    private void deleteFile(File file) {
        try {
            if (file.exists() && !file.delete()) {
                throw new IOException("删除文件失败: " + file.getAbsolutePath());
            }
        } catch (Exception e) {
            // 记录异常日志，或者根据需求处理
            System.err.println("删除文件失败: " + e.getMessage());
            // 根据需要抛出自定义异常
            throw new CustomException("删除文件失败: " + file.getAbsolutePath(), e);
        }
    }

}
