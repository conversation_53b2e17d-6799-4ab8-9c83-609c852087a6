package com.ruoyi.create.constant;

/**
 * 符号常量 字符厂里（）
 *
 * <AUTHOR> @date 2020/3/16 12:05
 */
public interface SymbolConstant {

    String PERIOD = ".";

    String COMMA = ",";

    String COLON = ":";

    String SEMICOLON = ";";

    String EXCLAMATION_MARK = "!";

    String QUESTION_MARK = "?";

    String HYPHEN = "-";
    String CH_DUNHAO = "、";

    String ASTERISK = "*";

    String APOSTROPHE = "`";

    String DASH = "-";

    String UNDER_SCORE = "_";

    String SINGLE_QUOTATION_MARK = "'";

    String DOUBLE_QUOTATION_MARK = "\"";

    String LEFT_ROUND_BRACKETS = "(";

    String LEFT_ALL_BRACKETS = "（";

    String RIGHT_ROUND_BRACKETS = ")";

    String RIGHT_ALL_BRACKETS = "）";

    String LEFT_SQUARE_BRACKETS = "[";

    String RIGHT_SQUARE_BRACKETS = "]";

    String LEFT_ANGLE_BRACKETS = "<";

    String RIGHT_ANGLE_BRACKETS = ">";

    String LEFT_CURLY_BRACKETS = "{";

    String RIGHT_CURLY_BRACKETS = "}";

    String DOLLAR = "$";

    String PERCENT = "%";

    String LEFT_DIVIDE = "/";

    String RIGHT_DIVIDE = "\\";

    String LEFT_DOUBLE_DIVIDE = "//";

    String RIGHT_DOUBLE_DIVIDE = "\\\\";

    String EQUAL = "=";
}

