<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.KnowledgeRoutingMapper">
    
    <resultMap type="KnowledgeRouting" id="KnowledgeRoutingResult">
        <result property="id"    column="id"    />
        <result property="appId"    column="app_id"    />
        <result property="secretKey"    column="secret_key"    />
        <result property="menuRouting"    column="menu_routing"    />
        <result property="notes"    column="notes"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectKnowledgeRoutingVo">
        select id, app_id, secret_key, menu_routing,notes, create_by, create_time, update_by, update_time from s_knowledge_routing
    </sql>

    <select id="selectKnowledgeRoutingList" parameterType="KnowledgeRouting" resultMap="KnowledgeRoutingResult">
        <include refid="selectKnowledgeRoutingVo"/>
        <where>  
            <if test="appId != null  and appId != ''"> and app_id = #{appId}</if>
            <if test="secretKey != null  and secretKey != ''"> and secret_key = #{secretKey}</if>
            <if test="menuRouting != null  and menuRouting != ''"> and menu_routing = #{menuRouting}</if>
        </where>
    </select>
    
    <select id="selectKnowledgeRoutingById" parameterType="Long" resultMap="KnowledgeRoutingResult">
        <include refid="selectKnowledgeRoutingVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertKnowledgeRouting" parameterType="KnowledgeRouting" useGeneratedKeys="true" keyProperty="id">
        insert into s_knowledge_routing
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appId != null">app_id,</if>
            <if test="secretKey != null">secret_key,</if>
            <if test="menuRouting != null">menu_routing,</if>
            <if test="notes != null">notes,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appId != null">#{appId},</if>
            <if test="secretKey != null">#{secretKey},</if>
            <if test="menuRouting != null">#{menuRouting},</if>
            <if test="notes != null">#{notes},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateKnowledgeRouting" parameterType="KnowledgeRouting">
        update s_knowledge_routing
        <trim prefix="SET" suffixOverrides=",">
            <if test="appId != null">app_id = #{appId},</if>
            <if test="secretKey != null">secret_key = #{secretKey},</if>
            <if test="menuRouting != null">menu_routing = #{menuRouting},</if>
            <if test="notes != null">notes = #{notes},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKnowledgeRoutingById" parameterType="Long">
        delete from s_knowledge_routing where id = #{id}
    </delete>

    <delete id="deleteKnowledgeRoutingByIds" parameterType="String">
        delete from s_knowledge_routing where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectKnowledgeByMenuRouting"  resultMap="KnowledgeRoutingResult">
        <include refid="selectKnowledgeRoutingVo"/>
         where menu_routing = #{menuRouting}
    </select>
</mapper>