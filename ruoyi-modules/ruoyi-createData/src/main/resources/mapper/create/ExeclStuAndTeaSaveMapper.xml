<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.ExeclStuAndTeaSaveMapper">





    <resultMap id="Coll" type="com.ruoyi.create.domain.execl.Coll">
        <result column="id" property="id"></result>
        <result column="colle_name" property="colleName"></result>
    </resultMap>
    <resultMap id="Coll2" type="com.ruoyi.create.domain.CollegeInfo">
        <result column="id" property="id"></result>
        <result column="colle_name" property="colleName"></result>
    </resultMap>

    <resultMap id="SUser" type="com.ruoyi.create.domain.execl.User">
        <id column="user_id" property="userId" />
        <result column="dept_id" property="deptId" />
        <result column="user_name" property="userName" />
        <result column="nick_name" property="nickName" />
        <result column="user_type" property="userType" />
        <result column="email" property="email" />
        <result column="phonenumber" property="phonenumber" />
        <result column="sex" property="sex" />
        <result column="avatar" property="avatar" />
        <result column="password" property="password" />
        <result column="status" property="status" />
        <result column="del_flag" property="delFlag" />
        <result column="login_ip" property="loginIp" />
        <result column="login_date" property="loginDate" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remark" property="remark" />
        <result column="identity_card" property="identityCard" />
        <result column="auth_status" property="authStatus" />
        <result column="university_id" property="universityId" />
        <result column="college_id" property="collegeId" />
        <result column="major_id" property="majorId" />
        <result column="class_id" property="classId" />
        <result column="job_id" property="jobId" />
        <result column="student_id" property="studentId" />
        <result column="exp_time_start" property="expTimeStart" />
        <result column="exp_time_end" property="expTimeEnd" />
    </resultMap>

    <resultMap id="Tea" type="com.ruoyi.create.domain.execl.Teacher">
        <result column="teacher_id" property="teacherId"/>
        <result column="teacher_name" property="teacherName"/>
        <result column="univer_id" property="univerId"/>
        <result column="colle_id" property="colleId"/>
    </resultMap>

    <resultMap id="Stu" type="com.ruoyi.create.domain.execl.Student">
        <result column="student_id" property="studentId"/>
        <result column="sex" property="sex"/>

        <result column="student_name" property="studentName"/>
        <result column="univer_id" property="univerId"/>

        <result column="colle_id" property="colleId"/>

        <result column="major_id" property="majorId"/>

        <result column="class_id" property="classId"/>

        <result column="educational_system" property="educationalSystem"/>
        <result column="current_grade" property="currentGrade"/>
        <result column="school_status" property="schoolStatus"/>

        <result column="student_status" property="studentStatus"/>

        <result column="student_current_status" property="studentCurrentStatus"/>

        <result column="gradation" property="gradation"/>
    </resultMap>



    <resultMap id="majorInfo" type="com.ruoyi.create.domain.execl.Major">
        <id property="id" column="id" />
        <result property="majorName" column="major_name" />
        <result property="univerId" column="univer_id" />
        <result property="colleId" column="colle_id" />
        <result property="phone" column="phone" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <resultMap id="majorInfo2" type="com.ruoyi.create.domain.MajorInfo">
        <id property="id" column="id" />
        <result property="majorName" column="major_name" />
        <result property="univerId" column="univer_id" />
        <result property="colleId" column="colle_id" />
        <result property="phone" column="phone" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <resultMap id="ClassInfo" type="com.ruoyi.create.domain.execl.Class">
        <id property="id" column="id" />
        <result property="className" column="class_name" />
        <result property="univerId" column="univer_id" />
        <result property="colleId" column="colle_id" />
        <result property="majorId" column="major_id" />
        <result property="classTeacher" column="class_teacher" />
        <result property="phone" column="phone" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <resultMap id="ClassInfo2" type="com.ruoyi.create.domain.ClassInfo">
        <id property="id" column="id" />
        <result property="className" column="class_name" />
        <result property="univerId" column="univer_id" />
        <result property="colleId" column="colle_id" />
        <result property="majorId" column="major_id" />
        <result property="classTeacher" column="class_teacher" />
        <result property="phone" column="phone" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
    </resultMap>


    <resultMap id="CollInfo" type="com.ruoyi.create.domain.execl.Coll">
        <id property="id" column="id" />
        <result property="colleName" column="colle_name" />
        <result property="univerId" column="univer_id" />
        <result property="phone" column="phone" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
    </resultMap>
    <resultMap id="CollInfo2" type="com.ruoyi.create.domain.CollegeInfo">
        <id property="id" column="id" />
        <result property="colleName" column="colle_name" />
        <result property="univerId" column="univer_id" />
        <result property="phone" column="phone" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
    </resultMap>
    <resultMap id="Dept" type="com.ruoyi.create.domain.SysDept">
        <result column="dept_id" property="deptId"/>
        <result column="parent_id" property="parentId"/>
        <result column="ancestors" property="ancestors"/>
        <result column="dept_name" property="deptName"/>
        <result column="order_num" property="orderNum"/>
        <result column="leader" property="leader"/>
        <result column="phone" property="phone"/>
        <result column="email" property="email"/>
        <result column="status" property="status"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>


    <select id="selectCollByName"  resultMap="Coll">
        select id,colle_name
        from s_college_info
        where colle_name=#{collName} and univer_id=#{univerId}
    </select>
    <select id="selectCollIdByName" resultType="long">
        select id
        from s_college_info
        where colle_name = #{collName} and univer_id=#{univerId}
    </select>
    <select id="selectMajIdByName" resultType="long">
        select id
        from s_major_info
        where major_name = #{majName} and univer_id=#{univerId}
    </select>

    <select id="selectClaIdByName" resultType="long">
        select id
        from s_class_info
        where class_name = #{claName} and univer_id=#{univerId}
    </select>

    <select id="selectStuById" parameterType="long" resultMap="Stu">
        select id
        from s_student_info
        where student_id = #{studentId} and univer_id=1
    </select>

    <insert id="insertCla" useGeneratedKeys="true" keyProperty="id" parameterType="com.ruoyi.create.domain.execl.Class">
        insert into s_class_info(class_name,univer_id,colle_id,major_id,create_by,create_time)
        values (#{className},#{univerId},#{colleId},#{majorId},#{createBy},#{createTime})
    </insert>

    <insert id="insertMajor" useGeneratedKeys="true" keyProperty="id" parameterType="com.ruoyi.create.domain.execl.Major">
        insert into s_major_info(major_name,univer_id,colle_id,create_by,create_time)
        values (#{majorName},#{univerId},#{colleId},#{createBy},#{createTime})
    </insert>

    <insert id="insertColl" useGeneratedKeys="true" keyProperty="id" parameterType="com.ruoyi.create.domain.execl.Coll">
        insert into s_college_info(colle_name,univer_id,create_by,create_time)
        values (#{colleName},#{univerId},#{createBy},#{createTime})
    </insert>


    <insert id="insertOrUpdateBatchS" parameterType="com.ruoyi.create.domain.execl.Student">
        INSERT INTO s_student_info(
        student_id,
        student_name,
        sex,
        univer_id,
        colle_id,
        major_id,
        class_id,
        educational_system,
        current_grade,
        school_status,
        student_status,
        student_current_status,
        create_by,
        create_time,
        gradation
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.studentId},
                #{item.studentName},
                #{item.sexMark},
                #{item.univerId},
                #{item.colleId},
                #{item.majorId},
                #{item.classId},
                #{item.educationalSystem},
                #{item.currentGrade},
                #{item.schoolStatus},
                #{item.studentStatus},
                #{item.studentCurrentStatus},
                #{item.createBy},
                #{item.createTime},
                #{item.gradation}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        student_id = VALUES(student_id),
        student_name = VALUES(student_name),
        sex = VALUES(sex),
        univer_id = VALUES(univer_id),
        colle_id = VALUES(colle_id),
        major_id = VALUES(major_id),
        class_id = VALUES(class_id),
        educational_system = VALUES(educational_system),
        current_grade = VALUES(current_grade),
        school_status = VALUES(school_status),
        student_status = VALUES(student_status),
        student_current_status = VALUES(student_current_status),
        create_by = VALUES(create_by),
        create_time = VALUES(create_time),
        gradation = VALUES(gradation)
    </insert>
    <insert id="insertOrUpdateBatchT" parameterType="com.ruoyi.create.domain.execl.Teacher">
        INSERT INTO s_teacher_info(
        teacher_id,
        teacher_name,
        univer_id,
        colle_id,
        create_by,
        create_time
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.teacherId},
                #{item.teacherName},
                #{item.univerId},
                #{item.colleId},
                #{item.createBy},
                #{item.createTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        teacher_id = VALUES(teacher_id),
        teacher_name = VALUES(teacher_name),
        univer_id = VALUES(univer_id),
        colle_id = VALUES(colle_id),
        create_by = VALUES(create_by),
        create_time = VALUES(create_time)
    </insert>

    <insert id="insertOrUpdateBatchUS" useGeneratedKeys="true" keyProperty="list.userId"  parameterType="com.ruoyi.create.domain.execl.Student">
        INSERT INTO sys_user (
        dept_id,
        user_name,
        nick_name,

        user_type,
        sex,
        password,

        status,
        del_flag,
        create_by,
        create_time,

        auth_status,

        university_id,
        college_id,
        major_id,
        class_id,

        student_id
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.deptId},
                #{item.studentId},
                #{item.studentName},
                '00',
                #{item.sexMark},
                '$2a$10$gl0s.mkfM9ZSq84p.J5voeUl1mhrdzrNT3yOuKaGIUrW17PAHYzty',
                '0',
                '0',
                #{item.createBy},
                #{item.createTime},

                '0',

                #{item.univerId},
                #{item.colleId},
                #{item.majorId},
                #{item.classId},
                #{item.studentId}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        dept_id = values(dept_id),
        nick_name = VALUES(nick_name),

        user_type = VALUES(user_type),

        sex = VALUES(sex),
        password = VALUES(password),

        status = VALUES(status),
        del_flag = VALUES(del_flag),

        create_by = VALUES(create_by),
        create_time = VALUES(create_time),

        auth_status = VALUES(auth_status),

        university_id = VALUES(university_id),
        college_id = VALUES(college_id),
        major_id = VALUES(major_id),
        class_id = VALUES(class_id),

        student_id = VALUES(student_id)
    </insert>

    <insert id="insertOrUpdateUS" useGeneratedKeys="true" keyProperty="userId"  parameterType="com.ruoyi.create.domain.execl.Student">
        INSERT INTO sys_user (
            dept_id,
            user_name,
            nick_name,

            user_type,
            sex,
            password,

            status,
            del_flag,
            create_by,
            create_time,

            auth_status,

            university_id,
            college_id,
            major_id,
            class_id,

            student_id
        )
        VALUES
        (
            #{deptId},
            #{studentId},
            #{studentName},
            '00',
            #{sexMark},
            '$2a$10$gl0s.mkfM9ZSq84p.J5voeUl1mhrdzrNT3yOuKaGIUrW17PAHYzty',
            '0',
            '0',
            #{createBy},
            #{createTime},

            '2',

            #{univerId},
            #{colleId},
            #{majorId},
            #{classId},
            #{studentId}
        )
            ON DUPLICATE KEY UPDATE
                                 dept_id = VALUES(dept_id),
                                 nick_name = VALUES(nick_name),

                                 user_type = VALUES(user_type),

                                 sex = VALUES(sex),
                                 password = VALUES(password),

                                 status = VALUES(status),
                                 del_flag = VALUES(del_flag),

                                 create_by = VALUES(create_by),
                                 create_time = VALUES(create_time),

                                 auth_status = VALUES(auth_status),

                                 university_id = VALUES(university_id),
                                 college_id = VALUES(college_id),
                                 major_id = VALUES(major_id),
                                 class_id = VALUES(class_id),

                                 student_id = VALUES(student_id)
    </insert>



    <insert id="insertOrUpdateBatchUT" useGeneratedKeys="true" keyProperty="list.userId"  parameterType="com.ruoyi.create.domain.execl.Teacher">
        INSERT INTO sys_user (
        dept_id,
        user_name,
        nick_name,

        user_type,

        password,

        status,
        del_flag,
        create_by,
        create_time,

        auth_status,

        university_id,
        college_id,


        job_id
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.deptId},
                #{item.teacherId},
                #{item.teacherName},
                '00',
                '$2a$10$gl0s.mkfM9ZSq84p.J5voeUl1mhrdzrNT3yOuKaGIUrW17PAHYzty',
                '0',
                '0',
                #{item.createBy},
                #{item.createTime},

                '0',

                #{item.univerId},
                #{item.colleId},

                #{item.teacherId}

            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        dept_id =values(dept_id),
        nick_name = VALUES(nick_name),

        user_type = VALUES(user_type),

        password = VALUES(password),

        status = VALUES(status),
        del_flag = VALUES(del_flag),

        create_by = VALUES(create_by),
        create_time = VALUES(create_time),

        auth_status = VALUES(auth_status),

        university_id = VALUES(university_id),
        college_id = VALUES(college_id),

        job_id = VALUES(job_id)

    </insert>

    <insert id="insertOrUpdateUT" useGeneratedKeys="true" keyProperty="userId"  parameterType="com.ruoyi.create.domain.execl.Teacher">
        INSERT INTO sys_user (
            dept_id,
            user_name,
            nick_name,

            user_type,
            sex,
            password,

            status,
            del_flag,
            create_by,
            create_time,

            auth_status,

            university_id,
            college_id,

            job_id
        )
        VALUES(
                  #{deptId},
                  #{teacherId},
                  #{teacherName},
                  '00',
                  null,
                  '$2a$10$gl0s.mkfM9ZSq84p.J5voeUl1mhrdzrNT3yOuKaGIUrW17PAHYzty',
                  '0',
                  '0',
                  #{createBy},
                  #{createTime},

                  '2',

                  #{univerId},
                  #{colleId},

                  #{teacherId}

              )
            ON DUPLICATE KEY UPDATE
                                 dept_id = values(dept_id),
                                 nick_name = VALUES(nick_name),

                                 user_type = VALUES(user_type),
                                 sex = VALUES(sex),
                                 password = VALUES(password),

                                 status = VALUES(status),
                                 del_flag = VALUES(del_flag),

                                 create_by = VALUES(create_by),
                                 create_time = VALUES(create_time),

                                 auth_status = VALUES(auth_status),

                                 university_id = VALUES(university_id),
                                 college_id = VALUES(college_id),

                                 job_id = VALUES(job_id)

    </insert>

    <select id="selectUserIdByUserName" parameterType="string" resultType="string">
        select user_id from sys_user where user_name =#{userName}
    </select>

    <update id="updateAuthStatusByUserId" parameterType="com.ruoyi.create.domain.execl.User">
        UPDATE  sys_user
        set auth_status=#{authStatus}
        where user_id=#{userId}
    </update>

    <insert id="insertUserRoleBatchS" parameterType="long">
        insert into sys_user_role(user_id,role_id)
        values
        <foreach collection="list" item="item" separator=",">
                (#{item.userId}, '101')
        </foreach>
        ON DUPLICATE KEY UPDATE
        user_id = VALUES(user_id),
        role_id = VALUES(role_id)
    </insert>
    <insert id="insertUserRoleBatchSP" parameterType="long">
        insert into sys_user_role(user_id,role_id)
        values
        <foreach collection="list" item="item"  separator=",">
                (#{item.userId},'104')
        </foreach>
        ON DUPLICATE KEY UPDATE
        user_id = VALUES(user_id),
        role_id = VALUES(role_id)
    </insert>
    <insert id="insertUserRoleBatchT" parameterType="long">
        insert into sys_user_role(user_id,role_id)
        values
        <foreach collection="list" item="item"  separator=",">
                (#{item.userId},'102')
        </foreach>
        ON DUPLICATE KEY UPDATE
        user_id = VALUES(user_id),
        role_id = VALUES(role_id)
    </insert>


    <insert id="insertUserRoleS" parameterType="long">
        insert into sys_user_role(user_id,role_id)
        values(#{userId}, '101')
            ON DUPLICATE KEY UPDATE
                                 user_id = VALUES(user_id),
                                 role_id = VALUES(role_id)
    </insert>
    <insert id="insertUserRoleSP" parameterType="long">
        insert into sys_user_role(user_id,role_id)
        values(#{userId},'104')
            ON DUPLICATE KEY UPDATE
                                 user_id = VALUES(user_id),
                                 role_id = VALUES(role_id)
    </insert>
    <insert id="insertUserRoleT" parameterType="long">
        insert into sys_user_role(user_id,role_id)
        values(#{userId},'102')
            ON DUPLICATE KEY UPDATE
                                 user_id = VALUES(user_id),
                                 role_id = VALUES(role_id)
    </insert>




<!--                ==========================================================                                     -->


    <select id="selectStuById2" parameterType="string" resultMap="Stu">
        select student_id,student_name,univer_id,colle_id,major_id,class_id
        from s_student_info
        where student_id = #{studentId}
    </select>

    <select id="selectTeaById2" parameterType="string" resultMap="Tea">
        select teacher_id,teacher_name,univer_id,colle_id
        from s_teacher_info
        where teacher_id = #{teacherId}
    </select>

    <delete id="deleteByDeFalg" >
        delete from sys_user
        where del_flag='2'
    </delete>

    <select id="selectUniverNameById" parameterType="long" resultType="string">
        select univer_name from s_university where id = #{Id}
    </select>
    <!--                ==========================================================                                     -->

    <select id="selectDeptByNameAndPId" parameterType="com.ruoyi.create.domain.SysDept" resultMap="Dept">
        SELECT
            dept_id,parent_id,ancestors,dept_name,order_num,leader,phone,email,status,
            del_flag,create_by,create_time,update_by,update_time
        FROM sys_dept
        WHERE dept_name = #{deptName} and parent_id  = #{parentId}
    </select>
    <select id="selectDeptInit" parameterType="com.ruoyi.create.domain.SysDept" resultMap="Dept">
        SELECT
            dept_id,parent_id,ancestors,dept_name,order_num,leader,phone,email,status,
            del_flag,create_by,create_time,update_by,update_time
        FROM sys_dept
        WHERE ancestors = #{ancestors} and parent_id  = #{parentId}
    </select>


    <insert id="insertOrUpdateDept" useGeneratedKeys="true" keyProperty="deptId"  parameterType="com.ruoyi.create.domain.SysDept">
        INSERT INTO sys_dept (
            parent_id,
            ancestors,
            dept_name,
            order_num,
            leader,
            phone,
            email,
            status,
            del_flag,
            create_by,
            create_time,
            update_by,
            update_time
        ) VALUES (
                     #{parentId},
                     #{ancestors},
                     #{deptName},
                     #{orderNum},
                     #{leader},
                     #{phone},
                     #{email},
                     #{status},
                     #{delFlag},
                     #{createBy},
                     #{createTime},
                     #{updateBy},
                     #{updateTime}
                 ) ON DUPLICATE KEY UPDATE
            ancestors = VALUES(ancestors),
            order_num = VALUES(order_num),
            leader = VALUES(leader),
            phone = VALUES(phone),
            email = VALUES(email),
            status = VALUES(status),
            del_flag = VALUES(del_flag),
            update_by = VALUES(update_by),
            update_time = VALUES(update_time);

    </insert>


</mapper>
