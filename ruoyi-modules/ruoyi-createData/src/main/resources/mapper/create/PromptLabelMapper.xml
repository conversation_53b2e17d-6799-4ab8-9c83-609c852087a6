<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.PromptLabelMapper">
    
    <resultMap type="com.ruoyi.create.domain.PromptLabel" id="PromptLabelResult">
        <result property="id"    column="id"    />
        <result property="templatePk"    column="template_pk"    />
        <result property="labelId"    column="label_id"    />
        <result property="labelPk"    column="label_pk"    />
        <result property="labelName"    column="label_name"    />
        <result property="color"    column="color"    />
    </resultMap>

    <sql id="selectPromptLabelVo">
        select id, template_pk, label_id, label_pk, label_name, color from s_prompt_label
    </sql>

    <select id="selectPromptLabelList" parameterType="com.ruoyi.create.domain.PromptLabel" resultMap="PromptLabelResult">
        <include refid="selectPromptLabelVo"/>
        <where>  
            <if test="templatePk != null  and templatePk != ''"> and template_pk = #{templatePk}</if>
            <if test="labelId != null "> and label_id = #{labelId}</if>
            <if test="labelPk != null  and labelPk != ''"> and label_pk = #{labelPk}</if>
            <if test="labelName != null  and labelName != ''"> and label_name like concat('%', #{labelName}, '%')</if>
            <if test="color != null  and color != ''"> and color = #{color}</if>
        </where>
    </select>
    
    <select id="selectPromptLabelById" parameterType="Long" resultMap="PromptLabelResult">
        <include refid="selectPromptLabelVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertPromptLabel" parameterType="com.ruoyi.create.domain.PromptLabel" useGeneratedKeys="true" keyProperty="id">
        insert into s_prompt_label
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="templatePk != null">template_pk,</if>
            <if test="labelId != null">label_id,</if>
            <if test="labelPk != null">label_pk,</if>
            <if test="labelName != null">label_name,</if>
            <if test="color != null">color,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="templatePk != null">#{templatePk},</if>
            <if test="labelId != null">#{labelId},</if>
            <if test="labelPk != null">#{labelPk},</if>
            <if test="labelName != null">#{labelName},</if>
            <if test="color != null">#{color},</if>
         </trim>
    </insert>

    <update id="updatePromptLabel" parameterType="com.ruoyi.create.domain.PromptLabel">
        update s_prompt_label
        <trim prefix="SET" suffixOverrides=",">
            <if test="templatePk != null">template_pk = #{templatePk},</if>
            <if test="labelId != null">label_id = #{labelId},</if>
            <if test="labelPk != null">label_pk = #{labelPk},</if>
            <if test="labelName != null">label_name = #{labelName},</if>
            <if test="color != null">color = #{color},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePromptLabelById" parameterType="Long">
        delete from s_prompt_label where id = #{id}
    </delete>

    <delete id="deletePromptLabelByIds" parameterType="String">
        delete from s_prompt_label where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>