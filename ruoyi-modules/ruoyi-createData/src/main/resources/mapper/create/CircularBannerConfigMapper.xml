<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.CircularBannerConfigMapper">

    <resultMap type="CircularBannerConfig" id="CircularBannerConfigResult">
        <result property="id"    column="id"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileId"    column="file_id"    />
        <result property="title"    column="title"    />
        <result property="imageUrl"    column="image_url"    />
        <result property="description"    column="description"    />
        <result property="school"    column="school"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCircularBannerConfigVo">
        select id, file_name, file_id, title, image_url, description, school, create_by, create_time, update_by, update_time from s_circular_banner_config
    </sql>

    <select id="selectCircularBannerConfigList" parameterType="CircularBannerConfig" resultMap="CircularBannerConfigResult">
        <include refid="selectCircularBannerConfigVo"/>
        <where>
            <if test="fileName != null  and fileName != ''"> and file_name = #{fileName}</if>
            <if test="fileId != null  and fileId != ''"> and file_id = #{fileId}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="imageUrl != null  and imageUrl != ''"> and image_url = #{imageUrl}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="school != null  and school != ''"> and school = #{school}</if>
            <if test="school == null  or school == ''"> and school = "山东财经大学"</if>
        </where>
    </select>

    <select id="selectCircularBannerConfigListAll" resultMap="CircularBannerConfigResult">
        <include refid="selectCircularBannerConfigVo"/>
        where school = #{univerName}
    </select>

    <select id="selectCircularBannerConfigById" parameterType="Long" resultMap="CircularBannerConfigResult">
        <include refid="selectCircularBannerConfigVo"/>
        where id = #{id}
    </select>

    <select id="selectCircularBannerConfigBySchool" parameterType="String" resultMap="CircularBannerConfigResult">
        <include refid="selectCircularBannerConfigVo"/>
        where school = #{school}
    </select>

    <insert id="insertCircularBannerConfig" parameterType="CircularBannerConfig" useGeneratedKeys="true" keyProperty="id">
        insert into s_circular_banner_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileName != null">file_name,</if>
            <if test="fileId != null">file_id,</if>
            <if test="title != null">title,</if>
            <if test="imageUrl != null">image_url,</if>
            <if test="description != null">description,</if>
            <if test="school != null">school,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileName != null">#{fileName},</if>
            <if test="fileId != null">#{fileId},</if>
            <if test="title != null">#{title},</if>
            <if test="imageUrl != null">#{imageUrl},</if>
            <if test="description != null">#{description},</if>
            <if test="school != null">#{school},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateCircularBannerConfig" parameterType="CircularBannerConfig">
        update s_circular_banner_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="fileId != null">file_id = #{fileId},</if>
            <if test="title != null">title = #{title},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="description != null">description = #{description},</if>
            <if test="school != null">school = #{school},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCircularBannerConfigById" parameterType="Long">
        delete from s_circular_banner_config where id = #{id}
    </delete>

    <select id="selectCircularBannerConfigByFileName" parameterType="CircularBannerConfig" resultMap="CircularBannerConfigResult">
        <include refid="selectCircularBannerConfigVo"/>
        where file_name = #{fileName}
    </select>

    <select id="selectUniversity" parameterType="String" resultType="String">
        select univer_name from s_university where id in
            (select university_id from sys_user where user_name = #{userName})
    </select>

</mapper>