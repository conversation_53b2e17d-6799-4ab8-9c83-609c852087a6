<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.STextbookDataMapper">
    
    <resultMap type="com.ruoyi.create.domain.TextbookData" id="STextbookDataResult">
        <result property="id"    column="id"    />
        <result property="textbookId"    column="textbook_id"    />
        <result property="textbook"    column="textbook"    />
        <result property="academicDiscipline"    column="academic_discipline"    />
        <result property="universityName"    column="university_name"    />
        <result property="colleName"    column="colle_name"    />
        <result property="educationalLevel"    column="educational_level"    />
        <result property="majorName"    column="major_name"    />
        <result property="course"    column="course"    />
        <result property="publishingHouse"    column="publishing_house"    />
        <result property="author"    column="author"    />
        <result property="count"    column="count"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="fileName"    column="file_name"    />
        <result property="courseName"    column="course_name"    />
    </resultMap>

    <resultMap type="com.ruoyi.create.domain.MindMapping" id="MindMappingResult">
        <result property="academicDiscipline"    column="academic_discipline"    />
        <result property="colleName"    column="colle_name"    />
        <result property="majorName"    column="major_name"    />
        <result property="courseName"    column="course_name"    />
        <result property="fileName"    column="file_name"    />
    </resultMap>

    <resultMap type="com.ruoyi.create.domain.ThinkingKeyword" id="ThinkingKeywordResult">
        <result property="chapter"    column="chapter"    />
        <result property="keyword"    column="keyword"    />
    </resultMap>

    <resultMap type="com.ruoyi.create.domain.ProductSales" id="ProductSalesResult">
        <result property="product"    column="product"    />
        <result property="subject"    column="subject"    />
        <result property="course"    column="course"    />
        <result property="knowledge"    column="knowledge"    />
    </resultMap>

    <sql id="selectSTextbookDataVo">
        select id, textbook_id, textbook, academic_discipline, colle_name, educational_level, major_name,course, publishing_house, author, create_by, create_time, update_by, update_time from s_textbook_data
    </sql>

    <select id="selectSTextbookDataList" parameterType="com.ruoyi.create.domain.TextbookData" resultMap="STextbookDataResult">
        <include refid="selectSTextbookDataVo"/>
        <where>
            <if test="textbookId != null "> and textbook_id = #{textbookId}</if>
            <if test="textbook != null  and textbook != ''"> and textbook = #{textbook}</if>
            <if test="academicDiscipline != null  and academicDiscipline != ''"> and academic_discipline = #{academicDiscipline}</if>
            <if test="colleName != null  and colleName != ''"> and colle_name like concat('%', #{colleName}, '%')</if>
            <if test="educationalLevel != null  and educationalLevel != ''"> and educational_level = #{educationalLevel}</if>
            <if test="majorName != null  and majorName != ''"> and major_name like concat('%', #{majorName}, '%')</if>
            <if test="publishingHouse != null  and publishingHouse != ''"> and publishing_house = #{publishingHouse}</if>
            <if test="author != null  and author != ''"> and author = #{author}</if>
            <if test="course != null  and course != ''"> and course = #{course}</if>
        </where>
    </select>

    <select id="selectSTextbookDataById" parameterType="Long" resultMap="STextbookDataResult">
        <include refid="selectSTextbookDataVo"/>
        where id = #{id}
    </select>

    <select id="selectTextbookDataKnowledgeGraph" parameterType="com.ruoyi.create.domain.TextbookData" resultMap="STextbookDataResult">
        SELECT
            *,
            count(*) count
            FROM
            (
             SELECT
                textbook,
                academic_discipline,
                university_name,
                colle_name,
                major_name,
                course,
                publishing_house,
                author,
                category,
                keyword
                FROM
                    s_textbook_keyword_data c
                 JOIN s_textbook_data d ON c.textbook_id = d.textbook_id
            WHERE
                c.textbook_id IN ( SELECT DISTINCT a.textbook_id FROM s_textbook_keyword_data a JOIN s_textbook_data b ON a.textbook_id = b.textbook_id
            <where>
                <if test="knowledgeCategory != null  and knowledgeCategory != ''"> and knowledge_category =#{knowledgeCategory}</if>
                <if test="keywordValue != null  and keywordValue != '' and keyword != '新文科'"> and ${keywordValue} =#{keyword}</if>
            </where>
        )
        <if test="category != null  and category != ''"> and category = #{category}</if>
        <if test="course != null  and course != ''"> and course =#{course}</if>
        <if test="textbook != null  and textbook != ''"> and textbook =#{textbook}</if>
        GROUP BY
            ${field}
        ) e
        GROUP BY
             ${field}
        ORDER BY
        count DESC
        LIMIT 0,10
    </select>

    <insert id="insertSTextbookData" parameterType="com.ruoyi.create.domain.TextbookData">
        insert into s_textbook_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="textbookId != null">textbook_id,</if>
            <if test="textbook != null">textbook,</if>
            <if test="academicDiscipline != null">academic_discipline,</if>
            <if test="colleName != null">colle_name,</if>
            <if test="educationalLevel != null">educational_level,</if>
            <if test="majorName != null">major_name,</if>
            <if test="course != null">course,</if>
            <if test="publishingHouse != null">publishing_house,</if>
            <if test="author != null">author,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="textbookId != null">#{textbookId},</if>
            <if test="textbook != null">#{textbook},</if>
            <if test="academicDiscipline != null">#{academicDiscipline},</if>
            <if test="colleName != null">#{colleName},</if>
            <if test="educationalLevel != null">#{educationalLevel},</if>
            <if test="majorName != null">#{majorName},</if>
            <if test="course != null">#{course},</if>
            <if test="publishingHouse != null">#{publishingHouse},</if>
            <if test="author != null">#{author},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <insert id="insertSTextbookDataList" useGeneratedKeys="true" keyProperty="id">
        insert into s_textbook_data (textbook_id,textbook,academic_discipline,colle_name,educational_level,major_name,course,publishing_house,author ,create_by, create_time) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.textbookId},#{item.textbook},#{item.academicDiscipline},#{item.colleName},#{item.educationalLevel},#{item.majorName},#{item.course},#{item.publishingHouse},#{item.author},#{item.createBy},#{item.createTime})
        </foreach>
    </insert>

    <update id="updateSTextbookData" parameterType="com.ruoyi.create.domain.TextbookData">
        update s_textbook_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="textbookId != null">textbook_id = #{textbookId},</if>
            <if test="textbook != null">textbook = #{textbook},</if>
            <if test="academicDiscipline != null">academic_discipline = #{academicDiscipline},</if>
            <if test="colleName != null">colle_name = #{colleName},</if>
            <if test="educationalLevel != null">educational_level = #{educationalLevel},</if>
            <if test="majorName != null">major_name = #{majorName},</if>
            <if test="course != null  and course != ''"> and course = #{course}</if>
            <if test="publishingHouse != null">publishing_house = #{publishingHouse},</if>
            <if test="author != null">author = #{author},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSTextbookDataById" parameterType="String">
        delete from s_textbook_data where id = #{id}
    </delete>

    <delete id="deleteSTextbookDataByIds" parameterType="String">
        delete from s_textbook_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectByCourseName" parameterType="String" resultMap="STextbookDataResult">
        select id,file_name,course_name from  s_knowledge_base_file
        where course_name in
        <foreach item="courseName" collection="array" open="(" separator="," close=")">
            #{courseName}
        </foreach>
    </select>

    <select id="selectMindMappingList"  resultMap="MindMappingResult">
         select t1.academic_discipline, t1.colle_name, t1.major_name, t1.course_name,t2.file_name,t2.id
          from mind_mapping t1 left join s_knowledge_base_file t2 on t2.file_name != '' and t1.course_name = t2.course_name
    </select>

<!--    <select id="selectMindMappingList"  resultMap="MindMappingResult">-->
<!--         select academic_discipline, colle_name, major_name, course_name-->
<!--          from mind_mapping-->
<!--    </select>-->

    <select id="getThinkingKeyword" parameterType="Long"   resultMap="ThinkingKeywordResult">
         select t1.keyword,COALESCE(t2.chapter, '总章节') AS chapter from  s_textbook_keyword_data t1
         left join  s_textbook_keyword_analysis t2 on
         t1.category = t2.category and t1.keyword = t2.keyword
         where  t1.textbook_id = #{id}   AND t2.textbook_id = #{id}
    </select>

    <select id="selectCountSubject"  parameterType="String">
        select count(distinct major_name)
          from mind_mapping
    </select>

    <select id="selectCountCourse"  parameterType="String">
        select count(distinct course_name)
          from s_knowledge_base_file
    </select>

    <select id="selectCountKnowledge"  parameterType="String">
        select count(DISTINCT keyword)
          from s_textbook_keyword_data
    </select>

    <select id="selectProductSalesList"  parameterType="String">
        SELECT
    s.academic_discipline AS product,
    COUNT(DISTINCT s.major_name) AS subject,
    COUNT(DISTINCT s.course_name) AS course,
    COUNT(DISTINCT k.keyword) AS knowledge
FROM
    mind_mapping s
LEFT JOIN
    s_knowledge_base_file t ON s.discipline_id = t.discipline_id
LEFT JOIN
    s_textbook_keyword_data k ON t.id = k.textbook_id
GROUP BY
    s.academic_discipline;
    </select>
</mapper>