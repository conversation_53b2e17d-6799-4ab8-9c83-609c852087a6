<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.SStudentAttendanceMapper">

    <resultMap type="com.ruoyi.create.domain.SStudentAttendance" id="SStudentAttendanceResult">
        <result property="id"    column="id"    />
        <result property="courseId"    column="course_id"    />
        <result property="classId"    column="class_id"    />
        <result property="studentId"    column="student_id"    />
        <result property="attendanceTime"    column="attendance_time"    />
        <result property="attendanceDeadline"    column="attendance_deadline"    />
        <result property="status"    column="status"    />
        <result property="attendanceMethod"    column="attendance_method"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSStudentAttendanceVo">
        select id, course_id, class_id, student_id, attendance_time, attendance_deadline, status, attendance_method, create_by, create_time, update_by, update_time from s_student_attendance
    </sql>

    <select id="selectSStudentAttendanceList" parameterType="com.ruoyi.create.domain.SStudentAttendance" resultMap="SStudentAttendanceResult">
        <include refid="selectSStudentAttendanceVo"/>
        <where>
            <if test="courseId != null "> and course_id = #{courseId}</if>
            <if test="classId != null "> and class_id = #{classId}</if>
            <if test="studentId != null "> and student_id = #{studentId}</if>
            <if test="attendanceTime != null "> and attendance_time = #{attendanceTime}</if>
            <if test="attendanceDeadline != null "> and attendance_deadline = #{attendanceDeadline}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="attendanceMethod != null  and attendanceMethod != ''"> and attendance_method = #{attendanceMethod}</if>
        </where>
    </select>

    <select id="selectSStudentAttendanceById" parameterType="Long" resultMap="SStudentAttendanceResult">
        <include refid="selectSStudentAttendanceVo"/>
        where id = #{id}
    </select>

    <insert id="insertSStudentAttendance" parameterType="com.ruoyi.create.domain.SStudentAttendance">
        insert into s_student_attendance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="courseId != null">course_id,</if>
            <if test="classId != null">class_id,</if>
            <if test="studentId != null">student_id,</if>
            <if test="attendanceTime != null">attendance_time,</if>
            <if test="attendanceDeadline != null">attendance_deadline,</if>
            <if test="status != null">status,</if>
            <if test="attendanceMethod != null">attendance_method,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="classId != null">#{classId},</if>
            <if test="studentId != null">#{studentId},</if>
            <if test="attendanceTime != null">#{attendanceTime},</if>
            <if test="attendanceDeadline != null">#{attendanceDeadline},</if>
            <if test="status != null">#{status},</if>
            <if test="attendanceMethod != null">#{attendanceMethod},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSStudentAttendance" parameterType="com.ruoyi.create.domain.SStudentAttendance">
        update s_student_attendance
        <trim prefix="SET" suffixOverrides=",">
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="classId != null">class_id = #{classId},</if>
            <if test="studentId != null">student_id = #{studentId},</if>
            <if test="attendanceTime != null">attendance_time = #{attendanceTime},</if>
            <if test="attendanceDeadline != null">attendance_deadline = #{attendanceDeadline},</if>
            <if test="status != null">status = #{status},</if>
            <if test="attendanceMethod != null">attendance_method = #{attendanceMethod},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSStudentAttendanceById" parameterType="Long">
        delete from s_student_attendance where id = #{id}
    </delete>

    <delete id="deleteSStudentAttendanceByIds" parameterType="String">
        delete from s_student_attendance where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByCourseIdAndClassId">
        delete from s_student_attendance where course_id = #{courseId} and class_id = #{classId}
    </delete>

    <insert id="insertBatch">
        insert into s_student_attendance (course_id, class_id, student_id, attendance_deadline, status, create_by, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.courseId}, #{item.classId}, #{item.studentId}, #{item.attendanceDeadline}, #{item.status}, #{item.createBy}, #{item.createTime})
        </foreach>
    </insert>
</mapper>
