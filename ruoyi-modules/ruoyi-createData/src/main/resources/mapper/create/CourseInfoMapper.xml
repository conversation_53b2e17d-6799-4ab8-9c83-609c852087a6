<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.CourseInfoMapper">

    <resultMap type="CourseInfo" id="CourseInfoResult">
        <result property="id"    column="id"    />
        <result property="courseName"    column="course_name"    />
        <result property="scheduleTime"    column="schedule_time"    />
        <result property="schedulePeriod"    column="schedule_period"    />
        <result property="courseDescription"    column="course_description"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCourseInfoVo">
        select id, course_name, schedule_time, schedule_period, course_description, create_by, create_time, update_by, update_time from s_course_info
    </sql>

    <select id="selectCourseInfoList" parameterType="CourseInfo" resultMap="CourseInfoResult">
        <include refid="selectCourseInfoVo"/>
        <where>
            <if test="courseName != null  and courseName != ''"> and course_name like concat('%', #{courseName}, '%')</if>
            <if test="scheduleTime != null "> and schedule_time = #{scheduleTime}</if>
            <if test="schedulePeriod != null  and schedulePeriod != ''"> and schedule_period = #{schedulePeriod}</if>
            <if test="courseDescription != null  and courseDescription != ''"> and course_description = #{courseDescription}</if>
        </where>
    </select>

    <select id="selectCourseInfoById" parameterType="Long" resultMap="CourseInfoResult">
        <include refid="selectCourseInfoVo"/>
        where id = #{id}
    </select>
    <select id="getStudentCourseListByStudentId" resultType="com.ruoyi.create.Vo.CourseInfoVo">
        select sci.id,
               sci.course_name,
               sci.schedule_time,
               sci.schedule_period,
               sci.course_description,
               sti.teacher_name as createUser,
               sci.create_time
        from s_student_info ssi
                 left join s_course_class scc on ssi.class_id = scc.class_id
                 left JOIN s_course_info sci on scc.course_id = sci.id
                 left join s_teacher_info sti on sti.teacher_id = sci.create_by COLLATE utf8mb4_general_ci
        where ssi.student_id = #{studentId}
    </select>

    <insert id="insertCourseInfo" parameterType="CourseInfo">
        insert into s_course_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="courseName != null and courseName != ''">course_name,</if>
            <if test="scheduleTime != null">schedule_time,</if>
            <if test="schedulePeriod != null">schedule_period,</if>
            <if test="courseDescription != null">course_description,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="courseName != null and courseName != ''">#{courseName},</if>
            <if test="scheduleTime != null">#{scheduleTime},</if>
            <if test="schedulePeriod != null">#{schedulePeriod},</if>
            <if test="courseDescription != null">#{courseDescription},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateCourseInfo" parameterType="CourseInfo">
        update s_course_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="courseName != null and courseName != ''">course_name = #{courseName},</if>
            <if test="scheduleTime != null">schedule_time = #{scheduleTime},</if>
            <if test="schedulePeriod != null">schedule_period = #{schedulePeriod},</if>
            <if test="courseDescription != null">course_description = #{courseDescription},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCourseInfoById" parameterType="Long">
        delete from s_course_info where id = #{id}
    </delete>

    <delete id="deleteCourseInfoByIds" parameterType="String">
        delete from s_course_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
