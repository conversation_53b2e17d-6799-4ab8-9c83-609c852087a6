<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.CourseTopicsRepliesMapper">
    
    <resultMap type="com.ruoyi.create.domain.CourseTopicsReplies" id="CourseTopicsRepliesResult">
        <result property="id"    column="id"    />
        <result property="topicId"    column="topic_id"    />
        <result property="msgId"    column="msg_id"    />
        <result property="message"    column="message"    />
        <result property="msgUserId"    column="msg_user_id"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectCourseTopicsRepliesVo">
        select id, topic_id, msg_id, message, msg_user_id, create_time from s_course_topics_replies
    </sql>

    <select id="selectCourseTopicsRepliesList" parameterType="com.ruoyi.create.domain.CourseTopicsReplies" resultMap="CourseTopicsRepliesResult">
        select m.id, m.topic_id,m.msg_id, m.message, m.msg_user_id, m.create_time ,u.nick_name
        from s_course_topics_replies m
        left join sys_user u on m.msg_user_id =u.user_id
        <where>
            <if test="msgId != null"> and m.msg_id =#{msgId}</if>
            <if test="topicId != null "> and m.topic_id = #{topicId}</if>
            <if test="message != null  and message != ''"> and m.message = #{message}</if>
            <if test="msgUserId != null "> and m.msg_user_id = #{msgUserId}</if>
        </where>
        order by m.create_time Desc
    </select>
    
    <select id="selectCourseTopicsRepliesById" parameterType="Long" resultMap="CourseTopicsRepliesResult">
        <include refid="selectCourseTopicsRepliesVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCourseTopicsReplies" parameterType="com.ruoyi.create.domain.CourseTopicsReplies" useGeneratedKeys="true" keyProperty="id">
        insert into s_course_topics_replies
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="topicId != null">topic_id,</if>
            <if test="msgId != null">msg_id,</if>
            <if test="message != null">message,</if>
            <if test="msgUserId != null">msg_user_id,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="topicId != null">#{topicId},</if>
            <if test="msgId != null">#{msgId},</if>
            <if test="message != null">#{message},</if>
            <if test="msgUserId != null">#{msgUserId},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateCourseTopicsReplies" parameterType="com.ruoyi.create.domain.CourseTopicsReplies">
        update s_course_topics_replies
        <trim prefix="SET" suffixOverrides=",">
            <if test="topicId != null">topic_id = #{topicId},</if>
            <if test="msgId != null">msg_id = #{msgId},</if>
            <if test="message != null">message = #{message},</if>
            <if test="msgUserId != null">msg_user_id = #{msgUserId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCourseTopicsRepliesById" parameterType="Long">
        delete from s_course_topics_replies where id = #{id}
    </delete>

    <delete id="deleteCourseTopicsRepliesByIds" parameterType="String">
        delete from s_course_topics_replies where msg_id in
        <foreach item="msgId" collection="array" open="(" separator="," close=")">
            #{msgId}
        </foreach>
    </delete>

    <select id="selectCourseTopicsRepliesByIds"  resultMap="CourseTopicsRepliesResult">
        <include refid="selectCourseTopicsRepliesVo"/> where topic_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>