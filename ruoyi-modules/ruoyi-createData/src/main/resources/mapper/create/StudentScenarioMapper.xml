<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.StudentScenarioMapper">

    <resultMap type="com.ruoyi.create.domain.StudentScenario" id="StudentScenarioResult">
        <result property="id"    column="id"    />
        <result property="studentId"    column="student_id"    />
        <result property="scenario"    column="scenario"    />
        <result property="imageUrl"    column="imageUrl"    />
        <result property="school"    column="school"    />
        <result property="college"    column="college"    />
        <result property="major"    column="major"    />
    </resultMap>

    <sql id="selectStudentScenarioVo">
        select id, student_id, scenario, imageUrl, school, college, major from student_scenario
    </sql>

    <select id="selectStudentScenarioList" parameterType="com.ruoyi.create.domain.StudentScenario" resultMap="StudentScenarioResult">
        <include refid="selectStudentScenarioVo"/>
        <where>
            <if test="studentId != null "> and student_id = #{studentId}</if>
            <if test="scenario != null  and scenario != ''"> and scenario = #{scenario}</if>
            <if test="imageUrl != null  and imageUrl != ''"> and imageUrl = #{imageUrl}</if>
            <if test="school != null  and school != ''"> and school = #{school}</if>
            <if test="college != null  and college != ''"> and college = #{college}</if>
            <if test="major != null  and major != ''"> and major = #{major}</if>
        </where>
    </select>

    <select id="selectStudentScenarioById" parameterType="Long" resultMap="StudentScenarioResult">
        <include refid="selectStudentScenarioVo"/>
        where id = #{id}
    </select>

    <select id="selectStudentScenarioBgById" parameterType="com.ruoyi.create.domain.StudentScenario" resultMap="StudentScenarioResult">
        <include refid="selectStudentScenarioVo"/>
        where scenario = #{scenario} and student_id=#{studentId}
    </select>

    <insert id="insertStudentScenario" parameterType="com.ruoyi.create.domain.StudentScenario" useGeneratedKeys="true" keyProperty="id">
        insert into student_scenario
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="studentId != null">student_id,</if>
            <if test="scenario != null">scenario,</if>
            <if test="imageUrl != null">imageUrl,</if>
            <if test="school != null">school,</if>
            <if test="college != null">college,</if>
            <if test="major != null">major,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="studentId != null">#{studentId},</if>
            <if test="scenario != null">#{scenario},</if>
            <if test="imageUrl != null">#{imageUrl},</if>
            <if test="school != null">#{school},</if>
            <if test="college != null">#{college},</if>
            <if test="major != null">#{major},</if>
         </trim>
    </insert>

    <update id="updateStudentScenario" parameterType="com.ruoyi.create.domain.StudentScenario">
        update student_scenario
        <trim prefix="SET" suffixOverrides=",">
            <if test="studentId != null">student_id = #{studentId},</if>
            <if test="scenario != null">scenario = #{scenario},</if>
            <if test="imageUrl != null">imageUrl = #{imageUrl},</if>
            <if test="school != null">school = #{school},</if>
            <if test="college != null">college = #{college},</if>
            <if test="major != null">major = #{major},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteStudentScenarioById" parameterType="Long">
        delete from student_scenario where id = #{id}
    </delete>

    <delete id="deleteStudentScenarioByIds" parameterType="String">
        delete from student_scenario where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!--   插入或者更新数据 -->
    <insert id="insertOrUpdateStudentScenario" parameterType="com.ruoyi.create.domain.StudentScenario">
        INSERT INTO  student_scenario
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="studentId != null">student_id,</if>
            <if test="scenario != null">scenario,</if>
            <if test="imageUrl != null">imageUrl,</if>
            <if test="school != null">school,</if>
            <if test="college != null">college,</if>
            <if test="major != null">major,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="studentId != null">#{studentId},</if>
            <if test="scenario != null">#{scenario},</if>
            <if test="imageUrl != null">#{imageUrl},</if>
            <if test="school != null">#{school},</if>
            <if test="college != null">#{college},</if>
            <if test="major != null">#{major},</if>
        </trim>
        on DUPLICATE key update
        <trim prefix="" suffix="" suffixOverrides=",">
            <if test="imageUrl != null">imageUrl= VALUES(imageUrl),</if>
            <if test="school != null">school= VALUES(school),</if>
            <if test="college != null">college= VALUES(college),</if>
            <if test="major != null">major= VALUES(major),</if>
        </trim>
    </insert>


</mapper>
