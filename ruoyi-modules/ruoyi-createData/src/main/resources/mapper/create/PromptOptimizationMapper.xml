<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.PromptOptimizationMapper">

    <resultMap type="com.ruoyi.create.domain.PromptOptimization" id="PromptOptimizationResult">
        <result property="id"    column="id"    />
        <result property="content"    column="content"    />
        <result property="serviceName"    column="service_name"    />
        <result property="qualityOptFlag"    column="quality_opt_flag"    />
        <result property="shortPromptFlag"    column="short_prompt_flag"    />
        <result property="iterationRound"    column="iteration_round"    />
        <result property="thoughtChainFlag"    column="thought_chain_flag"    />
        <result property="optimizationId"    column="optimization_id"    />
        <result property="optimizeContent"    column="optimize_content"    />
        <result property="processStatus"    column="process_status"    />
        <result property="inferenceBefore"    column="inference_before"    />
        <result property="inferenceAfter"    column="inference_after"    />
    </resultMap>

    <sql id="selectPromptOptimizationVo">
        select id, content, service_name, quality_opt_flag, short_prompt_flag, iteration_round, thought_chain_flag, optimization_id, optimize_content, process_status, inference_before, inference_after from s_prompt_optimization
    </sql>

    <select id="selectPromptOptimizationList" parameterType="com.ruoyi.create.domain.PromptOptimization" resultMap="PromptOptimizationResult">
        <include refid="selectPromptOptimizationVo"/>
        <where>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="serviceName != null  and serviceName != ''"> and service_name like concat('%', #{serviceName}, '%')</if>
            <if test="qualityOptFlag != null  and qualityOptFlag != ''"> and quality_opt_flag = #{qualityOptFlag}</if>
            <if test="shortPromptFlag != null  and shortPromptFlag != ''"> and short_prompt_flag = #{shortPromptFlag}</if>
            <if test="iterationRound != null "> and iteration_round = #{iterationRound}</if>
            <if test="thoughtChainFlag != null  and thoughtChainFlag != ''"> and thought_chain_flag = #{thoughtChainFlag}</if>
            <if test="optimizationId != null  and optimizationId != ''"> and optimization_id = #{optimizationId}</if>
            <if test="optimizeContent != null  and optimizeContent != ''"> and optimize_content = #{optimizeContent}</if>
            <if test="processStatus != null "> and process_status = #{processStatus}</if>
            <if test="inferenceBefore != null  and inferenceBefore != ''"> and inference_before = #{inferenceBefore}</if>
            <if test="inferenceAfter != null  and inferenceAfter != ''"> and inference_after = #{inferenceAfter}</if>
        </where>
    </select>

    <select id="selectPromptOptimizationById" parameterType="Long" resultMap="PromptOptimizationResult">
        <include refid="selectPromptOptimizationVo"/>
        where id = #{id}
    </select>

    <insert id="insertPromptOptimization" parameterType="com.ruoyi.create.domain.PromptOptimization" useGeneratedKeys="true" keyProperty="id">
        insert into s_prompt_optimization
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="content != null">content,</if>
            <if test="serviceName != null">service_name,</if>
            <if test="qualityOptFlag != null">quality_opt_flag,</if>
            <if test="shortPromptFlag != null">short_prompt_flag,</if>
            <if test="iterationRound != null">iteration_round,</if>
            <if test="thoughtChainFlag != null">thought_chain_flag,</if>
            <if test="optimizationId != null">optimization_id,</if>
            <if test="optimizeContent != null">optimize_content,</if>
            <if test="processStatus != null">process_status,</if>
            <if test="inferenceBefore != null">inference_before,</if>
            <if test="inferenceAfter != null">inference_after,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="content != null">#{content},</if>
            <if test="serviceName != null">#{serviceName},</if>
            <if test="qualityOptFlag != null">#{qualityOptFlag},</if>
            <if test="shortPromptFlag != null">#{shortPromptFlag},</if>
            <if test="iterationRound != null">#{iterationRound},</if>
            <if test="thoughtChainFlag != null">#{thoughtChainFlag},</if>
            <if test="optimizationId != null">#{optimizationId},</if>
            <if test="optimizeContent != null">#{optimizeContent},</if>
            <if test="processStatus != null">#{processStatus},</if>
            <if test="inferenceBefore != null">#{inferenceBefore},</if>
            <if test="inferenceAfter != null">#{inferenceAfter},</if>
         </trim>
    </insert>

    <update id="updatePromptOptimization" parameterType="com.ruoyi.create.domain.PromptOptimization">
        update s_prompt_optimization
        <trim prefix="SET" suffixOverrides=",">
            <if test="content != null">content = #{content},</if>
            <if test="serviceName != null">service_name = #{serviceName},</if>
            <if test="qualityOptFlag != null">quality_opt_flag = #{qualityOptFlag},</if>
            <if test="shortPromptFlag != null">short_prompt_flag = #{shortPromptFlag},</if>
            <if test="iterationRound != null">iteration_round = #{iterationRound},</if>
            <if test="thoughtChainFlag != null">thought_chain_flag = #{thoughtChainFlag},</if>
            <if test="optimizationId != null">optimization_id = #{optimizationId},</if>
            <if test="optimizeContent != null">optimize_content = #{optimizeContent},</if>
            <if test="processStatus != null">process_status = #{processStatus},</if>
            <if test="inferenceBefore != null">inference_before = #{inferenceBefore},</if>
            <if test="inferenceAfter != null">inference_after = #{inferenceAfter},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePromptOptimizationById" parameterType="Long">
        delete from s_prompt_optimization where id = #{id}
    </delete>

    <delete id="deletePromptOptimizationByIds" parameterType="String">
        delete from s_prompt_optimization where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
