<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.LiteratrueDataMapper">

    <resultMap type="com.ruoyi.create.domain.LiteratrueData" id="LiteratrueDataResult">
        <result property="id"    column="id"    />
        <result property="titleId"    column="titleId"    />
        <result property="title"    column="title"    />
        <result property="category"    column="category"    />
        <result property="keyword"    column="keyword"    />
        <result property="keywordInit"    column="keywordInit"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectLiteratrueDataVo">
        select id, titleId, title, category, keyword, create_by, create_time, update_by, update_time from s_literatrue_data
    </sql>

    <select id="selectLiteratrueDataList" parameterType="com.ruoyi.create.domain.LiteratrueData" resultMap="LiteratrueDataResult">
        <include refid="selectLiteratrueDataVo"/>
        <where>
            <if test="titleId != null "> and titleId = #{titleId}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="category != null  and category != ''"> and category = #{category}</if>
            <if test="keyword != null  and keyword != ''"> and keyword = #{keyword}</if>
        </where>
    </select>
    
    <select id="selectLiteratrueDataById" parameterType="Long" resultMap="LiteratrueDataResult">
        <include refid="selectLiteratrueDataVo"/>
        where id = #{id}
    </select>
    <select id="selectMaxTitleId" resultType="java.lang.Long">
        select MAX(titleId) FROM s_literatrue_data
    </select>

    <select id="selectClickLiteratrueData" resultMap="LiteratrueDataResult">
        <include refid="selectLiteratrueDataVo"/>
        <where>
            <if test="keywordInit != null  and keywordInit != ''">
                titleId IN ( SELECT DISTINCT titleId FROM s_literatrue_data WHERE keyword = #{keywordInit})
            </if>
            <if test="keyword != null  and keyword != ''"> and keyword = #{keyword}</if>
        </where>
        GROUP BY titleId
    </select>

    <insert id="insertLiteratrueDataList" useGeneratedKeys="true" keyProperty="id">
        insert into s_literatrue_data (titleId,title, category,keyword,create_by, create_time) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.titleId},#{item.title},#{item.category},#{item.keyword},#{item.createBy},#{item.createTime})
        </foreach>
    </insert>

    <insert id="insertLiteratrueData" parameterType="com.ruoyi.create.domain.LiteratrueData" useGeneratedKeys="true" keyProperty="id">
        insert into s_literatrue_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="titleId != null">titleId,</if>
            <if test="title != null">title,</if>
            <if test="category != null">category,</if>
            <if test="keyword != null">keyword,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="titleId != null">#{titleId},</if>
            <if test="title != null">#{title},</if>
            <if test="category != null">#{category},</if>
            <if test="keyword != null">#{keyword},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateLiteratrueData" parameterType="com.ruoyi.create.domain.LiteratrueData">
        update s_literatrue_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="titleId != null">titleId = #{titleId},</if>
            <if test="title != null">title = #{title},</if>
            <if test="category != null">category = #{category},</if>
            <if test="keyword != null">keyword = #{keyword},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLiteratrueDataById" parameterType="Long">
        delete from s_literatrue_data where id = #{id}
    </delete>

    <delete id="deleteLiteratrueDataByIds" parameterType="String">
        delete from s_literatrue_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>