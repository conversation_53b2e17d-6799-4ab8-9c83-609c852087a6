<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.CourseManagementMapper">

    <resultMap type="com.ruoyi.create.domain.CourseManagement" id="CourseManagementResult">
        <result property="id" column="id"/>
        <result property="studentId" column="student_id"/>
        <result property="courseName" column="course_name"/>
        <result property="courseType" column="course_type"/>
        <result property="term" column="term"/>
        <result property="teacherId" column="teacher_id"/>
        <result property="studentName" column="student_name"/>
        <result property="studentClass" column="student_class"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectCourseManagementVo">
        SELECT id,
               student_id,
               course_name,
               course_type,
               term,
               teacher_id,
               student_class,
               create_by,
               create_time,
               update_by,
               update_time
        FROM s_strudent_course_info
    </sql>

    <select id="selectCourseManagementList" parameterType="com.ruoyi.create.domain.CourseManagement"
            resultMap="CourseManagementResult">
        select DISTINCT course_name, course_type, teacher_id, term from s_strudent_course_info
        <where>
            <if test="studentId != null  and studentId != ''">and student_id like concat('%', #{studentId}, '%')</if>
            <if test="courseName != null  and courseName != ''">and course_name like concat('%', #{courseName}, '%')
            </if>
            <if test="courseType != null  and courseType != ''">and course_type = #{courseType}</if>
            <if test="term != null and term != ''">and term = #{term}</if>
            <if test="teacherId != null  and teacherId != ''">and teacher_id = #{teacherId}</if>
        </where>
    </select>


    <select id="selectCourseManagementList1" parameterType="com.ruoyi.create.domain.CourseManagement"
            resultMap="CourseManagementResult">
        <include refid="selectCourseManagementVo"/>
        <where>
            <if test="courseName != null  and courseName != ''">and course_name = #{courseName}</if>
            <if test="courseType != null  and courseType != ''">and course_type = #{courseType}</if>
            <if test="term != null and term != ''">and term = #{term}</if>
        </where>
    </select>

    <select id="selectCourseManagementList2" parameterType="com.ruoyi.create.domain.CourseManagement"
            resultMap="CourseManagementResult">
        select e.* from (select c.*,d.class_name from (select a.*,b.student_name,b.class_id from s_strudent_course_info
        a JOIN s_student_info b on a.student_id = b.student_id COLLATE utf8mb4_general_ci) c JOIN s_class_info d ON
        c.class_id = d.id COLLATE utf8mb4_general_ci) e
        <where>
            <if test="studentId != null  and studentId != ''">and e.student_id = #{studentId}</if>
            <if test="studentName != null  and studentName != ''">and e.student_name = #{studentName}</if>
            <if test="studentClass != null and studentClass != ''">and e.class_name = #{studentClass}</if>
            <if test="courseName != null  and courseName != ''">and e.course_name = #{courseName}</if>
            <if test="courseType != null  and courseType != ''">and e.course_type = #{courseType}</if>
            <if test="term != null and term != ''">and e.term = #{term}</if>
        </where>
    </select>

    <select id="selectCourseManagementList3" parameterType="com.ruoyi.create.domain.CourseManagement"
            resultMap="CourseManagementResult">
        <include refid="selectCourseManagementVo"/>
        where id = #{id}
    </select>

    <select id="selectCourseManagementById" parameterType="Long" resultMap="CourseManagementResult">
        <include refid="selectCourseManagementVo"/>
        where id = #{id}
    </select>

    <insert id="insertCourseManagement" parameterType="com.ruoyi.create.domain.CourseManagement" useGeneratedKeys="true"
            keyProperty="id">
        insert into s_strudent_course_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null" >id,</if>
            <if test="studentId != null">student_id,</if>
            <if test="courseName != null">course_name,</if>
            <if test="courseType != null">course_type,</if>
            <if test="term != null">term,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="studentClass != null">student_class,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="universityId != null">university_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="studentId != null">#{studentId},</if>
            <if test="courseName != null">#{courseName},</if>
            <if test="courseType != null">#{courseType},</if>
            <if test="term != null">#{term},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="studentClass != null">#{studentClass},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="universityId != null">#{universityId},</if>
        </trim>
    </insert>

    <update id="updateCourseManagement" parameterType="com.ruoyi.create.domain.CourseManagement">
        update s_strudent_course_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="studentId != null and studentId != ''">student_id = #{studentId},</if>
            <if test="courseName != null and courseName != ''">course_name = #{courseName},</if>
            <if test="courseType != null">course_type = #{courseType},</if>
            <if test="term != null">term = #{term},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>
    <select id="seleteCourseVideoBusiIdByNameAndId" resultType="Long">
        SELECT id FROM s_course_video
        WHERE course_name = #{courseName}
          AND teacher_id = #{teacherId}
    </select>

    <delete id="deleteCourseManagementById" parameterType="Long">
        DELETE
        FROM s_strudent_course_info
        WHERE id = #{id}
    </delete>

    <delete id="deleteCourseManagement1" parameterType="com.ruoyi.create.domain.CourseManagement">
        DELETE
        FROM s_student_course
        WHERE id = #{id}
    </delete>

    <delete id="deleteCourseManagement" parameterType="com.ruoyi.create.domain.CourseManagement">
        DELETE
        FROM s_strudent_course_info
        WHERE id = #{id}
    </delete>

    <delete id="deleteCourseManagementByIds" parameterType="String">
        delete from s_strudent_course_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectCourseManagementAll" resultMap="CourseManagementResult">
        <include refid="selectCourseManagementVo"/>
    </select>

    <select id="selectCourseManagements2" parameterType="com.ruoyi.create.domain.CourseManagement" resultType="Long">
        SELECT id
        FROM s_student_course
        WHERE course_name = #{courseName}
          AND term = #{term}
          AND teacher_id = #{teacherId}
    </select>

    <select id="getAll" resultMap="CourseManagementResult">
        <include refid="selectCourseManagementVo"/>
    </select>

    <select id="selectByclassName" parameterType="String" resultType="String">
        SELECT student_id
        FROM s_student_info
        WHERE class_id = #{studentClass}
    </select>

    <select id="selectCourseManagementByStudentId" parameterType="String" resultType="String">
        SELECT student_name
        FROM s_student_info
        WHERE student_id = #{studentId}
    </select>

    <select id="selectCourseManagementByStudentId1" parameterType="String" resultType="String">
        SELECT class_name
        FROM s_class_info
        WHERE id = (SELECT class_id FROM s_student_info WHERE student_id = #{studentId})
    </select>

    <select id="selectCourseManagementByCourseName" parameterType="String"
            resultType="com.ruoyi.create.domain.ClassInfo">
        SELECT id, class_name
        FROM s_class_info
        WHERE id IN (SELECT DISTINCT class_id
                     FROM s_student_info
                     WHERE student_id COLLATE utf8mb4_0900_ai_ci IN
                           (SELECT student_id FROM s_strudent_course_info WHERE course_name = #{courseName}))
    </select>

    <select id="selectNameById" parameterType="String" resultType="String">
        SELECT teacher_name
        FROM s_teacher_info
        WHERE teacher_id = #{teacherId}
    </select>

    <insert id="insertStudentCourse" parameterType="com.ruoyi.create.domain.CourseManagement" useGeneratedKeys="true"
            keyProperty="id" keyColumn="id">
        INSERT INTO s_student_course (course_name, course_type, term, teacher_id, student_class, university_id)
        VALUES (#{courseName}, #{courseType}, #{term}, #{teacherId}, #{studentClass}, #{universityId})
    </insert>

    <insert id="insertSStudentCourse" parameterType="com.ruoyi.create.domain.StudentCourse" useGeneratedKeys="true" keyProperty="id">
        insert into s_student_course
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="courseName != null">course_name,</if>
            <if test="courseType != null">course_type,</if>
            <if test="term != null">term,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="studentClass != null">student_class,</if>
            <if test="universityId != null">university_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="courseName != null">#{courseName},</if>
            <if test="courseType != null">#{courseType},</if>
            <if test="term != null">#{term},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="studentClass != null">#{studentClass},</if>
            <if test="universityId != null">#{universityId},</if>
        </trim>
    </insert>

    <select id="selectStudentClassName" parameterType="String" resultType="String">
        SELECT class_name
        FROM s_class_info
        WHERE id = #{classId}
    </select>

    <select id="selectByJobId" parameterType="map" resultMap="CourseManagementResult">
    SELECT DISTINCT
    course_name,
    teacher_id
    FROM s_student_course
        <where>
        <if test="jobId != null and jobId != ''">
            AND teacher_id = #{jobId}
        </if>
        <if test="courseName != null and courseName != ''">
            AND course_name LIKE CONCAT('%', #{courseName}, '%')
        </if>
        <if test="universityId != null">
            AND university_id = #{universityId}
        </if>
         </where>
    </select>


<select id="selectCourseManagements" parameterType="String" resultType="com.ruoyi.create.domain.CourseManagement">
        SELECT id, course_name, course_type, term, teacher_id, student_class
        FROM s_student_course
        WHERE course_name = #{courseName}
          AND term = #{term}
          AND teacher_id = #{teacherId}
    </select>

    <select id="selectStudentClassName1" parameterType="String" resultType="String">
        SELECT class_name
        FROM s_class_info
        WHERE id = (SELECT class_id FROM s_student_info WHERE student_id = #{studentId})
    </select>

    <select id="selectStudentNameById" parameterType="String" resultType="String">
        SELECT student_name
        FROM s_student_info
        WHERE student_id = #{studentId}
    </select>

    <select id="selectIdByClassName" parameterType="String" resultType="Long">
        SELECT id
        FROM s_student_course
        WHERE course_name = #{courseName}
          AND student_class = #{className}
          AND teacher_id = #{jobId}
    </select>

    <select id="selectStudentCourseList" parameterType="String" resultType="com.ruoyi.create.domain.CourseManagement">
        <include refid="selectCourseManagementVo"/>
        where student_id=#{studentId}
    </select>

    <select id="selectStudentCourseInfo" resultMap="CourseManagementResult">
        SELECT id, course_name, course_type, term, teacher_id, student_class
        FROM s_strudent_course_info
        WHERE id = #{id}
          AND student_id = #{studentId};
    </select>
    <select id="selectStudentCourseInf2" resultMap="CourseManagementResult">
        SELECT id,
               course_name,
               course_type,
               term,
               teacher_id,
               student_class,
               student_id
        FROM s_strudent_course_info

        WHERE id = #{id}
          AND course_name = #{courseName}
    </select>

    <select id="getUserByStudentId" resultType="java.util.Map">
        SELECT user_id, user_name, student_id, major_id, nick_name
        FROM sys_user
        WHERE student_id = #{studentId}
          AND del_flag = '0'
    </select>
    <select id="selectStudentCourseInfoList" resultMap="CourseManagementResult">
        SELECT stci.*, su.user_name AS userName, su.user_id AS userId
        FROM s_strudent_course_info stci
                 LEFT JOIN sys_user su ON su.student_id = stci.student_id COLLATE utf8mb4_general_ci
        WHERE stci.id = #{id}
    </select>
    <select id="getDialogueRecordingCount" resultType="java.lang.String">
        SELECT COUNT(*)
        FROM s_dialogue_recording sdr
                 LEFT JOIN s_dialogue_details sdd ON sdd.dialogue_id = sdr.id
        WHERE sdr.create_by = #{userName}
          AND sdd.issue = 'user'
    </select>
    <select id="getUserByStudentIds" resultType="java.util.Map">
        SELECT
        user_id,dept_id,user_name,nick_name,phonenumber,auth_status,university_id,college_id,major_id,class_id,student_id
        FROM sys_user
        WHERE student_id IN
        <foreach item="item" index="index" collection="studentIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
        and del_flag = '0'
    </select>
    <select id="selectStudentCourseInfo2" resultType="com.ruoyi.create.domain.CourseManagement">
        SELECT stci.*, su.user_name AS userName, su.user_id AS userId
        FROM s_strudent_course_info stci
                 LEFT JOIN sys_user su ON su.student_id = stci.student_id COLLATE utf8mb4_general_ci
        WHERE stci.id = #{courseClassId} AND stci.course_name = #{courseName}
    </select>
    <select id="selectCourseManagementAll2" resultMap="CourseManagementResult">
        SELECT id, course_name
        FROM s_student_course;
    </select>
    <select id="selectCourseManagementAll3" resultType="com.ruoyi.create.domain.CourseManagement">
        SELECT stc.id, ssci.student_id, stc.course_name, stc.course_type, stc.term, stc.teacher_id, stc.student_class
        FROM s_student_course stc
                 LEFT JOIN s_strudent_course_info ssci ON ssci.id = stc.id;
    </select>

    <select id="selectTermByJobId" parameterType="map" resultMap="CourseManagementResult">
        SELECT DISTINCT course_name, term, teacher_id
        FROM s_student_course
        <where>
            <if test="jobId != null and jobId.trim() != ''">
                teacher_id = #{jobId}
            </if>
            <if test="universityId != null">
                AND university_id = #{universityId}
            </if>
            AND student_class != ''
        </where>
        ORDER BY term DESC
    </select>

    <select id="selectStudentClassByJobId" parameterType="map" resultType="com.ruoyi.create.domain.CourseManagement">
        SELECT id, course_name, course_type, term, teacher_id, student_class
        FROM s_student_course
        <where>
            <if test="jobId != null and jobId.trim() != ''">
                teacher_id = #{jobId}
            </if>
            <if test="universityId != null">
                AND university_id = #{universityId}
            </if>
            AND student_class != ''
        </where>
    </select>


    <select id="countStudentCourse">
        SELECT count(1)
        FROM s_student_course
        WHERE course_name = #{courseName}
          AND teacher_id = #{teacherId}
          AND university_id = #{universityId}
    </select>
    <select id="seleteCourseCaseBusiIdByNameAndId" resultType="java.lang.Long">

            SELECT id FROM s_course_case
            WHERE course_name = #{courseName}
              AND teacher_id = #{teacherId}

    </select>
    <select id="seletesCourseExercisesBusiIdByNameAndId" resultType="java.lang.Long">

        SELECT id FROM s_course_exercises
        WHERE course_name = #{courseName}
          AND teacher_id = #{teacherId}

    </select>
    <select id="selectDISTINCTCoursenameCourseManagementList"
            resultType="com.ruoyi.create.domain.CourseManagement">
        select DISTINCT course_name from s_strudent_course_info
        <where>
            <if test="studentId != null  and studentId != ''">and student_id like concat('%', #{studentId}, '%')</if>
            <if test="courseName != null  and courseName != ''">and course_name like concat('%', #{courseName}, '%')
            </if>
            <if test="courseType != null  and courseType != ''">and course_type = #{courseType}</if>
            <if test="term != null and term != ''">and term = #{term}</if>
            <if test="teacherId != null  and teacherId != ''">and teacher_id = #{teacherId}</if>
        </where>
    </select>
    <select id="selectKcglList" resultType="java.lang.String">
        SELECT st.teacher_id
        FROM s_ambit_teacher st
        INNER JOIN sys_user su ON st.teacher_id = su.job_id
        WHERE st.course_name IN (
            SELECT course_name FROM s_ambit_teacher WHERE teacher_id = #{jobId}
        )
          AND su.university_id = #{universityId}
    </select>
    <select id="selectByJobIds" resultType="com.ruoyi.create.domain.CourseManagement">
        SELECT DISTINCT course_name, teacher_id
        FROM s_student_course
        <where>
            <if test="jobIds != null and jobIds.size() > 0">
                teacher_id IN
                <foreach collection="jobIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="courseName != null and courseName != ''">
                 and course_name like concat('%', #{courseName}, '%')
            </if>
        </where>
    </select>
    <select id="selectTermByJobIds" resultType="com.ruoyi.create.domain.CourseManagement">
        SELECT DISTINCT course_name, term, teacher_id
        FROM s_student_course
        <where>
            <if test="jobIds != null and jobIds.size() > 0">
                teacher_id IN
                <foreach collection="jobIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            AND student_class != ''
        </where>
        ORDER BY term DESC
    </select>
    <select id="selectStudentClassByJobIds" resultType="com.ruoyi.create.domain.CourseManagement">
        SELECT id, course_name, course_type, term, teacher_id, student_class
        FROM s_student_course
        <where>
            <if test="jobIds != null and jobIds.size() > 0">
                teacher_id IN
                <foreach collection="jobIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            AND student_class != ''
        </where>
    </select>
    <select id="getStudentCourseStatistics" resultType="com.ruoyi.create.Vo.CourseStatisticsVo">
        SELECT ssci.course_name AS courseName,
               COUNT(DISTINCT ssci.student_id) AS totalStudentCount
        FROM s_student_course AS ssc
                 LEFT JOIN s_strudent_course_info ssci ON ssci.id = ssc.id
        WHERE ssci.student_id IS NOT null
          AND ssc.course_name = #{courseName}
        GROUP BY ssci.course_name;
    </select>

    <select id="getTeacherCourseStatistics" resultType="com.ruoyi.create.Vo.AmbitTeacherVo">
        SELECT id,name,sex,title,teacher_id
        FROM s_ambit_teacher
        WHERE course_name = #{courseName}
    </select>


    <delete id="deleteCourseManagement2" parameterType="com.ruoyi.create.domain.CourseManagement">
        DELETE
        FROM s_student_course
        WHERE course_name = #{courseName}
          AND teacher_id = #{teacherId}
    </delete>
    <delete id="deleteCourseName"  parameterType="com.ruoyi.create.domain.StudentCourse">
        DELETE
        FROM s_student_course
        WHERE course_name = #{courseName}
          AND teacher_id = #{teacherId}
    </delete>
    <delete id="deleteCourseVideoByNameAndId" parameterType="com.ruoyi.create.domain.CourseManagement">
            DELETE
            FROM s_course_video
            WHERE course_name = #{courseName}
              AND teacher_id = #{teacherId}
    </delete>
    <delete id="deleteCourseCaseByNameAndId" parameterType="com.ruoyi.create.domain.CourseManagement">
        DELETE
        FROM s_course_case
        WHERE course_name = #{courseName}
          AND teacher_id = #{teacherId}
    </delete>
    <delete id="deletesCourseExercisesByNameAndId">
        DELETE
        FROM s_course_exercises
        WHERE course_name = #{courseName}
          AND teacher_id = #{teacherId}
    </delete>
</mapper>
