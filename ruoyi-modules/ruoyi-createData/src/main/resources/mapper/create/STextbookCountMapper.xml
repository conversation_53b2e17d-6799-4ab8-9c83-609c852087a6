<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.STextbookCountMapper">
    
    <resultMap type="com.ruoyi.create.domain.TextbookCount" id="STextbookCountResult">
        <result property="id"    column="id"    />
        <result property="textbookId"    column="textbook_id"    />
        <result property="textbook"    column="textbook"    />
        <result property="subjectId"    column="subjectId"    />
        <result property="category"    column="category"    />
        <result property="keyword"    column="keyword"    />
        <result property="count"    column="count"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSTextbookCountVo">
        select id, textbook_id, textbook, subjectId, category, keyword, count, create_by, create_time, update_by, update_time from s_textbook_count
    </sql>

    <select id="selectSTextbookCountList" parameterType="com.ruoyi.create.domain.TextbookCount" resultMap="STextbookCountResult">
        <include refid="selectSTextbookCountVo"/>
        <where>  
            <if test="textbookId != null "> and textbook_id = #{textbookId}</if>
            <if test="textbook != null  and textbook != ''"> and textbook = #{textbook}</if>
            <if test="subjectId != null  and subjectId != ''"> and subjectId = #{subjectId}</if>
            <if test="category != null  and category != ''"> and category = #{category}</if>
            <if test="keyword != null  and keyword != ''"> and keyword = #{keyword}</if>
            <if test="count != null "> and count = #{count}</if>
        </where>
    </select>
    
    <select id="selectSTextbookCountById" parameterType="Long" resultMap="STextbookCountResult">
        <include refid="selectSTextbookCountVo"/>
        where id = #{id}
    </select>

    <insert id="insertSTextbookCount" parameterType="com.ruoyi.create.domain.TextbookCount">
        insert into s_textbook_count
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="textbookId != null">textbook_id,</if>
            <if test="textbook != null">textbook,</if>
            <if test="subjectId != null">subjectId,</if>
            <if test="category != null">category,</if>
            <if test="keyword != null">keyword,</if>
            <if test="count != null">count,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="textbookId != null">#{textbookId},</if>
            <if test="textbook != null">#{textbook},</if>
            <if test="subjectId != null">#{subjectId},</if>
            <if test="category != null">#{category},</if>
            <if test="keyword != null">#{keyword},</if>
            <if test="count != null">#{count},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSTextbookCount" parameterType="com.ruoyi.create.domain.TextbookCount">
        update s_textbook_count
        <trim prefix="SET" suffixOverrides=",">
            <if test="textbookId != null">textbook_id = #{textbookId},</if>
            <if test="textbook != null">textbook = #{textbook},</if>
            <if test="subjectId != null">subjectId = #{subjectId},</if>
            <if test="category != null">category = #{category},</if>
            <if test="keyword != null">keyword = #{keyword},</if>
            <if test="count != null">count = #{count},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSTextbookCountById" parameterType="Long">
        delete from s_textbook_count where id = #{id}
    </delete>

    <delete id="deleteSTextbookCountByIds" parameterType="String">
        delete from s_textbook_count where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>