<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.StudentSignMapper">

    <resultMap type="com.ruoyi.create.domain.StudentSign" id="StudentSignResult">
        <result property="id"    column="id"    />
        <result property="signId"    column="sign_id"    />
        <result property="studentId"    column="student_id"    />
        <result property="studentName"    column="student_name"    />
        <result property="sex"    column="sex"    />
        <result property="courseName"    column="course_name"    />
        <result property="courseType"    column="course_type"    />
        <result property="term"    column="term"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="signClass"    column="sign_class"    />
        <result property="isSign"    column="is_sign"    />
        <result property="signTime"    column="sign_time"    />
    </resultMap>

    <sql id="selectStudentSignVo">
        select id, sign_id, student_id, teacher_id, sign_class, course_name, term, course_type, is_sign, sign_time, start_time, end_time from s_student_sign
    </sql>

    <select id="selectStudentSignList" parameterType="com.ruoyi.create.domain.StudentSign" resultMap="StudentSignResult">
        <include refid="selectStudentSignVo"/>
        <where>
            1=1
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="id != null "> and sign_id = #{id}</if>
            <if test="signClass != null "> and sign_class = #{signClass}</if>
            <if test="studentId != null "> and student_id = #{studentId}</if>
            <if test="courseName != null "> and course_name = #{courseName}</if>
            <if test="courseType != null "> and course_type = #{courseType}</if>
            <if test="term != null "> and term = #{term}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
        </where>
    </select>

    <select id="selectStudentSignById" parameterType="Long" resultMap="StudentSignResult">
        <include refid="selectStudentSignVo"/>
        where id = #{id}
    </select>

    <select id="selectBystudentId" parameterType="String" resultMap="StudentSignResult">
        select student_name,sex from s_student_info where student_id = #{studentId}
    </select>

    <select id="selectIsAttendanceById" parameterType="Long" resultType="String">
        select is_attendance from s_sign_in_sessions where id = #{id}
    </select>

    <select id="selectStudentIdList" parameterType="com.ruoyi.create.domain.StudentSign" resultType="com.ruoyi.create.domain.CourseManagement">
        select student_id,course_name,course_type,term from s_strudent_course_info <where>
        <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
        <if test="courseName != null "> and course_name = #{courseName}</if>
        <if test="courseType != null "> and course_type = #{courseType}</if>
        <if test="term != null "> and term = #{term}</if>
    </where>
    </select>

    <insert id="insertStudentInfo" parameterType="com.ruoyi.create.domain.StudentSign">
        insert into s_student_sign
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="signId != null">sign_id,</if>
            <if test="studentId != null">student_id,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="signClass != null">sign_class,</if>
            <if test="courseName != null">course_name,</if>
            <if test="courseType != null">course_type,</if>
            <if test="term != null">term,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="signId != null">#{signId},</if>
            <if test="studentId != null">#{studentId},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="signClass != null">#{signClass},</if>
            <if test="courseName != null">#{courseName},</if>
            <if test="courseType != null">#{courseType},</if>
            <if test="term != null">#{term},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
        </trim>
    </insert>

    <delete id="deleteStudentInfo" parameterType="com.ruoyi.create.domain.StudentSign">
        delete from s_student_sign where student_id = #{studentId} and teacher_id = #{teacherId} and sign_class = #{signClass}
                                     and course_name = #{courseName} and course_type = #{courseType} and term = #{term}
    </delete>

    <select id="selectStudentAttendanceListByStudentId" parameterType="com.ruoyi.create.domain.StudentSign" resultType="com.ruoyi.create.domain.StudentSign">
        <include refid="selectStudentSignVo"/>
        <where>
            1=1
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="studentId != null "> and student_id = #{studentId}</if>
            <if test="courseName != null "> and course_name = #{courseName}</if>
            <if test="courseType != null "> and course_type = #{courseType}</if>
            <if test="term != null "> and term = #{term}</if>
        </where>
    </select>

    <select id="selectStudentAttendanceByCourseIdAndStudentId"
            resultType="com.ruoyi.create.domain.StudentSign">
        <include refid="selectStudentSignVo"/>
        where student_id = #{studentId} and course_Name = #{courseName}
    </select>

    <update id="updateStudentAttendance" parameterType="com.ruoyi.create.domain.StudentSign">
        update s_student_sign set is_sign = #{isSign}, sign_time = #{signTime}, photo_path = #{photoPath}, photo_name = #{photoName}
        <where>
            1=1
            <if test="id != null "> and id = #{id}</if>
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="signClass != null "> and sign_class = #{signClass}</if>
            <if test="studentId != null "> and student_id = #{studentId}</if>
            <if test="courseName != null "> and course_name = #{courseName}</if>
            <if test="courseType != null "> and course_type = #{courseType}</if>
            <if test="term != null "> and term = #{term}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
        </where>
    </update>

    <select id="selectClassByStudentId"
            parameterType="String" resultType="String">
        select class_name from s_class_info where id = (select class_id from s_student_info where student_id=#{studentId})
    </select>

    <select id="selectById"
            parameterType="Long" resultType="com.ruoyi.create.domain.AttendanceParam">
        select * from s_sign_in_sessions where id = #{id}
    </select>

    <select id="selectSignType"
            parameterType="com.ruoyi.create.domain.StudentSign" resultType="String">
        select distinct sign_type from s_sign_in_sessions
    <where>
        1=1
        <if test="teacherId != null || teacherId != ''"> and teacher_id = #{teacherId}</if>
        <if test="signClass != null || signClass != '' "> and sign_class = #{signClass}</if>
        <if test="courseName != null || courseName != '' "> and course_name = #{courseName}</if>
        <if test="courseType != null || courseType != '' "> and course_type = #{courseType}</if>
        <if test="term != null || term != '' "> and term = #{term}</if>
        <if test="startTime != null || startTime != '' "> and start_time = #{startTime}</if>
        <if test="endTime != null || endTime != '' "> and end_time = #{endTime}</if>
    </where>
    </select>
    <select id="selectSignTypeBySignId" resultType="java.lang.String">
        select distinct sign_type from s_sign_in_sessions
        <where>
            <if test="signId != null || signId != ''"> and id = #{signId}</if>
        </where>
    </select>


</mapper>
