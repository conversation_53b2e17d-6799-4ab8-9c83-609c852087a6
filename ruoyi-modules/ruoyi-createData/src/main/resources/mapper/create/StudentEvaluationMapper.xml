<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.StudentEvaluationMapper">
    
    <resultMap type="StudentEvaluation" id="StudentEvaluationResult">
        <result property="id"    column="id"    />
        <result property="studentId"    column="student_id"    />
        <result property="evaluationResult"    column="evaluation_result"    />
        <result property="evaluationTime"    column="evaluation_time"    />
        <result property="evaluationState"    column="evaluation_state"    />
        <result property="relation"    column="relation"    />
    </resultMap>

    <sql id="selectStudentEvaluationVo">
        select id, student_id, evaluation_result, evaluation_time, evaluation_state, relation from s_student_evaluation
    </sql>

    <select id="selectStudentEvaluationList" parameterType="String" resultMap="StudentEvaluationResult">
        <include refid="selectStudentEvaluationVo"/>
        <where>
            <if test="studentId != null  and studentId != ''">
                and student_id = #{studentId}
            </if>
        </where>
    </select>
    
    <select id="selectStudentEvaluationById" parameterType="Long" resultMap="StudentEvaluationResult">
        <include refid="selectStudentEvaluationVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertStudentEvaluation" parameterType="StudentEvaluation" useGeneratedKeys="true" keyProperty="id">
        insert into s_student_evaluation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="studentId != null and studentId != ''">student_id,</if>
            <if test="evaluationResult != null">evaluation_result,</if>
            <if test="evaluationState != null">evaluation_state,</if>
            <if test="relation != null">relation,</if>
            <if test="evaluationTime != null">evaluation_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="studentId != null and studentId != ''">#{studentId},</if>
            <if test="evaluationResult != null">#{evaluationResult},</if>
            <if test="evaluationState != null">#{evaluationState},</if>
            <if test="relation != null">#{relation},</if>
            <if test="evaluationTime != null">#{evaluationTime},</if>
         </trim>
    </insert>

    <update id="updateStudentEvaluation" parameterType="StudentEvaluation">
        update s_student_evaluation
        <trim prefix="SET" suffixOverrides=",">
            <if test="studentId != null and studentId != ''">student_id = #{studentId},</if>
            <if test="evaluationResult != null">evaluation_result = #{evaluationResult},</if>
            <if test="evaluationTime != null">evaluation_time = #{evaluationTime},</if>
            <if test="evaluationState != null">evaluation_state = #{evaluationState},</if>
            <if test="relation != null">relation = #{relation},</if>
        </trim>
        where student_id = #{studentId}
    </update>

    <delete id="deleteStudentEvaluationById" parameterType="String">
        delete from s_student_evaluation where student_id = #{studentId}
    </delete>

    <delete id="deleteStudentEvaluationByIds" parameterType="String">
        delete from s_student_evaluation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{student_id}
        </foreach>
    </delete>
</mapper>