<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.CareCallMapper">
    
    <resultMap type="com.ruoyi.create.domain.CareCall" id="CareCallResult">
        <result property="id"    column="id"    />
        <result property="serviceId"    column="service_id"    />
        <result property="serviceName"    column="service_name"    />
        <result property="inputTokensTotal"    column="input_tokens_total"    />
        <result property="outputTokensTotal"    column="output_tokens_total"    />
        <result property="tokensTotal"    column="tokens_total"    />
        <result property="succeedCallTotal"    column="succeed_call_total"    />
        <result property="failureCallTotal"    column="failure_call_total"    />
        <result property="callTotal"    column="call_total"    />
    </resultMap>

    <sql id="selectCareCallVo">
        select id, service_id, service_name, input_tokens_total, output_tokens_total, tokens_total, succeed_call_total, failure_call_total, call_total from s_care_call
    </sql>

    <select id="selectCareCallList" parameterType="com.ruoyi.create.domain.CareCall" resultMap="CareCallResult">
        <include refid="selectCareCallVo"/>
        <where>  
            <if test="serviceId != null  and serviceId != ''"> and service_id = #{serviceId}</if>
            <if test="serviceName != null  and serviceName != ''"> and service_name like concat('%', #{serviceName}, '%')</if>
            <if test="inputTokensTotal != null "> and input_tokens_total = #{inputTokensTotal}</if>
            <if test="outputTokensTotal != null "> and output_tokens_total = #{outputTokensTotal}</if>
            <if test="tokensTotal != null "> and tokens_total = #{tokensTotal}</if>
            <if test="succeedCallTotal != null "> and succeed_call_total = #{succeedCallTotal}</if>
            <if test="failureCallTotal != null "> and failure_call_total = #{failureCallTotal}</if>
            <if test="callTotal != null "> and call_total = #{callTotal}</if>
        </where>
    </select>
    
    <select id="selectCareCallById" parameterType="Long" resultMap="CareCallResult">
        <include refid="selectCareCallVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCareCall" parameterType="com.ruoyi.create.domain.CareCall" useGeneratedKeys="true" keyProperty="id">
        insert into s_care_call
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serviceId != null">service_id,</if>
            <if test="serviceName != null">service_name,</if>
            <if test="inputTokensTotal != null">input_tokens_total,</if>
            <if test="outputTokensTotal != null">output_tokens_total,</if>
            <if test="tokensTotal != null">tokens_total,</if>
            <if test="succeedCallTotal != null">succeed_call_total,</if>
            <if test="failureCallTotal != null">failure_call_total,</if>
            <if test="callTotal != null">call_total,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serviceId != null">#{serviceId},</if>
            <if test="serviceName != null">#{serviceName},</if>
            <if test="inputTokensTotal != null">#{inputTokensTotal},</if>
            <if test="outputTokensTotal != null">#{outputTokensTotal},</if>
            <if test="tokensTotal != null">#{tokensTotal},</if>
            <if test="succeedCallTotal != null">#{succeedCallTotal},</if>
            <if test="failureCallTotal != null">#{failureCallTotal},</if>
            <if test="callTotal != null">#{callTotal},</if>
         </trim>
    </insert>

    <update id="updateCareCall" parameterType="com.ruoyi.create.domain.CareCall">
        update s_care_call
        <trim prefix="SET" suffixOverrides=",">
            <if test="serviceId != null">service_id = #{serviceId},</if>
            <if test="serviceName != null">service_name = #{serviceName},</if>
            <if test="inputTokensTotal != null">input_tokens_total = #{inputTokensTotal},</if>
            <if test="outputTokensTotal != null">output_tokens_total = #{outputTokensTotal},</if>
            <if test="tokensTotal != null">tokens_total = #{tokensTotal},</if>
            <if test="succeedCallTotal != null">succeed_call_total = #{succeedCallTotal},</if>
            <if test="failureCallTotal != null">failure_call_total = #{failureCallTotal},</if>
            <if test="callTotal != null">call_total = #{callTotal},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCareCallById" parameterType="Long">
        delete from s_care_call where id = #{id}
    </delete>

    <delete id="deleteCareCallByIds" parameterType="String">
        delete from s_care_call where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>