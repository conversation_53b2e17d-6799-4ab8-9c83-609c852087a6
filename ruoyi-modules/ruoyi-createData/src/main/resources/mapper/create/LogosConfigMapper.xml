<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.LogosConfigMapper">

    <resultMap type="com.ruoyi.create.domain.LogosConfig" id="LogosConfigResult">
        <result property="id"    column="id"    />
        <result property="logoId"    column="logo_id"    />
        <result property="logoName"    column="logo_name"    />
        <result property="logoPath"    column="logo_path"    />
        <result property="logoPosition"    column="logo_position"    />
        <result property="title"    column="title"    />
        <result property="description"    column="description"    />
        <result property="school"    column="school"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectLogosConfigVo">
        select id, logo_id, logo_name, logo_path,logo_position, title, description, school, create_by, create_time, update_by, update_time from s_logos_config
    </sql>

    <select id="selectLogosConfigList" parameterType="com.ruoyi.create.domain.LogosConfig" resultMap="LogosConfigResult">
        <include refid="selectLogosConfigVo"/>
        <where>
            <if test="logoId != null "> and logo_id = #{logoId}</if>
            <if test="logoName != null  and logoName != ''"> and logo_name like concat('%', #{logoName}, '%')</if>
            <if test="logoPath != null  and logoPath != ''"> and logo_path = #{logoPath}</if>
            <if test="logoPosition != null  and logoPosition != ''"> and logo_position = #{logoPosition}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="school != null  and school != ''"> and school = #{school}</if>
            <if test="school == null  or school == ''"> and school = "山东财经大学"</if>
        </where>
    </select>

    <select id="selectLogosConfigById" parameterType="Long" resultMap="LogosConfigResult">
        <include refid="selectLogosConfigVo"/>
        where id = #{id}
    </select>

    <select id="selectLogosConfigBySchool" parameterType="String" resultMap="LogosConfigResult">
        <include refid="selectLogosConfigVo"/>
        where logo_position = #{logoPosition} and school = #{school}
    </select>

    <insert id="insertLogosConfig" parameterType="com.ruoyi.create.domain.LogosConfig">
        insert into s_logos_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="logoId != null">logo_id,</if>
            <if test="logoName != null">logo_name,</if>
            <if test="logoPath != null">logo_path,</if>
            <if test="logoPosition != null">logo_position,</if>
            <if test="title != null">title,</if>
            <if test="description != null">description,</if>
            <if test="school != null">school,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="logoId != null">#{logoId},</if>
            <if test="logoName != null">#{logoName},</if>
            <if test="logoPath != null">#{logoPath},</if>
            <if test="logoPosition != null">#{logoPosition},</if>
            <if test="title != null">#{title},</if>
            <if test="description != null">#{description},</if>
            <if test="school != null">#{school},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateLogosConfig" parameterType="com.ruoyi.create.domain.LogosConfig">
        update s_logos_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="logoId != null">logo_id = #{logoId},</if>
            <if test="logoName != null">logo_name = #{logoName},</if>
            <if test="logoPath != null">logo_path = #{logoPath},</if>
            <if test="logoPosition != null">logo_position = #{logoPosition},</if>
            <if test="title != null">title = #{title},</if>
            <if test="description != null">description = #{description},</if>
            <if test="school != null">school = #{school},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLogosConfigById" parameterType="Long">
        delete from s_logos_config where id = #{id}
    </delete>

    <select id="selectLogosConfigByLogoName" parameterType="com.ruoyi.create.domain.LogosConfig" resultMap="LogosConfigResult">
        <include refid="selectLogosConfigVo"/>
        where logo_name = #{logoName}
    </select>

    <select id="selectLogosConfigListAll" resultMap="LogosConfigResult">
        <include refid="selectLogosConfigVo"/>
        where logo_position = #{logoPosition} and school = #{univerName}
    </select>

</mapper>