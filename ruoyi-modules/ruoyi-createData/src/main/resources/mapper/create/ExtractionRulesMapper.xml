<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.ExtractionRulesMapper">

    <resultMap type="com.ruoyi.create.domain.ExtractionRules" id="ExtractionRulesResult">
        <result property="rulesId"    column="rules_id"    />
        <result property="rulesName"    column="rules_name"    />
        <result property="prompt"    column="prompt"    />
        <result property="response"    column="response"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectExtractionRulesVo">
        select rules_id, rules_name, prompt, response, create_by, create_time, update_by, update_time from s_extraction_rules
    </sql>

    <select id="selectExtractionRulesList" parameterType="com.ruoyi.create.domain.ExtractionRules" resultMap="ExtractionRulesResult">
        <include refid="selectExtractionRulesVo"/>
        <where>
            <if test="rulesName != null  and rulesName != ''"> and rules_name like concat('%', #{rulesName}, '%')</if>
            <if test="prompt != null  and prompt != ''"> and prompt = #{prompt}</if>
            <if test="response != null  and response != ''"> and response = #{response}</if>
        </where>
    </select>

    <select id="selectExtractionRulesByRulesId" parameterType="Long" resultMap="ExtractionRulesResult">
        <include refid="selectExtractionRulesVo"/>
        where rules_id = #{rulesId}
    </select>

    <insert id="insertExtractionRules" parameterType="com.ruoyi.create.domain.ExtractionRules" useGeneratedKeys="true" keyProperty="rulesId">
        insert into s_extraction_rules
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rulesName != null">rules_name,</if>
            <if test="prompt != null">prompt,</if>
            <if test="response != null">response,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rulesName != null">#{rulesName},</if>
            <if test="prompt != null">#{prompt},</if>
            <if test="response != null">#{response},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateExtractionRules" parameterType="com.ruoyi.create.domain.ExtractionRules">
        update s_extraction_rules
        <trim prefix="SET" suffixOverrides=",">
            <if test="rulesName != null">rules_name = #{rulesName},</if>
            <if test="prompt != null">prompt = #{prompt},</if>
            <if test="response != null">response = #{response},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where rules_id = #{rulesId}
    </update>

    <delete id="deleteExtractionRulesByRulesId" parameterType="Long">
        delete from s_extraction_rules where rules_id = #{rulesId}
    </delete>

    <delete id="deleteExtractionRulesByRulesIds" parameterType="String">
        delete from s_extraction_rules where rules_id in
        <foreach item="rulesId" collection="array" open="(" separator="," close=")">
            #{rulesId}
        </foreach>
    </delete>

    <select id="selectExtractionRulesAll" resultMap="ExtractionRulesResult">
        <include refid="selectExtractionRulesVo"/>
    </select>
</mapper>