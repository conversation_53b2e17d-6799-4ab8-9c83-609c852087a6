<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.TopicsReplyMapper">
    
    <resultMap type="com.ruoyi.create.domain.TopicsReply" id="TopicsReplyResult">
        <result property="id"    column="id"    />
        <result property="msgId"    column="msg_id"    />
        <result property="lastReplyId"    column="last_reply_id"    />
        <result property="replyId"    column="reply_id"    />
        <result property="replyMsg"    column="reply_msg"    />
        <result property="createTime"    column="create_time"    />
        <result property="lastReplyUid"    column="last_reply_uid"    />
        <result property="lastReplyUname"    column="last_reply_uname"    />
        <result property="replyUserid"    column="reply_userid"    />
        <result property="replyUserName"    column="reply_user_name"    />
    </resultMap>

    <sql id="selectTopicsReplyVo">
        select id, msg_id, last_reply_id, reply_id, reply_msg, create_time, last_reply_uid, last_reply_uname, reply_userid, reply_user_name from s_topics_reply
    </sql>

    <select id="selectTopicsReplyList" parameterType="com.ruoyi.create.domain.TopicsReply" resultMap="TopicsReplyResult">
        <include refid="selectTopicsReplyVo"/>
        <where>  
            <if test="msgId != null "> and msg_id = #{msgId}</if>
            <if test="lastReplyId != null "> and last_reply_id = #{lastReplyId}</if>
            <if test="replyMsg != null  and replyMsg != ''"> and reply_msg = #{replyMsg}</if>
            <if test="lastReplyUid != null "> and last_reply_uid = #{lastReplyUid}</if>
            <if test="lastReplyUname != null  and lastReplyUname != ''"> and last_reply_uname like concat('%', #{lastReplyUname}, '%')</if>
            <if test="replyUserid != null "> and reply_userid = #{replyUserid}</if>
            <if test="replyUserName != null  and replyUserName != ''"> and reply_user_name like concat('%', #{replyUserName}, '%')</if>
        </where>
    </select>
    
    <select id="selectTopicsReplyById" parameterType="Long" resultMap="TopicsReplyResult">
        <include refid="selectTopicsReplyVo"/>
        where msg_id = #{msgId}
    </select>
        
    <insert id="insertTopicsReply" parameterType="com.ruoyi.create.domain.TopicsReply" useGeneratedKeys="true" keyProperty="id">
        insert into s_topics_reply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="msgId != null">msg_id,</if>
            <if test="lastReplyId != null">last_reply_id,</if>
            <if test="replyId != null">reply_id,</if>
            <if test="replyMsg != null">reply_msg,</if>
            <if test="createTime != null">create_time,</if>
            <if test="lastReplyUid != null">last_reply_uid,</if>
            <if test="lastReplyUname != null">last_reply_uname,</if>
            <if test="replyUserid != null">reply_userid,</if>
            <if test="replyUserName != null">reply_user_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="msgId != null">#{msgId},</if>
            <if test="lastReplyId != null">#{lastReplyId},</if>
            <if test="replyId != null">#{replyId},</if>
            <if test="replyMsg != null">#{replyMsg},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="lastReplyUid != null">#{lastReplyUid},</if>
            <if test="lastReplyUname != null">#{lastReplyUname},</if>
            <if test="replyUserid != null">#{replyUserid},</if>
            <if test="replyUserName != null">#{replyUserName},</if>
         </trim>
    </insert>

    <update id="updateTopicsReply" parameterType="com.ruoyi.create.domain.TopicsReply">
        update s_topics_reply
        <trim prefix="SET" suffixOverrides=",">
            <if test="msgId != null">msg_id = #{msgId},</if>
            <if test="lastReplyId != null">last_reply_id = #{lastReplyId},</if>
            <if test="replyId != null">reply_id = #{replyId},</if>
            <if test="replyMsg != null">reply_msg = #{replyMsg},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="lastReplyUid != null">last_reply_uid = #{lastReplyUid},</if>
            <if test="lastReplyUname != null">last_reply_uname = #{lastReplyUname},</if>
            <if test="replyUserid != null">reply_userid = #{replyUserid},</if>
            <if test="replyUserName != null">reply_user_name = #{replyUserName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTopicsReplyById" parameterType="Long">
        delete from s_topics_reply where id = #{id}
    </delete>

    <delete id="deleteTopicsReplyByIds" parameterType="String">
        delete from s_topics_reply where msg_id in
        <foreach item="msgId" collection="array" open="(" separator="," close=")">
            #{msgId}
        </foreach>
    </delete>

    <select id="selectTopicsReply" resultMap="TopicsReplyResult">
        SELECT
            smr.msg_id,
            smr.last_reply_id,
            smr.reply_id,
            smr.reply_msg,
            smr.create_time,
            smr.last_reply_uid,
            u_last.nick_name AS last_reply_uname,
            smr.reply_userid,
            u_reply.nick_name AS reply_user_name
        FROM
            s_topics_reply smr
        LEFT JOIN sys_user u_last ON smr.last_reply_uid = u_last.user_id
        LEFT JOIN sys_user u_reply ON smr.reply_userid = u_reply.user_id
        where smr.msg_id = #{msgId}
        ORDER BY smr.create_time Desc
        limit 0,3
    </select>

    <select id="selectTopicsReplyAll" resultMap="TopicsReplyResult">
        <include refid="selectTopicsReplyVo"/>
        where msg_id = #{msgId}
        ORDER BY create_time Desc
    </select>

    <delete id="deleteTopicsReplylyIds">
        delete from s_topics_reply where reply_id in
        <foreach item="replyId" collection="array" open="(" separator="," close=")">
            #{replyId}
        </foreach>
    </delete>

    <select id="countTopicsReply">
        SELECT
            count(1)
        FROM
            s_topics_reply smr

        where smr.msg_id = #{msgId}

    </select>
</mapper>