<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.SpeechdraftProblemMapper">

    <resultMap type="com.ruoyi.create.domain.SpeechdraftProblem" id="SpeechdraftProblemResult">
        <result property="id"    column="id"    />
        <result property="problem"    column="problem"    />
        <result property="options"    column="options"    />
        <result property="anwers"    column="anwers"    />
        <result property="presentationId"    column="presentation_id"    />
        <result property="index"    column="index"    />
        <result property="problemIndex"    column="problem_index"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectSpeechdraftProblemVo">
        select id, problem, `options`, anwers, presentation_id, `index`, problem_index, remark, create_time from s_speechdraft_problem
    </sql>

    <select id="selectSpeechdraftProblemList" parameterType="com.ruoyi.create.domain.SpeechdraftProblem" resultMap="SpeechdraftProblemResult">
        <include refid="selectSpeechdraftProblemVo"/>
        <where>
            <if test="problem != null  and problem != ''"> and problem = #{problem}</if>
            <if test="options != null  and options != ''"> and options = #{options}</if>
            <if test="anwers != null  and anwers != ''"> and anwers = #{anwers}</if>
            <if test="presentationId != null  and presentationId != ''"> and presentation_id = #{presentationId}</if>
            <if test="index != null "> and `index` = #{index}</if>
            <if test="problemIndex != null "> and problem_index = #{problemIndex}</if>
        </where>
    </select>

    <select id="selectSpeechdraftProblemById" parameterType="Long" resultMap="SpeechdraftProblemResult">
        <include refid="selectSpeechdraftProblemVo"/>
        where id = #{id}
    </select>

    <insert id="insertSpeechdraftProblem" parameterType="com.ruoyi.create.domain.SpeechdraftProblem">
        insert into s_speechdraft_problem
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="problem != null and problem != ''">problem,</if>
            <if test="options != null and options != ''">options,</if>
            <if test="anwers != null and anwers != ''">anwers,</if>
            <if test="presentationId != null and presentationId != ''">presentation_id,</if>
            <if test="index != null">`index`,</if>
            <if test="problemIndex != null">problem_index,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="problem != null and problem != ''">#{problem},</if>
            <if test="options != null and options != ''">#{options},</if>
            <if test="anwers != null and anwers != ''">#{anwers},</if>
            <if test="presentationId != null and presentationId != ''">#{presentationId},</if>
            <if test="index != null">#{index},</if>
            <if test="problemIndex != null">#{problemIndex},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateSpeechdraftProblem" parameterType="com.ruoyi.create.domain.SpeechdraftProblem">
        update s_speechdraft_problem
        <trim prefix="SET" suffixOverrides=",">
            <if test="problem != null and problem != ''">problem = #{problem},</if>
            <if test="options != null and options != ''">options = #{options},</if>
            <if test="anwers != null and anwers != ''">anwers = #{anwers},</if>
            <if test="presentationId != null and presentationId != ''">presentation_id = #{presentationId},</if>
            <if test="index != null">`index` = #{index},</if>
            <if test="problemIndex != null">problem_index = #{problemIndex},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSpeechdraftProblemById" parameterType="Long">
        delete from s_speechdraft_problem where id = #{id}
    </delete>

    <delete id="deleteSpeechdraftProblemBypresentationId" parameterType="Long">
        delete from s_speechdraft_problem where presentation_id = #{presentationId}
    </delete>


    <delete id="deleteSpeechdraftProblemByIds" parameterType="String">
        delete from s_speechdraft_problem where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertSpeechdraftProblemList">
        insert into s_speechdraft_problem(problem, options, anwers,presentation_id,`index`,create_time) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.problem},#{item.options},#{item.anwers},#{item.presentationId},#{item.index},#{item.createTime})
        </foreach>
    </insert>
</mapper>
