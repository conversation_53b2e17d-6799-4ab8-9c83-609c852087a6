<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.SCourseClassMapper">

    <resultMap type="com.ruoyi.create.domain.AttendanceParam" id="AttendanceParamResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="courseName"    column="course_name"    />
        <result property="courseType"    column="course_type"    />
        <result property="term"    column="term"    />
        <result property="startTime"    column="start_time"    />
        <result property="validity"    column="validity"    />
        <result property="endTime"    column="end_time"    />
        <result property="signType"    column="sign_type"    />
        <result property="signClass"    column="sign_class"    />
        <result property="isAttendance"    column="is_attendance"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAttendanceParamVo">
        select id, teacher_id, course_name, course_type, term, start_time, end_time, sign_type, sign_class, is_attendance, create_by, create_time, update_by,update_time from s_sign_in_sessions
    </sql>

    <select id="selectSCourseClassList" parameterType="com.ruoyi.create.domain.AttendanceParam" resultMap="AttendanceParamResult">
        <include refid="selectAttendanceParamVo"/>
        <where>
            <if test="courseName != null "> and course_name = #{courseName}</if>
            <if test="courseType != null "> and course_type = #{courseType}</if>
            <if test="term != null "> and term = #{term}</if>
            <if test="signClass != null "> and sign_class = #{signClass}</if>
        </where>
    </select>

    <select id="selectSCourseClassById" parameterType="Long" resultMap="AttendanceParamResult">
        <include refid="selectAttendanceParamVo"/>
        where id = #{id}
    </select>

    <insert id="insertStartSignInSession" parameterType="com.ruoyi.create.domain.AttendanceParam">
        insert into s_sign_in_sessions
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="courseName != null">course_name,</if>
            <if test="courseType != null">course_type,</if>
            <if test="term != null">term,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="signType != null">sign_type,</if>
            <if test="signClass != null">sign_class,</if>
            <if test="isAttendance != null">is_attendance,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="courseName != null">#{courseName},</if>
            <if test="courseType != null">#{courseType},</if>
            <if test="term != null">#{term},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="signType != null">#{signType},</if>
            <if test="signClass != null">#{signClass},</if>
            <if test="isAttendance != null">#{isAttendance},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <insert id="insertStudentInfo" parameterType="com.ruoyi.create.domain.StudentSign">
        insert into s_student_sign
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="sign_id != null">sign_id,</if>
            <if test="studentId != null">student_id,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="signClass != null">sign_class,</if>
            <if test="courseName != null">course_name,</if>
            <if test="courseType != null">course_type,</if>
            <if test="term != null">term,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="sign_id != null">#{signId},</if>
            <if test="studentId != null">#{studentId},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="signClass != null">#{signClass},</if>
            <if test="courseName != null">#{courseName},</if>
            <if test="courseType != null">#{courseType},</if>
            <if test="term != null">#{term},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
        </trim>
    </insert>

    <select id="selectSignType" parameterType="com.ruoyi.create.domain.AttendanceParam" resultType="String">
        select distinct course_type from s_student_course
        <where>
            <if test="courseName != null "> and course_name = #{courseName}</if>
            <if test="term != null "> and term = #{term}</if>
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
        </where>
    </select>
</mapper>
