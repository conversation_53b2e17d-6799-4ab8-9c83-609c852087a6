<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.MySelfPortraitMapper">

    <!-- 结果映射 -->
    <resultMap id="MySelfPortraitResultMap" type="com.ruoyi.create.domain.MySelfPortrait">
        <id property="studentId" column="student_id" />
        <result property="laScore" column="la_score" />
        <result property="laHomework" column="la_homework" />
        <result property="laMastery" column="la_mastery" />
        <result property="laPerformance" column="la_performance" />
        <result property="laInterest" column="la_interest" />
        <result property="laTalent" column="la_talent" />
        <result property="laEvaluation" column="la_evaluation" />
        <result property="lbMorality" column="lb_morality" />
        <result property="lbAesthetics" column="lb_aesthetics" />
        <result property="lbHealth" column="lb_health" />
        <result property="lbCooperation" column="lb_cooperation" />
        <result property="lbLearning" column="lb_learning" />
        <result property="yuExperiment" column="yu_experiment" />
        <result property="yuElective" column="yu_elective" />
        <result property="yuGeneral" column="yu_general" />
        <result property="yuCore" column="yu_core" />
        <result property="yuFoundation" column="yu_foundation" />
        <result property="xuTotalCredits" column="xu_total_credits" />
        <result property="xuCompletedCredits" column="xu_completed_credits" />
        <result property="yuceFenxi" column="yuce_fenxi" />
        <result property="semester" column="semester" />
    </resultMap>

    <!-- 查询学生自画像信息 -->
    <select id="selectById" parameterType="java.lang.String" resultMap="MySelfPortraitResultMap">
    SELECT * FROM s_self_portrait
    WHERE student_id = #{studentId}
    ORDER BY statistics_time DESC
    LIMIT 1
</select>
    <!-- 插入更新预测分析内容 -->
    <update id="addOrUpdateYuceFenxi" parameterType="com.ruoyi.create.domain.MySelfPortrait">
        update s_self_portrait
        <trim prefix="SET" suffixOverrides=",">
            <if test="yuceFenxi != null">yuce_fenxi = #{yuceFenxi},</if>
        </trim>
        where student_id = #{studentId}
    </update>

    <update id="updateByStudentId" parameterType="com.ruoyi.create.domain.MySelfPortrait">
        UPDATE s_self_portrait
        <set>
            la_score = #{laScore},
            la_homework = #{laHomework},
            la_mastery = #{laMastery},
            la_performance = #{laPerformance},
            la_interest = #{laInterest},
            la_talent = #{laTalent},
            la_evaluation = #{laEvaluation},
            lb_morality = #{lbMorality},
            lb_aesthetics = #{lbAesthetics},
            lb_health = #{lbHealth},
            lb_cooperation = #{lbCooperation},
            lb_learning = #{lbLearning},
            yu_experiment = #{yuExperiment},
            yu_elective = #{yuElective},
            yu_general = #{yuGeneral},
            yu_core = #{yuCore},
            yu_foundation = #{yuFoundation},
            xu_total_credits = #{xuTotalCredits},
            xu_completed_credits = #{xuCompletedCredits},
            yuce_fenxi = #{yuceFenxi},
            statistics_time = #{statisticsTime},
            semester = #{semester}
        </set>
        WHERE student_id = #{studentId}
    </update>

<!--    <select id="getAllStudentIds" resultType="java.lang.String">-->
<!--        SELECT student_id FROM s_self_portrait-->
<!--    </select>-->
    <!-- 插入学生画像数据 -->
    <insert id="insert" parameterType="com.ruoyi.create.domain.MySelfPortrait">
        INSERT INTO s_self_portrait (
            student_id, la_score, la_homework, la_mastery, la_performance,
            la_interest, la_talent, la_evaluation, lb_morality, lb_aesthetics,
            lb_health, lb_cooperation, lb_learning,
            yu_experiment, yu_elective, yu_general, yu_core, yu_foundation,
            xu_total_credits, xu_completed_credits,
            yuce_fenxi, statistics_time, semester
        ) VALUES (
                     #{studentId}, #{laScore}, #{laHomework}, #{laMastery}, #{laPerformance},
                     #{laInterest}, #{laTalent}, #{laEvaluation}, #{lbMorality}, #{lbAesthetics},
                     #{lbHealth}, #{lbCooperation}, #{lbLearning},
                     #{yuExperiment}, #{yuElective}, #{yuGeneral}, #{yuCore}, #{yuFoundation},
                     #{xuTotalCredits}, #{xuCompletedCredits},
                     #{yuceFenxi}, #{statisticsTime}, #{semester}
                 )
    </insert>




</mapper>