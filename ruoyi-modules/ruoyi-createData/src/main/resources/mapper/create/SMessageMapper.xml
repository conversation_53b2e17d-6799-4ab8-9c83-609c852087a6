<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.SMessageMapper">
    
    <resultMap type="com.ruoyi.create.domain.SMessage" id="SMessageResult">
        <result property="id"    column="id"    />
        <result property="msgId"    column="msg_id"    />
        <result property="message"    column="message"    />
        <result property="msgUserId"    column="msg_user_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="nickName"    column="nick_name"    />
        <collection  property="sMsgReplyList"   javaType="java.util.List"  resultMap="SMsgReplyResult" />
    </resultMap>

    <resultMap type="com.ruoyi.create.domain.SMsgReply" id="SMsgReplyResult">
        <result property="msgId"    column="msg_id"    />
        <result property="lastReplyId"    column="last_reply_id"    />
        <result property="replyId"    column="reply_id"    />
        <result property="replyMsg"    column="reply_msg"    />
        <result property="createTime"    column="create_time"    />
        <result property="lastReplyUid"    column="last_reply_uid"    />
        <result property="lastReplyUname"    column="last_reply_uname"    />
        <result property="replyUserid"    column="reply_userid"    />
        <result property="replyUserName"    column="reply_user_name"    />
    </resultMap>

    <sql id="selectSMessageVo">
        select msg_id, message, msg_user_id, create_time from s_message
    </sql>

    <select id="selectSMessageList" parameterType="com.ruoyi.create.domain.SMessage" resultMap="SMessageResult">
        select m.msg_id, m.message, m.msg_user_id, m.create_time ,u.nick_name from s_message m
        left join sys_user u on m.msg_user_id =u.user_id
        <where>
            <if test="msgId != null"> and m.msg_id =#{msgId}</if>
            <if test="message != null  and message != ''"> and m.message = #{message}</if>
            <if test="msgUserId != null "> and m.msg_user_id = #{msgUserId}</if>
        </where>
        order by m.create_time Desc
    </select>
    
    <select id="selectSMessageByMsgId" parameterType="Long" resultMap="SMessageResult">
        <include refid="selectSMessageVo"/>
        where msg_id = #{msgId}
    </select>
        
    <insert id="insertSMessage" parameterType="com.ruoyi.create.domain.SMessage">
        insert into s_message
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="msgId != null">msg_id,</if>
            <if test="message != null">message,</if>
            <if test="msgUserId != null">msg_user_id,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="msgId != null">#{msgId},</if>
            <if test="message != null">#{message},</if>
            <if test="msgUserId != null">#{msgUserId},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateSMessage" parameterType="com.ruoyi.create.domain.SMessage">
        update s_message
        <trim prefix="SET" suffixOverrides=",">
            <if test="message != null">message = #{message},</if>
            <if test="msgUserId != null">msg_user_id = #{msgUserId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where msg_id = #{msgId}
    </update>

    <delete id="deleteSMessageByMsgId" parameterType="Long">
        delete from s_message where msg_id = #{msgId}
    </delete>

    <delete id="deleteSMessageByMsgIds" parameterType="String">
        delete from s_message where msg_id in 
        <foreach item="msgId" collection="array" open="(" separator="," close=")">
            #{msgId}
        </foreach>
    </delete>
</mapper>