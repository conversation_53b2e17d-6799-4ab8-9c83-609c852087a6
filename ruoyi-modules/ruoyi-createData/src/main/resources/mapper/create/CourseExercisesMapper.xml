<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.CourseExercisesMapper">
    
    <resultMap type="com.ruoyi.create.domain.CourseExercises" id="CourseExercisesResult">
        <result property="id"    column="id"    />
        <result property="exercisesName"    column="exercises_name"    />
        <result property="exercisesSynopsis"    column="exercises_synopsis"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="courseName"    column="course_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="filePath"    column="file_path"    />
    </resultMap>

    <sql id="selectCourseExercisesVo">
        select id, exercises_name, exercises_synopsis, teacher_id, course_name, create_by, create_time, update_by, update_time from s_course_exercises
    </sql>

    <select id="selectCourseExercisesList" parameterType="com.ruoyi.create.domain.CourseExercises" resultMap="CourseExercisesResult">
        select t1.id, t1.exercises_name, t1.exercises_synopsis, t1.teacher_id, t1.course_name, t1.create_by, t1.create_time, t1.update_by, t1.update_time, t2.file_path from s_course_exercises t1
        left join sys_file_info t2 on t1.id = t2.busi_id
        <where>  
            <if test="exercisesName != null  and exercisesName != ''"> and t1.exercises_name like concat('%', #{exercisesName}, '%')</if>
            <if test="exercisesSynopsis != null  and exercisesSynopsis != ''"> and t1.exercises_synopsis = #{exercisesSynopsis}</if>
            <if test="teacherId != null  and teacherId != ''"> and t1.teacher_id = #{teacherId}</if>
            <if test="courseName != null  and courseName != ''"> and t1.course_name like concat('%', #{courseName}, '%')</if>
        </where>
    </select>
    
    <select id="selectCourseExercisesById" parameterType="Long" resultMap="CourseExercisesResult">
        <include refid="selectCourseExercisesVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCourseExercises" parameterType="com.ruoyi.create.domain.CourseExercises">
        insert into s_course_exercises
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="exercisesName != null">exercises_name,</if>
            <if test="exercisesSynopsis != null">exercises_synopsis,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="courseName != null">course_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="exercisesName != null">#{exercisesName},</if>
            <if test="exercisesSynopsis != null">#{exercisesSynopsis},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="courseName != null">#{courseName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCourseExercises" parameterType="com.ruoyi.create.domain.CourseExercises">
        update s_course_exercises
        <trim prefix="SET" suffixOverrides=",">
            <if test="exercisesName != null">exercises_name = #{exercisesName},</if>
            <if test="exercisesSynopsis != null">exercises_synopsis = #{exercisesSynopsis},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="courseName != null">course_name = #{courseName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCourseExercisesById" parameterType="Long">
        delete from s_course_exercises where id = #{id}
    </delete>

    <delete id="deleteCourseExercisesByIds" parameterType="String">
        delete from s_course_exercises where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>