<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.CareResourceMapper">
    
    <resultMap type="com.ruoyi.create.domain.CareResource" id="CareResourceResult">
        <result property="id"    column="id"    />
        <result property="serviceId"    column="service_id"    />
        <result property="type"    column="type"    />
        <result property="qps"    column="qps"    />
    </resultMap>

    <sql id="selectCareResourceVo">
        select id, service_id, type, qps from s_care_resource
    </sql>

    <select id="selectCareResourceList" parameterType="com.ruoyi.create.domain.CareResource" resultMap="CareResourceResult">
        <include refid="selectCareResourceVo"/>
        <where>  
            <if test="serviceId != null  and serviceId != ''"> and service_id = #{serviceId}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="qps != null "> and qps = #{qps}</if>
        </where>
    </select>
    
    <select id="selectCareResourceById" parameterType="Long" resultMap="CareResourceResult">
        <include refid="selectCareResourceVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCareResource" parameterType="com.ruoyi.create.domain.CareResource" useGeneratedKeys="true" keyProperty="id">
        insert into s_care_resource
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serviceId != null">service_id,</if>
            <if test="type != null">type,</if>
            <if test="qps != null">qps,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serviceId != null">#{serviceId},</if>
            <if test="type != null">#{type},</if>
            <if test="qps != null">#{qps},</if>
         </trim>
    </insert>

    <update id="updateCareResource" parameterType="com.ruoyi.create.domain.CareResource">
        update s_care_resource
        <trim prefix="SET" suffixOverrides=",">
            <if test="serviceId != null">service_id = #{serviceId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="qps != null">qps = #{qps},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCareResourceById" parameterType="Long">
        delete from s_care_resource where id = #{id}
    </delete>

    <delete id="deleteCareResourceByIds" parameterType="String">
        delete from s_care_resource where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>