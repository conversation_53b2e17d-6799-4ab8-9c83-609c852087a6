<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.LiteratrueKeywordCountMapper">

    <resultMap type="com.ruoyi.create.domain.LiteratrueKeywordCount" id="LiteratrueKeywordCountResult">
        <result property="id"    column="id"    />
        <result property="titleId"    column="titleId"    />
        <result property="title"    column="title"    />
        <result property="subjectId"    column="subjectId"    />
        <result property="category"    column="category"    />
        <result property="keyword"    column="keyword"    />
        <result property="count"    column="count"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectLiteratrueKeywordCountVo">
        select id, titleId, title, subjectId, category, keyword, count, create_by, create_time, update_by, update_time from s_literatrue_keyword_count
    </sql>

    <select id="selectLiteratrueKeywordCountList" parameterType="com.ruoyi.create.domain.LiteratrueKeywordCount" resultMap="LiteratrueKeywordCountResult">
        <include refid="selectLiteratrueKeywordCountVo"/>
        <where>
            <if test="titleId != null "> and titleId = #{titleId}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="subjectId != null  and subjectId != ''"> and subjectId = #{subjectId}</if>
            <if test="category != null  and category != ''"> and category = #{category}</if>
            <if test="keyword != null  and keyword != ''"> and keyword = #{keyword}</if>
            <if test="count != null "> and count = #{count}</if>
        </where>
    </select>

    <select id="selectLiteratrueKeywordCountById" parameterType="Long" resultMap="LiteratrueKeywordCountResult">
        <include refid="selectLiteratrueKeywordCountVo"/>
        where id = #{id}
    </select>

    <select id="selectGroupLiteratrueKeywordCountList" parameterType="com.ruoyi.create.domain.LiteratrueKeywordCount"
            resultMap="LiteratrueKeywordCountResult">
        SELECT
            a.id id,
            a.category category,
            a.keyword keyword,
            a.count count
        FROM
            (
            SELECT
                id,
                category,
                keyword,
                count(*) count
            FROM
                s_literatrue_data
            WHERE
                titleId IN ( SELECT DISTINCT titleId FROM s_literatrue_data WHERE keyword =#{keyword})
            GROUP BY
                keyword
            ) a
        <where>
            <choose>
                <when test="category != null and category != '' and category != '相关主题'">
                    a.category = #{category}
                </when>
                <when test="category != null and category != '' and category == '相关主题'">
                    a.category NOT IN('高影响力作者', '研究机构')
                </when>
            </choose>
        </where>
        ORDER BY
            count DESC
        LIMIT 0,10
    </select>

    <select id="getTitleIdDesc" parameterType="com.ruoyi.create.domain.LiteratrueKeywordCount" resultType="java.lang.String">
        SELECT MAX(titleId) FROM s_literatrue_keyword_count
        <where>
            <if test="title != null  and title != ''"> and title = #{title}</if>
        </where>
    </select>

    <select id="getCategoryIdDesc" parameterType="com.ruoyi.create.domain.LiteratrueKeywordCount" resultType="java.lang.String">
        SELECT MAX(categoryId) FROM s_literatrue_keyword_count
        <where>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="category != null  and category != ''"> and category = #{category}</if>
        </where>
    </select>
    <select id="selectGroupLiteratrueKeywordCountListBySubject"
            resultMap="LiteratrueKeywordCountResult">
        SELECT
        a.id id,
        a.category category,
        a.keyword keyword,
        a.count count
        FROM
        (
        SELECT
        id,
        category,
        keyword,
        count(*) count
        FROM
        s_literatrue_data
        WHERE
        titleId IN ( SELECT DISTINCT titleId FROM s_literatrue_data WHERE keyword  like concat('%', #{keyword}, '%'))
        GROUP BY
        keyword
        ) a
        <where>
            <choose>
                <when test="category != null and category != '' and category != '相关主题'">
                    a.category = #{category}
                </when>
                <when test="category != null and category != '' and category == '相关主题'">
                    a.category NOT IN('高影响力作者', '研究机构')
                </when>
            </choose>
        </where>
        ORDER BY
        count DESC
        LIMIT 0,10
    </select>

    <insert id="insertLiteratrueKeywordCount" parameterType="com.ruoyi.create.domain.LiteratrueKeywordCount" useGeneratedKeys="true" keyProperty="id">
        insert into s_literatrue_keyword_count
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="titleId != null">titleId,</if>
            <if test="title != null">title,</if>
            <if test="subjectId != null">subjectId,</if>
            <if test="category != null">category,</if>
            <if test="keyword != null">keyword,</if>
            <if test="count != null">count,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="titleId != null">#{titleId},</if>
            <if test="title != null">#{title},</if>
            <if test="subjectId != null">#{subjectId},</if>
            <if test="category != null">#{category},</if>
            <if test="keyword != null">#{keyword},</if>
            <if test="count != null">#{count},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateLiteratrueKeywordCount" parameterType="com.ruoyi.create.domain.LiteratrueKeywordCount">
        update s_literatrue_keyword_count
        <trim prefix="SET" suffixOverrides=",">
            <if test="titleId != null">titleId = #{titleId},</if>
            <if test="title != null">title = #{title},</if>
            <if test="subjectId != null">subjectId = #{subjectId},</if>
            <if test="category != null">category = #{category},</if>
            <if test="keyword != null">keyword = #{keyword},</if>
            <if test="count != null">count = #{count},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLiteratrueKeywordCountById" parameterType="Long">
        delete from s_literatrue_keyword_count where id = #{id}
    </delete>

    <delete id="deleteLiteratrueKeywordCountByIds" parameterType="String">
        delete from s_literatrue_keyword_count where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>