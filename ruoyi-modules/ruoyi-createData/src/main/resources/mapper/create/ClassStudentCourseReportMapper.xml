<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.ClassStudentCourseReportMapper">

    <resultMap type="com.ruoyi.create.domain.ClassStudentCourseReport" id="ClassStudentCourseReportResult">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="courseClassId" column="course_class_id"/>
        <result property="courseClassName" column="course_class_name"/>
        <result property="studentId" column="student_id"/>
        <result property="userId" column="user_id"/>
        <result property="nickName" column="nick_name"/>
        <result property="courseName" column="course_name"/>
        <result property="reportDate" column="report_date"/>
        <result property="totalChapter" column="total_chapter"/>
        <result property="finishedChapter" column="finished_chapter"/>
        <result property="completionProgress" column="completion_progress"/>
        <result property="averageCompletionProgress" column="average_completion_progress"/>
        <result property="comparison" column="comparison"/>
        <result property="intelligentQuestionAnsweringCount" column="intelligent_question_answering_count"/>
        <result property="averageWeeklyQuestionAnsweringCount" column="average_weekly_question_answering_count"/>
        <result property="classAverageQuestionAnsweringCount" column="class_average_question_answering_count"/>
        <result property="comparisonWithClassAverageQuestionAnsweringCount"
                column="comparison_with_class_average_question_answering_count"/>
        <result property="totalHomework" column="total_homework"/>
        <result property="submittedTimes" column="submitted_times"/>
        <result property="submissionRatio" column="submission_ratio"/>
        <result property="learningProgressAdvice" column="learning_progress_advice"/>
        <result property="frequencyOfQuestionsAdvice" column="frequency_of_questions_advice"/>
        <result property="learningStrategyAdvice" column="learning_strategy_advice"/>
        <result property="countLeadingRatio" column="count_leading_ratio"/>
        <result property="countFlatRatio" column="count_flat_ratio"/>
        <result property="countLagRatio" column="count_lag_ratio"/>
        <result property="countP100" column="count_p100"/>
        <result property="countP9" column="count_p9"/>
        <result property="countP6" column="count_p6"/>
        <result property="lessThanP6NameStr" column="less_than_p6_name_str"/>
        <result property="quoteAnswerCountP9" column="quote_answer_count_p9"/>
        <result property="quoteAnswerCountP6" column="quote_answer_count_p6"/>
        <result property="quoteAnswerLessThantStr" column="quote_answer_less_thant_str"/>
        <result property="suggestionsForClassProgress" column="suggestions_for_class_progress"/>
        <result property="suggestionsForClassHomework" column="suggestions_for_class_homework"/>
        <result property="suggestionsForClassInteractions" column="suggestions_for_class_interactions"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectClassStudentCourseReportVo">
        SELECT id,
               type,
               course_class_id,
               course_class_name,
               student_id,
               user_id,
               nick_name,
               course_name,
               report_date,
               total_chapter,
               finished_chapter,
               completion_progress,
               average_completion_progress,
               comparison,
               intelligent_question_answering_count,
               average_weekly_question_answering_count,
               class_average_question_answering_count,
               comparison_with_class_average_question_answering_count,
               total_homework,
               submitted_times,
               submission_ratio,
               learning_progress_advice,
               frequency_of_questions_advice,
               learning_strategy_advice,
               count_leading_ratio,
               count_flat_ratio,
               count_lag_ratio,
               count_p100,
               count_p9,
               count_p6,
               less_than_p6_name_str,
               quote_answer_count_p9,
               quote_answer_count_p6,
               quote_answer_less_thant_str,
               suggestions_for_class_progress,
               suggestions_for_class_homework,
               suggestions_for_class_interactions,
               remark,
               create_by,
               create_time,
               update_by,
               update_time
        FROM s_class_student_course_report
    </sql>

    <select id="selectClassStudentCourseReportList" parameterType="com.ruoyi.create.domain.ClassStudentCourseReport"
            resultMap="ClassStudentCourseReportResult">
        <include refid="selectClassStudentCourseReportVo"/>
        <where>
            <if test="type != null  and type != ''">and type = #{type}</if>
            <if test="courseClassId != null ">and course_class_id = #{courseClassId}</if>
            <if test="courseClassName != null  and courseClassName != ''">and course_class_name like concat('%',
                #{courseClassName}, '%')
            </if>
            <if test="studentId != null  and studentId != ''">and student_id = #{studentId}</if>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="nickName != null  and nickName != ''">and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="courseName != null  and courseName != ''">and course_name like concat('%', #{courseName}, '%')
            </if>
            <if test="reportDate != null ">and report_date = #{reportDate}</if>
            <if test="totalChapter != null  and totalChapter != ''">and total_chapter = #{totalChapter}</if>
            <if test="finishedChapter != null  and finishedChapter != ''">and finished_chapter = #{finishedChapter}</if>
            <if test="completionProgress != null  and completionProgress != ''">and completion_progress =
                #{completionProgress}
            </if>
            <if test="averageCompletionProgress != null  and averageCompletionProgress != ''">and
                average_completion_progress = #{averageCompletionProgress}
            </if>
            <if test="comparison != null  and comparison != ''">and comparison = #{comparison}</if>
            <if test="intelligentQuestionAnsweringCount != null  and intelligentQuestionAnsweringCount != ''">and
                intelligent_question_answering_count = #{intelligentQuestionAnsweringCount}
            </if>
            <if test="averageWeeklyQuestionAnsweringCount != null  and averageWeeklyQuestionAnsweringCount != ''">and
                average_weekly_question_answering_count = #{averageWeeklyQuestionAnsweringCount}
            </if>
            <if test="classAverageQuestionAnsweringCount != null ">and class_average_question_answering_count =
                #{classAverageQuestionAnsweringCount}
            </if>
            <if test="comparisonWithClassAverageQuestionAnsweringCount != null  and comparisonWithClassAverageQuestionAnsweringCount != ''">
                and comparison_with_class_average_question_answering_count =
                #{comparisonWithClassAverageQuestionAnsweringCount}
            </if>
            <if test="totalHomework != null  and totalHomework != ''">and total_homework = #{totalHomework}</if>
            <if test="submittedTimes != null  and submittedTimes != ''">and submitted_times = #{submittedTimes}</if>
            <if test="submissionRatio != null  and submissionRatio != ''">and submission_ratio = #{submissionRatio}</if>
            <if test="learningProgressAdvice != null  and learningProgressAdvice != ''">and learning_progress_advice =
                #{learningProgressAdvice}
            </if>
            <if test="frequencyOfQuestionsAdvice != null  and frequencyOfQuestionsAdvice != ''">and
                frequency_of_questions_advice = #{frequencyOfQuestionsAdvice}
            </if>
            <if test="learningStrategyAdvice != null  and learningStrategyAdvice != ''">and learning_strategy_advice =
                #{learningStrategyAdvice}
            </if>
            <if test="countLeadingRatio != null  and countLeadingRatio != ''">and count_leading_ratio =
                #{countLeadingRatio}
            </if>
            <if test="countFlatRatio != null  and countFlatRatio != ''">and count_flat_ratio = #{countFlatRatio}</if>
            <if test="countLagRatio != null  and countLagRatio != ''">and count_lag_ratio = #{countLagRatio}</if>
            <if test="countP100 != null  and countP100 != ''">and count_p100 = #{countP100}</if>
            <if test="countP9 != null  and countP9 != ''">and count_p9 = #{countP9}</if>
            <if test="countP6 != null  and countP6 != ''">and count_p6 = #{countP6}</if>
            <if test="lessThanP6NameStr != null  and lessThanP6NameStr != ''">and less_than_p6_name_str =
                #{lessThanP6NameStr}
            </if>
            <if test="quoteAnswerCountP9 != null  and quoteAnswerCountP9 != ''">and quote_answer_count_p9 =
                #{quoteAnswerCountP9}
            </if>
            <if test="quoteAnswerCountP6 != null  and quoteAnswerCountP6 != ''">and quote_answer_count_p6 =
                #{quoteAnswerCountP6}
            </if>
            <if test="quoteAnswerLessThantStr != null  and quoteAnswerLessThantStr != ''">and
                quote_answer_less_thant_str = #{quoteAnswerLessThantStr}
            </if>
            <if test="suggestionsForClassProgress != null  and suggestionsForClassProgress != ''">and
                suggestions_for_class_progress = #{suggestionsForClassProgress}
            </if>
            <if test="suggestionsForClassHomework != null  and suggestionsForClassHomework != ''">and
                suggestions_for_class_homework = #{suggestionsForClassHomework}
            </if>
            <if test="suggestionsForClassInteractions != null  and suggestionsForClassInteractions != ''">and
                suggestions_for_class_interactions = #{suggestionsForClassInteractions}
            </if>
        </where>
    </select>

    <select id="selectClassStudentCourseReportById" parameterType="Long" resultMap="ClassStudentCourseReportResult">
        <include refid="selectClassStudentCourseReportVo"/>
        where id = #{id}
    </select>
    <select id="getSingleCourseAnalysis" resultMap="ClassStudentCourseReportResult">
        <include refid="selectClassStudentCourseReportVo"/>
        WHERE type = #{type} AND course_name = #{courseName} AND course_class_id = #{courseClassId} AND student_id =
        #{studentId}
        ORDER BY create_time DESC
        LIMIT 1
    </select>
    <select id="selectCourseReportInList" resultMap="ClassStudentCourseReportResult">
        SELECT c.*
        FROM s_class_student_course_report c
        JOIN (
        SELECT student_id, MAX(create_time) AS max_create_time
        FROM s_class_student_course_report
        WHERE type = 'student' AND
        student_id IN
        <foreach item="id" collection="collectStudentId" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY student_id
        ) latest
        ON c.student_id = latest.student_id AND c.create_time = latest.max_create_time AND c.type = 'student'
    </select>
    <select id="getClassCourseAnalysis" resultType="com.ruoyi.create.domain.ClassStudentCourseReport">
        <include refid="selectClassStudentCourseReportVo"/>
        WHERE course_class_id = #{courseClassId} AND type = #{type} AND course_name = #{courseName}
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <insert id="insertClassStudentCourseReport" parameterType="com.ruoyi.create.domain.ClassStudentCourseReport"
            useGeneratedKeys="true" keyProperty="id">
        insert into s_class_student_course_report
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null">type,</if>
            <if test="courseClassId != null">course_class_id,</if>
            <if test="courseClassName != null">course_class_name,</if>
            <if test="studentId != null">student_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="courseName != null">course_name,</if>
            <if test="reportDate != null">report_date,</if>
            <if test="totalChapter != null">total_chapter,</if>
            <if test="finishedChapter != null">finished_chapter,</if>
            <if test="completionProgress != null">completion_progress,</if>
            <if test="averageCompletionProgress != null">average_completion_progress,</if>
            <if test="comparison != null">comparison,</if>
            <if test="intelligentQuestionAnsweringCount != null">intelligent_question_answering_count,</if>
            <if test="averageWeeklyQuestionAnsweringCount != null">average_weekly_question_answering_count,</if>
            <if test="classAverageQuestionAnsweringCount != null">class_average_question_answering_count,</if>
            <if test="comparisonWithClassAverageQuestionAnsweringCount != null">
                comparison_with_class_average_question_answering_count,
            </if>
            <if test="totalHomework != null">total_homework,</if>
            <if test="submittedTimes != null">submitted_times,</if>
            <if test="submissionRatio != null">submission_ratio,</if>
            <if test="learningProgressAdvice != null">learning_progress_advice,</if>
            <if test="frequencyOfQuestionsAdvice != null">frequency_of_questions_advice,</if>
            <if test="learningStrategyAdvice != null">learning_strategy_advice,</if>
            <if test="countLeadingRatio != null">count_leading_ratio,</if>
            <if test="countFlatRatio != null">count_flat_ratio,</if>
            <if test="countLagRatio != null">count_lag_ratio,</if>
            <if test="countP100 != null">count_p100,</if>
            <if test="countP9 != null">count_p9,</if>
            <if test="countP6 != null">count_p6,</if>
            <if test="lessThanP6NameStr != null">less_than_p6_name_str,</if>
            <if test="quoteAnswerCountP9 != null">quote_answer_count_p9,</if>
            <if test="quoteAnswerCountP6 != null">quote_answer_count_p6,</if>
            <if test="quoteAnswerLessThantStr != null">quote_answer_less_thant_str,</if>
            <if test="suggestionsForClassProgress != null">suggestions_for_class_progress,</if>
            <if test="suggestionsForClassHomework != null">suggestions_for_class_homework,</if>
            <if test="suggestionsForClassInteractions != null">suggestions_for_class_interactions,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="type != null">#{type},</if>
            <if test="courseClassId != null">#{courseClassId},</if>
            <if test="courseClassName != null">#{courseClassName},</if>
            <if test="studentId != null">#{studentId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="courseName != null">#{courseName},</if>
            <if test="reportDate != null">#{reportDate},</if>
            <if test="totalChapter != null">#{totalChapter},</if>
            <if test="finishedChapter != null">#{finishedChapter},</if>
            <if test="completionProgress != null">#{completionProgress},</if>
            <if test="averageCompletionProgress != null">#{averageCompletionProgress},</if>
            <if test="comparison != null">#{comparison},</if>
            <if test="intelligentQuestionAnsweringCount != null">#{intelligentQuestionAnsweringCount},</if>
            <if test="averageWeeklyQuestionAnsweringCount != null">#{averageWeeklyQuestionAnsweringCount},</if>
            <if test="classAverageQuestionAnsweringCount != null">#{classAverageQuestionAnsweringCount},</if>
            <if test="comparisonWithClassAverageQuestionAnsweringCount != null">
                #{comparisonWithClassAverageQuestionAnsweringCount},
            </if>
            <if test="totalHomework != null">#{totalHomework},</if>
            <if test="submittedTimes != null">#{submittedTimes},</if>
            <if test="submissionRatio != null">#{submissionRatio},</if>
            <if test="learningProgressAdvice != null">#{learningProgressAdvice},</if>
            <if test="frequencyOfQuestionsAdvice != null">#{frequencyOfQuestionsAdvice},</if>
            <if test="learningStrategyAdvice != null">#{learningStrategyAdvice},</if>
            <if test="countLeadingRatio != null">#{countLeadingRatio},</if>
            <if test="countFlatRatio != null">#{countFlatRatio},</if>
            <if test="countLagRatio != null">#{countLagRatio},</if>
            <if test="countP100 != null">#{countP100},</if>
            <if test="countP9 != null">#{countP9},</if>
            <if test="countP6 != null">#{countP6},</if>
            <if test="lessThanP6NameStr != null">#{lessThanP6NameStr},</if>
            <if test="quoteAnswerCountP9 != null">#{quoteAnswerCountP9},</if>
            <if test="quoteAnswerCountP6 != null">#{quoteAnswerCountP6},</if>
            <if test="quoteAnswerLessThantStr != null">#{quoteAnswerLessThantStr},</if>
            <if test="suggestionsForClassProgress != null">#{suggestionsForClassProgress},</if>
            <if test="suggestionsForClassHomework != null">#{suggestionsForClassHomework},</if>
            <if test="suggestionsForClassInteractions != null">#{suggestionsForClassInteractions},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateClassStudentCourseReport" parameterType="com.ruoyi.create.domain.ClassStudentCourseReport">
        update s_class_student_course_report
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">type = #{type},</if>
            <if test="courseClassId != null">course_class_id = #{courseClassId},</if>
            <if test="courseClassName != null">course_class_name = #{courseClassName},</if>
            <if test="studentId != null">student_id = #{studentId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="courseName != null">course_name = #{courseName},</if>
            <if test="reportDate != null">report_date = #{reportDate},</if>
            <if test="totalChapter != null">total_chapter = #{totalChapter},</if>
            <if test="finishedChapter != null">finished_chapter = #{finishedChapter},</if>
            <if test="completionProgress != null">completion_progress = #{completionProgress},</if>
            <if test="averageCompletionProgress != null">average_completion_progress = #{averageCompletionProgress},
            </if>
            <if test="comparison != null">comparison = #{comparison},</if>
            <if test="intelligentQuestionAnsweringCount != null">intelligent_question_answering_count =
                #{intelligentQuestionAnsweringCount},
            </if>
            <if test="averageWeeklyQuestionAnsweringCount != null">average_weekly_question_answering_count =
                #{averageWeeklyQuestionAnsweringCount},
            </if>
            <if test="classAverageQuestionAnsweringCount != null">class_average_question_answering_count =
                #{classAverageQuestionAnsweringCount},
            </if>
            <if test="comparisonWithClassAverageQuestionAnsweringCount != null">
                comparison_with_class_average_question_answering_count =
                #{comparisonWithClassAverageQuestionAnsweringCount},
            </if>
            <if test="totalHomework != null">total_homework = #{totalHomework},</if>
            <if test="submittedTimes != null">submitted_times = #{submittedTimes},</if>
            <if test="submissionRatio != null">submission_ratio = #{submissionRatio},</if>
            <if test="learningProgressAdvice != null">learning_progress_advice = #{learningProgressAdvice},</if>
            <if test="frequencyOfQuestionsAdvice != null">frequency_of_questions_advice =
                #{frequencyOfQuestionsAdvice},
            </if>
            <if test="learningStrategyAdvice != null">learning_strategy_advice = #{learningStrategyAdvice},</if>
            <if test="countLeadingRatio != null">count_leading_ratio = #{countLeadingRatio},</if>
            <if test="countFlatRatio != null">count_flat_ratio = #{countFlatRatio},</if>
            <if test="countLagRatio != null">count_lag_ratio = #{countLagRatio},</if>
            <if test="countP100 != null">count_p100 = #{countP100},</if>
            <if test="countP9 != null">count_p9 = #{countP9},</if>
            <if test="countP6 != null">count_p6 = #{countP6},</if>
            <if test="lessThanP6NameStr != null">less_than_p6_name_str = #{lessThanP6NameStr},</if>
            <if test="quoteAnswerCountP9 != null">quote_answer_count_p9 = #{quoteAnswerCountP9},</if>
            <if test="quoteAnswerCountP6 != null">quote_answer_count_p6 = #{quoteAnswerCountP6},</if>
            <if test="quoteAnswerLessThantStr != null">quote_answer_less_thant_str = #{quoteAnswerLessThantStr},</if>
            <if test="suggestionsForClassProgress != null">suggestions_for_class_progress =
                #{suggestionsForClassProgress},
            </if>
            <if test="suggestionsForClassHomework != null">suggestions_for_class_homework =
                #{suggestionsForClassHomework},
            </if>
            <if test="suggestionsForClassInteractions != null">suggestions_for_class_interactions =
                #{suggestionsForClassInteractions},
            </if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteClassStudentCourseReportById" parameterType="Long">
        DELETE
        FROM s_class_student_course_report
        WHERE id = #{id}
    </delete>

    <delete id="deleteClassStudentCourseReportByIds" parameterType="String">
        delete from s_class_student_course_report where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


</mapper>