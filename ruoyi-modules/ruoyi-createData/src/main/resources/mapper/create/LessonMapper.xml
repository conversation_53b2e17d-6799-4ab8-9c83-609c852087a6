<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.LessonMapper">
    
    <resultMap type="Lesson" id="LessonResult">
        <result property="id"    column="id"    />
        <result property="lessonName"    column="lesson_name"    />
        <result property="textbookName"    column="textbook_name"    />
        <result property="textbookAuthor"    column="textbook_author"    />
        <result property="textbookVersion"    column="textbook_version"    />
        <result property="textbookPublisher"    column="textbook_publisher"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectLessonVo">
        select id, lesson_name, textbook_name, textbook_author, textbook_version, textbook_publisher, remark, create_by, create_time, update_by, update_time from s_lesson
    </sql>

    <select id="selectLessonList" parameterType="Lesson" resultMap="LessonResult">
        <include refid="selectLessonVo"/>
        <where>  
            <if test="lessonName != null  and lessonName != ''"> and lesson_name like concat('%', #{lessonName}, '%')</if>
            <if test="textbookName != null  and textbookName != ''"> and textbook_name like concat('%', #{textbookName}, '%')</if>
            <if test="textbookAuthor != null  and textbookAuthor != ''"> and textbook_author = #{textbookAuthor}</if>
            <if test="textbookVersion != null  and textbookVersion != ''"> and textbook_version = #{textbookVersion}</if>
            <if test="textbookPublisher != null  and textbookPublisher != ''"> and textbook_publisher = #{textbookPublisher}</if>
        </where>
    </select>
    
    <select id="selectLessonById" parameterType="Long" resultMap="LessonResult">
        <include refid="selectLessonVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertLesson" parameterType="Lesson" useGeneratedKeys="true" keyProperty="id">
        insert into s_lesson
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="lessonName != null and lessonName != ''">lesson_name,</if>
            <if test="textbookName != null and textbookName != ''">textbook_name,</if>
            <if test="textbookAuthor != null">textbook_author,</if>
            <if test="textbookVersion != null">textbook_version,</if>
            <if test="textbookPublisher != null">textbook_publisher,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="lessonName != null and lessonName != ''">#{lessonName},</if>
            <if test="textbookName != null and textbookName != ''">#{textbookName},</if>
            <if test="textbookAuthor != null">#{textbookAuthor},</if>
            <if test="textbookVersion != null">#{textbookVersion},</if>
            <if test="textbookPublisher != null">#{textbookPublisher},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateLesson" parameterType="Lesson">
        update s_lesson
        <trim prefix="SET" suffixOverrides=",">
            <if test="lessonName != null and lessonName != ''">lesson_name = #{lessonName},</if>
            <if test="textbookName != null and textbookName != ''">textbook_name = #{textbookName},</if>
            <if test="textbookAuthor != null">textbook_author = #{textbookAuthor},</if>
            <if test="textbookVersion != null">textbook_version = #{textbookVersion},</if>
            <if test="textbookPublisher != null">textbook_publisher = #{textbookPublisher},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLessonById" parameterType="Long">
        delete from s_lesson where id = #{id}
    </delete>

    <delete id="deleteLessonByIds" parameterType="String">
        delete from s_lesson where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>