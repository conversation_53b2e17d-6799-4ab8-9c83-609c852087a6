<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.ExternalCertificationMapper">

    <select id="selectUniverIdByName" parameterType="string" resultType="long">
        select id
        from s_university
        where univer_name = #{univerName}
    </select>
    <select id="selectCollIdByName" parameterType="com.ruoyi.create.domain.ExternalCertification" resultType="long">
        select id
        from s_college_info
        where colle_name = #{colleName} and univer_id=#{univerId}
    </select>
    <select id="selectMajIdByName" parameterType="com.ruoyi.create.domain.ExternalCertification" resultType="long">
        select id
        from s_major_info
        where major_name = #{majorName} and univer_id=#{univerId}
    </select>

    <select id="selectClaIdByName" parameterType="com.ruoyi.create.domain.ExternalCertification" resultType="long">
        select id
        from s_class_info
        where class_name = #{className} and univer_id=#{univerId}
    </select>



    <insert id="insertCla" useGeneratedKeys="true" keyProperty="id" parameterType="com.ruoyi.create.domain.execl.Class">
        insert into s_class_info(class_name,univer_id,colle_id,major_id,create_by,create_time)
        values (#{className},#{univerId},#{colleId},#{majorId},#{createBy},#{createTime})
    </insert>

    <insert id="insertMajor" useGeneratedKeys="true" keyProperty="id" parameterType="com.ruoyi.create.domain.execl.Major">
        insert into s_major_info(major_name,univer_id,colle_id,create_by,create_time)
        values (#{majorName},#{univerId},#{colleId},#{createBy},#{createTime})
    </insert>

    <insert id="insertColl" useGeneratedKeys="true" keyProperty="id" parameterType="com.ruoyi.create.domain.execl.Coll">
        insert into s_college_info(colle_name,univer_id,create_by,create_time)
        values (#{colleName},#{univerId},#{createBy},#{createTime})
    </insert>

    <insert id="insertUniver" useGeneratedKeys="true" keyProperty="id" parameterType="com.ruoyi.create.domain.execl.Univer">
        insert into s_university(univer_name,univer_addr,create_by,create_time)
        values (#{univerName},'主校区',#{createBy},#{createTime})
    </insert>


    <select id="selectStuByStuId" parameterType="string" resultType="long">
        select id
        from s_student_info
        where student_id = #{studentId}
    </select>
    <select id="selectTeaByTeaId" parameterType="string" resultType="long">
        select id
        from s_teacher_info
        where teacher_id = #{teacherId}
    </select>

    <insert id="insertOrUpdateS" parameterType="com.ruoyi.create.domain.ExternalCertification">
        INSERT INTO s_student_info(
        student_id,
        student_name,
        sex,
        univer_id,
        colle_id,
        major_id,
        class_id,
        create_by,
        create_time
        )
        VALUES(
                #{studentId},
                #{name},
                #{sexMark},
                #{univerId},
                #{colleId},
                #{majorId},
                #{classId},
                #{createBy},
                #{createTime}
            )
        ON DUPLICATE KEY UPDATE
        student_id = VALUES(student_id),
        student_name = VALUES(student_name),
        sex = VALUES(sex),
        univer_id = VALUES(univer_id),
        colle_id = VALUES(colle_id),
        major_id = VALUES(major_id),
        class_id = VALUES(class_id),
        create_by = VALUES(create_by),
        create_time = VALUES(create_time)
    </insert>

    <insert id="insertOrUpdateT" parameterType="com.ruoyi.create.domain.ExternalCertification">
        INSERT INTO s_teacher_info(
        teacher_id,
        teacher_name,
        univer_id,
        colle_id,
        create_by,
        create_time
        )
        VALUES(
                #{teacherId},
                #{name},
                #{univerId},
                #{colleId},
                #{createBy},
                #{createTime}
            )
        ON DUPLICATE KEY UPDATE
        teacher_id = VALUES(teacher_id),
        teacher_name = VALUES(teacher_name),
        univer_id = VALUES(univer_id),
        colle_id = VALUES(colle_id),
        create_by = VALUES(create_by),
        create_time = VALUES(create_time)
    </insert>


    <insert id="insertOrUpdateUS" useGeneratedKeys="true" keyProperty="userId"  parameterType="com.ruoyi.create.domain.ExternalCertification">
        INSERT INTO sys_user (
            user_name,
            nick_name,

            user_type,
            sex,
            password,

            status,
            del_flag,
            create_by,
            create_time,

            auth_status,

            university_id,
            college_id,
            major_id,
            class_id,

            student_id
        )
        VALUES
        (
            #{studentId},
            #{name},
            '00',
            #{sexMark},
            '$2a$10$gl0s.mkfM9ZSq84p.J5voeUl1mhrdzrNT3yOuKaGIUrW17PAHYzty',
            '0',
            '0',
            #{createBy},
            #{createTime},

            '2',

            #{univerId},
            #{colleId},
            #{majorId},
            #{classId},
            #{studentId}
        )
            ON DUPLICATE KEY UPDATE

                                 nick_name = VALUES(nick_name),

                                 user_type = VALUES(user_type),

                                 sex = VALUES(sex),
                                 password = VALUES(password),

                                 status = VALUES(status),
                                 del_flag = VALUES(del_flag),

                                 create_by = VALUES(create_by),
                                 create_time = VALUES(create_time),

                                 auth_status = VALUES(auth_status),

                                 university_id = VALUES(university_id),
                                 college_id = VALUES(college_id),
                                 major_id = VALUES(major_id),
                                 class_id = VALUES(class_id),

                                 student_id = VALUES(student_id)
    </insert>

    <insert id="insertOrUpdateUT" useGeneratedKeys="true" keyProperty="userId"  parameterType="com.ruoyi.create.domain.ExternalCertification">
        INSERT INTO sys_user (
            user_name,
            nick_name,

            user_type,
            sex,
            password,

            status,
            del_flag,
            create_by,
            create_time,

            auth_status,

            university_id,
            college_id,

            job_id
        )
        VALUES(
                  #{teacherId},
                  #{name},
                  '00',
                  #{sexMark},
                  '$2a$10$gl0s.mkfM9ZSq84p.J5voeUl1mhrdzrNT3yOuKaGIUrW17PAHYzty',
                  '0',
                  '0',
                  #{createBy},
                  #{createTime},

                  '2',

                  #{univerId},
                  #{colleId},

                  #{teacherId}

              )
            ON DUPLICATE KEY UPDATE

                                 nick_name = VALUES(nick_name),

                                 user_type = VALUES(user_type),
                                 sex = VALUES(sex),
                                 password = VALUES(password),

                                 status = VALUES(status),
                                 del_flag = VALUES(del_flag),

                                 create_by = VALUES(create_by),
                                 create_time = VALUES(create_time),

                                 auth_status = VALUES(auth_status),

                                 university_id = VALUES(university_id),
                                 college_id = VALUES(college_id),

                                 job_id = VALUES(job_id)

    </insert>

    <insert id="insertUserRoleS" parameterType="long">
        insert into sys_user_role(user_id,role_id)
        values(#{userId}, '101')
            ON DUPLICATE KEY UPDATE
                                 user_id = VALUES(user_id),
                                 role_id = VALUES(role_id)
    </insert>
    <insert id="insertUserRoleT" parameterType="long">
        insert into sys_user_role(user_id,role_id)
        values(#{userId},'102')
            ON DUPLICATE KEY UPDATE
                                 user_id = VALUES(user_id),
                                 role_id = VALUES(role_id)
    </insert>
</mapper>
