<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.HomeworkStudentMapper">

    <resultMap type="com.ruoyi.create.domain.HomeworkStudent" id="HomeworkStudentResult">
        <result property="id"    column="id"    />
        <result property="hmId"    column="hm_id"    />
        <result property="userId"    column="user_id"    />
        <result property="collegeId"    column="college_id"    />
        <result property="majorId"    column="major_id"    />
        <result property="classId"    column="class_id"    />
        <result property="className"    column="class_name"    />
        <result property="commitStatus"    column="commit_status"    />
        <result property="commitTime"    column="commit_time"    />
        <result property="cutoffTime"    column="cutoff_time"    />
        <result property="correctStatus"    column="correct_status"    />
        <result property="correctTime"    column="correct_time"    />
        <result property="hmName"    column="hw_name"    />
        <result property="majorName"    column="major_name"    />
        <result property="lessonName"    column="lesson_name"    />
        <result property="startTime"    column="start_time"    />
        <result property="remark"    column="remark"    />
        <result property="publishTime"    column="publish_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectHomeworkStudentVo">
        select id, hm_id, user_id, college_id, major_id, class_id, commit_status, commit_time, cutoff_time,correct_status, correct_time,start_time, remark from s_homework_student
    </sql>

    <select id="selectHomeworkStudentList" parameterType="com.ruoyi.create.domain.HomeworkStudent" resultMap="HomeworkStudentResult">
        SELECT
            stu.id,
            stu.hm_id,
            stu.user_id,
            stu.college_id,
            stu.major_id,
            stu.class_id,
            stu.commit_status,
            stu.commit_time,
            stu.cutoff_time,
            stu.correct_status,
            stu.correct_time,
            stu.start_time,
            stu.remark,
            h.hw_name,
            h.lesson_name,
            h.create_by,
            h.publish_time
        FROM
            s_homework_student stu
        LEFT JOIN s_homework h ON stu.hm_id = h.id
        <where>
            <if test="hmId != null "> and hm_id = #{hmId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="collegeId != null "> and college_id = #{collegeId}</if>
            <if test="majorId != null "> and major_id = #{majorId}</if>
            <if test="classId != null "> and class_id = #{classId}</if>
            <if test="commitStatus != null  and commitStatus != ''"> and commit_status = #{commitStatus}</if>
            <if test="commitTime != null "> and commit_time = #{commitTime}</if>
            <if test="correctStatus != null  and correctStatus != ''"> and correct_status = #{correctStatus}</if>
            <if test="correctTime != null "> and correct_time = #{correctTime}</if>
        </where>
        ORDER BY commit_status ,commit_time DESC
    </select>
    
    <select id="selectHomeworkStudentListCount" resultType="java.lang.String">
        SELECT count(*)
        FROM s_homework_student stu
        LEFT JOIN s_homework h ON stu.hm_id = h.id
        <where>
            <if test="hmId != null "> and hm_id = #{hmId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="collegeId != null "> and college_id = #{collegeId}</if>
            <if test="majorId != null "> and major_id = #{majorId}</if>
            <if test="classId != null "> and class_id = #{classId}</if>
            <if test="commitStatus != null  and commitStatus != ''"> and commit_status = #{commitStatus}</if>
            <if test="commitTime != null "> and commit_time = #{commitTime}</if>
            <if test="correctStatus != null  and correctStatus != ''"> and correct_status = #{correctStatus}</if>
            <if test="correctTime != null "> and correct_time = #{correctTime}</if>
        </where>
    </select>
    
    <select id="selectHomeworkStudentById" parameterType="Long" resultMap="HomeworkStudentResult">
        <include refid="selectHomeworkStudentVo"/>
        where id = #{id}
    </select>
    <select id="selectCorrectHomeworkStudentList" resultType="com.ruoyi.create.domain.HomeworkStudent">
        SELECT
        stu.id,
        stu.hm_id,
        stu.user_id,
        stu.college_id,
        stu.major_id,
        stu.class_id,
        stu.commit_status,
        stu.commit_time,
        stu.cutoff_time,
        stu.correct_status,
        stu.correct_time,
        stu.start_time,
        stu.remark,
        h.hw_name,
        h.lesson_name,
        h.create_by,
        c.class_name,
        m.major_name
        FROM
        s_homework_student stu
        LEFT JOIN s_homework h ON stu.hm_id = h.id
        LEFT JOIN s_class_info c ON stu.class_id = c.id
        LEFT JOIN s_major_info m ON stu.major_id = m.id
        <where>
            <if test="hmId != null "> and hm_id = #{hmId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="collegeId != null "> and college_id = #{collegeId}</if>
            <if test="majorId != null "> and major_id = #{majorId}</if>
            <if test="classId != null "> and class_id = #{classId}</if>
            <if test="className != null "> and class_name = #{className}</if>
            <if test="commitStatus != null  and commitStatus != '' and commitStatus != 3"> and stu.commit_status = #{commitStatus}</if>
            <if test="commitStatus == 3">
                <![CDATA[
            and stu.cutoff_time <= NOW()]]>
            </if>
            <if test="commitTime != null "> and commit_time = #{commitTime}</if>
            <if test="correctStatus != null  and correctStatus != ''"> and correct_status = #{correctStatus}</if>
            <if test="correctTime != null "> and correct_time = #{correctTime}</if>
        </where>
        ORDER BY commit_status ,commit_time DESC


    </select>
    <select id="selectClaByHId" resultType="com.ruoyi.create.domain.HomeworkStudent">
        select shs.*,
        su.univer_name AS univerName,sco.colle_name AS colleName,smi.major_name AS majorName,scci.class_name AS className
        from s_homework_student  shs
        LEFT JOIN s_university su ON  su.id = shs.univer_id
        LEFT JOIN s_college_info sco ON  sco.id = shs.college_id
        left join s_major_info smi on smi.id = shs.major_id
        LEFT JOIN s_class_info scci ON scci.id = shs.class_id
        where shs.hm_id= #{hmId}
        group by shs.class_id
    </select>
    
    <insert id="insertHomeworkStudent" parameterType="com.ruoyi.create.domain.HomeworkStudent" useGeneratedKeys="true" keyProperty="id">
        insert into s_homework_student
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hmId != null">hm_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="collegeId != null">college_id,</if>
            <if test="majorId != null">major_id,</if>
            <if test="classId != null">class_id,</if>
            <if test="commitStatus != null">commit_status,</if>
            <if test="commitTime != null">commit_time,</if>
            <if test="cutoffTime != null">cutoff_time,</if>
            <if test="correctStatus != null">correct_status,</if>
            <if test="correctTime != null">correct_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="hmId != null">#{hmId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="collegeId != null">#{collegeId},</if>
            <if test="majorId != null">#{majorId},</if>
            <if test="classId != null">#{classId},</if>
            <if test="commitStatus != null">#{commitStatus},</if>
            <if test="commitTime != null">#{commitTime},</if>
            <if test="cutoffTime != null">#{cutoffTime},</if>
            <if test="correctStatus != null">#{correctStatus},</if>
            <if test="correctTime != null">#{correctTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateHomeworkStudent" parameterType="com.ruoyi.create.domain.HomeworkStudent">
        update s_homework_student
        <trim prefix="SET" suffixOverrides=",">
            <if test="hmId != null">hm_id = #{hmId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="collegeId != null">college_id = #{collegeId},</if>
            <if test="majorId != null">major_id = #{majorId},</if>
            <if test="classId != null">class_id = #{classId},</if>
            <if test="commitStatus != null">commit_status = #{commitStatus},</if>
            <if test="commitTime != null">commit_time = #{commitTime},</if>
            <if test="cutoffTime != null">cutoff_time = #{cutoffTime},</if>
            <if test="correctStatus != null">correct_status = #{correctStatus},</if>
            <if test="correctTime != null">correct_time = #{correctTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateHomeworkStudentByHmId" parameterType="com.ruoyi.create.domain.HomeworkStudent">
        update s_homework_student
        <trim prefix="SET" suffixOverrides=",">
            <if test="collegeId != null">college_id = #{collegeId},</if>
            <if test="majorId != null">major_id = #{majorId},</if>
            <if test="classId != null">class_id = #{classId},</if>
            <if test="commitStatus != null">commit_status = #{commitStatus},</if>
            <if test="commitTime != null">commit_time = #{commitTime},</if>
            <if test="cutoffTime != null">cutoff_time = #{cutoffTime},</if>
            <if test="correctStatus != null">correct_status = #{correctStatus},</if>
            <if test="correctTime != null">correct_time = #{correctTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where hm_id = #{hmId} And user_id = #{userId}
    </update>
    <update id="updateHomeworkToClearCommitTime">
        update s_homework_student
        set commit_time = null
        where id = #{id}
    </update>
    
    <delete id="deleteHomeworkStudentById" parameterType="Long">
        delete from s_homework_student where id = #{id}
    </delete>

    <delete id="deleteHomeworkStudentByIds" parameterType="String">
        delete from s_homework_student where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
