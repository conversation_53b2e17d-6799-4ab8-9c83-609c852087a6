<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.ApplicationScenarioMapper">

    <resultMap type="com.ruoyi.create.domain.ApplicationScenario" id="ApplicationScenarioResult">
        <result property="id"    column="id"    />
        <result property="applicationName"    column="application_name"    />
        <result property="imagePath"    column="image_path"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="school"    column="school"    />
        <result property="college"    column="college"    />
        <result property="major"    column="major"    />
        <result property="prompt"    column="prompt"    />
    </resultMap>

    <sql id="selectApplicationScenarioVo">
        select id, application_name, image_path, create_by, update_by, create_time, update_time,school,college,major,prompt from application_scenario
    </sql>

    <select id="selectApplicationScenarioList" parameterType="com.ruoyi.create.domain.ApplicationScenario" resultMap="ApplicationScenarioResult">
        <include refid="selectApplicationScenarioVo"/>
        <where>
            <if test="applicationName != null  and applicationName != ''"> and application_name like concat('%', #{applicationName}, '%')</if>
            <if test="imagePath != null  and imagePath != ''"> and image_path = #{imagePath}</if>
            <if test="school != null  and school != ''"> and school = #{school}</if>
            <if test="college != null  and college != ''"> and college = #{college}</if>
            <if test="major != null  and major != ''"> and major = #{major}</if>
            <if test="prompt != null  and prompt != ''"> and prompt like concat('%', #{prompt}, '%')</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="universityId != null"> and university_id = #{universityId}</if>
        </where>
    </select>

    <select id="selectApplicationScenarioById" parameterType="Long" resultMap="ApplicationScenarioResult">
        <include refid="selectApplicationScenarioVo"/>
        where id = #{id}
    </select>

    <insert id="insertApplicationScenario" parameterType="com.ruoyi.create.domain.ApplicationScenario">
        insert into application_scenario
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="applicationName != null">application_name,</if>
            <if test="imagePath != null">image_path,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="school != null">school,</if>
            <if test="college != null">college,</if>
            <if test="major != null">major,</if>
            <if test="prompt != null">prompt,</if>
            <if test="universityId != null"> university_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="applicationName != null">#{applicationName},</if>
            <if test="imagePath != null">#{imagePath},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="school != null">#{school},</if>
            <if test="college != null">#{college},</if>
            <if test="major != null">#{major},</if>
            <if test="prompt != null">#{prompt},</if>
            <if test="universityId != null">   #{universityId}</if>
         </trim>
    </insert>

    <update id="updateApplicationScenario" parameterType="com.ruoyi.create.domain.ApplicationScenario">
        update application_scenario
        <trim prefix="SET" suffixOverrides=",">
            <if test="applicationName != null">application_name = #{applicationName},</if>
            <if test="imagePath != null">image_path = #{imagePath},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="school != null">school = #{school},</if>
            <if test="college != null">college = #{college},</if>
            <if test="major != null">major = #{major},</if>
            <if test="prompt != null">prompt = #{prompt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteApplicationScenarioById" parameterType="Long">
        delete from application_scenario where id = #{id}
    </delete>

    <delete id="deleteApplicationScenarioByIds" parameterType="String">
        delete from application_scenario where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectUserByUserName" resultType="com.ruoyi.create.dto.UserDto" parameterType="string">
        select university_id,college_id,major_id,student_id
        from sys_user
        where user_name=#{username} and del_flag='0'
    </select>

    <select id="selectUserRoleKeyByUserName" resultType="string" parameterType="string">
        SELECT  r.role_key
        FROM sys_user_role ur
                 LEFT JOIN sys_user u ON ur.user_id = u.user_id
                 LEFT JOIN sys_role r ON ur.role_id = r.role_id
        WHERE u.user_name = #{username};
    </select>
    <select id="selectUserNameByJobId" resultType="java.lang.String">
        SELECT su.user_name
        FROM sys_user su
                 JOIN s_ambit_teacher st ON su.job_id = st.teacher_id
        WHERE st.course_name in (
            SELECT course_name FROM s_ambit_teacher WHERE teacher_id = #{jobId}
        )
          AND su.university_id = #{universityId}
    </select>
    <select id="selectMajorListAll" resultType="com.ruoyi.create.domain.execl.Major">
        select * from s_major_info
    </select>
    <select id="selectApplicationScenarioListByUserNames" resultType="com.ruoyi.create.domain.ApplicationScenario">
        <include refid="selectApplicationScenarioVo"/>
        <where>
            create_by in
            <foreach collection="list" item="user" open="(" separator="," close=")">
            #{user}
        </foreach>
        </where>

    </select>
    <select id="selectApplicationScenarioByFileId" resultType="java.lang.Integer">
        select id from application_scenario where fileId=#{fileId}
    </select>
    <select id="selectApplicationScenarioByNameAndMajor" resultType="java.lang.Integer">
        select id from application_scenario where application_name=#{applicationName} and major=#{major}
    </select>


</mapper>
