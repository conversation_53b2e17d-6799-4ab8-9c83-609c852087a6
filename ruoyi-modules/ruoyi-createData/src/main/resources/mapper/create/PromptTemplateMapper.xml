<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.PromptTemplateMapper">
    
    <resultMap type="com.ruoyi.create.domain.PromptTemplate" id="PromptTemplateResult">
        <result property="id"    column="id"    />
        <result property="templatePk"    column="template_pk"    />
        <result property="templateName"    column="template_name"    />
        <result property="templateContent"    column="template_content"    />
        <result property="templateVariables"    column="template_variables"    />
        <result property="variableIdentifier"    column="variable_identifier"    />
        <result property="negativeTemplateContent"    column="negative_template_content"    />
        <result property="negativeTemplateVariables"    column="negative_template_variables"    />
        <result property="creatorName"    column="creator_name"    />
        <result property="templateType"    column="template_type"    />
        <result property="sceneType"    column="scene_type"    />
        <result property="frameworkType"    column="framework_type"    />
        <result property="hyperParametersStatus"    column="hyper_parameters_status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPromptTemplateVo">
        select id, template_pk, template_name, template_content, template_variables, variable_identifier, negative_template_content, negative_template_variables, creator_name, template_type, scene_type, framework_type, hyper_parameters_status, create_by, create_time, update_by, update_time from s_prompt_template
    </sql>

    <select id="selectPromptTemplateList" parameterType="com.ruoyi.create.domain.PromptTemplate" resultMap="PromptTemplateResult">
        <include refid="selectPromptTemplateVo"/>
        <where>  
            <if test="templatePk != null  and templatePk != ''"> and template_pk = #{templatePk}</if>
            <if test="templateName != null  and templateName != ''"> and template_name like concat('%', #{templateName}, '%')</if>
            <if test="templateContent != null  and templateContent != ''"> and template_content = #{templateContent}</if>
            <if test="templateVariables != null  and templateVariables != ''"> and template_variables = #{templateVariables}</if>
            <if test="variableIdentifier != null  and variableIdentifier != ''"> and variable_identifier = #{variableIdentifier}</if>
            <if test="negativeTemplateContent != null  and negativeTemplateContent != ''"> and negative_template_content = #{negativeTemplateContent}</if>
            <if test="negativeTemplateVariables != null  and negativeTemplateVariables != ''"> and negative_template_variables = #{negativeTemplateVariables}</if>
            <if test="creatorName != null  and creatorName != ''"> and creator_name like concat('%', #{creatorName}, '%')</if>
            <if test="sceneType != null "> and scene_type = #{sceneType}</if>
            <if test="frameworkType != null "> and framework_type = #{frameworkType}</if>
            <if test="hyperParametersStatus != null "> and hyper_parameters_status = #{hyperParametersStatus}</if>
            <if test="templateTypes != null  and templateTypes.size()>0 ">
                and (
                <foreach item="templateType" collection="templateTypes" open="" separator=" OR " close="">
                    <choose>
                        <when test="templateType == 'owned'">
                            template_type = 'owned' AND create_by = #{createBy}
                        </when>
                        <otherwise>
                            template_type = #{templateType}
                        </otherwise>
                    </choose>
                </foreach>
                )
            </if>
            <if test="code != null  and code != '' and code ==1"> and template_type in ('preset','communal') or (template_type = 'owned' and create_by = #{createBy})</if>
            <if test="code != null  and code != '' and code ==2"> and template_type ='communal' or (template_type = 'owned' and create_by = #{createBy}) </if>


        </where>
    </select>
    
    <select id="selectPromptTemplateById" parameterType="Long" resultMap="PromptTemplateResult">
        <include refid="selectPromptTemplateVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertPromptTemplate" parameterType="com.ruoyi.create.domain.PromptTemplate" useGeneratedKeys="true" keyProperty="id">
        insert into s_prompt_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="templatePk != null">template_pk,</if>
            <if test="templateName != null">template_name,</if>
            <if test="templateContent != null">template_content,</if>
            <if test="templateVariables != null">template_variables,</if>
            <if test="variableIdentifier != null">variable_identifier,</if>
            <if test="negativeTemplateContent != null">negative_template_content,</if>
            <if test="negativeTemplateVariables != null">negative_template_variables,</if>
            <if test="creatorName != null">creator_name,</if>
            <if test="templateType != null">template_type,</if>
            <if test="sceneType != null">scene_type,</if>
            <if test="frameworkType != null">framework_type,</if>
            <if test="hyperParametersStatus != null">hyper_parameters_status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="templatePk != null">#{templatePk},</if>
            <if test="templateName != null">#{templateName},</if>
            <if test="templateContent != null">#{templateContent},</if>
            <if test="templateVariables != null">#{templateVariables},</if>
            <if test="variableIdentifier != null">#{variableIdentifier},</if>
            <if test="negativeTemplateContent != null">#{negativeTemplateContent},</if>
            <if test="negativeTemplateVariables != null">#{negativeTemplateVariables},</if>
            <if test="creatorName != null">#{creatorName},</if>
            <if test="templateType != null">#{templateType},</if>
            <if test="sceneType != null">#{sceneType},</if>
            <if test="frameworkType != null">#{frameworkType},</if>
            <if test="hyperParametersStatus != null">#{hyperParametersStatus},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updatePromptTemplate" parameterType="com.ruoyi.create.domain.PromptTemplate">
        update s_prompt_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="templatePk != null">template_pk = #{templatePk},</if>
            <if test="templateName != null">template_name = #{templateName},</if>
            <if test="templateContent != null">template_content = #{templateContent},</if>
            <if test="templateVariables != null">template_variables = #{templateVariables},</if>
            <if test="variableIdentifier != null">variable_identifier = #{variableIdentifier},</if>
            <if test="negativeTemplateContent != null">negative_template_content = #{negativeTemplateContent},</if>
            <if test="negativeTemplateVariables != null">negative_template_variables = #{negativeTemplateVariables},</if>
            <if test="creatorName != null">creator_name = #{creatorName},</if>
            <if test="templateType != null">template_type = #{templateType},</if>
            <if test="sceneType != null">scene_type = #{sceneType},</if>
            <if test="frameworkType != null">framework_type = #{frameworkType},</if>
            <if test="hyperParametersStatus != null">hyper_parameters_status = #{hyperParametersStatus},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePromptTemplateById" parameterType="Long">
        delete from s_prompt_template where id = #{id}
    </delete>

    <delete id="deletePromptTemplateByIds" parameterType="String">
        delete from s_prompt_template where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectPromptTemplateByIds" parameterType="String" resultMap="PromptTemplateResult">
        <include refid="selectPromptTemplateVo"/> where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectPromptAll" parameterType="String"  resultMap="PromptTemplateResult">
        <include refid="selectPromptTemplateVo"/>
        where template_type ='communal' or (template_type = 'owned' and create_by = #{username})
    </select>
</mapper>