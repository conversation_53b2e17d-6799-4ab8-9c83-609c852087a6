<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.StudentPromotionMapper">

    <resultMap type="StudentPromotion" id="StudentPromotionResult">
        <result property="id"    column="id"    />
        <result property="presentationId"    column="presentation_id"    />
        <result property="platfigureId"    column="platfigure_id"    />
        <result property="studentId"    column="student_id"    />
        <result property="voiceId"    column="voice_id"    />
        <result property="status"    column="status"    />
        <result property="progress"    column="progress"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectStudentPromotionVo">
        select id, presentation_id, platfigure_id, student_id, voice_id, status, progress, create_time, update_time from s_student_promotion
    </sql>

    <select id="selectStudentPromotionList" parameterType="StudentPromotion" resultMap="StudentPromotionResult">
        <include refid="selectStudentPromotionVo"/>
        <where>
            <if test="presentationId != null "> and presentation_id = #{presentationId}</if>
            <if test="platfigureId != null "> and platfigure_id = #{platfigureId}</if>
            <if test="studentId != null "> and student_id = #{studentId}</if>
            <if test="voiceId != null "> and voice_id = #{voiceId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="progress != null  and progress != ''"> and progress = #{progress}</if>
        </where>
    </select>

    <select id="selectStudentPromotionById" parameterType="Long" resultMap="StudentPromotionResult">
        <include refid="selectStudentPromotionVo"/>
        where student_id = #{studentId}
    </select>
    <select id="selectStudentPromotionByStudentId" resultType="com.ruoyi.create.domain.StudentPromotionVO">
        select  sp.course,
                sp.presentation_name,
                sp.create_user,
                stp.platfigure_id,
                stp.status,
                stp.create_time,
                stp.presentation_id,
                stsr.progress,
                sti.teacher_name as createUserName
        from s_student_promotion stp
        left join s_presentation sp on stp.presentation_id = sp.presentation_id COLLATE utf8mb4_general_ci
        left join s_student_study_record stsr on stsr.create_by = stp.student_id and stp.presentation_id = stsr.presentation_id COLLATE utf8mb4_general_ci
        left join sys_user su on sp.create_user = su.user_name  COLLATE utf8mb4_general_ci
        inner join s_teacher_info sti on su.job_id = sti.teacher_id  COLLATE utf8mb4_general_ci
        <where>
            1=1
            <if test="course != null and course != ''"> and sp.course like concat('%', #{course}, '%')</if>
            <if test="createUserName != null and createUserName != ''"> and sti.teacher_name like concat('%', #{createUserName}, '%')</if>
            and stp.student_id = #{studentId}
        </where>
    </select>


    <insert id="insertStudentPromotion" parameterType="StudentPromotion" useGeneratedKeys="true" keyProperty="id">
        insert into s_student_promotion
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="presentationId != null">presentation_id,</if>
            <if test="platfigureId != null">platfigure_id,</if>
            <if test="studentId != null">student_id,</if>
            <if test="voiceId != null">voice_id,</if>
            <if test="status != null">status,</if>
            <if test="progress != null">progress,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="presentationId != null">#{presentationId},</if>
            <if test="platfigureId != null">#{platfigureId},</if>
            <if test="studentId != null">#{studentId},</if>
            <if test="voiceId != null">#{voiceId},</if>
            <if test="status != null">#{status},</if>
            <if test="progress != null">#{progress},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>


    <update id="updateStudentPromotion" parameterType="StudentPromotion">
        update s_student_promotion
        <trim prefix="SET" suffixOverrides=",">
            <if test="presentationId != null">presentation_id = #{presentationId},</if>
            <if test="platfigureId != null">platfigure_id = #{platfigureId},</if>
            <if test="voiceId != null">voice_id = #{voiceId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="progress != null">progress = #{progress},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where student_id = #{studentId}
    </update>


    <delete id="deleteStudentPromotionById" parameterType="Long">
        delete from s_student_promotion where id = #{id}
    </delete>

    <delete id="deleteStudentPromotionByIds" parameterType="String">
        delete from s_student_promotion where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
