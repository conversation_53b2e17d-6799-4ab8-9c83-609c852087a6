<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.SImageMapper">
    
    <resultMap type="com.ruoyi.create.domain.SImage" id="SImageResult">
        <result property="id"    column="id"    />
        <result property="imageName"    column="image_name"    />
        <result property="imageFlag"    column="image_flag"    />
        <result property="imageOrder"    column="image_order"    />
        <result property="fileId"    column="file_id"    />
        <result property="filePath"    column="file_path"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSImageVo">
        select id, image_name, image_flag, image_order, file_id, create_by, create_time, update_by, update_time from s_image
    </sql>

    <select id="selectSImageList" parameterType="com.ruoyi.create.domain.SImage" resultMap="SImageResult">
        SELECT
            s.id,
            s.image_name,
            s.image_flag,
            s.image_order,
            s.file_id,
            s.create_by,
            s.create_time,
            s.update_by,
            s.update_time,
            f.file_path
        FROM
            s_image s
        LEFT JOIN sys_file_info f ON s.file_id = f.file_object_name
        <where>  
            <if test="imageName != null  and imageName != ''"> and s.image_name like concat('%', #{imageName}, '%')</if>
            <if test="imageFlag != null  and imageFlag != ''"> and s.image_flag = #{imageFlag}</if>
            <if test="imageOrder != null  and imageOrder != ''"> and s.image_order = #{imageOrder}</if>
            <if test="fileId != null  and fileId != ''"> and s.file_id = #{fileId}</if>
        </where>
    </select>
    
    <select id="selectSImageById" parameterType="Long" resultMap="SImageResult">
        SELECT
            s.id,
            s.image_name,
            s.image_flag,
            s.image_order,
            s.file_id,
            s.create_by,
            s.create_time,
            s.update_by,
            s.update_time,
            f.file_path
        FROM
            s_image s
        LEFT JOIN sys_file_info f ON s.file_id = f.file_object_name
        where s.id = #{id}
    </select>
    <select id="selectImageAll" resultMap="SImageResult">
        SELECT
            s.id,
            s.image_name,
            s.image_flag,
            s.image_order,
            s.file_id,
            s.create_by,
            s.create_time,
            s.update_by,
            s.update_time,
            f.file_path
        FROM
            s_image s
        LEFT JOIN sys_file_info f ON s.file_id = f.file_object_name
    </select>

    <insert id="insertSImage" parameterType="com.ruoyi.create.domain.SImage" useGeneratedKeys="true" keyProperty="id">
        insert into s_image
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="imageName != null">image_name,</if>
            <if test="imageFlag != null">image_flag,</if>
            <if test="imageOrder != null">image_order,</if>
            <if test="fileId != null">file_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="imageName != null">#{imageName},</if>
            <if test="imageFlag != null">#{imageFlag},</if>
            <if test="imageOrder != null">#{imageOrder},</if>
            <if test="fileId != null">#{fileId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSImage" parameterType="com.ruoyi.create.domain.SImage">
        update s_image
        <trim prefix="SET" suffixOverrides=",">
            <if test="imageName != null">image_name = #{imageName},</if>
            <if test="imageFlag != null">image_flag = #{imageFlag},</if>
            <if test="imageOrder != null">image_order = #{imageOrder},</if>
            <if test="fileId != null">file_id = #{fileId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSImageById" parameterType="Long">
        delete from s_image where id = #{id}
    </delete>

    <delete id="deleteSImageByIds" parameterType="String">
        delete from s_image where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>