<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.CourseCaseMapper">
    
    <resultMap type="com.ruoyi.create.domain.CourseCase" id="CourseCaseResult">
        <result property="id"    column="id"    />
        <result property="caseName"    column="case_name"    />
        <result property="caseSynopsis"    column="case_synopsis"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="courseName"    column="course_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="filePath"    column="file_path"    />
    </resultMap>

    <sql id="selectCourseCaseVo">
        select id, case_name, case_synopsis, teacher_id, course_name, create_by, create_time, update_by, update_time from s_course_case
    </sql>

    <select id="selectCourseCaseList" parameterType="com.ruoyi.create.domain.CourseCase" resultMap="CourseCaseResult">
        select t1.id, t1.case_name, t1.case_synopsis, t1.teacher_id, t1.course_name, t1.create_by, t1.create_time, t1.update_by, t1.update_time, t2.file_path from s_course_case t1
        left join sys_file_info t2 on t1.id = t2.busi_id
        <where>  
            <if test="caseName != null  and caseName != ''"> and t1.case_name = #{caseName}</if>
            <if test="caseSynopsis != null  and caseSynopsis != ''"> and t1.case_synopsis = #{caseSynopsis}</if>
            <if test="teacherId != null  and teacherId != ''"> and t1.teacher_id = #{teacherId}</if>
            <if test="courseName != null  and courseName != ''"> and t1.course_name like concat('%', #{courseName}, '%')</if>
        </where>
    </select>
    
    <select id="selectCourseCaseById" parameterType="Long" resultMap="CourseCaseResult">
        <include refid="selectCourseCaseVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCourseCase" parameterType="com.ruoyi.create.domain.CourseCase">
        insert into s_course_case
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="caseName != null">case_name,</if>
            <if test="caseSynopsis != null">case_synopsis,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="courseName != null">course_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="caseName != null">#{caseName},</if>
            <if test="caseSynopsis != null">#{caseSynopsis},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="courseName != null">#{courseName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCourseCase" parameterType="com.ruoyi.create.domain.CourseCase">
        update s_course_case
        <trim prefix="SET" suffixOverrides=",">
            <if test="caseName != null">case_name = #{caseName},</if>
            <if test="caseSynopsis != null">case_synopsis = #{caseSynopsis},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="courseName != null">course_name = #{courseName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCourseCaseById" parameterType="Long">
        delete from s_course_case where id = #{id}
    </delete>

    <delete id="deleteCourseCaseByIds" parameterType="String">
        delete from s_course_case where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>