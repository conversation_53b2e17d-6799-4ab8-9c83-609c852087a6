<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.HomeworkMapper">

    <resultMap type="com.ruoyi.create.domain.Homework" id="HomeworkResult">
        <result property="id"    column="id"    />
        <result property="hwName"    column="hw_name"    />
        <result property="lessonId"    column="lesson_id"    />
        <result property="lessonName"    column="lesson_name"    />
        <result property="elseDec"    column="else_dec"    />
        <result property="homework"    column="homework"    />
        <result property="publishStatus"    column="publish_status"    />
        <result property="publishTime"    column="publish_time"    />
        <result property="cutoffTime"    column="cutoff_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectHomeworkVo">
        select id, hw_name, lesson_id, lesson_name, else_dec, homework, publish_status, publish_time, create_by, create_time, update_by, update_time from s_homework
    </sql>

    <select id="selectHomeworkList" parameterType="com.ruoyi.create.domain.Homework" resultMap="HomeworkResult">
        <include refid="selectHomeworkVo"/>
        <where>
            <if test="hwName != null  and hwName != ''"> and hw_name like concat('%', #{hwName}, '%')</if>
            <if test="lessonId != null "> and lesson_id = #{lessonId}</if>
            <if test="lessonName != null  and lessonName != ''"> and lesson_name like concat('%', #{lessonName}, '%')</if>
            <if test="elseDec != null  and elseDec != ''"> and else_dec = #{elseDec}</if>
            <if test="homework != null  and homework != ''"> and homework = #{homework}</if>
            <if test="publishStatus != null  and publishStatus != ''"> and publish_status = #{publishStatus}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="universityId != null "> and university_id = #{universityId}</if>
        </where>
    </select>

    <select id="selectHomeworkById" parameterType="Long" resultMap="HomeworkResult">
        <include refid="selectHomeworkVo"/>
        where id = #{id}
    </select>

    <insert id="insertHomework" parameterType="com.ruoyi.create.domain.Homework">
        insert into s_homework
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="hwName != null and hwName != ''">hw_name,</if>
            <if test="lessonId != null">lesson_id,</if>
            <if test="lessonName != null and lessonName != ''">lesson_name,</if>
            <if test="elseDec != null">else_dec,</if>
            <if test="homework != null">homework,</if>
            <if test="publishStatus != null">publish_status,</if>
            <if test="publishTime != null">publish_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="hwName != null and hwName != ''">#{hwName},</if>
            <if test="lessonId != null">#{lessonId},</if>
            <if test="lessonName != null and lessonName != ''">#{lessonName},</if>
            <if test="elseDec != null">#{elseDec},</if>
            <if test="homework != null">#{homework},</if>
            <if test="publishStatus != null">#{publishStatus},</if>
            <if test="publishTime != null">#{publishTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateHomework" parameterType="com.ruoyi.create.domain.Homework">
        update s_homework
        <trim prefix="SET" suffixOverrides=",">
            <if test="hwName != null and hwName != ''">hw_name = #{hwName},</if>
            <if test="lessonId != null">lesson_id = #{lessonId},</if>
            <if test="lessonName != null and lessonName != ''">lesson_name = #{lessonName},</if>
            <if test="elseDec != null">else_dec = #{elseDec},</if>
            <if test="homework != null">homework = #{homework},</if>
            <if test="publishStatus != null">publish_status = #{publishStatus},</if>
            <if test="publishTime != null">publish_time = #{publishTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeworkById" parameterType="Long">
        delete from s_homework where id = #{id}
    </delete>

    <delete id="deleteHomeworkByIds" parameterType="String">
        delete from s_homework where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
