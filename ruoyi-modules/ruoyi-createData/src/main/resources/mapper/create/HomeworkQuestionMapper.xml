<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.HomeworkQuestionMapper">

    <resultMap type="com.ruoyi.create.domain.HomeworkQuestion" id="HomeworkQuestionResult">
        <result property="id"    column="id"    />
        <result property="hmId"    column="hm_id"    />
        <result property="questionType"    column="question_type"    />
        <result property="questionOrder"    column="question_order"    />
        <result property="question"    column="question"    />
        <result property="correctAnswer"    column="correct_answer"    />
    </resultMap>

    <sql id="selectHomeworkQuestionVo">
        select id, hm_id, question_type, question_order, question, correct_answer from s_homework_question
    </sql>

    <select id="selectHomeworkQuestionList" parameterType="com.ruoyi.create.domain.HomeworkQuestion" resultMap="HomeworkQuestionResult">
        <include refid="selectHomeworkQuestionVo"/>
        <where>
            <if test="hmId != null "> and hm_id = #{hmId}</if>
            <if test="questionType != null  and questionType != ''"> and question_type = #{questionType}</if>
            <if test="questionOrder != null "> and question_order = #{questionOrder}</if>
            <if test="question != null  and question != ''"> and question = #{question}</if>
            <if test="correctAnswer != null  and correctAnswer != ''"> and correct_answer = #{correctAnswer}</if>
        </where>
    </select>

    <select id="selectHomeworkQuestionById" parameterType="Long" resultMap="HomeworkQuestionResult">
        <include refid="selectHomeworkQuestionVo"/>
        where id = #{id}
    </select>

    <insert id="insertHomeworkQuestion" parameterType="com.ruoyi.create.domain.HomeworkQuestion">
        insert into s_homework_question
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="hmId != null">hm_id,</if>
            <if test="questionType != null and questionType != ''">question_type,</if>
            <if test="questionOrder != null">question_order,</if>
            <if test="question != null and question != ''">question,</if>
            <if test="correctAnswer != null">correct_answer,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="hmId != null">#{hmId},</if>
            <if test="questionType != null and questionType != ''">#{questionType},</if>
            <if test="questionOrder != null">#{questionOrder},</if>
            <if test="question != null and question != ''">#{question},</if>
            <if test="correctAnswer != null">#{correctAnswer},</if>
         </trim>
    </insert>

    <update id="updateHomeworkQuestion" parameterType="com.ruoyi.create.domain.HomeworkQuestion">
        update s_homework_question
        <trim prefix="SET" suffixOverrides=",">
            <if test="hmId != null">hm_id = #{hmId},</if>
            <if test="questionType != null and questionType != ''">question_type = #{questionType},</if>
            <if test="questionOrder != null">question_order = #{questionOrder},</if>
            <if test="question != null and question != ''">question = #{question},</if>
            <if test="correctAnswer != null">correct_answer = #{correctAnswer},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeworkQuestionById" parameterType="Long">
        delete from s_homework_question where id = #{id}
    </delete>

    <delete id="deleteHomeworkQuestionByIds" parameterType="String">
        delete from s_homework_question where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
