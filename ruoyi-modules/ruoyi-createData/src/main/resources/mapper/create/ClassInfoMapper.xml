<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.ClassInfoMapper">
    
    <resultMap type="com.ruoyi.create.domain.ClassInfo" id="ClassInfoResult">
        <result property="id"    column="id"    />
        <result property="className"    column="class_name"    />
        <result property="univerId"    column="univer_id"    />
        <result property="colleId"    column="colle_id"    />
        <result property="majorId"    column="major_id"    />
        <result property="majorName"    column="major_name"    />
        <result property="classTeacher"    column="class_teacher"    />
        <result property="phone"    column="phone"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectClassInfoVo">
        select id, class_name, univer_id, colle_id, major_id, class_teacher, phone, create_by, create_time, update_by, update_time from s_class_info
    </sql>

    <select id="selectClassInfoList" parameterType="com.ruoyi.create.domain.ClassInfo" resultMap="ClassInfoResult">
        <include refid="selectClassInfoVo"/>
        <where>  
            <if test="className != null  and className != ''"> and class_name like concat('%', #{className}, '%')</if>
            <if test="univerId != null "> and univer_id = #{univerId}</if>
            <if test="colleId != null "> and colle_id = #{colleId}</if>
            <if test="majorId != null "> and major_id = #{majorId}</if>
            <if test="classTeacher != null  and classTeacher != ''"> and class_teacher = #{classTeacher}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
        </where>
    </select>
    
    <select id="selectClassInfoById" parameterType="Long" resultMap="ClassInfoResult">
        <include refid="selectClassInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectClassINfoByHmId" resultMap="ClassInfoResult">
        SELECT
            c.id,
            c.class_name,
            c.univer_id,
            c.colle_id,
            c.major_id,
            c.class_teacher,
            c.phone,
            c.create_by,
            c.create_time,
            c.update_by,
            c.update_time,
            m.major_name
        FROM
            s_homework_student stu LEFT JOIN s_class_info c ON c.id =stu.class_id
        LEFT JOIN s_major_info m ON c.major_id =m.id
        where stu.hm_id = #{hmId}
        GROUP BY class_id
    </select>

    <insert id="insertClassInfo" parameterType="com.ruoyi.create.domain.ClassInfo" useGeneratedKeys="true" keyProperty="id">
        insert into s_class_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="className != null and className != ''">class_name,</if>
            <if test="univerId != null">univer_id,</if>
            <if test="colleId != null">colle_id,</if>
            <if test="majorId != null">major_id,</if>
            <if test="classTeacher != null">class_teacher,</if>
            <if test="phone != null">phone,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="className != null and className != ''">#{className},</if>
            <if test="univerId != null">#{univerId},</if>
            <if test="colleId != null">#{colleId},</if>
            <if test="majorId != null">#{majorId},</if>
            <if test="classTeacher != null">#{classTeacher},</if>
            <if test="phone != null">#{phone},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateClassInfo" parameterType="com.ruoyi.create.domain.ClassInfo">
        update s_class_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="className != null and className != ''">class_name = #{className},</if>
            <if test="univerId != null">univer_id = #{univerId},</if>
            <if test="colleId != null">colle_id = #{colleId},</if>
            <if test="majorId != null">major_id = #{majorId},</if>
            <if test="classTeacher != null">class_teacher = #{classTeacher},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteClassInfoById" parameterType="Long">
        delete from s_class_info where id = #{id}
    </delete>

    <delete id="deleteClassInfoByIds" parameterType="String">
        delete from s_class_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>