<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.HomeworkQuestionReqMapper">

    <resultMap type="com.ruoyi.create.domain.HomeworkQuestionReq" id="HomeworkQuestionReqResult">
        <result property="id"    column="id"    />
        <result property="hmId"    column="hm_id"    />
        <result property="questionType"    column="question_type"    />
        <result property="count"    column="count"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectHomeworkQuestionReqVo">
        select id, hm_id, question_type, count, create_by, create_time, update_by, update_time from s_homework_question_req
    </sql>

    <select id="selectHomeworkQuestionReqList" parameterType="com.ruoyi.create.domain.HomeworkQuestionReq" resultMap="HomeworkQuestionReqResult">
        <include refid="selectHomeworkQuestionReqVo"/>
        <where>
            <if test="hmId != null "> and hm_id = #{hmId}</if>
            <if test="questionType != null  and questionType != ''"> and question_type = #{questionType}</if>
            <if test="count != null "> and count = #{count}</if>
        </where>
    </select>

    <select id="selectHomeworkQuestionReqById" parameterType="Long" resultMap="HomeworkQuestionReqResult">
        <include refid="selectHomeworkQuestionReqVo"/>
        where id = #{id}
    </select>

    <insert id="insertHomeworkQuestionReq" parameterType="com.ruoyi.create.domain.HomeworkQuestionReq">
        insert into s_homework_question_req
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="hmId != null">hm_id,</if>
            <if test="questionType != null and questionType != ''">question_type,</if>
            <if test="count != null">count,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="hmId != null">#{hmId},</if>
            <if test="questionType != null and questionType != ''">#{questionType},</if>
            <if test="count != null">#{count},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateHomeworkQuestionReq" parameterType="com.ruoyi.create.domain.HomeworkQuestionReq">
        update s_homework_question_req
        <trim prefix="SET" suffixOverrides=",">
            <if test="hmId != null">hm_id = #{hmId},</if>
            <if test="questionType != null and questionType != ''">question_type = #{questionType},</if>
            <if test="count != null">count = #{count},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeworkQuestionReqById" parameterType="Long">
        delete from s_homework_question_req where id = #{id}
    </delete>

    <delete id="deleteHomeworkQuestionReqByIds" parameterType="String">
        delete from s_homework_question_req where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
