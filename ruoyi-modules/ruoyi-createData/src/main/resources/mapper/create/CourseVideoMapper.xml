<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.CourseVideoMapper">
    
    <resultMap type="com.ruoyi.create.domain.CourseVideo" id="CourseVideoResult">
        <result property="id"    column="id"    />
        <result property="videoName"    column="video_name"    />
        <result property="videoSynopsis"    column="video_synopsis"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="courseName"    column="course_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="filePath"    column="file_path"    />
    </resultMap>

    <sql id="selectCourseVideoVo">
        select id, video_name, video_synopsis, teacher_id, course_name, create_by, create_time, update_by, update_time from s_course_video
    </sql>

    <select id="selectCourseVideoList" parameterType="com.ruoyi.create.domain.CourseVideo" resultMap="CourseVideoResult">
        select t1.id, t1.video_name, t1.video_synopsis, t1.teacher_id, t1.course_name, t1.create_by, t1.create_time, t1.update_by, t1.update_time, t2.file_path from s_course_video t1
        left join sys_file_info t2 on t1.id = t2.busi_id
        <where>  
            <if test="videoName != null  and videoName != ''"> and t1.video_name = #{videoName}</if>
            <if test="videoSynopsis != null  and videoSynopsis != ''"> and t1.video_synopsis = #{videoSynopsis}</if>
            <if test="teacherId != null  and teacherId != ''"> and t1.teacher_id = #{teacherId}</if>
            <if test="courseName != null  and courseName != ''"> and t1.course_name like concat('%', #{courseName}, '%')</if>
        </where>
    </select>
    
    <select id="selectCourseVideoById" parameterType="Long" resultMap="CourseVideoResult">
        <include refid="selectCourseVideoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCourseVideo" parameterType="com.ruoyi.create.domain.CourseVideo">
        insert into s_course_video
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="videoName != null">video_name,</if>
            <if test="videoSynopsis != null">video_synopsis,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="courseName != null">course_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="videoName != null">#{videoName},</if>
            <if test="videoSynopsis != null">#{videoSynopsis},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="courseName != null">#{courseName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCourseVideo" parameterType="com.ruoyi.create.domain.CourseVideo">
        update s_course_video
        <trim prefix="SET" suffixOverrides=",">
            <if test="videoName != null">video_name = #{videoName},</if>
            <if test="videoSynopsis != null">video_synopsis = #{videoSynopsis},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="courseName != null">course_name = #{courseName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCourseVideoById" parameterType="Long">
        delete from s_course_video where id = #{id}
    </delete>

    <delete id="deleteCourseVideoByIds" parameterType="String">
        delete from s_course_video where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>