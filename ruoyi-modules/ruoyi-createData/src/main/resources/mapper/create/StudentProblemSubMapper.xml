<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.StudentProblemSubMapper">

    <resultMap type="com.ruoyi.create.domain.StudentProblemSub" id="StudentProblemSubResult">
        <result property="id"    column="id"    />
        <result property="problemId"    column="problem_id"    />
        <result property="studentId"    column="student_id"    />
        <result property="subAnwer"    column="sub_anwer"    />
        <result property="createTime"    column="create_time"    />
        <result property="remark"    column="remark"    />
        <result property="isRight"    column="is_right"    />
    </resultMap>

    <sql id="selectStudentProblemSubVo">
        select id, problem_id, student_id, sub_anwer, create_time, remark, is_right from s_student_problem_sub
    </sql>

    <select id="selectStudentProblemSubList" parameterType="com.ruoyi.create.domain.StudentProblemSub" resultMap="StudentProblemSubResult">
        <include refid="selectStudentProblemSubVo"/>
        <where>
            <if test="problemId != null  and problemId != ''"> and problem_id = #{problemId}</if>
            <if test="studentId != null  and studentId != ''"> and student_id = #{studentId}</if>
            <if test="subAnwer != null  and subAnwer != ''"> and sub_anwer = #{subAnwer}</if>
            <if test="isRight != null "> and is_right = #{isRight}</if>
        </where>
    </select>

    <select id="selectStudentProblemSubById" parameterType="Long" resultMap="StudentProblemSubResult">
        <include refid="selectStudentProblemSubVo"/>
        where id = #{id}
    </select>

    <insert id="insertStudentProblemSub" parameterType="com.ruoyi.create.domain.StudentProblemSub">
        insert into s_student_problem_sub
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="problemId != null and problemId != ''">problem_id,</if>
            <if test="studentId != null and studentId != ''">student_id,</if>
            <if test="subAnwer != null">sub_anwer,</if>
            <if test="createTime != null">create_time,</if>
            <if test="remark != null">remark,</if>
            <if test="isRight != null">is_right,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="problemId != null and problemId != ''">#{problemId},</if>
            <if test="studentId != null and studentId != ''">#{studentId},</if>
            <if test="subAnwer != null">#{subAnwer},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="isRight != null">#{isRight},</if>
        </trim>
    </insert>

    <update id="updateStudentProblemSub" parameterType="com.ruoyi.create.domain.StudentProblemSub">
        update s_student_problem_sub
        <trim prefix="SET" suffixOverrides=",">
            <if test="problemId != null and problemId != ''">problem_id = #{problemId},</if>
            <if test="studentId != null and studentId != ''">student_id = #{studentId},</if>
            <if test="subAnwer != null">sub_anwer = #{subAnwer},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="isRight != null">is_right = #{isRight},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteStudentProblemSubById" parameterType="Long">
        delete from s_student_problem_sub where id = #{id}
    </delete>

    <delete id="deleteStudentProblemSubByIds" parameterType="String">
        delete from s_student_problem_sub where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>

