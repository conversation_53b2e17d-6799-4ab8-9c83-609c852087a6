<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.CategoryQuantityMapper">
    
    <resultMap type="com.ruoyi.create.domain.CategoryQuantity" id="CategoryQuantityResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="category"    column="category"    />
        <result property="count"    column="count"    />
    </resultMap>

    <sql id="selectCategoryQuantityVo">
        select id, title, category, count from s_category_quantity
    </sql>

    <select id="selectCategoryQuantityList" parameterType="com.ruoyi.create.domain.CategoryQuantity" resultMap="CategoryQuantityResult">
        <include refid="selectCategoryQuantityVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="category != null  and category != ''"> and category = #{category}</if>
            <if test="count != null "> and count = #{count}</if>
        </where>
    </select>
    
    <select id="selectCategoryQuantityById" parameterType="Long" resultMap="CategoryQuantityResult">
        <include refid="selectCategoryQuantityVo"/>
        where id = #{id}
    </select>
    <select id="selectCategoryCount" parameterType="String" resultMap="CategoryQuantityResult">
        <include refid="selectCategoryQuantityVo"/>
        <where>
            <if test="title != null  and title != ''"> and title = #{title}</if>
        </where>
        GROUP BY
            category
        ORDER BY
            count DESC
        LIMIT 0,8
    </select>

    <insert id="insertCategoryQuantity" parameterType="com.ruoyi.create.domain.CategoryQuantity" useGeneratedKeys="true" keyProperty="id">
        insert into s_category_quantity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null">title,</if>
            <if test="category != null">category,</if>
            <if test="count != null">count,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null">#{title},</if>
            <if test="category != null">#{category},</if>
            <if test="count != null">#{count},</if>
         </trim>
    </insert>

    <update id="updateCategoryQuantity" parameterType="com.ruoyi.create.domain.CategoryQuantity">
        update s_category_quantity
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="category != null">category = #{category},</if>
            <if test="count != null">count = #{count},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCategoryQuantityById" parameterType="Long">
        delete from s_category_quantity where id = #{id}
    </delete>

    <delete id="deleteCategoryQuantityByIds" parameterType="String">
        delete from s_category_quantity where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>