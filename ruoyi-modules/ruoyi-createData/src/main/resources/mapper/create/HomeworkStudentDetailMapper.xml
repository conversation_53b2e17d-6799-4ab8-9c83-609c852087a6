<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.HomeworkStudentDetailMapper">
    
    <resultMap type="com.ruoyi.create.domain.HomeworkStudentDetail" id="HomeworkStudentDetailResult">
        <result property="id"    column="id"    />
        <result property="hmId"    column="hm_id"    />
        <result property="questionId"    column="question_id"    />
        <result property="userId"    column="user_id"    />
        <result property="userAnswer"    column="user_answer"    />
        <result property="correctAnswer"    column="correct_answer"    />
    </resultMap>

    <sql id="selectHomeworkStudentDetailVo">
        select id, hm_id, question_id, user_id, user_answer, correct_answer from s_homework_student_detail
    </sql>

    <select id="selectHomeworkStudentDetailList" parameterType="com.ruoyi.create.domain.HomeworkStudentDetail" resultMap="HomeworkStudentDetailResult">
        <include refid="selectHomeworkStudentDetailVo"/>
        <where>  
            <if test="hmId != null "> and hm_id = #{hmId}</if>
            <if test="questionId != null "> and question_id = #{questionId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userAnswer != null  and userAnswer != ''"> and user_answer = #{userAnswer}</if>
            <if test="correctAnswer != null  and correctAnswer != ''"> and correct_answer = #{correctAnswer}</if>
        </where>
    </select>
    
    <select id="selectHomeworkStudentDetailById" parameterType="Long" resultMap="HomeworkStudentDetailResult">
        <include refid="selectHomeworkStudentDetailVo"/>
        where id = #{id}
    </select>
    <select id="selectHomeworkStudentDetailListAll" resultMap="HomeworkStudentDetailResult">
        SELECT * FROM (
        SELECT
        *,
        ROW_NUMBER() OVER (
        PARTITION BY hm_id, question_id, user_id
        ORDER BY create_time DESC
        ) AS rn
        FROM s_homework_student_detail
        <where>
            <if test="hmId != null"> AND hm_id = #{hmId}</if>
            <if test="questionId != null"> AND question_id = #{questionId}</if>
            <if test="userId != null"> AND user_id = #{userId}</if>
        </where>
        ) ranked
        WHERE rn = 1
    </select>

    <insert id="insertHomeworkStudentDetail" parameterType="com.ruoyi.create.domain.HomeworkStudentDetail" useGeneratedKeys="true" keyProperty="id">
        insert into s_homework_student_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hmId != null">hm_id,</if>
            <if test="questionId != null">question_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="userAnswer != null">user_answer,</if>
            <if test="correctAnswer != null">correct_answer,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="hmId != null">#{hmId},</if>
            <if test="questionId != null">#{questionId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userAnswer != null">#{userAnswer},</if>
            <if test="correctAnswer != null">#{correctAnswer},</if>
         </trim>
    </insert>
    <insert id="insertHomeworkStudentDetailList" parameterType="com.ruoyi.create.domain.HomeworkStudentDetail" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO s_homework_student_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <!--<if test="hmId != null">hm_id,</if>
            <if test="questionId != null">question_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="userAnswer != null">user_answer,</if>
            <if test="correctAnswer != null">correct_answer,</if>-->
            hm_id,question_id,user_id,user_answer,correct_answer,create_time
        </trim>
        VALUES
        <foreach collection="list" item="item" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <!--<if test="item.hmId != null">#{item.hmId},</if>
                <if test="item.questionId != null">#{item.questionId},</if>
                <if test="item.userId != null">#{item.userId},</if>
                <if test="item.userAnswer != null">#{item.userAnswer},</if>
                <if test="item.correctAnswer != null">#{item.correctAnswer},</if>-->
                #{item.hmId},#{item.questionId},#{item.userId},#{item.userAnswer},#{item.correctAnswer},#{item.createTime}
            </trim>
        </foreach>
    </insert>

    <update id="updateHomeworkStudentDetail" parameterType="com.ruoyi.create.domain.HomeworkStudentDetail">
        update s_homework_student_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="hmId != null">hm_id = #{hmId},</if>
            <if test="questionId != null">question_id = #{questionId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userAnswer != null">user_answer = #{userAnswer},</if>
            <if test="correctAnswer != null">correct_answer = #{correctAnswer},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeworkStudentDetailById" parameterType="Long">
        delete from s_homework_student_detail where id = #{id}
    </delete>

    <delete id="deleteHomeworkStudentDetailByIds" parameterType="String">
        delete from s_homework_student_detail where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>