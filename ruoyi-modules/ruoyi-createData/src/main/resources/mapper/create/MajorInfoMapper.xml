<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.MajorInfoMapper">
    
    <resultMap type="com.ruoyi.create.domain.MajorInfo" id="MajorInfoResult">
        <result property="id"    column="id"    />
        <result property="majorName"    column="major_name"    />
        <result property="univerId"    column="univer_id"    />
        <result property="colleId"    column="colle_id"    />
        <result property="contact"    column="contact"    />
        <result property="phone"    column="phone"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMajorInfoVo">
        select id, major_name, univer_id, colle_id, contact, phone, create_by, create_time, update_by, update_time from s_major_info
    </sql>

    <select id="selectMajorInfoList" parameterType="com.ruoyi.create.domain.MajorInfo" resultMap="MajorInfoResult">
        <include refid="selectMajorInfoVo"/>
        <where>  
            <if test="majorName != null  and majorName != ''"> and major_name like concat('%', #{majorName}, '%')</if>
            <if test="univerId != null "> and univer_id = #{univerId}</if>
            <if test="colleId != null "> and colle_id = #{colleId}</if>
            <if test="contact != null  and contact != ''"> and contact = #{contact}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
        </where>
    </select>
    
    <select id="selectMajorInfoById" parameterType="Long" resultMap="MajorInfoResult">
        <include refid="selectMajorInfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertMajorInfo" parameterType="com.ruoyi.create.domain.MajorInfo" useGeneratedKeys="true" keyProperty="id">
        insert into s_major_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="majorName != null and majorName != ''">major_name,</if>
            <if test="univerId != null">univer_id,</if>
            <if test="colleId != null">colle_id,</if>
            <if test="contact != null">contact,</if>
            <if test="phone != null">phone,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="majorName != null and majorName != ''">#{majorName},</if>
            <if test="univerId != null">#{univerId},</if>
            <if test="colleId != null">#{colleId},</if>
            <if test="contact != null">#{contact},</if>
            <if test="phone != null">#{phone},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMajorInfo" parameterType="com.ruoyi.create.domain.MajorInfo">
        update s_major_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="majorName != null and majorName != ''">major_name = #{majorName},</if>
            <if test="univerId != null">univer_id = #{univerId},</if>
            <if test="colleId != null">colle_id = #{colleId},</if>
            <if test="contact != null">contact = #{contact},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMajorInfoById" parameterType="Long">
        delete from s_major_info where id = #{id}
    </delete>

    <delete id="deleteMajorInfoByIds" parameterType="String">
        delete from s_major_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>