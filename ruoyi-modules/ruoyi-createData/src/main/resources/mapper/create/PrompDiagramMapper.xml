<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.PrompDiagramMapper">
    
    <resultMap type="com.ruoyi.create.domain.PrompDiagram" id="PrompDiagramResult">
        <result property="id"    column="id"    />
        <result property="templatePk"    column="template_pk"    />
        <result property="picSize"    column="pic_size"    />
        <result property="picNum"    column="pic_num"    />
        <result property="samplingSteps"    column="sampling_steps"    />
        <result property="samplingMode"    column="sampling_mode"    />
    </resultMap>

    <sql id="selectPrompDiagramVo">
        select id, template_pk, pic_size, pic_num, sampling_steps, sampling_mode from s_promp_diagram
    </sql>

    <select id="selectPrompDiagramList" parameterType="com.ruoyi.create.domain.PrompDiagram" resultMap="PrompDiagramResult">
        <include refid="selectPrompDiagramVo"/>
        <where>  
            <if test="templatePk != null  and templatePk != ''"> and template_pk = #{templatePk}</if>
            <if test="picSize != null  and picSize != ''"> and pic_size = #{picSize}</if>
            <if test="picNum != null "> and pic_num = #{picNum}</if>
            <if test="samplingSteps != null "> and sampling_steps = #{samplingSteps}</if>
            <if test="samplingMode != null  and samplingMode != ''"> and sampling_mode = #{samplingMode}</if>
        </where>
    </select>
    
    <select id="selectPrompDiagramById" parameterType="Long" resultMap="PrompDiagramResult">
        <include refid="selectPrompDiagramVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertPrompDiagram" parameterType="com.ruoyi.create.domain.PrompDiagram" useGeneratedKeys="true" keyProperty="id">
        insert into s_promp_diagram
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="templatePk != null">template_pk,</if>
            <if test="picSize != null">pic_size,</if>
            <if test="picNum != null">pic_num,</if>
            <if test="samplingSteps != null">sampling_steps,</if>
            <if test="samplingMode != null">sampling_mode,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="templatePk != null">#{templatePk},</if>
            <if test="picSize != null">#{picSize},</if>
            <if test="picNum != null">#{picNum},</if>
            <if test="samplingSteps != null">#{samplingSteps},</if>
            <if test="samplingMode != null">#{samplingMode},</if>
         </trim>
    </insert>

    <update id="updatePrompDiagram" parameterType="com.ruoyi.create.domain.PrompDiagram">
        update s_promp_diagram
        <trim prefix="SET" suffixOverrides=",">
            <if test="templatePk != null">template_pk = #{templatePk},</if>
            <if test="picSize != null">pic_size = #{picSize},</if>
            <if test="picNum != null">pic_num = #{picNum},</if>
            <if test="samplingSteps != null">sampling_steps = #{samplingSteps},</if>
            <if test="samplingMode != null">sampling_mode = #{samplingMode},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePrompDiagramById" parameterType="Long">
        delete from s_promp_diagram where id = #{id}
    </delete>

    <delete id="deletePrompDiagramByIds" parameterType="String">
        delete from s_promp_diagram where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>