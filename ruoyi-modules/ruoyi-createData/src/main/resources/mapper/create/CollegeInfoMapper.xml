<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.CollegeInfoMapper">

    <resultMap type="com.ruoyi.create.domain.CollegeInfo" id="CollegeInfoResult">
        <result property="id"    column="id"    />
        <result property="colleName"    column="colle_name"    />
        <result property="univerId"    column="univer_id"    />
        <result property="colleAddr"    column="colle_addr"    />
        <result property="contact"    column="contact"    />
        <result property="phone"    column="phone"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCollegeInfoVo">
        select id, colle_name, univer_id, colle_addr, contact, phone, create_by, create_time, update_by, update_time from s_college_info
    </sql>

    <select id="selectCollegeInfoList" parameterType="com.ruoyi.create.domain.CollegeInfo" resultMap="CollegeInfoResult">
        <include refid="selectCollegeInfoVo"/>
        <where>
            <if test="colleName != null  and colleName != ''"> and colle_name like concat('%', #{colleName}, '%')</if>
            <if test="univerId != null "> and univer_id = #{univerId}</if>
            <if test="colleAddr != null  and colleAddr != ''"> and colle_addr = #{colleAddr}</if>
            <if test="contact != null  and contact != ''"> and contact = #{contact}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
        </where>
    </select>

    <select id="selectCollegeInfoById" parameterType="Long" resultMap="CollegeInfoResult">
        <include refid="selectCollegeInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertCollegeInfo" parameterType="com.ruoyi.create.domain.CollegeInfo" useGeneratedKeys="true" keyProperty="id">
        insert into s_college_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="colleName != null and colleName != ''">colle_name,</if>
            <if test="univerId != null">univer_id,</if>
            <if test="colleAddr != null">colle_addr,</if>
            <if test="contact != null">contact,</if>
            <if test="phone != null">phone,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="colleName != null and colleName != ''">#{colleName},</if>
            <if test="univerId != null">#{univerId},</if>
            <if test="colleAddr != null">#{colleAddr},</if>
            <if test="contact != null">#{contact},</if>
            <if test="phone != null">#{phone},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCollegeInfo" parameterType="com.ruoyi.create.domain.CollegeInfo">
        update s_college_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="colleName != null and colleName != ''">colle_name = #{colleName},</if>
            <if test="univerId != null">univer_id = #{univerId},</if>
            <if test="colleAddr != null">colle_addr = #{colleAddr},</if>
            <if test="contact != null">contact = #{contact},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCollegeInfoById" parameterType="Long">
        delete from s_college_info where id = #{id}
    </delete>

    <delete id="deleteCollegeInfoByIds" parameterType="String">
        delete from s_college_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectCollegeInfoAll"  resultMap="CollegeInfoResult">
        <include refid="selectCollegeInfoVo"/>
    </select>

    <select id="getAll"  resultMap="CollegeInfoResult">
        <include refid="selectCollegeInfoVo"/>
    </select>
</mapper>
