<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.HomeworkQuestionOptionMapper">

    <resultMap type="com.ruoyi.create.domain.HomeworkQuestionOption" id="HomeworkQuestionOptionResult">
        <result property="id"    column="id"    />
        <result property="questionId"    column="question_id"    />
        <result property="optionMark"    column="option_mark"    />
        <result property="optionText"    column="option_text"    />
    </resultMap>

    <sql id="selectHomeworkQuestionOptionVo">
        select id, question_id, option_mark, option_text from s_homework_question_option
    </sql>

    <select id="selectHomeworkQuestionOptionList" parameterType="com.ruoyi.create.domain.HomeworkQuestionOption" resultMap="HomeworkQuestionOptionResult">
        <include refid="selectHomeworkQuestionOptionVo"/>
        <where>
            <if test="questionId != null "> and question_id = #{questionId}</if>
            <if test="optionMark != null  and optionMark != ''"> and option_mark = #{optionMark}</if>
            <if test="optionText != null  and optionText != ''"> and option_text = #{optionText}</if>
        </where>
    </select>

    <select id="selectHomeworkQuestionOptionById" parameterType="Long" resultMap="HomeworkQuestionOptionResult">
        <include refid="selectHomeworkQuestionOptionVo"/>
        where id = #{id}
    </select>

    <insert id="insertHomeworkQuestionOption" parameterType="com.ruoyi.create.domain.HomeworkQuestionOption">
        insert into s_homework_question_option
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="questionId != null">question_id,</if>
            <if test="optionMark != null and optionMark != ''">option_mark,</if>
            <if test="optionText != null and optionText != ''">option_text,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="questionId != null">#{questionId},</if>
            <if test="optionMark != null and optionMark != ''">#{optionMark},</if>
            <if test="optionText != null and optionText != ''">#{optionText},</if>
         </trim>
    </insert>

    <update id="updateHomeworkQuestionOption" parameterType="com.ruoyi.create.domain.HomeworkQuestionOption">
        update s_homework_question_option
        <trim prefix="SET" suffixOverrides=",">
            <if test="questionId != null">question_id = #{questionId},</if>
            <if test="optionMark != null and optionMark != ''">option_mark = #{optionMark},</if>
            <if test="optionText != null and optionText != ''">option_text = #{optionText},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeworkQuestionOptionById" parameterType="Long">
        delete from s_homework_question_option where id = #{id}
    </delete>

    <delete id="deleteHomeworkQuestionOptionByIds" parameterType="String">
        delete from s_homework_question_option where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
