<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.ImageStorageMapper">
    
    <resultMap type="com.ruoyi.create.domain.ImageStorage" id="ImageStorageResult">
        <result property="id"    column="id"    />
        <result property="dataId"    column="data_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="filePath"    column="file_path"    />
        <result property="imageNumber"    column="image_number"    />
    </resultMap>

    <sql id="selectImageStorageVo">
        select id, data_id, file_name, file_path,image_number from s_image_storage
    </sql>

    <select id="selectImageStorageList" parameterType="com.ruoyi.create.domain.ImageStorage" resultMap="ImageStorageResult">
        <include refid="selectImageStorageVo"/>
        <where>  
            <if test="dataId != null "> and data_id = #{dataId}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="filePath != null  and filePath != ''"> and file_path = #{filePath}</if>
            <if test="imageNumber != null  and imageNumber != ''"> and image_number = #{imageNumber}</if>
        </where>
    </select>
    
    <select id="selectImageStorageById" parameterType="Long" resultMap="ImageStorageResult">
        <include refid="selectImageStorageVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertImageStorage" parameterType="com.ruoyi.create.domain.ImageStorage">
        insert into s_image_storage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="dataId != null">data_id,</if>
            <if test="fileName != null">file_name,</if>
            <if test="filePath != null">file_path,</if>
            <if test="imageNumber != null">image_number,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="dataId != null">#{dataId},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="filePath != null">#{filePath},</if>
            <if test="imageNumber != null">#{imageNumber},</if>
         </trim>
    </insert>

    <update id="updateImageStorage" parameterType="com.ruoyi.create.domain.ImageStorage">
        update s_image_storage
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataId != null">data_id = #{dataId},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="filePath != null">file_path = #{filePath},</if>
            <if test="imageNumber != null">image_number = #{imageNumber},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteImageStorageById" parameterType="Long">
        delete from s_image_storage where id = #{id}
    </delete>

    <delete id="deleteImageStorageByIds" parameterType="String">
        delete from s_image_storage where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="countImageStorage" parameterType="String">
        select count(1) from s_image_storage
        where image_number = #{imageNumber}
    </select>

    <select id="selectImageStorageByImageNumber" parameterType="String" resultMap="ImageStorageResult">
        <include refid="selectImageStorageVo"/>
        where image_number = #{imageNumber}
    </select>
</mapper>