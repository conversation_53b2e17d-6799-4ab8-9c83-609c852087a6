<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.KnowledgeNumberMapper">
    <insert id="insertKnowledgeNumber">
        insert into s_knowledge_number(`year_month`,`knowle_number`)
        values(#{yearMonth},#{knowledgeNumber})
    </insert>
    <update id="postKnowledgeNumber">
        update s_knowledge_number
        <set>
            <if test="knowledgeNumber != null">
                `knowle_number` = #{knowledgeNumber},
            </if>
        </set>
        where `year_month` = #{yearMonth}
    </update>
    <delete id="deleteKnowledgeNumberByYearMonth">
        delete  from s_knowledge_number where `year_month`=#{yearMonth}
    </delete>
    <!-- 定义 resultMap 来手动绑定字段 -->
    <resultMap id="knowledgeNumberResultMap" type="com.ruoyi.create.domain.KnowledgeNumber">
        <result property="knowledgeNumber" column="knowle_number"/>
        <result property="yearMonth" column="year_month"/>
    </resultMap>

    <!-- 使用 resultMap 进行查询 -->
    <select id="selectKnowledgeNumber" resultMap="knowledgeNumberResultMap">
        select * from s_knowledge_number
    </select>


</mapper>