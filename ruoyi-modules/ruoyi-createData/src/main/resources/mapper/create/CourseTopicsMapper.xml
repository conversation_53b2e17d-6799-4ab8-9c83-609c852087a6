<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.CourseTopicsMapper">

    <resultMap type="com.ruoyi.create.domain.CourseTopics" id="CourseTopicsResult">
        <result property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="likeCount" column="like_count"/>
        <result property="sort" column="sort"/>
        <result property="tage" column="tage"/>
        <result property="attachments" column="attachments"/>
        <result property="sendId" column="send_id"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="sendName" column="sendName"/>
        <result property="className" column="className"/>
    </resultMap>

    <resultMap type="com.ruoyi.create.domain.CourseTopics" id="CourseTopicsAndReplacesResult">
        <result property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="likeCount" column="like_count"/>
        <result property="sort" column="sort"/>
        <result property="tage" column="tage"/>
        <result property="attachments" column="attachments"/>
        <result property="sendId" column="send_id"/>
        <result property="studentCourseId" column="student_course_id"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="sendName" column="send_name"/>
        <result property="sendName" column="sendName"/>
        <result property="className" column="className"/>
    </resultMap>

    <sql id="selectCourseTopicsVo">
        SELECT id,
               student_course_id,
               title,
               content,
               like_count,
               sort,
               tage,
               attachments,
               send_id,
               remark,
               create_by,
               create_time,
               update_by,
               update_time
        FROM s_course_topics
    </sql>

    <select id="selectCourseTopicsList" parameterType="com.ruoyi.create.domain.CourseTopics"
            resultMap="CourseTopicsResult">
        <include refid="selectCourseTopicsVo"/>
        <where>
            <if test="title != null  and title != ''">and title = #{title}</if>
            <if test="content != null  and content != ''">and content = #{content}</if>
            <if test="likeCount != null ">and like_count = #{likeCount}</if>
            <if test="sort != null ">and sort = #{sort}</if>
            <if test="tage != null  and tage != ''">and tage = #{tage}</if>
            <if test="attachments != null  and attachments != ''">and attachments = #{attachments}</if>
            <if test="sendId != null ">and send_id = #{sendId}</if>
            <if test="courseName != null  and courseName != ''">and course_name like concat('%', #{courseName}, '%')
            </if>
            <if test="courseType != null  and courseType != ''">and course_type = #{courseType}</if>
            <if test="term != null  and term != ''">and term = #{term}</if>
            <if test="teacherId != null ">and teacher_id = #{teacherId}</if>
            <if test="classId != null ">and class_id = #{classId}</if>
        </where>
    </select>

    <select id="selectCourseTopicsById" parameterType="Long" resultMap="CourseTopicsResult">
        SELECT sct.*,
               suser.nick_name AS sendName,
               ssc.student_class AS className
        FROM s_course_topics sct
                 LEFT JOIN sys_user suser ON suser.user_id = sct.send_id AND suser.del_flag = '0'
                 LEFT JOIN s_student_course ssc ON ssc.id = sct.student_course_id
        WHERE sct.id = #{id}
    </select>

    <select id="listAllTopics" resultType="com.ruoyi.create.domain.CourseTopics">
        SELECT *
        FROM s_course_notifications
    </select>
    <select id="listAllTopicsForTeacher" resultMap="CourseTopicsResult">
        SELECT
        sct.*,
        sti.teacher_name,
        suser.nick_name,
        sci.class_name
        FROM
        s_course_topics sct
        LEFT JOIN s_teacher_info sti ON sti.teacher_id = sct.teacher_id
        LEFT JOIN sys_user suser ON suser.user_id = sct.send_id AND suser.del_flag = '0'
        LEFT JOIN s_class_info sci ON sci.id = sct.class_id
        <where>
            AND sct.course_name = #{courseName}
            AND sct.course_type = #{courseType}
            AND sct.term = #{term}
            AND sct.teacher_id = #{teacherId}
            <if test="classId !=null">AND sct.class_id = #{classId}</if>
            <if test="title != null  and title != ''">AND title like concat('%',#{title},'%')</if>
            <if test="content != null  and content != ''">AND content like concat('%',#{content},'%')</if>
            <if test="tage != null  and tage != ''">AND tage like concat('%',#{tage},'%')</if>
        </where>
        ORDER BY sct.sort DESC,sct.create_time
    </select>
    <select id="listAllTopicsForStudent" resultType="com.ruoyi.create.domain.CourseTopics">
        SELECT sct.*, ssci.*
        FROM s_course_topics sct
        JOIN s_strudent_course_info ssci
        ON ssci.course_name = sct.course_name
        AND sct.course_type = ssci.course_type
        AND sct.term = ssci.term
        AND sct.teacher_id = ssci.teacher_id
        WHERE
        <where>
            AND sct.course_name = #{courseName}
            AND sct.course_type = #{courseType}
            AND sct.term = #{term}
            AND sct.teacher_id = #{teacherId}
            AND ssci.student_id = #{loginUserId}
            <if test="classId !=null">AND sct.class_id = #{classId}</if>
            <if test="title != null  and title != ''">AND title like concat('%',#{title},'%')</if>
            <if test="content != null  and content != ''">AND content like concat('%',#{content},'%')</if>
            <if test="tage != null  and tage != ''">AND tage like concat('%',#{tage},'%')</if>
        </where>
    </select>
    <select id="selectCourseTopicsList2" resultType="com.ruoyi.create.domain.CourseTopics">
        SELECT sct.*,
        suser.nick_name AS sendName,
        ssc.student_class AS className
        FROM s_course_topics sct
        LEFT JOIN sys_user suser ON suser.user_id = sct.send_id AND suser.del_flag = '0'
        LEFT JOIN s_student_course ssc ON ssc.id = sct.student_course_id
        <where>
            AND sct.student_course_id = #{studentCourseId}
            <if test="className !=null and className !=''">AND ssc.student_class = #{className}</if>
            <if test="title != null  and title != ''">AND title like concat('%',#{title},'%')</if>
            <if test="content != null  and content != ''">AND content like concat('%',#{content},'%')</if>
            <if test="tage != null  and tage != ''">AND tage like concat('%',#{tage},'%')</if>
        </where>
    </select>
    <insert id="insertCourseTopics" parameterType="com.ruoyi.create.domain.CourseTopics" useGeneratedKeys="true"
            keyProperty="id">
        insert into s_course_topics
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null">title,</if>
            <if test="studentCourseId != null">student_course_id,</if>
            <if test="content != null">content,</if>
            <if test="likeCount != null">like_count,</if>
            <if test="sort != null">sort,</if>
            <if test="tage != null">tage,</if>
            <if test="attachments != null">attachments,</if>
            <if test="sendId != null">send_id,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null">#{title},</if>
            <if test="studentCourseId != null">#{studentCourseId},</if>
            <if test="content != null">#{content},</if>
            <if test="likeCount != null">#{likeCount},</if>
            <if test="sort != null">#{sort},</if>
            <if test="tage != null">#{tage},</if>
            <if test="attachments != null">#{attachments},</if>
            <if test="sendId != null">#{sendId},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateCourseTopics" parameterType="com.ruoyi.create.domain.CourseTopics">
        update s_course_topics
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="studentCourseId != null">student_course_id = #{studentCourseId},</if>
            <if test="content != null">content = #{content},</if>
            <if test="likeCount != null">like_count = #{likeCount},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="tage != null">tage = #{tage},</if>
            <if test="attachments != null">attachments = #{attachments},</if>
            <if test="sendId != null">send_id = #{sendId},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCourseTopicsById" parameterType="Long">
        DELETE
        FROM s_course_topics
        WHERE id = #{id}
    </delete>

    <delete id="deleteCourseTopicsByIds" parameterType="String">
        delete from s_course_topics where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
