<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.CourseNotificationsMapper">

    <resultMap type="com.ruoyi.create.domain.CourseNotifications" id="CourseNotificationsResult">
        <result property="id" column="id"/>
        <result property="senderId" column="sender_id"/>
        <result property="studentCourseId" column="student_course_id"/>
        <result property="recipientUserIds" column="recipient_user_ids"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="sendTime" column="send_time"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectCourseNotificationsVo">
        SELECT id,
               sender_id,
               student_course_id,
               recipient_user_ids,
               title,
               content,
               send_time,
               remark,
               create_by,
               create_time,
               update_by,
               update_time
        FROM s_course_notifications
    </sql>

    <select id="selectCourseNotificationsList" parameterType="com.ruoyi.create.domain.CourseNotifications"
            resultMap="CourseNotificationsResult">
        <include refid="selectCourseNotificationsVo"/>
        <where>
            <if test="senderId != null ">and sender_id = #{senderId}</if>
            <if test="recipientClassIds != null  and recipientClassIds != ''">and recipient_class_ids =
                #{recipientClassIds}
            </if>
            <if test="recipientUserIds != null  and recipientUserIds != ''">and recipient_user_ids =
                #{recipientUserIds}
            </if>
            <if test="title != null  and title != ''">and title = #{title}</if>
            <if test="content != null  and content != ''">and content = #{content}</if>
            <if test="sendTime != null ">and send_time = #{sendTime}</if>
        </where>
    </select>

    <select id="selectCourseNotificationsById" parameterType="Long" resultMap="CourseNotificationsResult">
        SELECT scn.*, suser.nick_name AS sendName
        FROM s_course_notifications scn
                 LEFT JOIN sys_user suser ON suser.user_id = scn.sender_id AND suser.del_flag = '0'
        WHERE id = #{id}
    </select>
    <select id="selectCourseNotificationsForTeacher" resultMap="CourseNotificationsResult">
        SELECT scn.*,
        suser.nick_name AS sendName,
        ssc.student_class AS studentClassName
        FROM s_course_notifications scn
        LEFT JOIN sys_user suser ON suser.user_id = scn.sender_id AND suser.del_flag = '0'
        LEFT JOIN s_student_course ssc ON ssc.id = scn.student_course_id
        <where>
            AND scn.student_course_id = #{studentCourseId}
            <if test="title != null  and title != ''">AND title like concat('%',#{title},'%')</if>
            <if test="content != null  and content != ''">AND content like concat('%',#{content},'%')</if>
            <if test="studentClassName !=null and studentClassName !=''">AND ssc.student_class like
                concat('%',#{studentClassName},'%')
            </if>
        </where>

    </select>
    <select id="selectCourseNotificationsForStudent" resultMap="CourseNotificationsResult">
        SELECT scn.*,
        suser.nick_name AS sendName,
        ssc.student_class AS studentClassName
        FROM s_course_notifications scn
        LEFT JOIN sys_user suser ON suser.user_id = scn.sender_id AND suser.del_flag = '0'
        LEFT JOIN s_student_course ssc ON ssc.id = scn.student_course_id
        <where>
            AND scn.student_course_id = #{studentCourseId}
            <if test="title != null  and title != ''">AND title like concat('%',#{title},'%')</if>
            <if test="content != null  and content != ''">AND content like concat('%',#{content},'%')</if>
            <if test="studentClassName !=null and studentClassName !=''">and ssc.student_class like
                concat('%',#{studentClassName},'%')
            </if>
        </where>
    </select>
    <select id="selectCourseNotificationsForAll" resultMap="CourseNotificationsResult">
        SELECT scn.*,
        suser.nick_name AS sendName,
        ssc.student_class AS studentClassName
        FROM s_course_notifications scn
        LEFT JOIN sys_user suser ON suser.user_id = scn.sender_id AND suser.del_flag = '0'
        LEFT JOIN s_student_course ssc ON ssc.id = scn.student_course_id
        <where>
            <if test="title != null  and title != ''">AND title like concat('%',#{title},'%')</if>
            <if test="content != null  and content != ''">AND content like concat('%',#{content},'%')</if>
            <if test="studentClassName !=null and studentClassName !=''">and ssc.student_class like
                concat('%',#{studentClassName},'%')
            </if>
        </where>
    </select>
    <select id="selectCourseNotificationsList2" resultType="com.ruoyi.create.domain.CourseNotifications">
        SELECT scn.*,
        suser.nick_name AS sendName,
        ssc.student_class AS studentClassName
        FROM s_course_notifications scn
        LEFT JOIN sys_user suser ON suser.user_id = scn.sender_id AND suser.del_flag = '0'
        LEFT JOIN s_student_course ssc ON ssc.id = scn.student_course_id
        <where>
            AND scn.student_course_id = #{studentCourseId}
            <if test="title != null  and title != ''">AND title like concat('%',#{title},'%')</if>
            <if test="content != null  and content != ''">AND content like concat('%',#{content},'%')</if>
            <if test="studentClassName !=null and studentClassName !=''">and ssc.student_class like
                concat('%',#{studentClassName},'%')
            </if>
        </where>
        ORDER BY scn.create_time DESC
    </select>


    <insert id="insertCourseNotifications" parameterType="com.ruoyi.create.domain.CourseNotifications">
        insert into s_course_notifications
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="senderId != null">sender_id,</if>
            <if test="studentCourseId !=null">student_course_id,</if>
            <if test="recipientUserIds != null">recipient_user_ids,</if>
            <if test="title != null">title,</if>
            <if test="content != null">content,</if>
            <if test="sendTime != null">send_time,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="senderId != null">#{senderId},</if>
            <if test="studentCourseId !=null">#{studentCourseId},</if>
            <if test="recipientUserIds != null">#{recipientUserIds},</if>
            <if test="title != null">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="sendTime != null">#{sendTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateCourseNotifications" parameterType="com.ruoyi.create.domain.CourseNotifications">
        update s_course_notifications
        <trim prefix="SET" suffixOverrides=",">
            <if test="senderId != null">sender_id = #{senderId},</if>
            <if test="studentCourseId != null">student_course_id = #{studentCourseId},</if>
            <if test="recipientUserIds != null">recipient_user_ids = #{recipientUserIds},</if>
            <if test="title != null">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="sendTime != null">send_time = #{sendTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCourseNotificationsById" parameterType="Long">
        DELETE
        FROM s_course_notifications
        WHERE id = #{id}
    </delete>

    <delete id="deleteCourseNotificationsByIds" parameterType="String">
        delete from s_course_notifications where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
