<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.UserVoiceMapper">

    <resultMap type="UserVoice" id="UserVoiceResult">
        <result property="id"   column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="voiceRoleId"    column="voice_role_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectUserVoiceVo">
        select id,
               user_id,
               xf_voice_role_id,
               bd_voice_role_id,
               doubao_voice_role_id,
               voice_speed,
               voice_pitch,
               voice_volume,
               user_playflag,
               create_by,
               create_time,
               update_by,
               update_time
        from s_user_voice
    </sql>

    <select id="selectUserVoiceList" parameterType="UserVoice" resultMap="UserVoiceResult">
        <include refid="selectUserVoiceVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="voiceRoleId != null "> and voice_role_id = #{voiceRoleId}</if>
        </where>
    </select>

    <select id="selectUserVoiceById" parameterType="Long" resultMap="UserVoiceResult">
        <include refid="selectUserVoiceVo"/>
        where user_id = #{userId}
    </select>

    <insert id="insertUserVoice" parameterType="UserVoice" useGeneratedKeys="true" keyProperty="id">
        insert into s_user_voice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="bdVoiceRoleId != null">bd_voice_role_id,</if>
            <if test="xfVoiceRoleId != null">xf_voice_role_id,</if>
            <if test="douBaoVoiceRoleId != null">doubao_voice_role_id,</if>
            <if test="voiceSpeed != null">voice_speed,</if>
            <if test="voicePitch != null">voice_pitch,</if>
            <if test="voiceVolume != null">voice_volume,</if>
            <if test="userPlayflag != null">user_playflag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="bdVoiceRoleId != null">#{bdVoiceRoleId},</if>
            <if test="xfVoiceRoleId != null">#{xfVoiceRoleId},</if>
            <if test="douBaoVoiceRoleId != null">#{douBaoVoiceRoleId},</if>
            <if test="voiceSpeed != null">#{voiceSpeed},</if>
            <if test="voicePitch != null">#{voicePitch},</if>
            <if test="voiceVolume != null">#{voiceVolume},</if>
            <if test="userPlayflag != null">#{userPlayflag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateUserVoice" parameterType="UserVoice">
        update s_user_voice
        <trim prefix="SET" suffixOverrides=",">
            <if test="bdVoiceRoleId != null">bd_voice_role_id = #{bdVoiceRoleId},</if>
            <if test="xfVoiceRoleId != null">xf_voice_role_id = #{xfVoiceRoleId},</if>
            <if test="douBaoVoiceRoleId != null">doubao_voice_role_id = #{douBaoVoiceRoleId},</if>
            <if test="voiceSpeed != null">voice_speed = #{voiceSpeed},</if>
            <if test="voicePitch != null">voice_pitch = #{voicePitch},</if>
            <if test="voiceVolume != null">voice_volume = #{voiceVolume},</if>
            <if test="userPlayflag != null">user_playflag = #{userPlayflag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where user_id = #{userId}
    </update>

    <delete id="deleteUserVoiceById" parameterType="Long">
        delete from s_user_voice where id = #{id}
    </delete>

    <delete id="deleteUserVoiceByIds" parameterType="String">
        delete from s_user_voice where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
