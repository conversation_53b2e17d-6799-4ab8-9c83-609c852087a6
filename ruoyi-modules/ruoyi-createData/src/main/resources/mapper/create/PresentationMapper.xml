<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.PresentationMapper">

    <resultMap type="com.ruoyi.create.domain.Presentation" id="PresentationResult">
        <result property="id" column="id"/>
        <result property="school" column="school"/>
        <result property="college" column="college"/>
        <result property="major" column="major"/>
        <result property="grade" column="grade"/>
        <result property="course" column="course"/>
        <result property="presentationName" column="presentation_name"/>
        <result property="presentationAllpage" column="presentation_allpage"/>
        <result property="presentationId" column="presentation_id"/>
        <result property="presentationStatus" column="presentation_status"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="speechdraftpath" column="speechdraftpath"/>
        <result property="presentationPath" column="presentation_path"/>
        <result property="presentationHttp" column="presentation_http"/>
        <result property="presentationFileId" column="presentation_file_id"/>
        <result property="speechdraftFileId" column="speechdraft_file_id"/>
        <result property="remark" column="remark"/>
        <result property="isAllianceCourse" column="is_alliance_course"/>
        <result property="isExamine" column="is_examine"/>
        <result property="chapter" column="chapter"/>
        <result property="textBookId" column="text_book_id"/>
        <result property="videoPageIndexes" column="video_page_indexes"/>
    </resultMap>
    <sql id="selectPresentationVo">
        SELECT id,
               school,
               college,
               major,
               grade,
               course,
               presentation_name,
               presentation_id,
               presentation_allpage,
               presentation_status,
               create_user,
               create_time,
               update_by,
               update_time,
               speechdraftpath,
               presentation_path,
               presentation_http,
               presentation_file_id,
               speechdraft_file_id,
               remark,
               is_alliance_course,
               is_examine,
               chapter,
               text_book_id,
               video_page_indexes
        FROM s_presentation
    </sql>

    <select id="selectPresentationList" parameterType="com.ruoyi.create.domain.Presentation"
            resultMap="PresentationResult">
        <include refid="selectPresentationVo"/>
        <where>
            <if test="universityId != null">and university_id = #{universityId}</if>
            <if test="school != null  and school != ''">and school = #{school}</if>
            <if test="college != null  and college != ''">and college = #{college}</if>
            <if test="major != null  and major != ''">and major = #{major}</if>
            <if test="grade != null  and grade != ''">and grade = #{grade}</if>
            <if test="course != null  and course != ''">and course = #{course}</if>
            <if test="presentationName != null  and presentationName != ''">and presentation_name like concat('%',
                #{presentationName}, '%')
            </if>
            <if test="presentationId != null ">and presentation_id = #{presentationId}</if>
            <if test="presentationAllpage != null ">and presentation_allpage = #{presentationAllpage}</if>
            <if test="presentationStatus != null  and presentationStatus != ''">and presentation_status =
                #{presentationStatus}
            </if>
            <if test="createUser != null  and createUser != ''">and create_user = #{createUser}</if>
            <if test="speechdraftpath != null  and speechdraftpath != ''">and speechdraftpath = #{speechdraftpath}</if>
            <if test="presentationPath != null  and presentationPath != ''">and presentation_path =
                #{presentationPath}
            </if>
            <if test="presentationHttp != null  and presentationHttp != ''">and presentation_http =
                #{presentationHttp}
            </if>
            <if test="presentationFileId != null  and presentationFileId != ''">and presentation_file_id =
                #{presentationFileId}
            </if>
            <if test="speechdraftFileId != null  and speechdraftFileId != ''">and speechdraft_file_id =
                #{speechdraftFileId}
            </if>
            <if test="remark != null  and remark != ''">and remark = #{remark}</if>
            <if test="isAllianceCourse != null ">and is_alliance_course = #{isAllianceCourse}</if>
            <if test="isExamine != null  and isExamine != ''">and is_examine = #{isExamine}</if>
            <if test="chapter != null  and chapter != ''">and chapter = #{chapter}</if>
            <if test="textBookId != null  and textBookId != ''">and text_book_id = #{textBookId}</if>
        </where>
        order by major asc,course asc,presentation_name asc
    </select>

    <select id="selectPresentationListGroupByCourse" parameterType="com.ruoyi.create.domain.Presentation"
            resultMap="PresentationResult">
        <include refid="selectPresentationVo"/>
        <where>
            <if test="universityId != null">and university_id = #{universityId}</if>
            <if test="school != null  and school != ''">and school = #{school}</if>
            <if test="college != null  and college != ''">and college = #{college}</if>
            <if test="major != null  and major != ''">and major = #{major}</if>
            <if test="grade != null  and grade != ''">and grade = #{grade}</if>
            <if test="course != null  and course != ''">and course = #{course}</if>
            <if test="presentationName != null  and presentationName != ''">and presentation_name like concat('%',
                #{presentationName}, '%')
            </if>
            <if test="presentationId != null ">and presentation_id = #{presentationId}</if>
            <if test="presentationAllpage != null ">and presentation_allpage = #{presentationAllpage}</if>
            <if test="presentationStatus != null  and presentationStatus != ''">and presentation_status =
                #{presentationStatus}
            </if>
            <if test="createUser != null  and createUser != ''">and create_user = #{createUser}</if>
            <if test="speechdraftpath != null  and speechdraftpath != ''">and speechdraftpath = #{speechdraftpath}</if>
            <if test="presentationPath != null  and presentationPath != ''">and presentation_path =
                #{presentationPath}
            </if>
            <if test="presentationHttp != null  and presentationHttp != ''">and presentation_http =
                #{presentationHttp}
            </if>
            <if test="presentationFileId != null  and presentationFileId != ''">and presentation_file_id =
                #{presentationFileId}
            </if>
            <if test="speechdraftFileId != null  and speechdraftFileId != ''">and speechdraft_file_id =
                #{speechdraftFileId}
            </if>
            <if test="remark != null  and remark != ''">and remark = #{remark}</if>
            <if test="isAllianceCourse != null  and isAllianceCourse != ''">and is_alliance_course =
                #{isAllianceCourse}
            </if>
            <if test="isExamine != null  and isExamine != ''">and is_examine = #{isExamine}</if>
            <if test="chapter != null  and chapter != ''">and chapter = #{chapter}</if>
            <if test="textBookId != null  and textBookId != ''">and text_book_id = #{textBookId}</if>
        </where>
        group by major,course
        order by major asc,course asc,presentation_name asc
    </select>

    <select id="selectPresentationById" parameterType="Long" resultMap="PresentationResult">
        <include refid="selectPresentationVo"/>
        where id = #{id}
    </select>
    <select id="selectPresentationByFileId" parameterType="String" resultMap="PresentationResult">
        <include refid="selectPresentationVo"/>
        where presentation_file_id = #{presentationFileId}
    </select>

    <insert id="insertPresentation" parameterType="com.ruoyi.create.domain.Presentation" useGeneratedKeys="true" keyProperty="id">
        insert into s_presentation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="universityId != null">university_id,</if>
            <if test="school != null">school,</if>
            <if test="college != null">college,</if>
            <if test="major != null">major,</if>
            <if test="grade != null">grade,</if>
            <if test="course != null">course,</if>
            <if test="presentationName != null">presentation_name,</if>
            <if test="presentationAllpage != null">presentation_allpage,</if>
            <if test="presentationId != null">presentation_id,</if>
            <if test="presentationStatus != null">presentation_status,</if>
            <if test="createUser != null">create_user,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="speechdraftpath != null">speechdraftpath,</if>
            <if test="presentationPath != null">presentation_path,</if>
            <if test="presentationHttp != null">presentation_http,</if>
            <if test="presentationFileId != null">presentation_file_id,</if>
            <if test="speechdraftFileId != null">speechdraft_file_id,</if>
            <if test="remark != null">remark,</if>
            <if test="isExamine != null">is_examine,</if>
            <if test="isAllianceCourse != null">is_alliance_course,</if>
            <if test="chapter != null">chapter,</if>
            <if test="textBookId != null">text_book_id,</if>
            <if test="videoPageIndexes != null">video_page_indexes,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="universityId != null">#{universityId},</if>
            <if test="school != null">#{school},</if>
            <if test="college != null">#{college},</if>
            <if test="major != null">#{major},</if>
            <if test="grade != null">#{grade},</if>
            <if test="course != null">#{course},</if>
            <if test="presentationName != null">#{presentationName},</if>
            <if test="presentationAllpage != null">#{presentationAllpage},</if>
            <if test="presentationId != null">#{presentationId},</if>
            <if test="presentationStatus != null">#{presentationStatus},</if>
            <if test="createUser != null">#{createUser},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="speechdraftpath != null">#{speechdraftpath},</if>
            <if test="presentationPath != null">#{presentationPath},</if>
            <if test="presentationHttp != null">#{presentationHttp},</if>
            <if test="presentationFileId != null">#{presentationFileId},</if>
            <if test="speechdraftFileId != null">#{speechdraftFileId},</if>
            <if test="remark != null">#{remark},</if>
            <if test="isAllianceCourse != null">#{isAllianceCourse},</if>
            <if test="isExamine != null">#{isExamine},</if>
            <if test="chapter != null">#{chapter},</if>
            <if test="textBookId != null">#{textBookId},</if>
            <if test="videoPageIndexes != null">#{videoPageIndexes},</if>
        </trim>
    </insert>

    <update id="updatePresentation" parameterType="com.ruoyi.create.domain.Presentation">
        update s_presentation
        <trim prefix="SET" suffixOverrides=",">
            <if test="major != null">major = #{major},</if>

            <if test="grade != null">grade = #{grade},</if>
            <if test="course != null">course = #{course},</if>
            <if test="presentationName != null">presentation_name = #{presentationName},</if>
            <if test="presentationAllpage != null">presentation_allpage = #{presentationAllpage},</if>
            <if test="presentationId != null">presentation_id = #{presentationId},</if>
            <if test="presentationStatus != null">presentation_status = #{presentationStatus},</if>
            <if test="createUser != null">create_user = #{createUser},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="speechdraftpath != null">speechdraftpath = #{speechdraftpath},</if>
            <if test="presentationPath != null">presentation_path = #{presentationPath},</if>
            <if test="presentationHttp != null">presentation_http = #{presentationHttp},</if>
            <if test="presentationFileId != null">presentation_file_id = #{presentationFileId},</if>
            <if test="speechdraftFileId != null">speechdraft_file_id = #{speechdraftFileId},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="isAllianceCourse != null">is_alliance_course = #{isAllianceCourse},</if>
            <if test="isExamine != null">is_examine = #{isExamine},</if>
            <if test="chapter != null">chapter = #{chapter},</if>
            <if test="textBookId != null">text_book_id = #{textBookId},</if>
            <if test="videoPageIndexes != null">video_page_indexes = #{videoPageIndexes},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updatePresentationByPresentationId">
        UPDATE s_presentation
        SET video_page_indexes = #{videoPageIndexes}
        WHERE id = #{id}
    </update>

    <delete id="deletePresentationById" parameterType="Long">
        DELETE
        FROM s_presentation
        WHERE id = #{id}
    </delete>

    <delete id="deletePresentationByIds" parameterType="String">
        delete from s_presentation where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="selectUByT" parameterType="long" resultType="java.lang.Integer">
        SELECT univer_id
        FROM s_teacher_info
        WHERE teacher_id = #{id}
    </select>


    <select id="selectCByT" parameterType="long" resultType="java.lang.Integer">
        SELECT colle_id
        FROM s_teacher_info
        WHERE teacher_id = #{id}
    </select>


    <select id="selectNameById" parameterType="string" resultType="java.lang.String">
        SELECT file_origin_name
        FROM sys_file_info
        WHERE file_object_name = #{id}
    </select>

    <delete id="delStudentPromotionByPId" parameterType="long">
        DELETE
        FROM s_student_promotion
        WHERE presentation_id = #{presentationId}
    </delete>

    <delete id="delStudentStudyRecordByPId" parameterType="long">
        DELETE
        FROM s_student_study_record
        WHERE presentation_id = #{presentationId}
    </delete>


    <select id="selectUserByUserName" resultType="com.ruoyi.create.dto.UserDto" parameterType="string">
        SELECT university_id, college_id, major_id, student_id
        FROM sys_user
        WHERE user_name = #{username}
          AND del_flag = '0'
    </select>

    <select id="selectUserRoleKeyByUserName" resultType="string" parameterType="string">
        SELECT r.role_key
        FROM sys_user_role ur
                 LEFT JOIN sys_user u ON ur.user_id = u.user_id
                 LEFT JOIN sys_role r ON ur.role_id = r.role_id
        WHERE u.user_name = #{username};
    </select>

    <select id="selectChapterById" parameterType="long" resultType="java.lang.String">
        SELECT DISTINCT chapter
        FROM s_textbook_keyword_analysis
        WHERE textbook_id = #{id}
          AND chapter IS NOT NULL
    </select>

    <select id="selectFileNameById" parameterType="long" resultType="java.lang.String">
        SELECT file_name
        FROM s_knowledge_base_file
        WHERE id = #{id}
          AND file_name != ''
    </select>

    <select id="selectTxtIdByCourse" parameterType="com.ruoyi.create.domain.KnowledgeBaseFile"
            resultType="com.ruoyi.create.domain.KnowledgeBaseFile">
        SELECT id, file_name, course_name, major_id
        FROM s_knowledge_base_file
--         LEFT JOIN  s_presentation p on k.course_name = p.course and k.major_id =p.major
        WHERE course_name = #{courseName}
          AND major_id = #{majorId}
          AND file_name != ''
    </select>
    <select id="selectChapterByCourse" parameterType="string" resultType="java.lang.String">
        SELECT DISTINCT chapter
        FROM s_textbook_keyword_analysis
        WHERE textbook_id IN (SELECT DISTINCT k.id
                              FROM s_knowledge_base_file k
                                       LEFT JOIN s_presentation p ON k.course_name = p.course AND k.major_id = p.major
                              WHERE p.course = #{course})
          AND chapter IS NOT NULL
    </select>
    <select id="selectPresentationByPresentationId" parameterType="Long" resultMap="PresentationResult">
        <include refid="selectPresentationVo"/>
        where presentation_id = #{presentationId}
    </select>

    <select id="selectKnowledgeByPresentation" parameterType="com.ruoyi.create.domain.Presentation"  resultType="string">
        SELECT keyword
        FROM s_textbook_keyword_analysis
        WHERE textbook_id = #{textBookId}
          AND FIND_IN_SET(chapter,#{chapter}) > 0
    </select>

    <!--    &lt;!&ndash; 学校 &ndash;&gt;-->
    <!--    <resultMap type="com.ruoyi.create.domain.University" id="UniversityResult">-->
    <!--        <id property="id" column="id" />-->
    <!--        <result property="name" column="univer_name" />-->
    <!--        <result property="univerName" column="univer_name" />-->
    <!--        <result property="univerAddr" column="univer_addr" />-->
    <!--        <result property="contact" column="contact" />-->
    <!--        <result property="phone" column="phone" />-->
    <!--        <result property="createBy" column="create_by" />-->
    <!--        <result property="createTime" column="create_time" />-->
    <!--        <result property="updateBy" column="update_by" />-->
    <!--        <result property="updateTime" column="update_time" />-->
    <!--        <result property="colleName" column="colle_name" />-->
    <!--        <result property="majorName" column="major_name" />-->
    <!--        <result property="className" column="class_name" />-->
    <!--        <collection property="children" ofType="com.ruoyi.create.domain.CollegeInfo" resultMap="CollegeInfoResult"/>-->
    <!--    </resultMap>-->

    <!--    &lt;!&ndash; 学院 &ndash;&gt;-->
    <!--    <resultMap type="com.ruoyi.create.domain.CollegeInfo" id="CollegeInfoResult">-->
    <!--        <id property="id" column="id" />-->
    <!--        <result property="colleName" column="colle_name" />-->
    <!--        <result property="univerId" column="univer_id" />-->
    <!--        <result property="colleAddr" column="colle_addr" />-->
    <!--        <result property="contact" column="contact" />-->
    <!--        <result property="phone" column="phone" />-->
    <!--        <result property="createBy" column="create_by" />-->
    <!--        <result property="createTime" column="create_time" />-->
    <!--        <result property="updateBy" column="update_by" />-->
    <!--        <result property="updateTime" column="update_time" />-->
    <!--    </resultMap>-->

    <!--    &lt;!&ndash; 专业 &ndash;&gt;-->
    <!--    <resultMap type="com.ruoyi.create.domain.MajorInfo" id="MajorInfoResult">-->
    <!--        <id property="id" column="id" />-->
    <!--        <result property="name" column="major_name" />-->
    <!--        <result property="majorName" column="major_name" />-->
    <!--        <result property="univerId" column="univer_id" />-->
    <!--        <result property="colleId" column="colle_id" />-->
    <!--        <result property="contact" column="contact" />-->
    <!--        <result property="phone" column="phone" />-->
    <!--        <result property="createBy" column="create_by" />-->
    <!--        <result property="createTime" column="create_time" />-->
    <!--        <result property="updateBy" column="update_by" />-->
    <!--        <result property="updateTime" column="update_time" />-->
    <!--        <collection property="children" ofType="com.ruoyi.create.domain.ClassInfo" resultMap="ClassInfoResult"/>-->
    <!--    </resultMap>-->

    <!--    &lt;!&ndash; 班级 &ndash;&gt;-->
    <!--    <resultMap type="com.ruoyi.create.domain.ClassInfo" id="ClassInfoResult">-->
    <!--        <id property="id" column="id" />-->
    <!--        <result property="name" column="class_name" />-->
    <!--        <result property="className" column="class_name" />-->
    <!--        <result property="univerId" column="univer_id" />-->
    <!--        <result property="colleId" column="colle_id" />-->
    <!--        <result property="majorId" column="major_id" />-->
    <!--        <result property="classTeacher" column="class_teacher" />-->
    <!--        <result property="phone" column="phone" />-->
    <!--        <result property="createBy" column="create_by" />-->
    <!--        <result property="createTime" column="create_time" />-->
    <!--        <result property="updateBy" column="update_by" />-->
    <!--        <result property="updateTime" column="update_time" />-->
    <!--    </resultMap>-->

    <!--    &lt;!&ndash;    课件&ndash;&gt;-->
    <!--    <resultMap type="com.ruoyi.create.Vo.PresentationVo" id="PresentationResultVo">-->
    <!--        &lt;!&ndash; 必须有 id 元素，如果没有主键可以忽略 &ndash;&gt;-->
    <!--        <id property="id" column="id" />-->
    <!--        &lt;!&ndash; 映射普通字段 &ndash;&gt;-->
    <!--        <result property="school" column="school" />-->
    <!--        <result property="college" column="college" />-->
    <!--        <result property="major" column="major" />-->
    <!--        <result property="grade" column="grade" />-->
    <!--        <result property="course" column="course" />-->
    <!--        <result property="presentationName" column="presentation_name" />-->
    <!--        <result property="presentationAllpage" column="presentation_allpage" />-->
    <!--        <result property="presentationId" column="presentation_id" />-->
    <!--        <result property="presentationStatus" column="presentation_status" />-->
    <!--        <result property="createUser" column="create_user" />-->
    <!--        <result property="createTime" column="create_time" />-->
    <!--        <result property="updateBy" column="update_by" />-->
    <!--        <result property="updateTime" column="update_time" />-->
    <!--        <result property="speechdraftpath" column="speechdraftpath" />-->
    <!--        <result property="presentationPath" column="presentation_path" />-->
    <!--        <result property="presentationHttp" column="presentation_http" />-->

    <!--        &lt;!&ndash; 映射 University 对象 &ndash;&gt;-->
    <!--        <association property="university" javaType="com.ruoyi.create.domain.University" resultMap="UniversityResult" />-->
    <!--        &lt;!&ndash; 映射 CollegeInfo 对象 &ndash;&gt;-->
    <!--        <association property="collegeInfo" javaType="com.ruoyi.create.domain.CollegeInfo" resultMap="CollegeInfoResult" />-->
    <!--        &lt;!&ndash; 映射 MajorInfo 对象 &ndash;&gt;-->
    <!--        <association property="majorInfo" javaType="com.ruoyi.create.domain.MajorInfo" resultMap="MajorInfoResult" />-->
    <!--    </resultMap>-->


    <!--    &lt;!&ndash; 定义查询 PresentationVo 列表的方法 &ndash;&gt;-->
    <!--    <select id="selectPresentationAllList" resultMap="PresentationResultVo" parameterType="com.ruoyi.create.domain.Presentation">-->
    <!--        SELECT-->
    <!--        p.id,-->
    <!--        p.school,-->
    <!--        p.college,-->
    <!--        p.major,-->
    <!--        p.grade,-->
    <!--        p.course,-->
    <!--        p.presentation_name AS presentationName,-->
    <!--        p.presentation_allpage AS presentationAllpage,-->
    <!--        p.presentation_id AS presentationId,-->
    <!--        p.presentation_status AS presentationStatus,-->
    <!--        p.create_user AS createUser,-->
    <!--        p.create_time AS createTime,-->
    <!--        p.update_by AS updateBy,-->
    <!--        p.update_time AS updateTime,-->
    <!--        p.speechdraftpath,-->
    <!--        p.presentation_path AS presentationPath,-->
    <!--        p.presentation_http-->
    <!--        FROM s_presentation p-->
    <!--        LEFT JOIN s_university u ON p.school = u.id-->
    <!--        LEFT JOIN s_college_info c ON p.college = c.id-->
    <!--        LEFT JOIN s_major_info m ON p.major = m.id-->
    <!--        <where>-->
    <!--            <if test="school != null  and school != ''"> and school = #{school}</if>-->
    <!--            <if test="college != null  and college != ''"> and college = #{college}</if>-->
    <!--            <if test="major != null  and major != ''"> and major = #{major}</if>-->
    <!--            <if test="grade != null  and grade != ''"> and grade = #{grade}</if>-->
    <!--            <if test="course != null  and course != ''"> and course = #{course}</if>-->
    <!--            <if test="presentationName != null  and presentationName != ''"> and presentation_name like concat('%', #{presentationName}, '%')</if>-->
    <!--            <if test="presentationId != null "> and presentation_id = #{presentationId}</if>-->
    <!--            <if test="presentationAllpage != null "> and presentation_allpage = #{presentationAllpage}</if>-->
    <!--            <if test="presentationStatus != null  and presentationStatus != ''"> and presentation_status = #{presentationStatus}</if>-->
    <!--            <if test="createUser != null  and createUser != ''"> and create_user = #{createUser}</if>-->
    <!--            <if test="speechdraftpath != null  and speechdraftpath != ''"> and speechdraftpath = #{speechdraftpath}</if>-->
    <!--            <if test="presentationPath != null  and presentationPath != ''"> and presentation_path = #{presentationPath}</if>-->
    <!--        </where>-->
    <!--    </select>-->


</mapper>
