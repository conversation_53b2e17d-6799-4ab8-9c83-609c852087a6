<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.AverageMapper">

    <!-- 结果映射 -->
    <resultMap id="AverageResultMap" type="com.ruoyi.create.domain.Average">
        <id property="classId" column="class_id" />
        <result property="laScore" column="la_score" />
        <result property="laHomework" column="la_homework" />
        <result property="laMastery" column="la_mastery" />
        <result property="laPerformance" column="la_performance" />
        <result property="laInterest" column="la_interest" />
        <result property="laTalent" column="la_talent" />
        <result property="laEvaluation" column="la_evaluation" />
        <result property="lbMorality" column="lb_morality" />
        <result property="lbAesthetics" column="lb_aesthetics" />
        <result property="lbHealth" column="lb_health" />
        <result property="lbCooperation" column="lb_cooperation" />
        <result property="lbLearning" column="lb_learning" />
        <result property="statisticsTime" column="statistics_time" />
    </resultMap>

    <!-- 查询学生自画像信息 -->
    <select id="selectAById" parameterType="java.lang.String" resultMap="AverageResultMap">
        SELECT * FROM s_class_average WHERE class_id = #{classId}
    </select>



</mapper>