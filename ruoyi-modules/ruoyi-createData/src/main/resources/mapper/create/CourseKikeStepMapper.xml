<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.CourseKikeStepMapper">
    
    <resultMap type="com.ruoyi.create.domain.CourseKikeStep" id="CourseKikeStepResult">
        <result property="id"    column="id"    />
        <result property="msgId"    column="msg_id"    />
        <result property="kikeStep"    column="kike_step"    />
        <result property="createBy"    column="create_by"    />
    </resultMap>

    <sql id="selectCourseKikeStepVo">
        select id, msg_id, kike_step, create_by from s_course_kike_step
    </sql>

    <select id="selectCourseKikeStepList" parameterType="com.ruoyi.create.domain.CourseKikeStep" resultMap="CourseKikeStepResult">
        <include refid="selectCourseKikeStepVo"/>
        <where>  
            <if test="msgId != null "> and msg_id = #{msgId}</if>
            <if test="kikeStep != null  and kikeStep != ''"> and kike_step = #{kikeStep}</if>
        </where>
    </select>
    
    <select id="selectCourseKikeStepById" parameterType="Long" resultMap="CourseKikeStepResult">
        <include refid="selectCourseKikeStepVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCourseKikeStep" parameterType="com.ruoyi.create.domain.CourseKikeStep" useGeneratedKeys="true" keyProperty="id">
        insert into s_course_kike_step
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="msgId != null">msg_id,</if>
            <if test="kikeStep != null">kike_step,</if>
            <if test="createBy != null">create_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="msgId != null">#{msgId},</if>
            <if test="kikeStep != null">#{kikeStep},</if>
            <if test="createBy != null">#{createBy},</if>
         </trim>
    </insert>

    <update id="updateCourseKikeStep" parameterType="com.ruoyi.create.domain.CourseKikeStep">
        update s_course_kike_step
        <trim prefix="SET" suffixOverrides=",">
            <if test="msgId != null">msg_id = #{msgId},</if>
            <if test="kikeStep != null">kike_step = #{kikeStep},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCourseKikeStepById" parameterType="Long">
        delete from s_course_kike_step where id = #{id}
    </delete>

    <delete id="deleteCourseKikeStepByIds" parameterType="String">
        delete from s_course_kike_step where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteCourseKikeStep">
        delete from s_course_kike_step where msg_id = #{msgId} and create_by = #{createBy}
    </delete>

    <select id="countCourseKike">
        SELECT
            count(1)
        FROM
            s_course_kike_step
        where msg_id = #{msgId} and kike_step = '1'
    </select>

    <select id="countCourseStep">
        SELECT
            count(1)
        FROM
            s_course_kike_step
        where msg_id = #{msgId} and kike_step = '2'
    </select>

    <select id="selectCourseKikeStepByUserName">
        SELECT
            kike_step
        FROM
            s_course_kike_step
        where msg_id = #{msgId} and create_by = #{createBy}
    </select>
</mapper>