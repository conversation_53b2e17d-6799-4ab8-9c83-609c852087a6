<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.PresentationPathMapper">

    <resultMap type="com.ruoyi.create.domain.PresentationPath" id="PresentationPathResult">
        <result property="id"    column="id"    />
        <result property="sPresentationPath"    column="s_presentation_path"    />
        <result property="sPresentaationId"    column="s_presentaation_id"    />
    </resultMap>

    <sql id="selectPresentationPathVo">
        select id, s_presentation_path, s_presentaation_id from s_presentation_path
    </sql>

    <select id="selectPresentationPathList" parameterType="com.ruoyi.create.domain.PresentationPath" resultMap="PresentationPathResult">
        <include refid="selectPresentationPathVo"/>
        <where>
            <if test="sPresentationPath != null  and sPresentationPath != ''"> and s_presentation_path = #{sPresentationPath}</if>
            <if test="sPresentaationId != null "> and s_presentaation_id = #{sPresentaationId}</if>
        </where>
    </select>

    <select id="selectPresentationPathById" parameterType="Long" resultMap="PresentationPathResult">
        <include refid="selectPresentationPathVo"/>
        where id = #{id}
    </select>

    <insert id="insertPresentationPath" parameterType="com.ruoyi.create.domain.PresentationPath" useGeneratedKeys="true" keyProperty="id">
        insert into s_presentation_path
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sPresentationPath != null">s_presentation_path,</if>
            <if test="sPresentaationId != null">s_presentaation_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sPresentationPath != null">#{sPresentationPath},</if>
            <if test="sPresentaationId != null">#{sPresentaationId},</if>
        </trim>
    </insert>

    <update id="updatePresentationByPresentationId">
        update s_presentation
        <trim prefix="SET" suffixOverrides=",">
            <if test="videoPageIndexes != null and videoPageIndexes !=''"> video_page_indexes= #{videoPageIndexes},</if>
        </trim>
        where presentation_id = #{presentationFileId}
    </update>

    <delete id="deletePresentationPathById" parameterType="Long">
        delete from s_presentation_path where id = #{id}
    </delete>

    <delete id="deletePresentationPathByIds" parameterType="String">
        delete from s_presentation_path where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
