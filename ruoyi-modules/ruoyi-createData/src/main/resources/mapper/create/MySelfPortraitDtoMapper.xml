<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.MySelfPortraitDtoMapper">

    <!-- 结果映射 -->


    <!--   1.知识点掌握度-->
    <select id="selectMaxProgressByStudentId" resultType="java.lang.Double">
        SELECT
            MAX(s.progress)
        FROM
            s_student_study_record s
                JOIN
            sys_user u ON s.create_by = u.user_name
        WHERE
            u.student_id = #{studentId}
    </select>
    <!--   2.作业质量-->
    <select id="selectCorrectRateByStudentId" resultType="java.lang.Double">
        SELECT
            IFNULL(ROUND(SUM(CASE WHEN is_right = 1 THEN 1 ELSE 0 END) * 1.0 / COUNT(*), 4), 0)
        FROM
            s_student_problem_sub
        WHERE
            student_id = #{studentId}
    </select>
    <!--   3.专业兴趣 -->
    <select id="selectOnTimeRateByStudentId" resultType="java.lang.Integer">
        SELECT
            IFNULL(FLOOR(
                           SUM(CASE WHEN attendance_time &lt;= attendance_deadline THEN 1 ELSE 0 END) * 100.0 / COUNT(*)
                   ), 0) AS onTimeRate
        FROM
            s_student_attendance
        WHERE
            student_id = #{studentId}
          AND attendance_time IS NOT NULL
          AND attendance_deadline IS NOT NULL
    </select>
    <!--   4.他人评价打分 -->
    <select id="selectRemarksByStudentId" resultType="java.lang.String">
        SELECT s.remark
        FROM s_homework_student s
                JOIN
            sys_user u ON s.user_id = u.user_id
        WHERE u.student_id = #{studentId}
    </select>







</mapper>