<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.StudentMapper">

    <!-- 映射实体类 -->
    <resultMap id="StudentInfoResultMap" type="com.ruoyi.create.domain.execl.Student">
        <id property="userId" column="id"/>
        <result property="studentId" column="student_id"/>
        <result property="studentName" column="student_name"/>
        <result property="sex" column="sex"/>
        <result property="univerId" column="univer_id"/>
        <result property="colleId" column="colle_id"/>
        <result property="majorId" column="major_id"/>
        <result property="classId" column="class_id"/>
        <result property="educationalSystem" column="educational_system"/>
        <result property="currentGrade" column="current_grade"/>
        <result property="schoolStatus" column="school_status"/>
        <result property="studentStatus" column="student_status"/>
        <result property="studentCurrentStatus" column="student_current_status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

<!--    //查询所有学生id-->
    <select id="getAllStudentIds" resultType="java.lang.String">
        SELECT student_id FROM s_student_info
    </select>




</mapper>