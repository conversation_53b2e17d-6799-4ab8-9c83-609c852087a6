<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.SMsgReplyMapper1">
    
    <resultMap type="com.ruoyi.create.domain.SMsgReply1" id="SMsgReplyResult">
        <result property="id"    column="id"    />
        <result property="msgId"    column="msg_id"    />
        <result property="lastReplyId"    column="last_reply_id"    />
        <result property="replyId"    column="reply_id"    />
        <result property="replyMsg"    column="reply_msg"    />
        <result property="createTime"    column="create_time"    />
        <result property="lastReplyUid"    column="last_reply_uid"    />
        <result property="lastReplyUname"    column="last_reply_uname"    />
        <result property="replyUserid"    column="reply_userid"    />
        <result property="replyUserName"    column="reply_user_name"    />
        <result property="img"    column="img"    />
    </resultMap>

    <resultMap type="com.ruoyi.system.api.domain.SysUser" id="SysUserResult">
        <id     property="userId"       column="user_id"      />
        <result property="deptId"       column="dept_id"      />
        <result property="userName"     column="user_name"    />
        <result property="nickName"     column="nick_name"    />
        <result property="email"        column="email"        />
        <result property="phonenumber"  column="phonenumber"  />
        <result property="sex"          column="sex"          />
        <result property="avatar"       column="avatar"       />
        <result property="password"     column="password"     />
        <result property="status"       column="status"       />
        <result property="delFlag"      column="del_flag"     />
        <result property="loginIp"      column="login_ip"     />
        <result property="loginDate"    column="login_date"   />
        <result property="createBy"     column="create_by"    />
        <result property="createTime"   column="create_time"  />
        <result property="updateBy"     column="update_by"    />
        <result property="updateTime"   column="update_time"  />
        <result property="remark"       column="remark"       />
        <result property="identityCard"   column="identity_card"  />
        <result property="authStatus"       column="auth_status"       />
        <result property="university"       column="university"       />
        <result property="college"       column="college"       />
        <result property="specialty"       column="specialty"       />
        <result property="jobId"       column="job_id"       />
        <result property="studentId"       column="student_id"       />
        <result property="grade"       column="grade"       />
        <result property="authRoleId"       column="auth_role_id"       />
        <association property="dept"    javaType="com.ruoyi.system.api.domain.SysDept"         resultMap="deptResult" />
        <collection  property="roles"   javaType="java.util.List"  resultMap="RoleResult" />
    </resultMap>

    <resultMap id="deptResult" type="com.ruoyi.system.api.domain.SysDept">
        <id     property="deptId"    column="dept_id"     />
        <result property="parentId"  column="parent_id"   />
        <result property="deptName"  column="dept_name"   />
        <result property="ancestors" column="ancestors"   />
        <result property="orderNum"  column="order_num"   />
        <result property="leader"    column="leader"      />
        <result property="status"    column="dept_status" />
    </resultMap>

    <resultMap id="RoleResult" type="com.ruoyi.system.api.domain.SysRole">
        <id     property="roleId"       column="role_id"        />
        <result property="roleName"     column="role_name"      />
        <result property="roleKey"      column="role_key"       />
        <result property="roleSort"     column="role_sort"      />
        <result property="dataScope"    column="data_scope"     />
        <result property="status"       column="role_status"    />
    </resultMap>

    <sql id="selectSMsgReplyVo">
        select msg_id, last_reply_id, reply_id, reply_msg, create_time, last_reply_uid, last_reply_uname, reply_userid, reply_user_name ,img from s_msg_reply1
    </sql>

    <select id="selectSMsgReplyList" parameterType="com.ruoyi.create.domain.SMsgReply" resultMap="SMsgReplyResult">
        <include refid="selectSMsgReplyVo"/>
        <where>  
            <if test="lastReplyId != null "> and last_reply_id = #{lastReplyId}</if>
            <if test="replyId != null "> and reply_id = #{replyId}</if>
            <if test="replyMsg != null  and replyMsg != ''"> and reply_msg = #{replyMsg}</if>
            <if test="lastReplyUid != null "> and last_reply_uid = #{lastReplyUid}</if>
            <if test="lastReplyUname != null  and lastReplyUname != ''"> and last_reply_uname like concat('%', #{lastReplyUname}, '%')</if>
            <if test="replyUserid != null "> and reply_userid = #{replyUserid}</if>
            <if test="replyUserName != null  and replyUserName != ''"> and reply_user_name like concat('%', #{replyUserName}, '%')</if>
            <if test="img != null  and img != ''"> and reply_user_name like concat('%', #{img}, '%')</if>
        </where>
    </select>
    
    <select id="selectSMsgReplyByMsgId" parameterType="Long" resultMap="SMsgReplyResult">
        <include refid="selectSMsgReplyVo"/>
        where msg_id = #{msgId}
    </select>
    <select id="selectSMsgReplyPortion" resultMap="SMsgReplyResult">
        SELECT
            smr.msg_id,
            smr.last_reply_id,
            smr.reply_id,
            smr.reply_msg,
            smr.create_time,
            smr.last_reply_uid,
            smr.img,
            u_last.nick_name AS last_reply_uname,
            smr.reply_userid,
            u_reply.nick_name AS reply_user_name
        FROM
            s_msg_reply1 smr
        LEFT JOIN sys_user u_last ON smr.last_reply_uid = u_last.user_id
        LEFT JOIN sys_user u_reply ON smr.reply_userid = u_reply.user_id
        where smr.msg_id = #{msgId}
        ORDER BY smr.create_time Desc
        limit 0,3
    </select>
    <select id="selectSMsgReplyAll" resultMap="SMsgReplyResult">
        <include refid="selectSMsgReplyVo"/>
        where msg_id = #{msgId}
        ORDER BY create_time Desc
    </select>
    <select id="selectUserById" resultMap="SysUserResult">
        select u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.avatar, u.phonenumber, u.password, u.sex, u.status,
        u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark,u.identity_card,u.auth_status, u.university
        ,u.college,u.specialty, u.job_id,u.student_id,u.grade,
        d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.status as dept_status,
        r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status
        from sys_user u
		    left join sys_dept d on u.dept_id = d.dept_id
		    left join sys_user_role ur on u.user_id = ur.user_id
		    left join sys_role r on r.role_id = ur.role_id
        where u.user_id = #{userId}
    </select>

    <insert id="insertSMsgReply" parameterType="com.ruoyi.create.domain.SMsgReply">
        insert into s_msg_reply1
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="msgId != null">msg_id,</if>
            <if test="lastReplyId != null">last_reply_id,</if>
            <if test="replyId != null">reply_id,</if>
            <if test="replyMsg != null">reply_msg,</if>
            <if test="createTime != null">create_time,</if>
            <if test="lastReplyUid != null">last_reply_uid,</if>
            <if test="lastReplyUname != null">last_reply_uname,</if>
            <if test="replyUserid != null">reply_userid,</if>
            <if test="replyUserName != null">reply_user_name,</if>
            <if test="img != null">img,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="msgId != null">#{msgId},</if>
            <if test="lastReplyId != null">#{lastReplyId},</if>
            <if test="replyId != null">#{replyId},</if>
            <if test="replyMsg != null">#{replyMsg},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="lastReplyUid != null">#{lastReplyUid},</if>
            <if test="lastReplyUname != null">#{lastReplyUname},</if>
            <if test="replyUserid != null">#{replyUserid},</if>
            <if test="replyUserName != null">#{replyUserName},</if>
            <if test="img != null">#{img},</if>
         </trim>
    </insert>

    <update id="updateSMsgReply" parameterType="com.ruoyi.create.domain.SMsgReply">
        update s_msg_reply1
        <trim prefix="SET" suffixOverrides=",">
            <if test="lastReplyId != null">last_reply_id = #{lastReplyId},</if>
            <if test="replyId != null">reply_id = #{replyId},</if>
            <if test="replyMsg != null">reply_msg = #{replyMsg},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="lastReplyUid != null">last_reply_uid = #{lastReplyUid},</if>
            <if test="lastReplyUname != null">last_reply_uname = #{lastReplyUname},</if>
            <if test="replyUserid != null">reply_userid = #{replyUserid},</if>
            <if test="replyUserName != null">reply_user_name = #{replyUserName},</if>
            <if test="img != null">img = #{img},</if>
        </trim>
        where msg_id = #{msgId}
    </update>

    <delete id="deleteSMsgReplyByMsgId" parameterType="Long">
        delete from s_msg_reply1 where msg_id = #{msgId}
    </delete>

    <delete id="deleteSMsgReplyByMsgIds" parameterType="String">
        delete from s_msg_reply1 where msg_id in
        <foreach item="msgId" collection="array" open="(" separator="," close=")">
            #{msgId}
        </foreach>
    </delete>
    <delete id="deleteSMsgReplyByReplyIds">
        delete from s_msg_reply1 where reply_id in
        <foreach item="replyId" collection="array" open="(" separator="," close=")">
            #{replyId}
        </foreach>
    </delete>
</mapper>