<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.UserVoiceTypeMapper">

    <resultMap type="UserVoiceType" id="UserVoiceTypeResult">
        <result property="id"    column="id"    />
        <result property="userName"    column="user_name"    />
        <result property="figureId"    column="figure_id"    />
        <result property="per"    column="per"    />
        <result property="voiceType"    column="voice_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectUserVoiceTypeVo">
        select id, user_name, figure_id, per,voice_type, create_by, create_time, update_by, update_time from plat_user_figure
    </sql>

    <select id="selectUserVoiceTypeList" parameterType="UserVoiceType" resultMap="UserVoiceTypeResult">
        <include refid="selectUserVoiceTypeVo"/>
        <where>
            <if test="userName != null  and userName != ''"> and user_name = #{userName}</if>
            <if test="figureId != null  and figureId != ''"> and figure_id = #{figureId}</if>
            <if test="per != null  and per != ''"> and per = #{per}</if>
            <if test="voiceType != null  and voiceType != ''"> and voice_type = #{voiceType}</if>
        </where>
    </select>

    <select id="selectUserVoiceTypeById" parameterType="Long" resultMap="UserVoiceTypeResult">
        <include refid="selectUserVoiceTypeVo"/>
        where id = #{id}
    </select>

    <insert id="insertUserVoiceType" parameterType="UserVoiceType" useGeneratedKeys="true" keyProperty="id">
        insert into plat_user_figure
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userName != null">user_name,</if>
            <if test="figureId != null">figure_id,</if>
            <if test="per != null">per,</if>
            <if test="voiceType != null">voice_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userName != null">#{userName},</if>
            <if test="figureId != null">#{figureId},</if>
            <if test="per != null">#{per},</if>
            <if test="voiceType != null">#{voiceType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateUserVoiceType" parameterType="UserVoiceType">
        update plat_user_figure
        <trim prefix="SET" suffixOverrides=",">
            <if test="userName != null">user_name = #{userName},</if>
            <if test="figureId != null">figure_id = #{figureId},</if>
            <if test="per != null">per = #{per},</if>
            <if test="voiceType != null">voice_type = #{voiceType},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserVoiceTypeById" parameterType="Long">
        delete from plat_user_figure where id = #{id}
    </delete>

    <delete id="deleteUserVoiceTypeByIds" parameterType="String">
        delete from plat_user_figure where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectUserVoiceTypeByUserName" parameterType="String" resultMap="UserVoiceTypeResult">
        <include refid="selectUserVoiceTypeVo"/>
        where user_name = #{userName}
    </select>

    <update id="updateUserVoiceTypeByUserName" parameterType="UserVoiceType">
        update plat_user_figure
        <trim prefix="SET" suffixOverrides=",">
            <if test="voiceType != null">voice_type = #{voiceType},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where user_name = #{userName}
    </update>

    <update id="updateUserVoiceTypeByUserNameTmp" parameterType="String">
        update plat_user_figure
        <trim prefix="SET" suffixOverrides=",">
            <if test="voiceType != null">voice_type = #{voiceType},</if>
        </trim>
        where user_name = #{userName}
    </update>

</mapper>