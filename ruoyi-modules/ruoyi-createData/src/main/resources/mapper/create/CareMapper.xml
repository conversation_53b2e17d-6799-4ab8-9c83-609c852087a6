<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.CareMapper">
    
    <resultMap type="com.ruoyi.create.domain.Care" id="CareResult">
        <result property="id"    column="id"    />
        <result property="serviceId"    column="service_id"    />
        <result property="modelId"    column="model_id"    />
        <result property="modelVersionId"    column="model_version_id"    />
        <result property="name"    column="name"    />
        <result property="description"    column="description"    />
        <result property="url"    column="url"    />
        <result property="serviceType"    column="service_type"    />
        <result property="runStatus"    column="run_status"    />
        <result property="chargeStatus"    column="charge_status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCareVo">
        select id, service_id, model_id, model_version_id, name, description, url, service_type, run_status, charge_status, create_by, create_time, update_by, update_time from s_care
    </sql>

    <select id="selectCareList" parameterType="com.ruoyi.create.domain.Care" resultMap="CareResult">
        <include refid="selectCareVo"/>
        <where>  
            <if test="serviceId != null  and serviceId != ''"> and service_id = #{serviceId}</if>
            <if test="modelId != null  and modelId != ''"> and model_id = #{modelId}</if>
            <if test="modelVersionId != null  and modelVersionId != ''"> and model_version_id = #{modelVersionId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="url != null  and url != ''"> and url = #{url}</if>
            <if test="serviceType != null  and serviceType != ''"> and service_type = #{serviceType}</if>
            <if test="runStatus != null  and runStatus != ''"> and run_status = #{runStatus}</if>
            <if test="chargeStatus != null  and chargeStatus != ''"> and charge_status = #{chargeStatus}</if>
        </where>
    </select>
    
    <select id="selectCareById" parameterType="Long" resultMap="CareResult">
        <include refid="selectCareVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCare" parameterType="com.ruoyi.create.domain.Care" useGeneratedKeys="true" keyProperty="id">
        insert into s_care
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serviceId != null">service_id,</if>
            <if test="modelId != null">model_id,</if>
            <if test="modelVersionId != null">model_version_id,</if>
            <if test="name != null">name,</if>
            <if test="description != null">description,</if>
            <if test="url != null">url,</if>
            <if test="serviceType != null">service_type,</if>
            <if test="runStatus != null">run_status,</if>
            <if test="chargeStatus != null">charge_status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serviceId != null">#{serviceId},</if>
            <if test="modelId != null">#{modelId},</if>
            <if test="modelVersionId != null">#{modelVersionId},</if>
            <if test="name != null">#{name},</if>
            <if test="description != null">#{description},</if>
            <if test="url != null">#{url},</if>
            <if test="serviceType != null">#{serviceType},</if>
            <if test="runStatus != null">#{runStatus},</if>
            <if test="chargeStatus != null">#{chargeStatus},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCare" parameterType="com.ruoyi.create.domain.Care">
        update s_care
        <trim prefix="SET" suffixOverrides=",">
            <if test="serviceId != null">service_id = #{serviceId},</if>
            <if test="modelId != null">model_id = #{modelId},</if>
            <if test="modelVersionId != null">model_version_id = #{modelVersionId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="url != null">url = #{url},</if>
            <if test="serviceType != null">service_type = #{serviceType},</if>
            <if test="runStatus != null">run_status = #{runStatus},</if>
            <if test="chargeStatus != null">charge_status = #{chargeStatus},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCareById" parameterType="Long">
        delete from s_care where id = #{id}
    </delete>

    <delete id="deleteCareByIds" parameterType="String">
        delete from s_care where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>