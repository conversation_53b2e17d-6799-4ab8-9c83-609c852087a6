<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.UniversityMapper">
    <!-- 学校 -->
    <resultMap type="com.ruoyi.create.domain.University" id="UniversityResult">
        <result property="id" column="id"/>
        <result property="id" column="univer_id"/>
        <result property="name" column="univer_name_tree"/>
        <result property="univerName" column="univer_name"/>
        <result property="univerAddr" column="univer_addr"/>
        <result property="contact" column="contact"/>
        <result property="phone" column="phone"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="colleName" column="colle_name"/>
        <result property="majorName" column="major_name"/>
        <result property="className" column="class_name"/>
        <collection property="children" ofType="com.ruoyi.create.domain.CollegeInfo" resultMap="CollegeInfoResult"/>
    </resultMap>
    <!-- 学院 -->
    <resultMap type="com.ruoyi.create.domain.CollegeInfo" id="CollegeInfoResult">
        <result property="id" column="id"/>
        <result property="id" column="colle_id"/>
        <result property="name" column="colle_name_tree"/>
        <result property="colleName" column="colle_name"/>
        <result property="univerId" column="univer_id"/>
        <result property="colleAddr" column="colle_addr"/>
        <result property="contact" column="contact"/>
        <result property="phone" column="phone"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <collection property="children" ofType="com.ruoyi.create.domain.MajorInfo" resultMap="MajorInfoResult"/>
    </resultMap>
    <!-- 专业 -->
    <resultMap type="com.ruoyi.create.domain.MajorInfo" id="MajorInfoResult">
        <result property="id" column="id"/>
        <result property="id" column="major_id"/>
        <result property="name" column="major_name_tree"/>
        <result property="majorName" column="major_name"/>
        <result property="univerId" column="univer_id"/>
        <result property="colleId" column="colle_id"/>
        <result property="contact" column="contact"/>
        <result property="phone" column="phone"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <collection property="children" ofType="com.ruoyi.create.domain.ClassInfo" resultMap="ClassInfoResult"/>
    </resultMap>
    <!-- 班级 -->
    <resultMap type="com.ruoyi.create.domain.ClassInfo" id="ClassInfoResult">
        <result property="id" column="id"/>
        <result property="id" column="class_id"/>
        <result property="name" column="class_name_tree"/>
        <result property="className" column="class_name"/>
        <result property="univerId" column="univer_id"/>
        <result property="colleId" column="colle_id"/>
        <result property="majorId" column="major_id"/>
        <result property="classTeacher" column="class_teacher"/>
        <result property="phone" column="phone"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <resultMap type="com.ruoyi.create.Vo.AmbitTeacherVo" id="AmbitTeacherResult">
        <result property="id" column="id"/>
        <result property="majorId" column="major_id"/>
        <result property="disciplineId" column="discipline_id"/>
        <result property="name" column="name"/>
        <result property="sex" column="sex"/>
        <result property="title" column="title"/>
        <result property="education" column="education"/>
        <result property="researchDirection" column="research_direction"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="courseName" column="course_name"/>
        <result property="researchType" column="research_type"/>
        <result property="teacherId" column="teacher_id"/>
        <result property="colleId" column="colle_id"/>
        <result property="colleName" column="colle_name"/>
        <result property="univerId" column="univer_id"/>
        <result property="univerName" column="univer_name"/>
        <result property="isPersonCharge" column="is_person_charge"/>
        <result property="majorName" column="major_name"/>
        <result property="collegeId" column="college_id"/>
        <result property="collegeName" column="college_name"/>
    </resultMap>
    <sql id="selectUniversityVo">
        select id,
               univer_name,
               univer_addr,
               contact,
               phone,
               create_by,
               create_time,
               update_by,
               update_time
        from s_university
    </sql>

    <select id="selectUniversityList" parameterType="com.ruoyi.create.domain.University" resultMap="UniversityResult">
        <include refid="selectUniversityVo"/>
        <where>
            <if test="univerName != null  and univerName != ''">and univer_name like concat('%', #{univerName}, '%')
            </if>
            <if test="univerAddr != null  and univerAddr != ''">and univer_addr = #{univerAddr}</if>
            <if test="contact != null  and contact != ''">and contact = #{contact}</if>
            <if test="phone != null  and phone != ''">and phone = #{phone}</if>
        </where>
    </select>

    <select id="selectUniversityById" parameterType="Long" resultMap="UniversityResult">
        <include refid="selectUniversityVo"/>
        where id = #{id}
    </select>
    <select id="selectUniversityListAll" resultMap="UniversityResult">
        SELECT univ.id          AS univer_id,
               univ.univer_name AS univer_name_tree,
               colle.id         AS colle_id,
               colle.colle_name AS colle_name_tree,
               major.id         AS major_id,
               major.major_name AS major_name_tree,
               class.id         AS class_id,
               class.class_name AS class_name_tree
        FROM s_university univ
                 LEFT JOIN
             s_college_info colle ON colle.univer_id = univ.id
                 LEFT JOIN
             s_major_info major ON major.colle_id = colle.id
                 LEFT JOIN
             s_class_info class ON class.major_id = major.id;
    </select>
    <select id="selectListAll" resultMap="UniversityResult">
        SELECT
        univ.id          AS univer_id,
        univ.univer_name AS univer_name_tree,
        colle.id         AS colle_id,
        colle.colle_name AS colle_name_tree,
        major.id         AS major_id,
        major.major_name AS major_name_tree,
        class.id         AS class_id,
        class.class_name AS class_name_tree
        FROM s_university univ
        LEFT JOIN s_college_info colle ON colle.univer_id = univ.id
        LEFT JOIN s_major_info major ON major.colle_id = colle.id
        INNER JOIN s_class_info class ON class.major_id = major.id  <!-- 改为INNER JOIN -->
    </select>



    <select id="selectUniversityListallTeacher" resultMap="UniversityResult">
        SELECT univ.id          AS univer_id,
               univ.univer_name AS univer_name_tree,
               colle.id         AS colle_id,
               colle.colle_name AS colle_name_tree,
               major.id         AS major_id,
               major.major_name AS major_name_tree
        FROM s_university univ
                 LEFT JOIN
             s_college_info colle ON colle.univer_id = univ.id
                 LEFT JOIN
             s_major_info major ON major.colle_id = colle.id
    </select>


    <select id="getUniversityInfo" resultMap="UniversityResult">
        SELECT
        univ.id AS univer_id,
        univ.univer_name AS univer_name,
        colle.id AS colle_id,
        colle.colle_name AS colle_name,
        major.id AS major_id,
        major.major_name AS major_name,
        class.id AS class_id,
        class.class_name AS class_name
        FROM
        s_university univ
        LEFT JOIN
        s_college_info colle ON colle.univer_id = univ.id
        LEFT JOIN
        s_major_info major ON major.colle_id = colle.id
        LEFT JOIN
        s_class_info class ON class.major_id = major.id
        <where>
            <if test="id != null and id != ''">and univ.id = #{id}</if>
            <if test="collegeId != null and collegeId != ''">and colle.id = #{collegeId}</if>
            <if test="majorId != null and majorId != ''">and major.id = #{majorId}</if>
            <if test="classId != null and classId != ''">and class.id = #{classId}</if>
        </where>
    </select>


    <insert id="insertUniversity" parameterType="com.ruoyi.create.domain.University" useGeneratedKeys="true"
            keyProperty="id">
        insert into s_university
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="univerName != null and univerName != ''">univer_name,</if>
            <if test="univerAddr != null and univerAddr != ''">univer_addr,</if>
            <if test="contact != null">contact,</if>
            <if test="phone != null">phone,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="univerName != null and univerName != ''">#{univerName},</if>
            <if test="univerAddr != null and univerAddr != ''">#{univerAddr},</if>
            <if test="contact != null">#{contact},</if>
            <if test="phone != null">#{phone},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateUniversity" parameterType="com.ruoyi.create.domain.University">
        update s_university
        <trim prefix="SET" suffixOverrides=",">
            <if test="univerName != null and univerName != ''">univer_name = #{univerName},</if>
            <if test="univerAddr != null and univerAddr != ''">univer_addr = #{univerAddr},</if>
            <if test="contact != null">contact = #{contact},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUniversityById" parameterType="Long">
        delete
        from s_university
        where id = #{id}
    </delete>

    <delete id="deleteUniversityByIds" parameterType="String">
        delete from s_university where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectUniversityListByUniversityId" resultMap="UniversityResult">
        SELECT
        univ.id          AS univer_id,
        univ.univer_name AS univer_name_tree,
        colle.id         AS colle_id,
        colle.colle_name AS colle_name_tree,
        major.id         AS major_id,
        major.major_name AS major_name_tree,
        class.id         AS class_id,
        class.class_name AS class_name_tree
        FROM s_university univ
        LEFT JOIN s_college_info colle ON colle.univer_id = univ.id
        LEFT JOIN s_major_info major ON major.colle_id = colle.id
        INNER JOIN s_class_info class ON class.major_id = major.id  <!-- 使用 INNER JOIN -->
        WHERE univ.id = #{universityId};
    </select>


    <select id="selectUniversityListById" resultMap="UniversityResult">
        SELECT
        univ.id          AS univer_id,
        univ.univer_name AS univer_name_tree,
        colle.id         AS colle_id,
        colle.colle_name AS colle_name_tree,
        major.id         AS major_id,
        major.major_name AS major_name_tree,
        class.id         AS class_id,
        class.class_name AS class_name_tree
        FROM s_university univ
        LEFT JOIN s_college_info colle ON colle.univer_id = univ.id
        LEFT JOIN s_major_info major ON major.colle_id = colle.id
        LEFT JOIN s_class_info class ON class.major_id = major.id
        WHERE univ.id = #{universityId}
        AND class.id IS NOT NULL;  <!-- 只有有班级的记录才显示 -->
    </select>


    <select id="selectUniversityListByUniverName" resultType="com.ruoyi.create.domain.University">
        SELECT *
        from s_university
        WHERE univer_name like concat('%', #{univerName}, '%')
    </select>
    <select id="selectAmbitTeacherVoListByCourseName" resultMap="AmbitTeacherResult">
        SELECT sat.*
        FROM s_ambit_teacher sat
                 JOIN s_union_courses suc ON sat.course_name = suc.course_name
        WHERE suc.course_name = #{courseName}
          AND FIND_IN_SET(sat.univer_id, suc.school_ids) > 0;
    </select>
</mapper>
