<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.KnowledgeSpecialityMapper">
    
    <resultMap type="KnowledgeSpeciality" id="KnowledgeSpecialityResult">
        <result property="id"    column="id"    />
        <result property="appId"    column="app_id"    />
        <result property="secretKey"    column="secret_key"    />
        <result property="universityId"    column="university_id"    />
        <result property="collegeId"    column="college_id"    />
        <result property="majorId"    column="major_id"    />
        <result property="universityName"    column="univer_name"    />
        <result property="collegeName"    column="colle_name"    />
        <result property="majorName"    column="major_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectKnowledgeSpecialityVo">
        select id, app_id, secret_key, university_id, college_id, major_id, create_by, create_time, update_by, update_time from s_knowledge_speciality
    </sql>

    <select id="selectKnowledgeSpecialityList" parameterType="KnowledgeSpeciality" resultMap="KnowledgeSpecialityResult">
        select t1.id, t1.app_id, t1.secret_key, t2.univer_name, t3.colle_name, t4.major_name
         from s_knowledge_speciality t1 left join s_university t2 on t1.university_id=t2.id
        left join s_college_info t3 on t1.college_id=t3.id
        left join s_major_info t4 on t1.major_id=t4.id
        <where>  
            <if test="appId != null  and appId != ''"> and t1.app_id = #{appId}</if>
            <if test="secretKey != null  and secretKey != ''"> and t1.secret_key = #{secretKey}</if>
            <if test="universityId != null "> and t1.university_id = #{universityId}</if>
            <if test="collegeId != null "> and t1.college_id = #{collegeId}</if>
            <if test="majorId != null "> and t1.major_id = #{majorId}</if>
        </where>
    </select>
    
    <select id="selectKnowledgeSpecialityById" parameterType="Long" resultMap="KnowledgeSpecialityResult">
        <include refid="selectKnowledgeSpecialityVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertKnowledgeSpeciality" parameterType="KnowledgeSpeciality" useGeneratedKeys="true" keyProperty="id">
        insert into s_knowledge_speciality
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appId != null">app_id,</if>
            <if test="secretKey != null">secret_key,</if>
            <if test="universityId != null">university_id,</if>
            <if test="collegeId != null">college_id,</if>
            <if test="majorId != null">major_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appId != null">#{appId},</if>
            <if test="secretKey != null">#{secretKey},</if>
            <if test="universityId != null">#{universityId},</if>
            <if test="collegeId != null">#{collegeId},</if>
            <if test="majorId != null">#{majorId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateKnowledgeSpeciality" parameterType="KnowledgeSpeciality">
        update s_knowledge_speciality
        <trim prefix="SET" suffixOverrides=",">
            <if test="appId != null">app_id = #{appId},</if>
            <if test="secretKey != null">secret_key = #{secretKey},</if>
            <if test="universityId != null">university_id = #{universityId},</if>
            <if test="collegeId != null">college_id = #{collegeId},</if>
            <if test="majorId != null">major_id = #{majorId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteKnowledgeSpecialityById" parameterType="Long">
        delete from s_knowledge_speciality where id = #{id}
    </delete>

    <delete id="deleteKnowledgeSpecialityByIds" parameterType="String">
        delete from s_knowledge_speciality where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectKnowledgeSpecialityByMajorInfoId" parameterType="Long" resultMap="KnowledgeSpecialityResult">
        <include refid="selectKnowledgeSpecialityVo"/>
        where major_id = #{id}
    </select>
</mapper>