<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.ExamineTxtMapper">

    <resultMap type="com.ruoyi.create.domain.ExamineTxt" id="ExamineTxtResult">
        <result property="id"    column="id"    />
        <result property="presentionId"    column="presention_id"    />
        <result property="isExamine"    column="is_examine"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="status"    column="status"    />
        <result property="suggestion"    column="suggestion"    />
    </resultMap>

    <sql id="selectExamineTxtVo">
        select id, presention_id, is_examine, create_time, create_by, update_time, update_by, status, suggestion from examine_txt
    </sql>

    <select id="selectExamineTxtList" parameterType="com.ruoyi.create.domain.ExamineTxt" resultMap="ExamineTxtResult">
        <include refid="selectExamineTxtVo"/>
        <where>
            <if test="presentionId != null "> and presention_id = #{presentionId}</if>
            <if test="isExamine != null "> and is_examine = #{isExamine}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="suggestion != null  and suggestion != ''"> and suggestion = #{suggestion}</if>
        </where>
    </select>

    <select id="selectExamineTxtById" parameterType="Long" resultMap="ExamineTxtResult">
        <include refid="selectExamineTxtVo"/>
        where id = #{id}
    </select>
    <select id="selectSuggestionByPId" parameterType="Long" resultType="java.lang.String">
        select suggestion from examine_txt
        where presention_id = #{id} and is_examine=0 and status=1
    </select>

    <insert id="insertExamineTxt" parameterType="com.ruoyi.create.domain.ExamineTxt" useGeneratedKeys="true" keyProperty="id">
        insert into examine_txt
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="presentionId != null">presention_id,</if>
            <if test="isExamine != null">is_examine,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="status != null">status,</if>
            <if test="suggestion != null">suggestion,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="presentionId != null">#{presentionId},</if>
            <if test="isExamine != null">#{isExamine},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="status != null">#{status},</if>
            <if test="suggestion != null">#{suggestion},</if>
         </trim>
    </insert>

    <update id="updateExamineTxt" parameterType="com.ruoyi.create.domain.ExamineTxt">
        update examine_txt
        <trim prefix="SET" suffixOverrides=",">
            <if test="presentionId != null">presention_id = #{presentionId},</if>
            <if test="isExamine != null">is_examine = #{isExamine},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="status != null">status = #{status},</if>
            <if test="suggestion != null">suggestion = #{suggestion},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteExamineTxtById" parameterType="Long">
        delete from examine_txt where id = #{id}
    </delete>
    <delete id="deleteExamineTxtByPId" parameterType="Long">
        delete from examine_txt where presention_id = #{id}
    </delete>

    <delete id="deleteExamineTxtByIds" parameterType="String">
        delete from examine_txt where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
