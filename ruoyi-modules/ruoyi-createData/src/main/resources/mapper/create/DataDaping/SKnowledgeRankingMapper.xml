<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.DataDaping.SKnowledgeRankingMapper">

    <resultMap type="SKnowledgeRanking" id="SKnowledgeRankingResult">
        <result property="id" column="id" />
        <result property="major" column="major" />
        <result property="course" column="course" />
        <result property="counts" column="counts" />
        <result property="ranks" column="ranks" />
        <result property="statisticsTime" column="statistics_time" />
    </resultMap>

    <sql id="selectSKnowledgeRankingVo">
        SELECT id, major, course, counts, ranks, statistics_time
        FROM s_daping_knowledge_ranking
    </sql>

    <select id="selectLatestKnowledgeRankingList" resultMap="SKnowledgeRankingResult">
        SELECT
            id,
            major,
            course,
            counts,
            ranks,
            statistics_time
        FROM s_daping_knowledge_ranking
        WHERE statistics_time = (
            SELECT MAX(statistics_time)
            FROM s_daping_knowledge_ranking
        )
        ORDER BY counts DESC
            LIMIT 10
    </select>


</mapper>
