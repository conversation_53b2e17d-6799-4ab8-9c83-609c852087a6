<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.DataDaping.SWisdomStatisticsMapper">
    
    <resultMap type="SWisdomStatistics" id="SWisdomStatisticsResult">
        <result property="id"    column="id"    />
        <result property="totalDuration"    column="total_duration"    />
        <result property="intelligentCount"    column="intelligent_count"    />
        <result property="studyCount"    column="study_count"    />
        <result property="coursewareCount"    column="courseware_count"    />
        <result property="statisticsTime"    column="statistics_time"    />
    </resultMap>

    <sql id="selectSWisdomStatisticsVo">
        select id, total_duration, intelligent_count, study_count, courseware_count, statistics_time from s_daping_wisdom_statistics
    </sql>

    <select id="selectLatestWisdomStatistics" resultMap="SWisdomStatisticsResult">
        SELECT id, total_duration, intelligent_count, study_count, courseware_count, statistics_time
        FROM s_daping_wisdom_statistics
        WHERE statistics_time = (
            SELECT MAX(statistics_time) FROM s_daping_wisdom_statistics
        )
    </select>



</mapper>