<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.DataDaping.SCourseKnowledgeMapper">
    
    <resultMap type="SCourseKnowledge" id="SCourseKnowledgeResult">
        <result property="id"    column="id"    />
        <result property="subject"    column="subject"    />
        <result property="counts"    column="counts"    />
        <result property="statisticsTime"    column="statistics_time"    />
    </resultMap>

    <sql id="selectSCourseKnowledgeVo">
        select id, subject, counts, statistics_time from s_daping_course_knowledge
    </sql>


<!--    查询最新的条目-->
    <select id="selectLatestSCourseKnowledge" resultMap="SCourseKnowledgeResult">
        SELECT id, subject, counts, statistics_time
        FROM s_daping_course_knowledge
        WHERE statistics_time = (
            SELECT MAX(statistics_time) FROM s_daping_course_knowledge
        )
    </select>






</mapper>