<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.DataDaping.SPlatformRealMapper">
    
    <resultMap type="SPlatformReal" id="SPlatformRealResult">
        <result property="id"    column="id"    />
        <result property="teacherCount"    column="teacher_count"    />
        <result property="studentCount"    column="student_count"    />
        <result property="courseCount"    column="course_count"    />
        <result property="textbookCount"    column="textbook_count"    />
        <result property="knowledgePointCount"    column="knowledge_point_count"    />
        <result property="qaCount"    column="qa_count"    />
        <result property="thesisCount"    column="thesis_count"    />
        <result property="implementedScenariosCount"    column="implemented_scenarios_count"    />
        <result property="implementedScenarios"    column="implemented_scenarios"    />
        <result property="voiceUsageCount"    column="voice_usage_count"    />
        <result property="pptGeneratedCount"    column="ppt_generated_count"    />
        <result property="taskCount"    column="task_count"    />
        <result property="userOnlineRate"    column="user_online_rate"    />
        <result property="datasetGenerationCount"    column="dataset_generation_count"    />
        <result property="datasetCount"    column="dataset_count"    />
        <result property="aiRate"    column="ai_rate"    />
        <result property="teachingRate"    column="teaching_rate"    />
        <result property="statisticsTime"    column="statistics_time"    />
    </resultMap>

    <sql id="selectSPlatformRealVo">
        select id, teacher_count, student_count, course_count, textbook_count, knowledge_point_count, qa_count, thesis_count, implemented_scenarios_count, implemented_scenarios, voice_usage_count, ppt_generated_count, task_count, user_online_rate, dataset_generation_count, dataset_count, ai_rate, teaching_rate, statistics_time from s_daping_platform_real
    </sql>

    <select id="selectLatestPlatformReal" resultMap="SPlatformRealResult">
        SELECT * FROM s_daping_platform_real
        WHERE statistics_time = (
            SELECT MAX(statistics_time) FROM s_daping_platform_real
        )
    </select>


</mapper>