<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.DataDaping.SWeeklyCompletionMapper">
    
    <resultMap type="SWeeklyCompletion" id="SWeeklyCompletionResult">
        <result property="id"    column="id"    />
        <result property="weekDay"    column="week_day"    />
        <result property="homeworkCount"    column="homework_count"    />
        <result property="lateHomeworkCount"    column="late_homework_count"    />
        <result property="incompleteHomeworkCount"    column="incomplete_homework_count"    />
        <result property="putHomework"    column="put_homework"    />
        <result property="putPpt"    column="put_ppt"    />
        <result property="statisticsTime"    column="statistics_time"    />
    </resultMap>

    <sql id="selectSWeeklyCompletionVo">
        select id, week_day, homework_count, late_homework_count, incomplete_homework_count, put_homework, put_ppt, statistics_time from s_daping_weekly_completion
    </sql>

    <select id="selectLatestWeeklyCompletionList" resultMap="SWeeklyCompletionResult">
        SELECT id, week_day, homework_count, late_homework_count, incomplete_homework_count, put_homework, put_ppt, statistics_time
        FROM s_daping_weekly_completion
        WHERE statistics_time = (
            SELECT MAX(statistics_time) FROM s_daping_weekly_completion
        )
        ORDER BY FIELD(week_day, 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun')
    </select>



</mapper>