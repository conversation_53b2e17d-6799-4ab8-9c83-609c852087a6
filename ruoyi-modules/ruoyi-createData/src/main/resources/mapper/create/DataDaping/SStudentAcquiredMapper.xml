<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.DataDaping.SStudentAcquiredMapper">
    
    <resultMap type="SStudentAcquired" id="SStudentAcquiredResult">
        <result property="id"    column="id"    />
        <result property="subject"    column="subject"    />
        <result property="counts"    column="counts"    />
        <result property="statisticsTime"    column="statistics_time"    />
    </resultMap>

    <sql id="selectSStudentAcquiredVo">
        select id, subject, counts, statistics_time from s_daping_student_acquired
    </sql>

    <select id="selectLatestStudentAcquiredList" resultMap="SStudentAcquiredResult">
        SELECT id, subject, counts, statistics_time
        FROM s_daping_student_acquired
        WHERE statistics_time = (
            SELECT MAX(statistics_time) FROM s_daping_student_acquired
        )
    </select>



</mapper>