<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.DataDaping.SStudentRankingMapper">
    
    <resultMap type="SStudentRanking" id="SStudentRankingResult">
        <result property="id"    column="id"    />
        <result property="ranks"    column="ranks"    />
        <result property="studentName"    column="student_name"    />
        <result property="institute"    column="institute"    />
        <result property="major"    column="major"    />
        <result property="studentClass"    column="student_class"    />
        <result property="statisticsTime"    column="statistics_time"    />
    </resultMap>

    <sql id="selectSStudentRankingVo">
        select id, ranks, student_name, institute, major, student_class, statistics_time from s_daping_student_ranking
    </sql>

    <select id="selectLatestStudentRankingList" resultMap="SStudentRankingResult">
        SELECT id, ranks, student_name, institute, major, student_class, statistics_time
        FROM s_daping_student_ranking
        WHERE statistics_time = (
            SELECT MAX(statistics_time) FROM s_daping_student_ranking
        )
        ORDER BY ranks ASC
    </select>


</mapper>