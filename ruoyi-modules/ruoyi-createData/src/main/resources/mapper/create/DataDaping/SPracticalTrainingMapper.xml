<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.DataDaping.SPracticalTrainingMapper">
    
    <resultMap type="SPracticalTraining" id="SPracticalTrainingResult">
        <result property="id"    column="id"    />
        <result property="scenarioName"    column="scenario_name"    />
        <result property="trainingParticipantsCount"    column="training_participants_count"    />
        <result property="statisticsTime"    column="statistics_time"    />
    </resultMap>

    <sql id="selectSPracticalTrainingVo">
        select id, scenario_name, training_participants_count, statistics_time from s_daping_practical_training
    </sql>

    <select id="selectLatestPracticalTrainingList" resultMap="SPracticalTrainingResult">
        SELECT id, scenario_name, training_participants_count, statistics_time
        FROM s_daping_practical_training
        WHERE statistics_time = (
            SELECT MAX(statistics_time) FROM s_daping_practical_training
        )
        ORDER BY training_participants_count DESC
            LIMIT 5
    </select>




</mapper>