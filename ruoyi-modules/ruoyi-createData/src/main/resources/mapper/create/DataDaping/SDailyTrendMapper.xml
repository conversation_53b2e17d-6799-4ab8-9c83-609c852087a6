<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.DataDaping.SDailyTrendMapper">
    
    <resultMap type="SDailyTrend" id="SDailyTrendResult">
        <result property="id"    column="id"    />
        <result property="weekDay"    column="week_day"    />
        <result property="checkInCount"    column="check_in_count"    />
        <result property="statisticsTime"    column="statistics_time"    />
    </resultMap>

    <sql id="selectSDailyTrendVo">
        select id, week_day, check_in_count, statistics_time from s_daping_daily_trend
    </sql>

    <select id="selectLatestDailyTrendList" resultMap="SDailyTrendResult">
        SELECT id, week_day, check_in_count, statistics_time
        FROM s_daping_daily_trend
        WHERE statistics_time = (
            SELECT MAX(statistics_time) FROM s_daping_daily_trend
        )
        ORDER BY FIELD(week_day, '星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日')
    </select>



</mapper>