<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.CourseMaterialsMapper">

    <resultMap type="com.ruoyi.create.domain.CourseMaterials" id="CourseMaterialsResult">
        <result property="id" column="id"/>
        <result property="studentCourseId" column="student_course_id"/>
        <result property="fileObjectName" column="file_object_name"/>
        <result property="type" column="type"/>
        <result property="lessonPlanName" column="lesson_plan_name"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="fileOriginName" column="file_origin_name"/>
        <result property="className" column="class_name"/>
    </resultMap>

    <sql id="selectCourseMaterialsVo">
        SELECT id,
               student_course_id,
               file_object_name,
               type,
               lesson_plan_name,
               remark,
               create_by,
               create_time,
               update_by,
               update_time
        FROM s_course_materials
    </sql>

    <select id="selectCourseMaterialsList" parameterType="com.ruoyi.create.domain.CourseMaterials"
            resultMap="CourseMaterialsResult">
        <include refid="selectCourseMaterialsVo"/>
        <where>
            <if test="type != null  and type != ''">and type = #{type}</if>
        </where>
    </select>

    <select id="selectCourseMaterialsById" parameterType="Long" resultMap="CourseMaterialsResult">
        SELECT scm.*,
               sfi.file_origin_name,
               suer.nick_name AS createByName
        FROM s_course_materials scm
                 LEFT JOIN sys_file_info sfi ON sfi.file_object_name = scm.file_object_name
                 LEFT JOIN sys_user suer ON suer.user_id = scm.create_by AND suer.del_flag = '0'
        WHERE scm.id = #{id}
    </select>
    <select id="selectCourseMaterialsListAndFileName" resultMap="CourseMaterialsResult">
        SELECT scm.* ,
        sfi.file_origin_name
        FROM
        s_course_materials scm
        LEFT JOIN sys_file_info sfi ON sfi.file_object_name = scm.file_object_name
        <where>
            <if test="fileOriginName!=null and fileOriginName !=''">AND sfi.file_origin_name like
                concat('%',#{fileOriginName},'%')
            </if>
            <if test="lessonPlanName !=null and lessonPlanName != ''">AND scm.lesson_plan_name like
                concat('%',#{lessonPlanName},'%')
            </if>
        </where>
    </select>
    <select id="selectCourseMaterialsListAndFileNameAll" resultType="com.ruoyi.create.domain.CourseMaterials">
        SELECT scm.* ,
        sfi.file_origin_name
        FROM
        s_course_materials scm
        LEFT JOIN sys_file_info sfi ON sfi.file_object_name = scm.file_object_name
        <where>
            <if test="type !=null and type !=''">AND scm.type = #{type}</if>
            <if test="fileOriginName!=null and fileOriginName !=''">AND sfi.file_origin_name like
                concat('%',#{fileOriginName},'%')
            </if>
            <if test="lessonPlanName !=null and lessonPlanName != ''">AND scm.lesson_plan_name like
                concat('%',#{lessonPlanName},'%')
            </if>
        </where>
    </select>
    <select id="selectCourseMaterialsListForTeacher" resultType="com.ruoyi.create.domain.CourseMaterials">
        SELECT scm.*,
        sfi.file_origin_name
        FROM s_course_materials scm
        JOIN s_student_course ssc ON ssc.id = scm.student_course_id
        JOIN sys_file_info sfi ON sfi.file_object_name = scm.file_object_name
        <where>
            AND scm.student_course_id = #{loginTeacherId}
            <if test="type !=null and type !=''">AND scm.type = #{type}</if>
            <if test="fileOriginName!=null and fileOriginName !=''">AND sfi.file_origin_name like
                concat('%',#{fileOriginName},'%')
            </if>
            <if test="lessonPlanName !=null and lessonPlanName != ''">AND scm.lesson_plan_name like
                concat('%',#{lessonPlanName},'%')
            </if>
        </where>
        ORDER BY scm.id,ssc.id
    </select>
    <select id="selectCourseMaterialsListForStudent" resultType="com.ruoyi.create.domain.CourseMaterials">
        SELECT scm.*,sfi.file_origin_name
        FROM s_course_materials scm
        JOIN sys_file_info sfi ON sfi.file_object_name = scm.file_object_name
        JOIN s_strudent_course_info ssci ON ssci.id = scm.student_course_id
        AND ssci.student_id = #{studentCourseId}
        <where>
            <if test="type !=null and type !=''">AND scm.type = #{type}</if>
            <if test="fileOriginName!=null and fileOriginName !=''">AND sfi.file_origin_name like
                concat('%',#{fileOriginName},'%')
            </if>
            <if test="lessonPlanName !=null and lessonPlanName != ''">AND scm.lesson_plan_name like
                concat('%',#{lessonPlanName},'%')
            </if>
        </where>
        ORDER BY scm.id,ssc.id
    </select>
    <select id="selectCourseMaterialsLis2" resultType="com.ruoyi.create.domain.CourseMaterials">
        SELECT scm.* ,
        sfi.file_origin_name,
        sfi.file_path AS filePath,
        suer.nick_name AS createByName
        FROM
        s_course_materials scm
        LEFT JOIN sys_file_info sfi ON sfi.file_object_name = scm.file_object_name
        LEFT JOIN sys_user suer ON suer.user_id = scm.create_by AND suer.del_flag = '0'
        <where>
            AND scm.student_course_id = #{studentCourseId}
            <if test="type !=null and type !=''">AND scm.type = #{type}</if>
            <if test="fileOriginName!=null and fileOriginName !=''">AND sfi.file_origin_name like
                concat('%',#{fileOriginName},'%')
            </if>
            <if test="lessonPlanName !=null and lessonPlanName != ''">AND scm.lesson_plan_name like
                concat('%',#{lessonPlanName},'%')
            </if>
        </where>
    </select>
    <select id="selectBusiidSysFileInfoByCourseId" >
        select busi_id
        FROM sys_file_info
        WHERE file_object_name IN (
            SELECT file_object_name FROM s_course_materials WHERE student_course_id = #{id}
        );
    </select>
    <select id="selectFileObjectNameByCourseId" resultType="java.lang.String">
        SELECT file_object_name FROM s_course_materials WHERE student_course_id = #{id}
    </select>
    <select id="selectFileObjectNameById" resultType="java.lang.String">
        SELECT file_object_name FROM s_course_materials WHERE id = #{id}
    </select>

    <insert id="insertCourseMaterials" parameterType="com.ruoyi.create.domain.CourseMaterials">
        insert into s_course_materials
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="studentCourseId != null">student_course_id,</if>
            <if test="fileObjectName != null">file_object_name,</if>
            <if test="type != null">type,</if>
            <if test="lessonPlanName != null">lesson_plan_name,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="studentCourseId != null">#{studentCourseId},</if>
            <if test="fileObjectName != null">#{fileObjectName},</if>
            <if test="type != null">#{type},</if>
            <if test="lessonPlanName != null">#{lessonPlanName},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateCourseMaterials" parameterType="com.ruoyi.create.domain.CourseMaterials">
        update s_course_materials
        <trim prefix="SET" suffixOverrides=",">
            <if test="studentCourseId != null">student_course_id = #{studentCourseId},</if>
            <if test="fileObjectName != null">file_object_name = #{fileObjectName},</if>
            <if test="type != null">type = #{type},</if>
            <if test="lessonPlanName != null">lesson_plan_name = #{lessonPlanName},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCourseMaterialsById" parameterType="Long">
        DELETE
        FROM s_course_materials
        WHERE id = #{id}
    </delete>

    <delete id="deleteCourseMaterialsByIds" parameterType="String">
        delete from s_course_materials where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteCourseMaterialsByStudentCourseId" parameterType="Long">
        DELETE
        FROM s_course_materials
        WHERE student_course_id = #{id}
    </delete>
    <delete id="deleteSysFileInfoByCourseId">
        DELETE
        FROM sys_file_info
        WHERE file_object_name IN (
            SELECT file_object_name FROM s_course_materials WHERE student_course_id = #{id}
        );
    </delete>
    <delete id="deleteSysFileInfoById">
        DELETE
        FROM sys_file_info
        WHERE file_object_name IN (
            SELECT file_object_name FROM s_course_materials WHERE id = #{id}
        );
    </delete>
    <delete id="deleteCourseNotificationsByStudentCourseId" parameterType="Long">
        DELETE
        FROM s_course_notifications
        WHERE student_course_id  =#{id}
    </delete>
    <delete id="deletesCourseTopicsRepliesByCourseId">
        DELETE
        FROM s_course_topics_replies
        WHERE topic_id IN (
            SELECT id FROM s_course_topics WHERE student_course_id = #{id}
        );
    </delete>
    <delete id="deletesCourseTopicsByStudentCourseId">
        DELETE
        FROM s_course_topics
        WHERE student_course_id = #{id}
    </delete>
    <delete id="deletesSignInSessionsByIdAndName">
        DELETE
        FROM s_student_sign
        WHERE id IN (
            SELECT sign_id FROM s_student_sign WHERE
        <where>
            1=1
            <if test="teacherId != null and teacherId != ''"> and teacher_id = #{teacherId}</if>
            <if test="courseName != null and courseName != ''"> and course_name = #{courseName}</if>
        </where>
        );
    </delete>
    <delete id="deletesStudentSignByIdAndName">
        DELETE
        FROM s_student_sign
        <where>
            1=1
            <if test="teacherId != null and teacherId != ''"> and teacher_id = #{teacherId}</if>
            <if test="courseName != null and courseName != ''"> and course_name = #{courseName}</if>
        </where>
    </delete>
</mapper>
