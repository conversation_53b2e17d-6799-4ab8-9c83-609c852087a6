<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.create.mapper.ConsonantMapper">

    <resultMap type="com.ruoyi.create.domain.Consonant" id="ConsonantResult">
        <result property="id"    column="id"    />
        <result property="speechdraftId"    column="speechdraft_id"    />
        <result property="consonant"    column="consonant"    />
        <result property="indexPage"    column="index_page"    />
        <result property="indexSentence"    column="index_sentence"    />
        <result property="txtSentence" column="txt_sentence"/>
        <result property="motion" column="motion"/>
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectConsonantVo">
        select id, speechdraft_id, consonant, index_page, index_sentence,txt_sentence,motion, remark, status, update_time, create_time from s_consonant
    </sql>

    <select id="selectConsonantList" parameterType="com.ruoyi.create.domain.Consonant" resultMap="ConsonantResult">
        <include refid="selectConsonantVo"/>
        <where>
            <if test="speechdraftId != null  and speechdraftId != ''"> and speechdraft_id = #{speechdraftId}</if>
            <if test="consonant != null  and consonant != ''"> and consonant = #{consonant}</if>
            <if test="indexPage != null  and indexPage != ''"> and index_page = #{indexPage}</if>
            <if test="indexSentence != null  and indexSentence != ''"> and index_sentence = #{indexSentence}</if>
            <if test="txtSentence != null  and txtSentence != ''"> and txt_sentence = #{txtSentence}</if>
            <if test="motion != null  and motion != ''"> and motion = #{motion}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>

    <select id="selectConsonantById" parameterType="Long" resultMap="ConsonantResult">
        <include refid="selectConsonantVo"/>
        where id = #{id}
    </select>

    <insert id="insertConsonant" parameterType="com.ruoyi.create.domain.Consonant" useGeneratedKeys="true" keyProperty="id">
        insert into s_consonant
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="speechdraftId != null">speechdraft_id,</if>
            <if test="consonant != null">consonant,</if>
            <if test="indexPage != null">index_page,</if>
            <if test="indexSentence != null">index_sentence,</if>
            <if test="txtSentence != null">txt_sentence,</if>
            <if test="motion != null">motion,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="speechdraftId != null">#{speechdraftId},</if>
            <if test="consonant != null">#{consonant},</if>
            <if test="indexPage != null">#{indexPage},</if>
            <if test="indexSentence != null">#{indexSentence},</if>
            <if test="txtSentence != null">#{txtSentence},</if>
            <if test="motion != null">#{motion},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateConsonant" parameterType="com.ruoyi.create.domain.Consonant">
        update s_consonant
        <trim prefix="SET" suffixOverrides=",">
            <if test="speechdraftId != null">speechdraft_id = #{speechdraftId},</if>
            <if test="consonant != null">consonant = #{consonant},</if>
            <if test="indexPage != null">index_page = #{indexPage},</if>
            <if test="indexSentence != null">index_sentence = #{indexSentence},</if>
            <if test="txtSentence != null">txt_sentence = #{txtSentence},</if>
            <if test="motion != null">motion = #{motion},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteConsonantById" parameterType="Long">
        delete from s_consonant where id = #{id}
    </delete>

    <delete id="deleteConsonantByPresationId" parameterType="Long">
        delete from s_consonant where speechdraft_id = #{id}
    </delete>

    <delete id="deleteConsonantByIds" parameterType="String">
        delete from s_consonant where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateSpeechdraftId" parameterType="map">
        UPDATE s_consonant
        SET speechdraft_id = #{newSpeechdraftId}
        WHERE speechdraft_id = #{oldSpeechdraftId}
    </update>
</mapper>
