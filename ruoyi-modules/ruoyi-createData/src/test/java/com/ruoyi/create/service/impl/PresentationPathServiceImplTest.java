package com.ruoyi.create.service.impl;

import com.spire.presentation.FileFormat;
import com.spire.presentation.Presentation;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;
/**
*@BelongsProject: large-model-end
*@BelongsPackage: com.ruoyi.create.service.impl
*@Author: zhao_tian_qi
*@CreateTime: 2024-11-15  17:09
*@Description: TODO
*@Version: 1.0
*/class PresentationPathServiceImplTest {
    @Test
    public void te() throws Exception {
        Presentation newppt = new Presentation();
        newppt.loadFromFile("D:\\ruoyi\\uploadDataPath\\ppt\\pptx\\132\\20241115\\2068854943\\slide_3.pptx");
        String format = "D:\\ruoyi\\uploadDataPath\\ppt\\pptx\\132\\20241115\\2068854943\\slide_3.html";
        newppt.saveToFile(format, FileFormat.HTML);

    }

}
