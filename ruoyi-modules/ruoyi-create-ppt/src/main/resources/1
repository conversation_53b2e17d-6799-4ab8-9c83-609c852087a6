D:\codeSpace\tools\jdk\jdk-1.8\bin\java.exe "-javaagent:D:\codeSpace\tools\IntelliJ IDEA 2024.1\lib\idea_rt.jar=60053:D:\codeSpace\tools\IntelliJ IDEA 2024.1\bin" -Dfile.encoding=UTF-8 -classpath D:\codeSpace\tools\jdk\jdk-1.8\jre\lib\charsets.jar;D:\codeSpace\tools\jdk\jdk-1.8\jre\lib\deploy.jar;D:\codeSpace\tools\jdk\jdk-1.8\jre\lib\ext\access-bridge-64.jar;D:\codeSpace\tools\jdk\jdk-1.8\jre\lib\ext\cldrdata.jar;D:\codeSpace\tools\jdk\jdk-1.8\jre\lib\ext\dnsns.jar;D:\codeSpace\tools\jdk\jdk-1.8\jre\lib\ext\jaccess.jar;D:\codeSpace\tools\jdk\jdk-1.8\jre\lib\ext\jfxrt.jar;D:\codeSpace\tools\jdk\jdk-1.8\jre\lib\ext\localedata.jar;D:\codeSpace\tools\jdk\jdk-1.8\jre\lib\ext\nashorn.jar;D:\codeSpace\tools\jdk\jdk-1.8\jre\lib\ext\sunec.jar;D:\codeSpace\tools\jdk\jdk-1.8\jre\lib\ext\sunjce_provider.jar;D:\codeSpace\tools\jdk\jdk-1.8\jre\lib\ext\sunmscapi.jar;D:\codeSpace\tools\jdk\jdk-1.8\jre\lib\ext\sunpkcs11.jar;D:\codeSpace\tools\jdk\jdk-1.8\jre\lib\ext\zipfs.jar;D:\codeSpace\tools\jdk\jdk-1.8\jre\lib\javaws.jar;D:\codeSpace\tools\jdk\jdk-1.8\jre\lib\jce.jar;D:\codeSpace\tools\jdk\jdk-1.8\jre\lib\jfr.jar;D:\codeSpace\tools\jdk\jdk-1.8\jre\lib\jfxswt.jar;D:\codeSpace\tools\jdk\jdk-1.8\jre\lib\jsse.jar;D:\codeSpace\tools\jdk\jdk-1.8\jre\lib\management-agent.jar;D:\codeSpace\tools\jdk\jdk-1.8\jre\lib\plugin.jar;D:\codeSpace\tools\jdk\jdk-1.8\jre\lib\resources.jar;D:\codeSpace\tools\jdk\jdk-1.8\jre\lib\rt.jar;D:\codeSpace\source\CodeSource\sdyr\source\shanshi-large-model\ruoyi-modules\ruoyi-create-ppt\target\classes;D:\codeSpace\source\apache-maven-3.9.5\repository\e-iceblue\spire.presentation.free\5.1.0\spire.presentation.free-5.1.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\apache\poi\poi\5.2.2\poi-5.2.2.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\apache\commons\commons-math3\3.6.1\commons-math3-3.6.1.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\commons-io\commons-io\2.13.0\commons-io-2.13.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\zaxxer\SparseBitSet\1.2\SparseBitSet-1.2.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\apache\poi\poi-ooxml\5.2.2\poi-ooxml-5.2.2.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\apache\poi\poi-ooxml-lite\5.2.2\poi-ooxml-lite-5.2.2.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\apache\xmlbeans\xmlbeans\5.0.3\xmlbeans-5.0.3.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\xml-apis\xml-apis\1.4.01\xml-apis-1.4.01.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\github\virtuald\curvesapi\1.07\curvesapi-1.07.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\apache\poi\poi-scratchpad\5.2.2\poi-scratchpad-5.2.2.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\projectlombok\lombok\1.18.24\lombok-1.18.24.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;D:\codeSpace\source\CodeSource\sdyr\source\shanshi-large-model\ruoyi-common\ruoyi-common-swagger\target\classes;D:\codeSpace\source\apache-maven-3.9.5\repository\io\springfox\springfox-swagger2\3.0.0\springfox-swagger2-3.0.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\springfox\springfox-spi\3.0.0\springfox-spi-3.0.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\springfox\springfox-schema\3.0.0\springfox-schema-3.0.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\springfox\springfox-swagger-common\3.0.0\springfox-swagger-common-3.0.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\swagger\core\v3\swagger-annotations\2.1.2\swagger-annotations-2.1.2.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\springfox\springfox-spring-web\3.0.0\springfox-spring-web-3.0.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\github\classgraph\classgraph\4.8.83\classgraph-4.8.83.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\springfox\springfox-spring-webflux\3.0.0\springfox-spring-webflux-3.0.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\plugin\spring-plugin-core\2.0.0.RELEASE\spring-plugin-core-2.0.0.RELEASE.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\plugin\spring-plugin-metadata\2.0.0.RELEASE\spring-plugin-metadata-2.0.0.RELEASE.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\swagger\swagger-models\1.6.2\swagger-models-1.6.2.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\mapstruct\mapstruct\1.3.1.Final\mapstruct-1.3.1.Final.jar;D:\codeSpace\source\CodeSource\sdyr\source\shanshi-large-model\ruoyi-common\ruoyi-common-log\target\classes;D:\codeSpace\source\CodeSource\sdyr\source\shanshi-large-model\ruoyi-common\ruoyi-common-core\target\classes;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\cloud\spring-cloud-starter-openfeign\3.1.8\spring-cloud-starter-openfeign-3.1.8.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\cloud\spring-cloud-openfeign-core\3.1.8\spring-cloud-openfeign-core-3.1.8.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\github\openfeign\form\feign-form-spring\3.8.0\feign-form-spring-3.8.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\github\openfeign\form\feign-form\3.8.0\feign-form-3.8.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\commons-fileupload\commons-fileupload\1.5\commons-fileupload-1.5.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\github\openfeign\feign-core\11.10\feign-core-11.10.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\github\openfeign\feign-slf4j\11.10\feign-slf4j-11.10.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\cloud\spring-cloud-starter-loadbalancer\3.1.7\spring-cloud-starter-loadbalancer-3.1.7.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\cloud\spring-cloud-loadbalancer\3.1.7\spring-cloud-loadbalancer-3.1.7.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\projectreactor\reactor-core\3.4.34\reactor-core-3.4.34.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\projectreactor\addons\reactor-extra\3.4.10\reactor-extra-3.4.10.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\boot\spring-boot-starter-cache\2.7.18\spring-boot-starter-cache-2.7.18.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\stoyanr\evictor\1.0.0\evictor-1.0.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\spring-context-support\5.3.33\spring-context-support-5.3.33.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\spring-context\5.3.33\spring-context-5.3.33.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\spring-web\5.3.33\spring-web-5.3.33.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\alibaba\transmittable-thread-local\2.14.4\transmittable-thread-local-2.14.4.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\github\pagehelper\pagehelper-spring-boot-starter\2.0.0\pagehelper-spring-boot-starter-2.0.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\mybatis\spring\boot\mybatis-spring-boot-starter\2.3.1\mybatis-spring-boot-starter-2.3.1.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\mybatis\spring\boot\mybatis-spring-boot-autoconfigure\2.3.1\mybatis-spring-boot-autoconfigure-2.3.1.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\mybatis\mybatis\3.5.13\mybatis-3.5.13.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\github\pagehelper\pagehelper-spring-boot-autoconfigure\2.0.0\pagehelper-spring-boot-autoconfigure-2.0.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\github\pagehelper\pagehelper\6.0.0\pagehelper-6.0.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\github\jsqlparser\jsqlparser\4.5\jsqlparser-4.5.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\boot\spring-boot-starter-validation\2.7.18\spring-boot-starter-validation-2.7.18.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.83\tomcat-embed-el-9.0.83.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\hibernate\validator\hibernate-validator\6.2.5.Final\hibernate-validator-6.2.5.Final.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\jboss\logging\jboss-logging\3.4.3.Final\jboss-logging-3.4.3.Final.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\fasterxml\jackson\core\jackson-databind\2.13.5\jackson-databind-2.13.5.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\fasterxml\jackson\core\jackson-annotations\2.13.5\jackson-annotations-2.13.5.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\fasterxml\jackson\core\jackson-core\2.13.5\jackson-core-2.13.5.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\alibaba\fastjson2\fastjson2\2.0.43\fastjson2-2.0.43.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\jsonwebtoken\jjwt\0.9.1\jjwt-0.9.1.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\javax\servlet\javax.servlet-api\4.0.1\javax.servlet-api-4.0.1.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\swagger\swagger-annotations\1.6.2\swagger-annotations-1.6.2.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\baomidou\mybatis-plus-extension\3.5.5\mybatis-plus-extension-3.5.5.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\baomidou\mybatis-plus-core\3.5.5\mybatis-plus-core-3.5.5.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\baomidou\mybatis-plus-annotation\3.5.5\mybatis-plus-annotation-3.5.5.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\alibaba\easyexcel\2.1.1\easyexcel-2.1.1.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\cglib\cglib\3.1\cglib-3.1.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\ow2\asm\asm\4.2\asm-4.2.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\ehcache\ehcache\3.10.8\ehcache-3.10.8.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\javax\cache\cache-api\1.1.1\cache-api-1.1.1.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\cn\hutool\hutool-all\5.8.5\hutool-all-5.8.5.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\boot\spring-boot-starter-test\2.7.18\spring-boot-starter-test-2.7.18.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\boot\spring-boot-starter\2.7.18\spring-boot-starter-2.7.18.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\boot\spring-boot\2.7.18\spring-boot-2.7.18.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\boot\spring-boot-starter-logging\2.7.18\spring-boot-starter-logging-2.7.18.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\ch\qos\logback\logback-classic\1.2.12\logback-classic-1.2.12.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\ch\qos\logback\logback-core\1.2.12\logback-core-1.2.12.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\yaml\snakeyaml\1.30\snakeyaml-1.30.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\boot\spring-boot-test\2.7.18\spring-boot-test-2.7.18.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\boot\spring-boot-test-autoconfigure\2.7.18\spring-boot-test-autoconfigure-2.7.18.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\jayway\jsonpath\json-path\2.7.0\json-path-2.7.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\net\minidev\json-smart\2.4.11\json-smart-2.4.11.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\net\minidev\accessors-smart\2.4.11\accessors-smart-2.4.11.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\assertj\assertj-core\3.22.0\assertj-core-3.22.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\junit\jupiter\junit-jupiter\5.8.2\junit-jupiter-5.8.2.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\junit\jupiter\junit-jupiter-api\5.8.2\junit-jupiter-api-5.8.2.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\junit\platform\junit-platform-commons\1.8.2\junit-platform-commons-1.8.2.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\junit\jupiter\junit-jupiter-params\5.8.2\junit-jupiter-params-5.8.2.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\junit\jupiter\junit-jupiter-engine\5.8.2\junit-jupiter-engine-5.8.2.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\junit\platform\junit-platform-engine\1.8.2\junit-platform-engine-1.8.2.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\mockito\mockito-core\4.5.1\mockito-core-4.5.1.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\net\bytebuddy\byte-buddy\1.12.23\byte-buddy-1.12.23.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\net\bytebuddy\byte-buddy-agent\1.12.23\byte-buddy-agent-1.12.23.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\objenesis\objenesis\3.2\objenesis-3.2.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\mockito\mockito-junit-jupiter\4.5.1\mockito-junit-jupiter-4.5.1.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\spring-core\5.3.33\spring-core-5.3.33.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\spring-jcl\5.3.33\spring-jcl-5.3.33.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\spring-test\5.3.33\spring-test-5.3.33.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\github\ulisesbocchio\jasypt-spring-boot-starter\3.0.2\jasypt-spring-boot-starter-3.0.2.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\github\ulisesbocchio\jasypt-spring-boot\3.0.2\jasypt-spring-boot-3.0.2.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\jasypt\jasypt\1.9.3\jasypt-1.9.3.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\boot\spring-boot-starter-web\2.7.18\spring-boot-starter-web-2.7.18.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\boot\spring-boot-starter-json\2.7.18\spring-boot-starter-json-2.7.18.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.5\jackson-datatype-jdk8-2.13.5.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.5\jackson-datatype-jsr310-2.13.5.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.5\jackson-module-parameter-names-2.13.5.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\boot\spring-boot-starter-tomcat\2.7.18\spring-boot-starter-tomcat-2.7.18.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.83\tomcat-embed-core-9.0.83.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.83\tomcat-embed-websocket-9.0.83.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\spring-webmvc\5.3.33\spring-webmvc-5.3.33.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\spring-expression\5.3.33\spring-expression-5.3.33.jar;D:\codeSpace\source\CodeSource\sdyr\source\shanshi-large-model\ruoyi-common\ruoyi-common-security\target\classes;D:\codeSpace\source\CodeSource\sdyr\source\shanshi-large-model\ruoyi-api\ruoyi-api-system\target\classes;D:\codeSpace\source\CodeSource\sdyr\source\shanshi-large-model\ruoyi-common\ruoyi-common-redis\target\classes;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\boot\spring-boot-starter-data-redis\2.7.18\spring-boot-starter-data-redis-2.7.18.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\data\spring-data-redis\2.7.18\spring-data-redis-2.7.18.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\data\spring-data-keyvalue\2.7.18\spring-data-keyvalue-2.7.18.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\data\spring-data-commons\2.7.18\spring-data-commons-2.7.18.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\spring-oxm\5.3.33\spring-oxm-5.3.33.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\lettuce\lettuce-core\6.1.10.RELEASE\lettuce-core-6.1.10.RELEASE.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-discovery\2021.0.5.0\spring-cloud-starter-alibaba-nacos-discovery-2021.0.5.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\alibaba\cloud\spring-cloud-alibaba-commons\2021.0.5.0\spring-cloud-alibaba-commons-2021.0.5.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\alibaba\nacos\nacos-client\2.2.0\nacos-client-2.2.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\alibaba\nacos\nacos-auth-plugin\2.2.0\nacos-auth-plugin-2.2.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\alibaba\nacos\nacos-encryption-plugin\2.2.0\nacos-encryption-plugin-2.2.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\apache\httpcomponents\httpasyncclient\4.1.5\httpasyncclient-4.1.5.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\apache\httpcomponents\httpcore-nio\4.4.16\httpcore-nio-4.4.16.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\apache\httpcomponents\httpclient\4.5.14\httpclient-4.5.14.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\prometheus\simpleclient\0.15.0\simpleclient-0.15.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\prometheus\simpleclient_tracer_otel\0.15.0\simpleclient_tracer_otel-0.15.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\prometheus\simpleclient_tracer_common\0.15.0\simpleclient_tracer_common-0.15.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\prometheus\simpleclient_tracer_otel_agent\0.15.0\simpleclient_tracer_otel_agent-0.15.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\alibaba\spring\spring-context-support\1.0.11\spring-context-support-1.0.11.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\cloud\spring-cloud-commons\3.1.7\spring-cloud-commons-3.1.7.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\security\spring-security-crypto\5.7.11\spring-security-crypto-5.7.11.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\cloud\spring-cloud-context\3.1.7\spring-cloud-context-3.1.7.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-config\2021.0.5.0\spring-cloud-starter-alibaba-nacos-config-2021.0.5.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\boot\spring-boot-starter-aop\2.7.18\spring-boot-starter-aop-2.7.18.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\spring-aop\5.3.33\spring-aop-5.3.33.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\net\sf\saxon\Saxon-HE\10.6\Saxon-HE-10.6.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\baomidou\mybatis-plus-boot-starter\3.5.3.1\mybatis-plus-boot-starter-3.5.3.1.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\baomidou\mybatis-plus\3.5.3.1\mybatis-plus-3.5.3.1.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\boot\spring-boot-autoconfigure\2.7.18\spring-boot-autoconfigure-2.7.18.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\boot\spring-boot-starter-jdbc\2.7.18\spring-boot-starter-jdbc-2.7.18.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\spring-jdbc\5.3.33\spring-jdbc-5.3.33.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\mysql\mysql-connector-j\8.0.33\mysql-connector-j-8.0.33.jar;D:\codeSpace\source\CodeSource\sdyr\source\shanshi-large-model\ruoyi-common\ruoyi-common-datasource\target\classes;D:\codeSpace\source\apache-maven-3.9.5\repository\com\alibaba\druid-spring-boot-starter\1.2.20\druid-spring-boot-starter-1.2.20.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\alibaba\druid\1.2.20\druid-1.2.20.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\glassfish\jaxb\jaxb-runtime\2.3.9\jaxb-runtime-2.3.9.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\glassfish\jaxb\txw2\2.3.9\txw2-2.3.9.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\sun\istack\istack-commons-runtime\3.0.12\istack-commons-runtime-3.0.12.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\baomidou\dynamic-datasource-spring-boot-starter\4.2.0\dynamic-datasource-spring-boot-starter-4.2.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\baomidou\dynamic-datasource-spring-boot-common\4.2.0\dynamic-datasource-spring-boot-common-4.2.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\baomidou\dynamic-datasource-spring\4.2.0\dynamic-datasource-spring-4.2.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\baomidou\dynamic-datasource-creator\4.2.0\dynamic-datasource-creator-4.2.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\apache\commons\commons-compress\1.26.2\commons-compress-1.26.2.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\javassist\javassist\3.20.0-GA\javassist-3.20.0-GA.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-sentinel\2021.0.5.0\spring-cloud-starter-alibaba-sentinel-2021.0.5.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\alibaba\csp\sentinel-transport-simple-http\1.8.6\sentinel-transport-simple-http-1.8.6.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\alibaba\csp\sentinel-transport-common\1.8.6\sentinel-transport-common-1.8.6.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\alibaba\csp\sentinel-datasource-extension\1.8.6\sentinel-datasource-extension-1.8.6.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\alibaba\fastjson\1.2.83_noneautotype\fastjson-1.2.83_noneautotype.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\alibaba\csp\sentinel-annotation-aspectj\1.8.6\sentinel-annotation-aspectj-1.8.6.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\alibaba\csp\sentinel-core\1.8.6\sentinel-core-1.8.6.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\alibaba\cloud\spring-cloud-circuitbreaker-sentinel\2021.0.5.0\spring-cloud-circuitbreaker-sentinel-2021.0.5.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\alibaba\csp\sentinel-reactor-adapter\1.8.6\sentinel-reactor-adapter-1.8.6.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\alibaba\csp\sentinel-spring-webflux-adapter\1.8.6\sentinel-spring-webflux-adapter-1.8.6.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\alibaba\csp\sentinel-spring-webmvc-adapter\1.8.6\sentinel-spring-webmvc-adapter-1.8.6.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\alibaba\csp\sentinel-parameter-flow-control\1.8.6\sentinel-parameter-flow-control-1.8.6.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\googlecode\concurrentlinkedhashmap\concurrentlinkedhashmap-lru\1.4.2\concurrentlinkedhashmap-lru-1.4.2.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\alibaba\csp\sentinel-cluster-server-default\1.8.6\sentinel-cluster-server-default-1.8.6.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\alibaba\csp\sentinel-cluster-common-default\1.8.6\sentinel-cluster-common-default-1.8.6.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\netty\netty-handler\4.1.101.Final\netty-handler-4.1.101.Final.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\netty\netty-common\4.1.101.Final\netty-common-4.1.101.Final.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\netty\netty-resolver\4.1.101.Final\netty-resolver-4.1.101.Final.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\netty\netty-buffer\4.1.101.Final\netty-buffer-4.1.101.Final.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\netty\netty-transport\4.1.101.Final\netty-transport-4.1.101.Final.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\netty\netty-transport-native-unix-common\4.1.101.Final\netty-transport-native-unix-common-4.1.101.Final.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\netty\netty-codec\4.1.101.Final\netty-codec-4.1.101.Final.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\alibaba\csp\sentinel-cluster-client-default\1.8.6\sentinel-cluster-client-default-1.8.6.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\alibaba\cloud\spring-cloud-alibaba-sentinel-datasource\2021.0.5.0\spring-cloud-alibaba-sentinel-datasource-2021.0.5.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\boot\spring-boot-starter-actuator\2.7.18\spring-boot-starter-actuator-2.7.18.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\2.7.18\spring-boot-actuator-autoconfigure-2.7.18.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\boot\spring-boot-actuator\2.7.18\spring-boot-actuator-2.7.18.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\micrometer\micrometer-core\1.9.17\micrometer-core-1.9.17.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\hdrhistogram\HdrHistogram\2.1.12\HdrHistogram-2.1.12.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\springfox\springfox-swagger-ui\3.0.0\springfox-swagger-ui-3.0.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\springfox\springfox-spring-webmvc\3.0.0\springfox-spring-webmvc-3.0.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\io\springfox\springfox-core\3.0.0\springfox-core-3.0.0.jar;D:\codeSpace\source\CodeSource\sdyr\source\shanshi-large-model\ruoyi-common\ruoyi-common-datascope\target\classes;D:\codeSpace\source\apache-maven-3.9.5\repository\com\aliyun\dysmsapi20170525\2.0.1\dysmsapi20170525-2.0.1.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\aliyun\tea-rpc\0.0.11\tea-rpc-0.0.11.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\aliyun\tea-rpc-util\0.1.3\tea-rpc-util-0.1.3.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\dom4j\dom4j\2.1.3\dom4j-2.1.3.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\aliyun\endpoint-util\0.0.6\endpoint-util-0.0.6.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\aliyun\tea-openapi\0.0.13\tea-openapi-0.0.13.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\aliyun\credentials-java\0.2.4\credentials-java-0.2.4.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\ini4j\ini4j\0.5.4\ini4j-0.5.4.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\sun\xml\bind\jaxb-core\2.3.0\jaxb-core-2.3.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\sun\xml\bind\jaxb-impl\2.3.0\jaxb-impl-2.3.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\aliyun\openapiutil\0.1.3\openapiutil-0.1.3.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\bouncycastle\bcpkix-jdk15on\1.65\bcpkix-jdk15on-1.65.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\bouncycastle\bcprov-jdk15on\1.65\bcprov-jdk15on-1.65.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\aliyun\tea-console\0.0.1\tea-console-0.0.1.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\aliyun\darabonba-env\0.1.1\darabonba-env-0.1.1.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\aliyun\tea-util\0.2.11\tea-util-0.2.11.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\google\code\gson\gson\2.9.1\gson-2.9.1.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\aliyun\darabonba-time\0.0.1\darabonba-time-0.0.1.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\jacoco\org.jacoco.agent\0.8.4\org.jacoco.agent-0.8.4-runtime.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\aliyun\darabonba-string\0.0.3\darabonba-string-0.0.3.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\aliyun\tea\1.3.2\tea-1.3.2.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\squareup\okhttp3\okhttp\4.9.3\okhttp-4.9.3.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\com\squareup\okio\okio\2.8.0\okio-2.8.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\jetbrains\kotlin\kotlin-stdlib-common\1.6.21\kotlin-stdlib-common-1.6.21.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\jetbrains\kotlin\kotlin-stdlib\1.6.21\kotlin-stdlib-1.6.21.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\jetbrains\annotations\13.0\annotations-13.0.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\commons-lang\commons-lang\2.6\commons-lang-2.6.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\net\coobird\thumbnailator\0.4.8\thumbnailator-0.4.8.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\spring-tx\5.3.33\spring-tx-5.3.33.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\spring-beans\5.3.33\spring-beans-5.3.33.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\mybatis\mybatis-spring\2.0.3\mybatis-spring-2.0.3.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\cloud\spring-cloud-starter-bootstrap\3.1.7\spring-cloud-starter-bootstrap-3.1.7.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\cloud\spring-cloud-starter\3.1.7\spring-cloud-starter-3.1.7.jar;D:\codeSpace\source\apache-maven-3.9.5\repository\org\springframework\security\spring-security-rsa\1.0.11.RELEASE\spring-security-rsa-1.0.11.RELEASE.jar com.ruoyi.ppt.controller.CareerPlanProcessor
大纲格式不规范，尝试自动修复...
修复完成，继续处理。
{"type":"cover","data":{"title":"大学生职业生涯规划","text":"助力大学生规划职业发展之路"}}
{"type":"contents","data":{"items":["评估与调整","认识职业生涯规划","制定行动计划","自我评估","职业环境分析","设定职业目标"]}}
{"type":"transition","data":{"title":"评估与调整","text":"确保职业规划有效实施"}}
{"type":"content","data":{"title":"评估指标","items":[{"title":"职业目标的达","text":"这表明 职业目标的达成情况，设定明确且可衡量的目标，为职业生涯提供清晰的方向指引和动力来源。"},{"title":"个人满意度和成就感","text":"这要求我们 个人满意度和成就感，重视这一点能够帮助我们在职业生涯中更加从容应对各种机遇与挑战。"}]}}
{"type":"content","data":{"title":"评估周期","items":[{"title":"评估","text":"这强调了 定期进行阶段性评估，分析过程应收集多方面信息，考虑各种可能性，避免片面判断。"},{"title":"评估周期","text":"这要求我们 评估周期的核心要素，定期进行分析和反思，能够及时发现问题并做出调整，保持职业发展的正确方向。"}]}}
{"type":"content","data":{"title":"调整策略","items":[{"title":"调整目标和计","text":"这意味着 根据评估结果调整目标和计划，通过系统化的规划和持续的努力，能够更有效地达成职业目标，提升个人发展空间。"},{"title":"应对职业发展中的变化","text":"这强调了 应对职业发展中的变化和挑战，在职业发展过程中，应当关注这一方面并不断完善，以适应职场的变化与挑战。"}]}}
{"type":"transition","data":{"title":"认识职业生涯规划","text":"了解规划定义、重要性及与大学生关联"}}
{"type":"content","data":{"title":"职业生涯规划的定义","items":[{"title":"计划","text":"规划是对个人职业发展的长期计划，这需要明确的时间表和具体步骤，定期回顾和调整，确保规划的有效实施。"},{"title":"职业目标、发","text":"这意味着 涵盖职业目标、发展路径等方面，目标应当具有挑战性但又切实可行，既能激发潜能，又不至于造成挫折感。"}]}}
{"type":"content","data":{"title":"规划与大学生的关联性","items":[{"title":"帮助大学生顺利过渡到","text":"这要求我们 帮助大学生顺利过渡到职场，重视这一点能够帮助我们在职业生涯中更加从容应对各种机遇与挑战。"},{"title":"提升大学学习的针对性","text":"这反映出 提升大学学习的针对性和有效性，在职业发展过程中，应当关注这一方面并不断完善，以适应职场的变化与挑战。"}]}}
{"type":"content","data":{"title":"规划的重要性","items":[{"title":"职业方向，避","text":"这反映出 明确职业方向，避免盲目探索，目标应当具有挑战性但又切实可行，既能激发潜能，又不至于造成挫折感。"},{"title":"提高就业竞争力","text":"这表明 提高就业竞争力，重视这一点能够帮助我们在职业生涯中更加从容应对各种机遇与挑战。"},{"title":"能力","text":"这反映出 增强自我认知和自我管理能力，掌握核心技能是职业发展的关键，需要不断学习和实践，保持竞争优势。"}]}}
{"type":"transition","data":{"title":"制定行动计划","text":"规划各阶段具体行动方案"}}
{"type":"content","data":{"title":"毕业后初期计划","items":[{"title":"求职策略和渠","text":"这表明 求职策略和渠道选择，重视这一点能够帮助我们在职业生涯中更加从容应对各种机遇与挑战。"},{"title":"计划","text":"这表明 入职后的适应和发展计划，通过系统化的规划和持续的努力，能够更有效地达成职业目标，提升个人发展空间。"}]}}
{"type":"content","data":{"title":"大学期间计划","items":[{"title":"课程学习安排","text":"这强调了 课程学习安排，通过系统化的规划和持续的努力，能够更有效地达成职业目标，提升个人发展空间。"},{"title":"社团活动和志愿者经历","text":"这反映出 社团活动和志愿者经历规划，在职业发展过程中，应当关注这一方面并不断完善，以适应职场的变化与挑战。"},{"title":"计划","text":"这意味着 职业技能培训和考证计划，合理的规划能够帮助我们避免资源浪费，集中精力在最重要的目标上。"}]}}
{"type":"content","data":{"title":"持续发展计划","items":[{"title":"估和目标调整","text":"这要求我们 定期的自我评估和目标调整，阶段性目标的设定和达成，能够给予我们成就感和前进的动力。"},{"title":"参加行业培训和进修提","text":"这意味着 参加行业培训和进修提升，重视这一点能够帮助我们在职业生涯中更加从容应对各种机遇与挑战。"}]}}
{"type":"transition","data":{"title":"自我评估","text":"从多方面认识自己，为职业规划奠基"}}
{"type":"content","data":{"title":"技能盘点","items":[{"title":"专业技能掌握","text":"这意味着 专业技能掌握情况，技能培养需要理论学习与实践应用相结合，在真实场景中检验和提升。"},{"title":"通用技能如沟","text":"这强调了 通用技能如沟通、团队协作等，在当今快速变化的职场环境中，技能更新迭代速度加快，持续学习尤为重要。"}]}}
{"type":"content","data":{"title":"价值观澄清","items":[{"title":"职业价值，如","text":"个人看重的职业价值，如薪资、稳定性等，重视这一点能够帮助我们在职业生涯中更加从容应对各种机遇与挑战。"},{"title":"价值观澄","text":"这意味着 价值观澄清的核心要素，重视这一点能够帮助我们在职业生涯中更加从容应对各种机遇与挑战。"}]}}
{"type":"content","data":{"title":"性格分析","items":[{"title":"外向或内向性格特点","text":"这要求我们 外向或内向性格特点，在职业发展过程中，应当关注这一方面并不断完善，以适应职场的变化与挑战。"},{"title":"不同性格适合的职业类","text":"这强调了 不同性格适合的职业类型，在职业发展过程中，应当关注这一方面并不断完善，以适应职场的变化与挑战。"}]}}
{"type":"content","data":{"title":"兴趣探索","items":[{"title":"个人兴趣爱好与职业的","text":"这表明 个人兴趣爱好与职业的契合度，这是职业规划中的重要环节，需认真对待并结合个人实际情况灵活调整。"},{"title":"如何将兴趣转化为职业","text":"这要求我们 如何将兴趣转化为职业动力，重视这一点能够帮助我们在职业生涯中更加从容应对各种机遇与挑战。"}]}}
{"type":"transition","data":{"title":"职业环境分析","text":"洞察行业、职业和企业情况"}}
{"type":"content","data":{"title":"企业调研","items":[{"title":"不同规模企业的特点和","text":"这表明 不同规模企业的特点和优势，这是职业规划中的重要环节，需认真对待并结合个人实际情况灵活调整。"},{"title":"目标企业","text":"这意味着 目标企业的文化和发展前景，设定明确且可衡量的目标，为职业生涯提供清晰的方向指引和动力来源。"}]}}
{"type":"content","data":{"title":"职业细分与要求","items":[{"title":"目标职业","text":"这要求我们 目标职业的具体工作内容，设定明确且可衡量的目标，为职业生涯提供清晰的方向指引和动力来源。"},{"title":"识、技能和经","text":"这反映出 所需的知识、技能和经验，技能培养需要理论学习与实践应用相结合，在真实场景中检验和提升。"}]}}
{"type":"content","data":{"title":"行业现状与趋势","items":[{"title":"热门行业的发展动态","text":"这表明 热门行业的发展动态，在职业发展过程中，应当关注这一方面并不断完善，以适应职场的变化与挑战。"},{"title":"新兴行业的崛起机会","text":"这表明 新兴行业的崛起机会，这是职业规划中的重要环节，需认真对待并结合个人实际情况灵活调整。"}]}}
{"type":"transition","data":{"title":"设定职业目标","text":"分阶段明确职业追求"}}
{"type":"content","data":{"title":"长期目标","items":[{"title":"职业生涯的终极理想","text":"这强调了 职业生涯的终极理想，这是职业规划中的重要环节，需认真对待并结合个人实际情况灵活调整。"},{"title":"个人在行业中的地位和","text":"这反映出 个人在行业中的地位和影响力，在职业发展过程中，应当关注这一方面并不断完善，以适应职场的变化与挑战。"}]}}
{"type":"content","data":{"title":"中期目标","items":[{"title":"方向","text":"毕业后1 - 5年的职业发展方向，目标应当具有挑战性但又切实可行，既能激发潜能，又不至于造成挫折感。"},{"title":"期望达到的职位和薪资","text":"这反映出 期望达到的职位和薪资水平，在职业发展过程中，应当关注这一方面并不断完善，以适应职场的变化与挑战。"}]}}
{"type":"content","data":{"title":"短期目标","items":[{"title":"目标","text":"这要求我们 大学期间的学业目标，目标应当具有挑战性但又切实可行，既能激发潜能，又不至于造成挫折感。"},{"title":"可参加的实践活动和实","text":"这表明 可参加的实践活动和实习机会，重视这一点能够帮助我们在职业生涯中更加从容应对各种机遇与挑战。"}]}}
{"type":"end"}

警告: 大纲格式不符合规范
错误: 小节 '评估周期' 下的内容项数量必须为2-4个，当前有 1 个。
错误: 小节 '价值观澄清' 下的内容项数量必须为2-4个，当前有 1 个。


进程已结束，退出代码为 0
