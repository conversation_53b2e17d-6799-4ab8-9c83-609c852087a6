package com.ruoyi.ppt.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.ppt.domain.PptPersonHistory;

/**
 * aippt ppt生成记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
public interface PptPersonHistoryMapper extends BaseMapper<PptPersonHistory>
{
    /**
     * 查询aippt ppt生成记录
     * 
     * @param id aippt ppt生成记录主键
     * @return aippt ppt生成记录
     */
    public PptPersonHistory selectPptPersonHistoryById(Long id);

    /**
     * 查询aippt ppt生成记录列表
     * 
     * @param pptPersonHistory aippt ppt生成记录
     * @return aippt ppt生成记录集合
     */
    public List<PptPersonHistory> selectPptPersonHistoryList(PptPersonHistory pptPersonHistory);

    /**
     * 新增aippt ppt生成记录
     * 
     * @param pptPersonHistory aippt ppt生成记录
     * @return 结果
     */
    public int insertPptPersonHistory(PptPersonHistory pptPersonHistory);

    /**
     * 修改aippt ppt生成记录
     * 
     * @param pptPersonHistory aippt ppt生成记录
     * @return 结果
     */
    public int updatePptPersonHistory(PptPersonHistory pptPersonHistory);

    /**
     * 删除aippt ppt生成记录
     * 
     * @param id aippt ppt生成记录主键
     * @return 结果
     */
    public int deletePptPersonHistoryById(Long id);

    /**
     * 批量删除aippt ppt生成记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePptPersonHistoryByIds(Long[] ids);

    PptPersonHistory getOneByTaskId(String taskId);
}
