package com.ruoyi.ppt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.ppt.domain.PptTemplateManagement;

import java.util.List;

/**
 * ppt模板（压缩包）管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-10-21
 */
public interface PptTemplateManagementMapper extends BaseMapper<PptTemplateManagement> {
    /**
     * 查询ppt模板（压缩包）管理
     *
     * @param id ppt模板管理主键
     * @return ppt模板管理
     */
    public PptTemplateManagement selectPptTemplateManagementById(Long id);

    /**
     * 查询ppt模板（压缩包）管理列表
     *
     * @param pptTemplateManagement ppt模板管理
     * @return ppt模板管理集合
     */
    public List<PptTemplateManagement> selectPptTemplateManagementList(PptTemplateManagement pptTemplateManagement);

    /**
     * 新增ppt模板（压缩包）管理
     *
     * @param pptTemplateManagement ppt模板管理
     * @return 结果
     */
    public int insertPptTemplateManagement(PptTemplateManagement pptTemplateManagement);

    /**
     * 修改ppt模板（压缩包）管理
     *
     * @param pptTemplateManagement ppt模板管理
     * @return 结果
     */
    public int updatePptTemplateManagement(PptTemplateManagement pptTemplateManagement);

    /**
     * 删除ppt模板（压缩包）管理
     *
     * @param id ppt模板管理主键
     * @return 结果
     */
    public int deletePptTemplateManagementById(Long id);

    /**
     * 批量删除ppt模板（压缩包）管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePptTemplateManagementByIds(Long[] ids);
}
