package com.ruoyi.ppt.utilsService;


import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.ppt.config.ConfigPrinterConfig;
import com.ruoyi.ppt.config.ThreadPoolDealTask;
import com.spire.presentation.FileFormat;
import com.spire.presentation.Presentation;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.xslf.usermodel.XMLSlideShow;
import org.apache.poi.xslf.usermodel.XSLFSlide;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.*;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Component
public class PPTToLongImage {


    private static final Logger log = LoggerFactory.getLogger(PPTToLongImage.class);
    @Resource
    private SetPptFontStyle setPptFontStyle;
    @Resource
    private ThreadPoolDealTask threadPoolDealTask;
    @Resource
    private FileCustomUtils fileCustomUtils;
    @Resource
    private ConfigPrinterConfig configPrinterConfig;
    //    public static void main(String[] args) {
//        String pptPath = "D:\\commonProgram\\Browser\\edge-download\\第12章城市摊贩市场规划与管理 (21).pptx";
//        String outputPath = "D:\\tmp\\t"+ "ssssLogImage.png";
//        generateLongImageFromPPT(pptPath, outputPath, 2.txt, 10, 10);
//
//    }

    /**
     * 长图输出
     *
     * @param pptPath           ppt
     * @param outputPath        输出
     * @param slidesPerRow      每行显示的个数
     * @param horizontalSpacing 水平间距
     * @param verticalSpacing   垂直间距
     * @param highImageQuality  是否高质量输出
     */
//    @LogExecutionTime("生成预览长图耗时")
    public void generateLongImageFromPPT(String pptPath, String outputPath,
                                         Integer slidesPerRow, Integer horizontalSpacing,
                                         Integer verticalSpacing, String highImageQuality) {
        if (StringUtils.isBlank(highImageQuality)) {
            highImageQuality = "false";
        }
        setPptFontStyle.dealPptFontsToImg(pptPath, "宋体");

        // 参数验证和默认值设置
        if (StringUtils.isBlank(pptPath)) throw new IllegalArgumentException("PPT 路径不能为空.");
        if (StringUtils.isBlank(outputPath)) throw new IllegalArgumentException("输出路径不能为空.");


        slidesPerRow = (slidesPerRow != null && slidesPerRow > 0) ? slidesPerRow : 1;
        horizontalSpacing = (horizontalSpacing != null && horizontalSpacing >= 0) ? horizontalSpacing : 0;
        verticalSpacing = (verticalSpacing != null && verticalSpacing >= 0) ? verticalSpacing : 0;

        try (FileInputStream fis = new FileInputStream(pptPath);
             XMLSlideShow ppt = new XMLSlideShow(fis)) {

            Dimension pgsize = ppt.getPageSize();
            int slideCount = ppt.getSlides().size();
            double scale = "true".equals(highImageQuality) ? 4.0 : 2.0;


            int scaledWidth = Math.max((int) (pgsize.width * scale), pgsize.width);
            int scaledHeight = Math.max((int) (pgsize.height * scale), pgsize.height);

            int totalWidth = Math.max((scaledWidth + horizontalSpacing) * slidesPerRow - horizontalSpacing, pgsize.width);
            int totalHeight = Math.max((scaledHeight + verticalSpacing) * ((int) Math.ceil((double) slideCount / slidesPerRow)) - verticalSpacing, pgsize.height);


            List<CompletableFuture<BufferedImage>> futures = new ArrayList<>();
            try {
                String finalHighImageQuality = highImageQuality;
                for (XSLFSlide slide : ppt.getSlides()) {
                    CompletableFuture<BufferedImage> future = CompletableFuture.supplyAsync(() -> {
                        BufferedImage slideImage = new BufferedImage(scaledWidth, scaledHeight, BufferedImage.TYPE_INT_RGB);
                        Graphics2D slideGraphics = slideImage.createGraphics();

                        dealHighImageQuality(finalHighImageQuality, slideGraphics);
                        slideGraphics.setPaint(Color.white);
                        slideGraphics.fillRect(0, 0, scaledWidth, scaledHeight);
                        slideGraphics.scale(scale, scale);

                        try {
                            slide.draw(slideGraphics);
                        } catch (Exception e) {
                            log.error("绘制幻灯片 {} 第 {} 时出错: {}", pptPath, slide.getSlideNumber(), e.getMessage());
                        } finally {
                            slideGraphics.dispose(); // 释放 Graphics2D 资源
                        }
                        return slideImage;
                    }, threadPoolDealTask.getThreadPoolExecutor());  // 使用自定义线程池执行任务

                    futures.add(future);
                }
                // 使用 CompletableFuture.allOf 等待所有异步任务完成
                CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
                // 当所有任务完成时，将结果汇总
                CompletableFuture<List<BufferedImage>> allImagesFuture = allFutures.thenApply(v ->
                        futures.stream().map(CompletableFuture::join).collect(Collectors.toList())
                );

                // 等待所有任务完成并获取所有幻灯片图像
                List<BufferedImage> slideImages = allImagesFuture.get();
                futures.clear();

                // 绘制幻灯片到长图中
                BufferedImage longImage = new BufferedImage(totalWidth, totalHeight, BufferedImage.TYPE_INT_RGB);
                Graphics2D slideGraphics = longImage.createGraphics();
                slideGraphics.setPaint(Color.white);
                slideGraphics.fillRect(0, 0, totalWidth, totalHeight);

                if ("true".equals(highImageQuality)) {
                    dealHighImageQuality(highImageQuality, slideGraphics);
                }
                int yOffset = 0, xOffset = 0;

                // 将每个幻灯片图像绘制到长图中
                for (int i = 0; i < slideCount; i++) {
                    BufferedImage slideImage = slideImages.get(i);
                    slideGraphics.drawImage(slideImage, xOffset, yOffset, null);
                    xOffset += scaledWidth + horizontalSpacing;

                    if ((i + 1) % slidesPerRow == 0) {
                        xOffset = 0;
                        yOffset += scaledHeight + verticalSpacing;
                    }

                    // 释放当前幻灯片图像的内存
                    slideImage.flush();
                }

                // 将最终的长图像写入输出文件
                slideImages.clear();
                try (FileOutputStream out = new FileOutputStream(outputPath)) {
                    ImageIO.write(longImage, "jpeg", out);
                }
                slideGraphics.dispose();  // 释放Graphics2D资源
                longImage.flush();  // 释放BufferedImage内存
            } catch (Exception e) {
                log.error("生成预览图失败：{}", e.getMessage());
            }
        } catch (IOException e) {
            log.error("生成预览图，读取PPT文件失败：{}", e.getMessage());
        }
    }

    //    @LogExecutionTime("生成预览单图耗时")
    public List<String> generateSingleImagesFromPPT(String pptPath, String outputDir, String highImageQuality) {
        if (StringUtils.isBlank(highImageQuality)) {
            highImageQuality = "false";
        }
        setPptFontStyle.dealPptFontsToImg(pptPath, "宋体");

        // 参数验证和默认值设置
        if (StringUtils.isBlank(pptPath)) throw new IllegalArgumentException("PPT 路径不能为空.");
        if (StringUtils.isBlank(outputDir)) throw new IllegalArgumentException("输出目录不能为空.");

        List<String> base64Images = new ArrayList<>(); // 存放Base64图像信息
        int slideCount;

        try (FileInputStream fis = new FileInputStream(pptPath);
             XMLSlideShow ppt = new XMLSlideShow(fis)) {

            Dimension pgsize = ppt.getPageSize();
            slideCount = ppt.getSlides().size();
            double scale = "true".equals(highImageQuality) ? 4.0 : 2.0;

            int scaledWidth = Math.max((int) (pgsize.width * scale), pgsize.width);
            int scaledHeight = Math.max((int) (pgsize.height * scale), pgsize.height);

            // 初始化 base64Images 为指定大小的 ArrayList
            base64Images = new ArrayList<>(Collections.nCopies(slideCount, null));

            List<CompletableFuture<Void>> futures = new ArrayList<>();
            try {
                String finalHighImageQuality = highImageQuality;
                for (int i = 0; i < slideCount; i++) {
                    final int slideIndex = i;  // Declare slideIndex as final variable
                    List<String> finalBase64Images = base64Images;
                    CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                        try {
                            BufferedImage slideImage = new BufferedImage(scaledWidth, scaledHeight, BufferedImage.TYPE_INT_RGB);
                            Graphics2D slideGraphics = slideImage.createGraphics();

                            dealHighImageQuality(finalHighImageQuality, slideGraphics);
                            slideGraphics.setPaint(Color.white);
                            slideGraphics.fillRect(0, 0, scaledWidth, scaledHeight);
                            slideGraphics.scale(scale, scale);
                            ppt.getSlides().get(slideIndex).draw(slideGraphics);  // Draw the slide
                            slideGraphics.dispose();  // Release resources

                            // Add sequence number
                            Graphics2D g = slideImage.createGraphics();
                            g.setColor(Color.BLACK);
                            g.setFont(new Font("Arial", Font.BOLD, (int) (10 * scale)));
                            String slideNumber = configPrinterConfig.getWatermarkText() + (slideIndex + 1);
                            FontMetrics fontMetrics = g.getFontMetrics();
                            int textWidth = fontMetrics.stringWidth(slideNumber);
                            int textHeight = fontMetrics.getHeight();

                            // Set position to the right bottom corner, with a small margin
                            int x = scaledWidth - textWidth - 10; // 10 pixels from the right
                            int y = scaledHeight - textHeight + 5; // 5 pixels from the bottom

                            g.drawString(slideNumber, x, y);  // Draw the sequence number
                            g.dispose();
                            slideImage.flush();

                            // Convert image to Base64
                            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                                ImageIO.write(slideImage, "jpg", baos);
                                baos.flush();
                                byte[] imageBytes = baos.toByteArray();
                                String base64Image = Base64.getEncoder().encodeToString(imageBytes);
                                finalBase64Images.set(slideIndex, base64Image);  // Store Base64 image directly by index
                            }

                            // Save the image to the output directory asynchronously
                            String outputPath = String.format("%s/slide_%d.jpg", outputDir, slideIndex + 1);
                            CompletableFuture.runAsync(() -> {
                                try {
                                    ImageIO.write(slideImage, "jpg", new File(outputPath));
                                } catch (IOException e) {
                                    log.error("保存幻灯片 {} 的图像失败: {}", slideIndex + 1, e.getMessage());
                                }
                            }, threadPoolDealTask.getThreadPoolExecutor());

                            slideImage.flush();  // Release BufferedImage memory
                        } catch (Exception e) {
                            log.error("生成幻灯片 {} 的图像失败: {}", slideIndex + 1, e.getMessage());
                        }
                    }, threadPoolDealTask.getThreadPoolExecutor());  // Use custom thread pool

                    futures.add(future);  // Collect futures
                }

                // 等待所有任务完成
                CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
                allOf.join();  // Blocks until all are complete

            } catch (Exception e) {
                log.error("生成幻灯片图像失败: {}", e.getMessage());
            }
        } catch (IOException e) {
            log.error("读取PPT文件失败: {}", e.getMessage());
        }
        System.gc(); // 提示垃圾回收
        return base64Images; // 返回Base64图像信息
    }

    //    @LogExecutionTime("生成预览单图耗时")
    public List<String> generateSingleImagesFromPPT2(String pptPath, String outputDir, String highImageQuality) {
        if (StringUtils.isBlank(highImageQuality)) {
            highImageQuality = "false";
        }

        setPptFontStyle.dealPptFontsToImg(pptPath, "宋体");

        if (StringUtils.isBlank(pptPath)) throw new IllegalArgumentException("PPT 路径不能为空.");
        if (StringUtils.isBlank(outputDir)) throw new IllegalArgumentException("输出目录不能为空.");

        List<String> resultImages = new ArrayList<>();

        ZipSecureFile.setMinInflateRatio(0.001);  // 允许更高的压缩比
        try (FileInputStream fis = new FileInputStream(pptPath);
             XMLSlideShow ppt = new XMLSlideShow(fis)) {

            Dimension pgsize = ppt.getPageSize();
            int slideCount = ppt.getSlides().size();
            double scale = "true".equals(highImageQuality) ? 4.0 : 2.0;

            int scaledWidth = Math.max((int) (pgsize.width * scale), pgsize.width);
            int scaledHeight = Math.max((int) (pgsize.height * scale), pgsize.height);

            List<CompletableFuture<String>> futures = new ArrayList<>();

            for (int i = 0; i < slideCount; i++) {
                int slideIndex = i;
                String finalHighImageQuality = highImageQuality;
                CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        BufferedImage slideImage = new BufferedImage(scaledWidth, scaledHeight, BufferedImage.TYPE_INT_RGB);
                        Graphics2D slideGraphics = slideImage.createGraphics();

                        dealHighImageQuality(finalHighImageQuality, slideGraphics);
                        slideGraphics.setPaint(Color.white);
                        slideGraphics.fillRect(0, 0, scaledWidth, scaledHeight);
                        slideGraphics.scale(scale, scale);
                        ppt.getSlides().get(slideIndex).draw(slideGraphics);
                        slideGraphics.dispose();

                        // Add sequence number
                        Graphics2D g = slideImage.createGraphics();
                        g.setColor(Color.BLACK);
                        g.setFont(new Font("Arial", Font.BOLD, (int) (10 * scale)));

                        String slideNumber = configPrinterConfig.getWatermarkText() + (slideIndex + 1);
                        int textWidth = g.getFontMetrics().stringWidth(slideNumber);
                        int textHeight = g.getFontMetrics().getHeight();
                        int x = scaledWidth - textWidth - 10;
                        int y = scaledHeight - textHeight + 5;
                        g.drawString(slideNumber, x, y);
                        g.dispose();
                        // Save image to output directory
                        String outputPath = String.format("%s/slide_%d.jpg", outputDir, slideIndex + 1);
                        // 将路径中的所有分隔符统一为正斜杠 "/"
                        outputPath = fileCustomUtils.normalizePath(outputPath);

                        ImageIO.write(slideImage, "jpg", new File(outputPath));
                        slideImage.flush();  // Release BufferedImage memory

                        return outputPath;  // Return the path
                    } catch (Exception e) {
                        log.error("生成幻灯片 {} 的图像失败: {}", slideIndex + 1, e.getMessage());
                        return null;  // Return null on failure
                    }
                }, threadPoolDealTask.getThreadPoolExecutor());

                futures.add(future);
            }

            // 等待所有任务完成并收集结果
            CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            allOf.join();  // Blocks until all are complete

            // 收集所有有效的结果
            for (CompletableFuture<String> future : futures) {
                try {
                    String result = future.get();
                    if (result != null) {
                        resultImages.add(result);
                    }
                } catch (Exception e) {
                    log.error("获取生成图片的路径时出错: {}", e.getMessage());
                }
            }
            futures.clear();
        } catch (IOException e) {
            log.error("读取PPT文件失败: {}", e.getMessage());
        }

        return resultImages;  // 返回图片地址集合
    }

    public List<String> generateSingleImagesFromPPT2(String pptPath, String outputDir, String highImageQuality, String targetPath) {
        if (StringUtils.isBlank(highImageQuality)) {
            highImageQuality = "false";
        }

        setPptFontStyle.dealPptFontsToImg(pptPath, "宋体");

        if (StringUtils.isBlank(pptPath)) throw new IllegalArgumentException("PPT 路径不能为空.");
        if (StringUtils.isBlank(outputDir)) throw new IllegalArgumentException("输出目录不能为空.");

        List<String> resultImages = new ArrayList<>();

        ZipSecureFile.setMinInflateRatio(0.001);  // 允许更高的压缩比
        try (FileInputStream fis = new FileInputStream(pptPath);
             XMLSlideShow ppt = new XMLSlideShow(fis)) {

            Dimension pgsize = ppt.getPageSize();
            int slideCount = ppt.getSlides().size();
            double scale = "true".equals(highImageQuality) ? 4.0 : 2.0;

            int scaledWidth = Math.max((int) (pgsize.width * scale), pgsize.width);
            int scaledHeight = Math.max((int) (pgsize.height * scale), pgsize.height);

            List<CompletableFuture<String>> futures = new ArrayList<>();

            for (int i = 0; i < slideCount; i++) {
                int slideIndex = i;
                String finalHighImageQuality = highImageQuality;
                CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        BufferedImage slideImage = new BufferedImage(scaledWidth, scaledHeight, BufferedImage.TYPE_INT_RGB);
                        Graphics2D slideGraphics = slideImage.createGraphics();

                        dealHighImageQuality(finalHighImageQuality, slideGraphics);
                        slideGraphics.setPaint(Color.white);
                        slideGraphics.fillRect(0, 0, scaledWidth, scaledHeight);
                        slideGraphics.scale(scale, scale);
                        ppt.getSlides().get(slideIndex).draw(slideGraphics);
                        slideGraphics.dispose();

                        // Add sequence number
                        Graphics2D g = slideImage.createGraphics();
                        g.setColor(Color.BLACK);
                        g.setFont(new Font("Arial", Font.BOLD, (int) (10 * scale)));

                        String slideNumber = configPrinterConfig.getWatermarkText() + (slideIndex + 1);
                        int textWidth = g.getFontMetrics().stringWidth(slideNumber);
                        int textHeight = g.getFontMetrics().getHeight();
                        int x = scaledWidth - textWidth - 10;
                        int y = scaledHeight - textHeight + 5;
                        g.drawString(slideNumber, x, y);
                        g.dispose();

                        // Save image to output directory
                        String outputPath = null;
                        if (StringUtils.isBlank(targetPath)) {
                            outputPath = targetPath;
                        }
                        outputPath = String.format("%s/slide_%d.jpg", outputDir, slideIndex + 1);
                        // 将路径中的所有分隔符统一为正斜杠 "/"
                        outputPath = fileCustomUtils.normalizePath(outputPath);

                        ImageIO.write(slideImage, "jpg", new File(outputPath));
                        slideImage.flush();  // Release BufferedImage memory

                        return outputPath;  // Return the path
                    } catch (Exception e) {
                        log.error("生成幻灯片 {} 的图像失败: {}", slideIndex + 1, e.getMessage());
                        return null;  // Return null on failure
                    }
                }, threadPoolDealTask.getThreadPoolExecutor());

                futures.add(future);
            }

            // 等待所有任务完成并收集结果
            CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            allOf.join();  // Blocks until all are complete

            // 收集所有有效的结果
            for (CompletableFuture<String> future : futures) {
                try {
                    String result = future.get();
                    if (result != null) {
                        resultImages.add(result);
                    }
                } catch (Exception e) {
                    log.error("获取生成图片的路径时出错: {}", e.getMessage());
                }
            }
            futures.clear();
        } catch (IOException e) {
            log.error("读取PPT文件失败: {}", e.getMessage());
        }

        return resultImages;  // 返回图片地址集合
    }




    private void dealHighImageQuality(String highImageQuality, Graphics2D slideGraphics) {
        if ("true".equals(highImageQuality)) {
            // 开启抗锯齿
            slideGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            // 设置渲染质量为高质量
            slideGraphics.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            // 开启文本抗锯齿
            slideGraphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            // 设置插值类型为双三次插值
            slideGraphics.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
            // 启用颜色平滑
            slideGraphics.setRenderingHint(RenderingHints.KEY_COLOR_RENDERING, RenderingHints.VALUE_COLOR_RENDER_QUALITY);
            // 启用形状几何学的子像素调整
            slideGraphics.setRenderingHint(RenderingHints.KEY_STROKE_CONTROL, RenderingHints.VALUE_STROKE_PURE);
            // 启用全场景抗锯齿
            slideGraphics.setRenderingHint(RenderingHints.KEY_FRACTIONALMETRICS, RenderingHints.VALUE_FRACTIONALMETRICS_ON);
        } else {
            // 开启抗锯齿
            slideGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            // 开启文本抗锯齿
            slideGraphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            // 启用全场景抗锯齿
            slideGraphics.setRenderingHint(RenderingHints.KEY_FRACTIONALMETRICS, RenderingHints.VALUE_FRACTIONALMETRICS_ON);
        }

        slideGraphics.setPaint(Color.white);
    }



    public void splitPptAntoImg(Map<Long, List<String>> map, Long id, String pptFilePath) throws Exception {
        // 临时文件列表
        List<String> list = new ArrayList<>();

        // 加载PPT文件
        Presentation ppt1 = new Presentation();
        ppt1.loadFromFile(pptFilePath);
        File file = new File(pptFilePath);

        //遍历文档1
        for (int i = 0; i < ppt1.getSlides().getCount(); i++) {
            log.info("线程: {}，开始生成PPT {},第 {} / {} 页幻灯片", Thread.currentThread().getName(), file.getName(), i, ppt1.getSlides().getCount());
            File tempFile = null;
            try {
                //新建一个PPT文档，并移除默认生成的第一页幻灯片
                Presentation newppt = new Presentation();
                newppt.getSlides().removeAt(0);

                //将每一页添加到新建的文档，并保存
                newppt.getSlides().append(ppt1.getSlides().get(i));
                // 创建一个临时文件来保存每一页PPT
                tempFile = File.createTempFile("ppt_", ".pptx");
                String tmpFilePath = tempFile.getAbsolutePath();
                newppt.saveToFile(tmpFilePath, FileFormat.PPTX_2013);
                newppt.dispose();
                // 处理单页ppt生成图片
                String targetPath = pptFilePath.replace(".pptx", "_img_" +
                        UUID.randomUUID().toString().replace("-", "").substring(0, 7) +
                        ".jpg");
                dealPpToImg(tmpFilePath, targetPath);
                list.add(targetPath);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }finally {
                // 确保临时文件删除
                if (tempFile != null && tempFile.exists()) {
                    if (!tempFile.delete()) {
                        tempFile.deleteOnExit();
                    }
                }
            }
        }
        map.put(id, list);
        ppt1.dispose();
    }


    private List<String> dealPpToImg(String pptPath, String targetPath) throws Exception {
        List<String> filePaths = new ArrayList<>();
        //load an example PPTX file
        Presentation presentation = new Presentation();
        presentation.loadFromFile(pptPath);
        Random random = new Random();
        String filePath = null;
        if (StringUtils.isBlank(targetPath)) {
            filePath = pptPath.replace(".pptx", "_img_"
                    + UUID.randomUUID().toString().replace("-", "")
                    .substring(0, 7) + ".jpg");
        } else {
            filePath = targetPath;
        }
        //loop through the slides
        for (int j = 0; j < presentation.getSlides().getCount(); j++) {
            //save each slide as a BufferedImage
            BufferedImage image = presentation.getSlides().get(j).saveAsImage();
            //save BufferedImage as PNG file format

            ImageIO.write(image, "PNG", new File(filePath));
            filePaths.add(filePath);
            image.flush();
        }
        presentation.dispose();
        return filePaths;
    }
}
