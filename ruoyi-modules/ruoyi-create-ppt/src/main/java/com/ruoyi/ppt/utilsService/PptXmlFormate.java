package com.ruoyi.ppt.utilsService;

import com.ruoyi.ppt.aopCustom.customAnnotations.LogExecutionTime;
import com.ruoyi.ppt.config.ThreadPoolDealTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.xslf.usermodel.XMLSlideShow;
import org.apache.poi.xslf.usermodel.XSLFSlide;
import org.apache.xmlbeans.XmlObject;
import org.springframework.stereotype.Component;
import org.w3c.dom.*;

import javax.annotation.Resource;
import javax.xml.XMLConstants;
import javax.xml.namespace.NamespaceContext;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathExpressionException;
import javax.xml.xpath.XPathFactory;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

@Slf4j
@Component
public class PptXmlFormate {

    @Resource
    private ThreadPoolDealTask threadPoolDealTask;

    //    @LogExecutionTime("PPT XML标签调整耗时")
    public void xmlFormate(String pptxFilePath, String useFont) {
        useFont = StringUtils.isBlank(useFont) ? "宋体" : useFont;

        // 使用 try-with-resources 自动关闭临时文件和 Zip 流
        try {
            // 创建临时文件来存储更新后的 PPTX 文件
            File tempFile = File.createTempFile("pptx_temp" + UUID.randomUUID(), ".zip");
            tempFile.deleteOnExit();

            // 读取 PPTX 文件并更新 XML
            try (FileInputStream fis = new FileInputStream(pptxFilePath);
                 ZipInputStream zis = new ZipInputStream(fis);
                 FileOutputStream fos = new FileOutputStream(tempFile);
                 ZipOutputStream zos = new ZipOutputStream(fos)) {

                ZipEntry entry;
                Map<String, ByteArrayOutputStream> updatedFiles = new HashMap<>();

                // 遍历 PPTX 文件中的每个条目
                while ((entry = zis.getNextEntry()) != null) {
                    String entryName = entry.getName();


                    // 读取条目内容
                    byte[] buffer = new byte[4096];
                    int len;
                    try (ByteArrayOutputStream entryData = new ByteArrayOutputStream()) {
                        while ((len = zis.read(buffer)) > 0) {
                            entryData.write(buffer, 0, len);
                        }

                        // 仅处理幻灯片 XML 文件
                        if (entryName.startsWith("ppt/slides/slide") && entryName.endsWith(".xml")) {
                            // 解析 XML
                            try (ByteArrayInputStream bais = new ByteArrayInputStream(entryData.toByteArray());
                                 ByteArrayOutputStream updatedXml = new ByteArrayOutputStream()) {

                                DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
                                factory.setNamespaceAware(true);

                                DocumentBuilder builder = factory.newDocumentBuilder();
                                Document document = builder.parse(bais);


                                // 删除标签
                                String custDataLstXpath = "//*[local-name()='custDataLst']";
                                removeNodesUsingXPath(document, custDataLstXpath);

//                                // 查找替换内容
//                                String[] xpaths = {
//                                        "//*[local-name()='latin']",
//                                        "//*[local-name()='ea']",
//                                        "//*[local-name()='sym']",
//                                        "//*[local-name()='cs']",
//                                        "//*[local-name()='rpr']"
//                                };
//
//                                for (String xpath : xpaths) {
//                                    replaceNodesUsingXPath(document, xpath, finalUseFont);
//                                }

                                // 写入修改后的 XML 内容
                                TransformerFactory transformerFactory = TransformerFactory.newInstance();
                                Transformer transformer = transformerFactory.newTransformer();
                                transformer.setOutputProperty(OutputKeys.INDENT, "yes");
                                DOMSource source = new DOMSource(document);
                                StreamResult result = new StreamResult(updatedXml);
                                transformer.transform(source, result);
                                updatedFiles.put(entryName, updatedXml);

                            }
                        } else {
                            // 对于其他条目，保持原样
                            updatedFiles.put(entryName, entryData);
                        }
                    }

                }

                // 写入更新后的 PPTX 文件
                for (Map.Entry<String, ByteArrayOutputStream> fileEntry : updatedFiles.entrySet()) {
                    String entryName = fileEntry.getKey();
                    ByteArrayOutputStream fileData = fileEntry.getValue();

                    zos.putNextEntry(new ZipEntry(entryName));
                    fileData.writeTo(zos);
                    zos.closeEntry();
                }
                zos.finish();
            }

            // 将临时文件移动到原始 PPTX 文件路径，替换原文件
            Files.move(tempFile.toPath(), new File(pptxFilePath).toPath(), StandardCopyOption.REPLACE_EXISTING);

        } catch (IOException e) {
            log.error("处理文件时发生 IO 错误: {}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("处理过程中发生错误: {}", e.getMessage(), e);
        }
    }

    public void xmlFormate2(String pptxFilePath, String useFont) {
        long startTime = System.currentTimeMillis();
        useFont = StringUtils.isBlank(useFont) ? "宋体" : useFont;
        try {
            // 创建临时文件来存储更新后的 PPTX 文件
            File tempFile = File.createTempFile("pptx_temp" + UUID.randomUUID(), ".zip");
            tempFile.deleteOnExit();

            // 读取 PPTX 文件并更新 XML
            try (FileInputStream fis = new FileInputStream(pptxFilePath);
                 ZipInputStream zis = new ZipInputStream(fis);
                 FileOutputStream fos = new FileOutputStream(tempFile);
                 ZipOutputStream zos = new ZipOutputStream(fos)) {

                ZipEntry entry;
                Map<String, ByteArrayOutputStream> updatedFiles = new HashMap<>();

                // 遍历 PPTX 文件中的每个条目
                while ((entry = zis.getNextEntry()) != null) {
                    String entryName = entry.getName();
                    ByteArrayOutputStream entryData = new ByteArrayOutputStream();

                    // 读取条目内容
                    byte[] buffer = new byte[4096];
                    int len;
                    while ((len = zis.read(buffer)) > 0) {
                        entryData.write(buffer, 0, len);
                    }

                    // 仅处理幻灯片 XML 文件
                    if (entryName.startsWith("ppt/slides/slide") && entryName.endsWith(".xml")) {
                        // 解析 XML
                        ByteArrayInputStream bais = new ByteArrayInputStream(entryData.toByteArray());
                        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
                        factory.setNamespaceAware(true);
                        DocumentBuilder builder = factory.newDocumentBuilder();
                        Document document = builder.parse(bais);
                        String NAMESPACE_URI = "http://schemas.openxmlformats.org/presentationml/2006/main";


                        String finalUseFont = useFont;
                        Future<?> future = threadPoolDealTask.submitTask(() -> {
                            // 删除标签
                            String custDataLstXpath = "//*[local-name()='custDataLst']";
                            removeNodesUsingXPath(document, custDataLstXpath);

                            // 查找替换内容
                            String xpath = "//*[local-name()='latin']";
                            replaceNodesUsingXPath(document, xpath, finalUseFont);
                            xpath = "//*[local-name()='ea']";
                            replaceNodesUsingXPath(document, xpath, finalUseFont);
                            xpath = "//*[local-name()='sym']";
                            replaceNodesUsingXPath(document, xpath, finalUseFont);
                            xpath = "//*[local-name()='cs']";
                            replaceNodesUsingXPath(document, xpath, finalUseFont);
                            xpath = "//*[local-name()='rpr']";
                            replaceNodesUsingXPath(document, xpath, finalUseFont);
                        });
                        future.get();


                        // 查找替换内容 <a:latin> 和 <a:ea>  <a:t>节点
//                        searchAndReplaceNodes(document, NAMESPACE_URI, "latin", "typeface", "华文彩云");
//                        searchAndReplaceNodes(document, NAMESPACE_URI, "ea", "typeface", "华文彩云");
//                        searchAndReplaceNodes(document, NAMESPACE_URI, "t", "typeface", "华文彩云");


//                        String xpath = "//*[local-name()='latin']";
//                        replaceNodesUsingXPath(document, xpath, useFont);
//                        xpath = "//*[local-name()='ea']";
//                        replaceNodesUsingXPath(document, xpath, useFont);
//                        xpath = "//*[local-name()='sym']";
//                        replaceNodesUsingXPath(document, xpath, useFont);
//                        xpath = "//*[local-name()='cs']";
//                        replaceNodesUsingXPath(document, xpath, useFont);
//                        xpath = "//*[local-name()='rpr']";
//                        replaceNodesUsingXPath(document, xpath, useFont);


                        // 写入修改后的 XML 内容
                        TransformerFactory transformerFactory = TransformerFactory.newInstance();
                        Transformer transformer = transformerFactory.newTransformer();
                        transformer.setOutputProperty(OutputKeys.INDENT, "yes");
                        DOMSource source = new DOMSource(document);
                        ByteArrayOutputStream updatedXml = new ByteArrayOutputStream();
                        StreamResult result = new StreamResult(updatedXml);
                        transformer.transform(source, result);
                        updatedFiles.put(entryName, updatedXml);
                    } else {
                        // 对于其他条目，保持原样
                        updatedFiles.put(entryName, entryData);
                    }
                }


                // 写入更新后的 PPTX 文件
                for (Map.Entry<String, ByteArrayOutputStream> fileEntry : updatedFiles.entrySet()) {
                    String entryName = fileEntry.getKey();
                    ByteArrayOutputStream fileData = fileEntry.getValue();

                    zos.putNextEntry(new ZipEntry(entryName));
                    fileData.writeTo(zos);
                    zos.closeEntry();
                }
                zos.finish();
            }

            // 将临时文件移动到原始 PPTX 文件路径，替换原文件
            Files.move(tempFile.toPath(), new File(pptxFilePath).toPath(), StandardCopyOption.REPLACE_EXISTING);
            long endTime = System.currentTimeMillis();
            log.info("ppt xml标签 兼容调整耗时：" + (endTime - startTime) + "ms");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public void xmlFormate3(String pptxFilePath, String useFont) {
        try {
            // 设置允许的最小解压比，将其设得比默认更低，如 0.001
            ZipSecureFile.setMinInflateRatio(0.001);  // 允许更高的压缩比
            // 加载PPT文件

//            XMLSlideShow ppt = new XMLSlideShow(OPCPackage.open(pptFile));
            try (XMLSlideShow ppt = new XMLSlideShow(Files.newInputStream(Paths.get(pptxFilePath)))) {
                // 获取所有幻灯片
                List<XSLFSlide> slides = ppt.getSlides();

                // 遍历每张幻灯片
                for (XSLFSlide slide : slides) {
                    // 获取XML内容
                    XmlObject slideXml = slide.getXmlObject();

                    // 删除 <p:custDataLst> 标签
                    removeCustomDataList(slideXml);
                }
                // 保存修改后的PPT
                try (FileOutputStream out = new FileOutputStream(pptxFilePath)) {
                    ppt.write(out);
                }

            }
        } catch (Exception e) {
            log.error("Error processing the PPT file: {}", e.getMessage());
        }

    }

    // 通过XPath或其他方式删除 <p:custDataLst> 包裹的数据
    private static void removeCustomDataList(XmlObject slideXml) throws Exception {
        // 使用XPath查找 <p:custDataLst> 标签
        String xPathExpression = "declare namespace p='http://schemas.openxmlformats.org/presentationml/2006/main'; //p:custDataLst";
        XmlObject[] custDataLstNodes = slideXml.selectPath(xPathExpression);

        // 遍历找到的所有 <p:custDataLst> 节点并删除
        for (XmlObject custDataLstNode : custDataLstNodes) {
            custDataLstNode.getDomNode().getParentNode().removeChild(custDataLstNode.getDomNode());
        }
    }

    public void setNodesAttributeUsingXPath(String pptxFilePath) {
        try {
            // 创建临时文件来存储更新后的 PPTX 文件
            File tempFile = File.createTempFile("pptx_temp" + UUID.randomUUID(), ".zip");
            tempFile.deleteOnExit();

            // 读取 PPTX 文件并更新 XML
            try (FileInputStream fis = new FileInputStream(pptxFilePath);
                 ZipInputStream zis = new ZipInputStream(fis);
                 FileOutputStream fos = new FileOutputStream(tempFile);
                 ZipOutputStream zos = new ZipOutputStream(fos)) {

                ZipEntry entry;
                Map<String, ByteArrayOutputStream> updatedFiles = new HashMap<>();

                // 遍历 PPTX 文件中的每个条目
                while ((entry = zis.getNextEntry()) != null) {
                    String entryName = entry.getName();
                    ByteArrayOutputStream entryData = new ByteArrayOutputStream();

                    // 读取条目内容
                    byte[] buffer = new byte[4096];
                    int len;
                    while ((len = zis.read(buffer)) > 0) {
                        entryData.write(buffer, 0, len);
                    }

                    // 仅处理幻灯片 XML 文件
                    if (entryName.startsWith("ppt/slides/slide") && entryName.endsWith(".xml")) {
                        // 解析 XML
                        ByteArrayInputStream bais = new ByteArrayInputStream(entryData.toByteArray());
                        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
                        factory.setNamespaceAware(true);
                        DocumentBuilder builder = factory.newDocumentBuilder();
                        Document document = builder.parse(bais);
                        // 修改 <p:cNvPr> 的 id 属性
                        String xpathExpression = "//*[local-name()='cNvPr']";
                        setNodesShapIdAttributeUsingXPath(document, xpathExpression, "id");


                        // 写入修改后的 XML 内容
                        TransformerFactory transformerFactory = TransformerFactory.newInstance();
                        Transformer transformer = transformerFactory.newTransformer();
                        transformer.setOutputProperty(OutputKeys.INDENT, "yes");
                        DOMSource source = new DOMSource(document);
                        ByteArrayOutputStream updatedXml = new ByteArrayOutputStream();
                        StreamResult result = new StreamResult(updatedXml);
                        transformer.transform(source, result);
                        updatedFiles.put(entryName, updatedXml);
                    } else {
                        // 对于其他条目，保持原样
                        updatedFiles.put(entryName, entryData);
                    }
                }


                // 写入更新后的 PPTX 文件
                for (Map.Entry<String, ByteArrayOutputStream> fileEntry : updatedFiles.entrySet()) {
                    String entryName = fileEntry.getKey();
                    ByteArrayOutputStream fileData = fileEntry.getValue();

                    zos.putNextEntry(new ZipEntry(entryName));
                    fileData.writeTo(zos);
                    zos.closeEntry();
                }
                zos.finish();
            }

            // 将临时文件移动到原始 PPTX 文件路径，替换原文件
            Files.move(tempFile.toPath(), new File(pptxFilePath).toPath(), StandardCopyOption.REPLACE_EXISTING);
            System.out.println("PPTX file updated successfully!");

        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    public void replaceNodesUsingXPath(Document document, String xpathExpression, String useFont) {
        XPath xPath = XPathFactory.newInstance().newXPath();
        // 设置命名空间前缀和 URI
        NamespaceContext nsContext = new NamespaceContext() {
            public String getNamespaceURI(String prefix) {
                switch (prefix) {
                    case "a":
                        return "http://schemas.openxmlformats.org/drawingml/2006/main";
                    case "p":
                        return "http://schemas.openxmlformats.org/presentationml/2006/main";
                    default:
                        return XMLConstants.NULL_NS_URI;
                }
            }

            public String getPrefix(String namespaceURI) {
                return null;
            }

            public Iterator getPrefixes(String namespaceURI) {
                return null;
            }
        };
        xPath.setNamespaceContext(nsContext);

        try {
            NodeList nodes = (NodeList) xPath.evaluate(xpathExpression, document, XPathConstants.NODESET);

            for (int i = nodes.getLength() - 1; i >= 0; i--) { // 从后向前遍历
                Node node = nodes.item(i);

                // 打印节点的值（如果是文本节点）
                if (node.getNodeType() == Node.ELEMENT_NODE) {
                    Element element = (Element) node;
                    // 打印并修改属性
                    NamedNodeMap attributes = element.getAttributes();
                    boolean typefaceFound = false;
                    for (int j = 0; j < attributes.getLength(); j++) {
                        Node attribute = attributes.item(j);
                        if ("typeface".equals(attribute.getNodeName())) {
                            // 修改 typeface 属性的值
                            attribute.setNodeValue(useFont);
                            typefaceFound = true;
                        }
                    }

                    // 如果没有找到 typeface 属性，则可以选择添加
                    if (!typefaceFound) {
                        element.setAttribute("typeface", useFont);
                    }
                }

            }
        } catch (XPathExpressionException e) {
            log.error("Error evaluating XPath expression: {}", e.getMessage());
        }

    }


    public void removeNodesUsingXPath(Document document, String xpathExpression) {
        XPath xPath = XPathFactory.newInstance().newXPath();
        // 设置命名空间前缀和 URI
        NamespaceContext nsContext = new NamespaceContext() {
            public String getNamespaceURI(String prefix) {
                switch (prefix) {
                    case "a":
                        return "http://schemas.openxmlformats.org/drawingml/2006/main";
                    case "p":
                        return "http://schemas.openxmlformats.org/presentationml/2006/main";
                    default:
                        return XMLConstants.NULL_NS_URI;
                }
            }

            public String getPrefix(String namespaceURI) {
                return null;
            }

            public Iterator getPrefixes(String namespaceURI) {
                return null;
            }
        };
        xPath.setNamespaceContext(nsContext);
        try {
            NodeList nodes = (NodeList) xPath.evaluate(xpathExpression, document, XPathConstants.NODESET);

            for (int i = nodes.getLength() - 1; i >= 0; i--) { // 从后向前遍历
                Node node = nodes.item(i);
                Node parentNode = node.getParentNode();
                if (parentNode != null) {
                    parentNode.removeChild(node);
                }
            }
        } catch (Exception e) {
            log.error("Error removing nodes using XPath: {}", e.getMessage());
        }

    }

    // 删除嵌套标签
    private void removeNodes(Document document, String parentNamespaceURI, String localName1, String localName2, String localName3, String localName4) {
        NodeList spNodes = document.getElementsByTagNameNS(parentNamespaceURI, localName1);
        for (int i = 0; i < spNodes.getLength(); i++) {
            Node spNode = spNodes.item(i);
            NodeList nvSpPrNodes = ((Element) spNode).getElementsByTagNameNS(parentNamespaceURI, localName2);
            for (int j = 0; j < nvSpPrNodes.getLength(); j++) {
                Node nvSpPrNode = nvSpPrNodes.item(j);
                NodeList nvPrNodes = ((Element) nvSpPrNode).getElementsByTagNameNS(parentNamespaceURI, localName3);
                for (int k = 0; k < nvPrNodes.getLength(); k++) {
                    Node nvPrNode = nvPrNodes.item(k);
                    NodeList custDataLstNodes = ((Element) nvPrNode).getElementsByTagNameNS(parentNamespaceURI, localName4);
                    for (int l = 0; l < custDataLstNodes.getLength(); l++) {
                        Node custDataLstNode = custDataLstNodes.item(l);
                        // 删除 <p:custDataLst>
                        nvPrNode.removeChild(custDataLstNode);
                    }
                }
            }
        }
    }

    private void removeNodes(Document document, String namespaceURI, String localName1, String localName2, String localName3) {
        // 查找所有 p:sp 节点
        NodeList spNodes = document.getElementsByTagNameNS(namespaceURI, localName1);
        for (int i = 0; i < spNodes.getLength(); i++) {
            Node spNode = spNodes.item(i);

            // 在 p:sp 节点下查找 p:spPr 节点
            NodeList spPrNodes = ((Element) spNode).getElementsByTagNameNS(namespaceURI, localName2);
            for (int j = 0; j < spPrNodes.getLength(); j++) {
                Node spPrNode = spPrNodes.item(j);

                // 在 p:spPr 节点下查找目标节点
                NodeList targetNodes = ((Element) spPrNode).getElementsByTagNameNS(namespaceURI, localName3);
                for (int k = 0; k < targetNodes.getLength(); k++) {
                    Node targetNode = targetNodes.item(k);
                    spPrNode.removeChild(targetNode);

                }
            }
        }
    }

    // 替换标签内容
    private void searchAndReplaceNodes(Document document, String parentNamespaceURI, String localName, String attributeName, String attributeValue) {
        NodeList latinNodes = document.getElementsByTagNameNS(parentNamespaceURI, localName);
        for (int i = 0; i < latinNodes.getLength(); i++) {
            Node latinNode = latinNodes.item(i);
            ((Element) latinNode).setAttribute(attributeName, attributeValue);
        }
    }


    private void setNodesShapIdAttributeUsingXPath(Document document, String xpathExpression, String attributeName) throws XPathExpressionException {
        XPath xPath = XPathFactory.newInstance().newXPath();

        // 设置命名空间上下文
        NamespaceContext nsContext = new NamespaceContext() {
            public String getNamespaceURI(String prefix) {
                switch (prefix) {
                    case "a":
                        return "http://schemas.openxmlformats.org/drawingml/2006/main";
                    case "p":
                        return "http://schemas.openxmlformats.org/presentationml/2006/main";
                    default:
                        return XMLConstants.NULL_NS_URI;
                }
            }

            public String getPrefix(String namespaceURI) {
                return null;
            }

            public Iterator<String> getPrefixes(String namespaceURI) {
                return null;
            }
        };
        xPath.setNamespaceContext(nsContext);

        // 评估 XPath 表达式并获取节点列表
        NodeList nodes = (NodeList) xPath.evaluate(xpathExpression, document, XPathConstants.NODESET);

        // 遍历并修改节点的属性
        for (int i = 0; i < nodes.getLength(); i++) {
            Node node = nodes.item(i);
            if (node.getNodeType() == Node.ELEMENT_NODE) {
                Element element = (Element) node;

                // 打印旧值
                String oldAttributeValue = element.getAttribute(attributeName);
//                System.out.println("Old " + attributeName + " value: " + oldAttributeValue);

                // 生成唯一且较大的数值
                String newValue = getUniqueAndBigUuid(Integer.parseInt(oldAttributeValue));
                element.setAttribute(attributeName, newValue);

                // 打印新值
//                System.out.println("New " + attributeName + " value: " + newValue);
            }
        }
    }

    /**
     * 获取唯一且较大的数值字符串
     *
     * @param originalNum 原始的数值
     * @return 唯一且更大的数值字符串
     */
    private String getUniqueAndBigUuid(int originalNum) {
        Random random = new Random(1000);
        int newNum;

        do {
            newNum = random.nextInt();
        } while (newNum <= originalNum); // 确保新数值大于原始数值

        return String.valueOf(newNum);
    }
}
