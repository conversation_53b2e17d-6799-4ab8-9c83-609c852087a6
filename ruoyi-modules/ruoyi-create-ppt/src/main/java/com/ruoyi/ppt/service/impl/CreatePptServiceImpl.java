package com.ruoyi.ppt.service.impl;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.shaded.com.google.gson.reflect.TypeToken;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.TokenConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.RSAUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.ppt.config.ConfigPrinterConfig;
import com.ruoyi.ppt.config.RedissonLimitService;
import com.ruoyi.ppt.config.ThreadPoolDealTask;
import com.ruoyi.ppt.constant.Constants;
import com.ruoyi.ppt.domain.*;
import com.ruoyi.ppt.dto.*;
import com.ruoyi.ppt.enmu.LanguageTypeEnum;
import com.ruoyi.ppt.service.*;
import com.ruoyi.ppt.service.async.AsyncService;
import com.ruoyi.ppt.sse.RedisPublisher;
import com.ruoyi.ppt.utilsService.*;
import com.ruoyi.ppt.vo.QianFanTextToImgVo;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.sl.usermodel.PictureData;
import org.apache.poi.xslf.usermodel.XMLSlideShow;
import org.apache.poi.xslf.usermodel.XSLFPictureData;
import org.apache.poi.xslf.usermodel.XSLFPictureShape;
import org.apache.poi.xslf.usermodel.XSLFSlide;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.geom.Rectangle2D;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.NoSuchAlgorithmException;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.ruoyi.ppt.constant.Constants.CONTENT_UID_KEY;

@Service
@Slf4j
public class CreatePptServiceImpl implements ICreatePptService {

    @Resource
    private RedisService redisService;
    @Resource
    private RemoteFileService remoteFileService;
    @Resource
    private ConfigPrinterConfig configPrinterConfig;
    //	@Resource
//	private SseMessageService sseMessageService;
    @Resource
    private RedisPublisher redisPublisher;
    @Resource
    private TextUtils textUtils;
    @Resource
    private ThreadPoolDealTask threadPoolDealTask;
    @Resource
    private SetPptFontStyle setPptFontStyle;
    @Resource
    private PptLogoAdder pptLogoAdder;
    @Resource
    private PPTToLongImage pptToLongImage;
    @Resource
    private PptUtils pptUtilsService;
    @Resource
    private AsyncService asyncService;
    @Resource
    private PptXmlFormate pptXmlFormate;
    @Resource
    private FileCustomUtils fileCustomUtils;
    @Resource
    private IPptTemplateManagementService pptTemplateManagementService;
    @Resource
    private IPptTemplateManagementDetailService pptTemplateManagementDetailService;

    @Resource
    private IPptPersonHistoryService pptPersonHistoryService;
    @Resource
    private RedissonLimitService redissonLimitService;

    @Resource
    private IPptPersonTemplateService pptPersonTemplateService;
    @Resource
    private RemoteUserService remoteUserService;
    private static final OkHttpClient HTTP_CLIENT = new OkHttpClient().newBuilder()
            .connectTimeout(300, TimeUnit.SECONDS)
            .readTimeout(600, TimeUnit.SECONDS)
            .writeTimeout(600, TimeUnit.SECONDS)
            .build();

    private final Snowflake snowflake = new Snowflake(1, 1);


    private final com.google.gson.Gson gson = new com.google.gson.Gson();

    private static final String BAIDU_API_BEARER_TOKEN = "bce-v3/ALTAK-dyQhjZkw5RjqzaBOcJ5lc/04a5392ac58e7e75a61e7ce5a4a92a62613b075c"; // Replace with your Bearer Token or load securely
    private static final String BAIDU_CHAT_V2_API_ENDPOINT = "https://qianfan.baidubce.com/v2/chat/completions";
    private static final ObjectMapper objectMapper = new ObjectMapper();


    private static final Set<String> ALLOWED_FILE_EXTENSIONS = new HashSet<>(Arrays.asList(
            "doc", "docx", "pdf", "ppt", "pptx", "txt", "md",
            "xls", "xlsx", "csv", "html", "epub", "mobi", "xmind", "mm"
    ));

    @Override
    public ResultDto createPpt(String filePath, String themeFolderBasePath, String outFileFolder) throws Exception {
        PptUtils pptUtils = new PptUtils();
//        String filePath ="D:\\tmp\\教学讲义-formatText.txt";
//        String themeFolderBasePath = "D:\\tmp\\ppt-model\\dy-121\\";
//        String outFileFolder = "D:\\tmp\\";
//        ResultDto pptResult = pptUtils.createPpt(filePath, themeFolderBasePath, outFileFolder);
//        System.out.println("PPT生成所在的路径：" + pptResult.getRemark());
        return null;
    }


    /**
     * 自定义工具服务根据模板生成ppt
     *
     * @param pPtParam prompt模板优化
     * @return 结果
     * <AUTHOR>
     * @date 2024/09/05
     */
    @Override
    public AjaxResult createPPTCustom(PPtParam pPtParam) {
        AjaxResult result = AjaxResult.success();
        try {
            ResultDto resultDto = createPPTCustomDetails(pPtParam);
            if (resultDto == null) {
                return AjaxResult.error("创建ppt内部发生异常");
            }
            String path = resultDto.getPptPath();
            result.put("pptPath", path);
            result.put("pptName", resultDto.getPptName());
            // 统一修改字体
            // 格式化ppt xml
            pptXmlFormate.xmlFormate3(path, pPtParam.getUseFont());
//            setPptFontStyle.dealPptFonts(path, pPtParam.getUseFont());

            // 反馈ppt生成进度
            redisPublisher.publish2(
                    "字体调整成功"
                            + "%end%"
                            + Constants.PPT_PROGRESS_FONT_SUCCESS);
//			sseMessageService.sendMessage(SecurityUtils.getUserId().toString(),
//					"字体调整成功",
//					Constants.PPT_PROGRESS_FONT_SUCCESS);

            boolean addLogo = "true".equals(pPtParam.getAddLogo());
            boolean showLongImg = "true".equals(pPtParam.getShowLongImg());

            if (addLogo) {
                if (StringUtils.isBlank(pPtParam.getLogoPosition())) {
                    addLogoInPPT(path, 0, 0, null, pPtParam.getLogoPath(), pPtParam.getLogoZoom());
                } else {
                    SysDictData logoPositionDictData = getLogoPositionLabel(pPtParam.getLogoPosition(), Constants.Logo_Position);
                    addLogoInPPT(path, 0, 0, logoPositionDictData.getDictValue(), pPtParam.getLogoPath(), pPtParam.getLogoZoom());
                }

//				sseMessageService.sendMessage(SecurityUtils.getUserId().toString(),
//						"logo添加成功",
//						Constants.PPT_PROGRESS_LOGO_SUCCESS);
                redisPublisher.publish2(
                        "logo添加成功"
                                + "%end%"
                                + Constants.PPT_PROGRESS_LOGO_SUCCESS);
            }

            if (showLongImg) {
//                String outPath = generatePreviewImage(path, pPtParam);
//                result.put("longImg", "outPath");
                List<String> imgSinglePreviewList = generateSingleImgPreviewImages(path, pPtParam);
                result.put("imgSinglePreviewList", imgSinglePreviewList);

//				sseMessageService.sendMessage(SecurityUtils.getUserId().toString(),
//						"渲染预览成功",
//						Constants.PPT_PROGRESS_PREVIEW_SUCCESS);
                redisPublisher.publish2(
                        "渲染预览成功"
                                + "%end%"
                                + Constants.PPT_PROGRESS_PREVIEW_SUCCESS);
            }

            redisPublisher.publish2(
                    "完成"
                            + "%end%"
                            + Constants.PPT_PROGRESS_FINISH);
//			sseMessageService.sendMessage(SecurityUtils.getUserId().toString(),
//					"完成",
//					Constants.PPT_PROGRESS_FINISH);


////          延迟后删除生成的PPT文件 远程调用file文件模块
//			remoteFileService.scheduleFileDeletion(FileDeletionParamsDto.builder()
//					.filepathList(Collections.singletonList(path))
//					.delayMinutes(10)
//					.build());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return result;
    }

    /**
     * 创建 PPT自定义详细信息
     *
     * @param pPtParam p pt param
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/09/05
     */
    public ResultDto createPPTCustomDetails(PPtParam pPtParam) throws Exception {
        // 根据模板主题id来获取模板信息
        getTemplateManagement(pPtParam);

        // 如果传递的标志是auto 自动选择主题 则从现有的主题中随机选取一个
        justIsAutoTheme(pPtParam);

        // 根据主题id来获取模板详细信息 map集合封装
        List<Map<String, String>> templateManagementDetailMapList = getTemplateManagementDetail(pPtParam.getTemplateManagementId());

        // 构建输出目录
        String outFileFolder = createOutFileFolder(pPtParam);
//		// 检查并创建输出目录 调用远程file文件服务
//		AjaxResult remoteResult = remoteFileService.checkAndCreateDirectory(outFileFolder);
//		if ("200".equals(remoteResult.get("code").toString())) {
//			log.info((remoteResult.get("msg").toString()));
//		} else {
//			log.error(remoteResult.get("msg").toString());
//			throw new RuntimeException(remoteResult.get("msg").toString());
//		}
        fileCustomUtils.checkAndCreateDirectory(outFileFolder);

        // 获取类型 file/ text
        String type = pPtParam.getRequestType();

        // 需要删除的文件地址集合
        Set<String> deleteFileSet = new HashSet<>();
        // ppt创建最终的参数对象
        PptCreateParamsDto pptCreateParamsDto = new PptCreateParamsDto();
        pptCreateParamsDto.setTemplateManagementDetailMapList(templateManagementDetailMapList);

        // 反馈ppt生成进度
//		sseMessageService.sendMessage(SecurityUtils.getUserId().toString(),
//				"初始化完成", Constants.PPT_PROGRESS_INIT_SUCCESS);
        redisPublisher.publish2(
                "初始化完成"
                        + "%end%"
                        + Constants.PPT_PROGRESS_INIT_SUCCESS);

        // 返回的dto
        ResultDto resultDto = null;
        switch (type) {
            case "file":
                // 根据上传的文件进行处理 生成ppt文件
                resultDto = dealUploadFileCreatePpt(pPtParam, pptCreateParamsDto, outFileFolder, deleteFileSet);
                break;
            case "text":
                // 处理输入的文本来生成ppt
                resultDto = dealInputTextCreatePpt(pPtParam, pptCreateParamsDto, outFileFolder, deleteFileSet);
                break;
            default:
                throw new RuntimeException("不支持的格式类型");
        }

////      异步删除中间格式化文档 远程file服务调用进行文件延迟删除
//		CompletableFuture.runAsync(() -> {
//			remoteFileService.scheduleFileDeletion(FileDeletionParamsDto.builder()
//					.filepathList(new ArrayList<>(deleteFileSet))
//					.delayMinutes(3)
//					.build());
//		});
        asyncService.deleteFileSetAsync(deleteFileSet);
        // 延时删除文件或者目录
        fileCustomUtils.scheduleFileDeletion(outFileFolder, 10);
        return resultDto;
    }

    /**
     * 获取模板管理详细信息
     *
     * @param templateManagementId 模板管理ID
     * @return List<Map < String, String>>
     * <AUTHOR>
     * @date 2024/09/05
     */
    private List<Map<String, String>> getTemplateManagementDetail(Long templateManagementId) {
        List<Map<String, String>> detailMapList = pptTemplateManagementDetailService.list(new LambdaQueryWrapper<PptTemplateManagementDetail>()
                        .eq(PptTemplateManagementDetail::getTemplateManagementId, templateManagementId))
                .stream().map(item -> {
                    Map<String, String> map = new HashMap<>();
                    map.put("id", String.valueOf(item.getId()));
                    map.put("templateManagementId", String.valueOf(item.getTemplateManagementId()));
                    map.put("fileName", item.getFileName());
                    map.put("contentType", item.getContentType());
                    map.put("themeLabel", item.getThemeLabel());
                    map.put("versionStructure", item.getVersionStructure());
                    map.put("suffix", item.getSuffix());
                    map.put("absolutePath", item.getAbsolutePath());
                    map.put("unzipFolder", item.getUnzipFolder());
                    map.put("fileNameWithExtension", item.getFileName() + "." + item.getSuffix());
                    return map;
                }).collect(Collectors.toList());

        if (detailMapList.isEmpty()) {
            throw new RuntimeException("模板主题详情列表为空");
        }

        return detailMapList;
    }

    /**
     * 根据模板主题id来获取模板信息 并设置到pPtParam
     *
     * @param pPtParam p pt param
     * <AUTHOR>
     * @date 2024/09/05
     */
    private void getTemplateManagement(PPtParam pPtParam) {
        Long templateManagementId = pPtParam.getTemplateManagementId();
        if (templateManagementId == null) {
            throw new RuntimeException("模板主题为空, 请先选择主题");
        }
        pPtParam.setTemplateManagement(pptTemplateManagementService.getById(templateManagementId));
    }

    /**
     * 根据上传的文件进行处理 生成ppt文件
     *
     * @param pPtParam           参数
     * @param deleteFileSet      待删除的临时文件集合
     * @param pptCreateParamsDto ppt创建参数对象
     * @param outFileFolder      输出文件路径
     */
    private ResultDto dealUploadFileCreatePpt(PPtParam pPtParam, PptCreateParamsDto pptCreateParamsDto, String outFileFolder, Set<String> deleteFileSet) {
        // 对上传的文件进行处理 转换成 可用txt文档 地址
        String fileToFormatTextPath = dealFileToFormatText(pPtParam, deleteFileSet);
        // 非空判断，继续把可用的txt文档 通过ppt模板 转换成 ppt文件
        if (!StringUtils.isNotBlank(fileToFormatTextPath)) {
            throw new RuntimeException("文件转换异常");
        }
        // 反馈ppt生成进度
//		sseMessageService.sendMessage(SecurityUtils.getUserId().toString(),
//				"文本解析成功",
//				Constants.PPT_PROGRESS_TEXT_PARSE_SUCCESS);
        redisPublisher.publish2(
                "文本解析成功"
                        + "%end%"
                        + Constants.PPT_PROGRESS_TEXT_PARSE_SUCCESS);
        // 把格式后的txt文档 生成ppt
        pptCreateParamsDto.setFormateTextfilePath(fileToFormatTextPath);
        pptCreateParamsDto.setOutFileFolder(outFileFolder);
        pptCreateParamsDto.setPptTemplateManagement(pPtParam.getTemplateManagement());
        ResultDto resultDto = dealFormatTextToCreatePpt(pptCreateParamsDto);
        // 反馈ppt生成进度
//		sseMessageService.sendMessage(SecurityUtils.getUserId().toString(),
//				"ppt生成成功",
//				Constants.PPT_PROGRESS_PPT_SUCCESS);

        redisPublisher.publish2(
                "ppt生成成功成"
                        + "%end%"
                        + Constants.PPT_PROGRESS_PPT_SUCCESS);
        return resultDto;
    }

    /**
     * 处理输入的文本来生成ppt
     *
     * @param pPtParam           参数
     * @param outFileFolder      输出目录
     * @param deleteFileSet      待删除的临时文件地址集合
     * @param pptCreateParamsDto ppt生成参数对象
     */
    private ResultDto dealInputTextCreatePpt(PPtParam pPtParam, PptCreateParamsDto pptCreateParamsDto, String outFileFolder, Set<String> deleteFileSet) throws IOException {
        // 根据输入的文本先生成txt文件
        String generateTxtFilePath = generateTxtFile(pPtParam.getQuery(), outFileFolder, deleteFileSet);
        // 非空判断，
        if (StringUtils.isBlank(generateTxtFilePath)) {
            throw new RuntimeException("文件转换异常");
        }
        // 继续txt文档 格式化 可用的txt文档
        pPtParam.setFilePath(generateTxtFilePath);
        pPtParam.setFileId(null);
        String formatTextFilePath = dealFileToFormatText(pPtParam, deleteFileSet);
        // 非空判断，
        if (StringUtils.isBlank(formatTextFilePath)) {
            throw new RuntimeException("文件转换异常");
        }
        // 反馈ppt生成进度
//		sseMessageService.sendMessage(SecurityUtils.getUserId().toString(),
//				"文本解析成功",
//				Constants.PPT_PROGRESS_TEXT_PARSE_SUCCESS);

        redisPublisher.publish2(
                "文本解析成功"
                        + "%end%"
                        + Constants.PPT_PROGRESS_TEXT_PARSE_SUCCESS);

        // 继续把可用的txt文档 通过ppt模板 转换成 ppt文件
        pptCreateParamsDto.setFormateTextfilePath(formatTextFilePath);
        pptCreateParamsDto.setPptTemplateManagement(pPtParam.getTemplateManagement());
        pptCreateParamsDto.setOutFileFolder(outFileFolder);
        ResultDto resultDto = dealFormatTextToCreatePpt(pptCreateParamsDto);
        // 反馈ppt生成进度
//		sseMessageService.sendMessage(SecurityUtils.getUserId().toString(),
//				"ppt生成成功",
//				Constants.PPT_PROGRESS_PPT_SUCCESS);

        redisPublisher.publish2(
                "ppt生成成功"
                        + "%end%"
                        + Constants.PPT_PROGRESS_PPT_SUCCESS);
        return resultDto;
    }

    /**
     * 判断传递的参数是否是随机自动选择
     */
    private void justIsAutoTheme(PPtParam pPtParam) {
        if ("auto".equals(pPtParam.getTemplateManagement().gettValue())) {
            List<PptTemplateManagement> list = pptTemplateManagementService.list().stream().filter(item ->
                    !"auto".equals(item.gettValue())).collect(Collectors.toList());
            if (list.isEmpty()) {
                throw new RuntimeException("暂无可用主题模板,请先上传模板");
            }
            // 重新设置随机选择的主题管理 和id
            PptTemplateManagement templateManagement = list.get(RandomUtil.randomInt(list.size()));
            pPtParam.setTemplateManagementId(templateManagement.getId());
            pPtParam.setTemplateManagement(templateManagement);
        }
    }

    /**
     * 构建ppt输出目录
     */
    private String createOutFileFolder(PPtParam pPtParam) {
        // 输出文件夹目录 通过字典数据获得输出文件目录 D:\\tmp\result\\
        String osName = Utils.getOsName();
        String dictValue = "";
        ;
        if ("win".equals(osName)) {
            dictValue = dealPptParamArgs(Constants.WIN_Out_File_Folder, Constants.Sys_Dict_Theme_File_Path).getDictValue();
        } else if ("linux".equals(osName)) {
            dictValue = dealPptParamArgs(Constants.LINUX_Out_File_Folder, Constants.Sys_Dict_Theme_File_Path).getDictValue();
        } else {
            throw new RuntimeException(osName + "暂不支持的操作系统");
        }

        // 重新定义数据目录 加上日期格式
        // 例如 dictValue= D:\tmp\result\
        // 新的输入文件夹目录 D:\tmp\result\2023-05-21\out\currentDate\
        // 获取当前日期并格式化
        String currentDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        long l = System.currentTimeMillis();
        String outFileFolder = "";
        if (dictValue.endsWith(File.separator)) {
            outFileFolder = dictValue + currentDate + File.separator + "out" + File.separator + l + File.separator;
            pPtParam.setOutFileFolder(outFileFolder);
        } else {
            outFileFolder = dictValue + File.separator + currentDate + File.separator + "out" + File.separator + l + File.separator;
            pPtParam.setOutFileFolder(outFileFolder);
        }
        // 将路径中的所有分隔符统一为正斜杠 "/"
        outFileFolder = fileCustomUtils.normalizePath(outFileFolder);
        return outFileFolder;
    }

    public String generatePreviewImage(String path, PPtParam pPtParam) throws Exception {
        // 生成预览图逻辑
        int lastDotIndex = path.lastIndexOf('.');
        String outPath = lastDotIndex > 0 ? path.substring(0, lastDotIndex) + ".png" : path + ".png";

        File file = new File(path);
        String newName = "copy_" + System.currentTimeMillis() + file.getName();
        String path2 = file.getPath().replace(file.getName(), newName);
        File tmpFile = new File(path2);
        setPptFontStyle.copyFile(file, tmpFile);

        String[] split = pPtParam.getSpacingXY().split("(?i)and");
        if (split.length != 2) {
            split = new String[]{"0", "0"};
        }

        pptToLongImage.generateLongImageFromPPT(path2, outPath,
                Integer.valueOf(pPtParam.getLongImgRows()),
                Integer.valueOf(split[0]), Integer.valueOf(split[1]), pPtParam.getSelectedQuality());

        deleteFileSingleAsync(tmpFile);

        return outPath;
    }

    public List<String> generateSingleImgPreviewImages(String path, PPtParam pPtParam) throws Exception {
        // 生成预览单图逻辑
        String outPath = "";
        if (pPtParam.getOutFileFolder().endsWith(File.separator)) {
            outPath = pPtParam.getOutFileFolder() + "slideImgPreview" + File.separator;
        } else {
            outPath = pPtParam.getOutFileFolder() + File.separator + "slideImgPreview" + File.separator;
        }

        // 检查并创建输出目录
        fileCustomUtils.checkAndCreateDirectory(outPath);

        File file = new File(path);
        String newName = "copy_" + System.currentTimeMillis() + file.getName();
        String tmpFilePath = file.getPath().replace(file.getName(), newName);
        File tmpFile = new File(tmpFilePath);
        setPptFontStyle.copyFile(file, tmpFile);

//        List<String> imgSinglePreviewList = pptToLongImage.generateSingleImagesFromPPT(tmpFilePath, outPath, pPtParam.getSelectedQuality());

        List<String> imgSinglePreviewList = pptToLongImage.generateSingleImagesFromPPT2(path, outPath, pPtParam.getSelectedQuality());
        deleteFileSingleAsync(tmpFile);

        return imgSinglePreviewList;
    }


    /**
     * 异常处理删除中间文档集合
     */
    public CompletableFuture<String> deleteFileSetAsync(Set<String> filePaths) {
        // 如果列表为空，直接返回一个已经完成的 CompletableFuture
        if (filePaths == null || filePaths.isEmpty()) {
            return CompletableFuture.completedFuture("No files to delete.");
        }

        return CompletableFuture.supplyAsync(() -> {
            StringBuilder resultMessage = new StringBuilder();
            for (String filePath : filePaths) {
                File file = new File(filePath);
                int attempts = 0;
                boolean deleted = false;
                while (attempts < 3 && !deleted) {
                    if (file.exists()) {
                        deleted = file.delete();
                        if (deleted) {
//                            resultMessage.append("File deleted successfully: ").append(filePath).append("\n");
                        } else {
//                            resultMessage.append("Failed to delete file: ").append(filePath).append("\n");
                            attempts++;
                            try {
                                Thread.sleep(10000); // Wait for 1 minute before retrying
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt(); // Restore interrupt status
                            }
                        }
                    } else {
                        resultMessage.append("File does not exist: ").append(filePath).append("\n");
                        break;
                    }
                }
                if (!deleted) {
                    resultMessage.append("Failed to delete file after 3 attempts: ").append(filePath).append("\n");
                }
            }
            return resultMessage.toString();
        });
    }

    /**
     * 异常处理删除中间文件
     */
    public void deleteFileSingleAsync(File file) {
        // 如果列表为空，直接返回一个已经完成的 CompletableFuture
        if (file == null || !file.exists()) {
            return;
        }
        CompletableFuture.supplyAsync(() -> {
            StringBuilder resultMessage = new StringBuilder();
            int attempts = 0;
            boolean deleted = false;
            while (attempts < 5 && !deleted) {
                if (file.exists()) {
                    deleted = file.delete();
                    if (deleted) {
                        resultMessage.append("File deleted successfully");
                    } else {
                        attempts++;
                        try {
                            Thread.sleep(10000); // Wait for 1 minute before retrying
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt(); // Restore interrupt status
                        }
                    }
                }
            }
            if (!deleted) {
                resultMessage.append("Failed to delete file after 3 attempts: ").append(file.getPath());
            }
            return resultMessage.toString();
        });
    }

    /**
     * 根据键值查询系统Sys_Dict_Theme_File_Path字典数据，
     * 主要用于获取到logoPosition的位置标识
     *
     * @param key 要查询的字典键值。
     * @param str 要查询的字典标签
     * @return 匹配的字典数据。
     * @throws RuntimeException 如果字典数据为空或查询不到匹配数据，则抛出运行时异常。
     */
    private SysDictData getLogoPositionLabel(String key, String str) {
        if (StringUtils.isBlank(key)) {
            throw new RuntimeException("字典键值key不能为空");
        }
        // 根据键值获取字典数据
        List<Object> dictCacheObjectList = redisService.getCacheObject(str);

        // 非空判断
        if (CollectionUtils.isEmpty(dictCacheObjectList)) {
            throw new RuntimeException("字典数据+ 【" + str + "】异常,请先配置字典");
        }

        // 通过流处理objet类型数据 转成 相应实体数据
        List<SysDictData> dictCacheList = dictCacheObjectList.stream()
                .map(dataObj -> JSON.parseObject(JSON.toJSONString(dataObj), SysDictData.class))
                .collect(Collectors.toList());

        List<SysDictData> collectList = dictCacheList.stream()
                .filter(dictData -> StringUtils.equals(key, dictData.getDictValue()))
                .collect(Collectors.toList());

        if (collectList.isEmpty()) {
            throw new RuntimeException("当前key：【" + key + "】无对应的字典数据,请先配置字典");
        }
        return collectList.get(0);
    }


    /**
     * 根据键值查询系统字典数据，并返回匹配的第一条数据。
     * 主要用于根据特定键值（如主题文件路径）从系统字典中检索相应数据。
     *
     * @param key     要查询的字典键值。
     * @param sysDict Constants.Sys_Dict_Theme_File_Path
     * @return 匹配的字典数据。
     * @throws RuntimeException 如果字典数据为空或查询不到匹配数据，则抛出运行时异常。
     */
    private SysDictData dealPptParamArgs(String key, String sysDict) {
        if (StringUtils.isBlank(key)) {
            throw new RuntimeException("字典键值key不能为空");
        }
        // 根据键值获取字典数据
        List<Object> dictCacheObjectList = redisService.getCacheObject(sysDict);

        // 非空判断
        if (CollectionUtils.isEmpty(dictCacheObjectList)) {
            throw new RuntimeException("字典数据+ 【" + sysDict + "】异常,请先配置字典");
        }

        // 通过流处理objet类型数据 转成 相应实体数据
        List<SysDictData> dictCacheList = dictCacheObjectList.stream()
                .map(dataObj -> JSON.parseObject(JSON.toJSONString(dataObj), SysDictData.class))
                .collect(Collectors.toList());

        List<SysDictData> collectList = dictCacheList.stream()
                .filter(dictData -> StringUtils.equals(key, dictData.getDictLabel()))
                .collect(Collectors.toList());

        if (collectList.isEmpty()) {
            throw new RuntimeException("当前key：【" + key + "】无对应的字典数据,请先配置字典");
        }
        return collectList.get(0);
    }

    /**
     * 根据文本内容生成txt文档
     *
     * @param query         内容
     * @param outFileFolder 输出文件夹目录
     * @param deleteFileSet 需要删除的中间格式化产生的文档地址集合
     * @return 地址
     */
    private String generateTxtFile(String query, String outFileFolder, Set<String> deleteFileSet) throws IOException {
        if (query == null || query.isEmpty()) {
            throw new RuntimeException("内容不能为空");
        }
        if (outFileFolder == null || outFileFolder.isEmpty()) {
            throw new RuntimeException("输出文件夹目录不能为空");
        }
        // 检验输入的字数 超出设定的字数 方法会抛出异常不需要二次判定
        int fontNum = countCharactersInString(query);
        log.info("输入文本字数：{},校验通过", fontNum);
        // 反馈ppt生成进度
//		sseMessageService.sendMessage(SecurityUtils.getUserId().toString(),
//				"输入文本字数:" + fontNum + "校验通过",
//				Constants.PPT_PROGRESS_TEXT_SUCCESS);

        redisPublisher.publish2(
                "输入文本字数:" + fontNum + "校验通过"
                        + "%end%"
                        + Constants.PPT_PROGRESS_TEXT_SUCCESS);

        // 当前时间戳
        String timestamp = String.valueOf(System.currentTimeMillis());
        String filePath = outFileFolder + timestamp + ".txt";

        //创建文件对象
        File file = new File(filePath);

        try (BufferedWriter writer = new BufferedWriter(new FileWriter(file))) {
            try {
                // 将内容写入文件
                writer.write(query);
            } finally {
                writer.close();
            }

        } catch (IOException e) {
            // 处理异常
            throw new RuntimeException(e);
        }
//		// 使用远程file服务进行根据文本内容创建文件
//		AjaxResult remoteResult = remoteFileService.textFileCreate(TextFileCreationDto.builder()
//				.text(query)
//				.filePath(filePath)
//				.build());
//		if ("200".equals(remoteResult.get("code").toString())) {
//			log.info((remoteResult.get("msg").toString()));
//		} else {
//			log.error(remoteResult.get("msg").toString());
//            throw new RuntimeException(remoteResult.get("msg").toString());
//		}

        deleteFileSet.add(filePath);
        // 返回文件路径
        return filePath;
    }

    /**
     * 通过theme来获取ppt模板
     *
     * @param theme 主题 淡雅-dy
     * @return 模板 dy
     * @getThemeFolderBase 主题文件目录
     */
    private String getPptModeByTheme(String theme, String themeFolderBase) {
        // 根据key获取缓存字典数据
        List<Object> dictCacheObjectList = redisService.getCacheObject(Constants.Sys_Dict_Custom_Theme);

        // 非空判断
        if (CollectionUtils.isEmpty(dictCacheObjectList)) {
            throw new RuntimeException("字典数据+ 【" + Constants.Sys_Dict_Custom_Theme + "】异常,请先配置字典");
        }
        // 通过流处理objet类型数据 转成 相应实体数据 并过滤出 status=0的正常数据
        List<SysDictData> dictCacheList = dictCacheObjectList.stream()
                .map(obj -> JSON.parseObject(JSON.toJSONString(obj), SysDictData.class))
                .filter(obj -> StringUtils.equals("0", obj.getStatus()))
                .collect(Collectors.toList());

//        System.out.println("Sys_Dict_Theme" + Constants.Sys_Dict_Custom_Theme);
//        System.out.println("dictCache" + dictCacheList);


        // 获取主题对应的字典数据
        List<SysDictData> collectList = dictCacheList.stream()
                .filter(dictData -> StringUtils.equals(theme, dictData.getDictValue()))
                .collect(Collectors.toList());
//        System.out.println("collectList==" + collectList);
//        System.out.println("theme:" + theme);

        // 非空判断
        if (collectList.isEmpty()) {
            throw new RuntimeException("主题模板数据异常,请重新尝试");
        }

        // 判断是否是自动，随机从已有的字典中获取数据主题 （auto）
        if ("auto".equals(collectList.get(0).getDictValue())) {
            dictCacheList = dictCacheList.stream()
                    .filter(dict -> !StringUtils.equals("auto", dict.getDictValue())).collect(Collectors.toList());
            int i = RandomUtil.randomInt(dictCacheList.size());
            SysDictData sysDictData = dictCacheList.get(i);
            String dictValue = sysDictData.getDictValue();
            System.out.println("随机选择主题：" + dictValue);
            return dictValue;
//            File baseDir = new File(themeFolderBase);
//            if (!baseDir.exists()) {
//                throw new RuntimeException("模板目录文件夹不存在,请重新尝试");
//            }
//            // 获取该目录下的所有子目录
//            File[] subdirectories = baseDir.listFiles(File::isDirectory);
//            // 随机选择一个目录获取到主题标识
//            assert subdirectories != null;
//            int i = RandomUtil.randomInt(subdirectories.length);
//            // 获取 "-" 之前的内容作为 theme
//            String subdirectoryName = subdirectories[i].getName();
//            String substring = subdirectoryName.substring(0, subdirectoryName.indexOf("-"));
//            return substring;
        } else {
            // 正常情况只会有一个匹配，默认取第一个
            return collectList.get(0).getDictValue();
        }
    }

    /**
     * 添加logo
     *
     * @param pptxFilePath pptx文件路径
     * @param imageWidth   logo宽度
     * @param imageHeight  logo高度
     * @param logoZoom     logo缩放
     */
    public void addLogoInPPTBySlide(String pptxFilePath, double imageWidth, double imageHeight, String logoPosition, String logoZoom) throws Exception {
//        String pptxFilePath = "D:\\tmp\\t\\simpleStyleEducation_page_4_0_1_1_1.pptx"; // 输入PPT文件路径
//        String logoFilePath = "D:\\tmp\\t\\logo2.png"; // 输入Logo图片路径
        // 通过redis缓存获取到log路径
        SysDictData sysDictData = dealPptParamArgs(Constants.Logo_Path, Constants.Sys_Dict_Theme_File_Path);
        String dictValue = sysDictData.getDictValue();
        // 使用 try-with-resources 语句来自动关闭资源
        try (XMLSlideShow ppt = new XMLSlideShow(new FileInputStream(pptxFilePath));
             FileInputStream logoInputStream = new FileInputStream(dictValue)) {

            // 读取 logo 图片
            byte[] logoBytes = IOUtils.toByteArray(logoInputStream); // 图片的输入流
            XSLFPictureData pictureData = ppt.addPicture(logoBytes, PictureData.PictureType.PNG);

            // 获取图片原始大小
//            BufferedImage logoImage = ImageIO.read(new ByteArrayInputStream(logoBytes));
            if (imageWidth == 0 || imageHeight == 0) {
                imageWidth = 200.0;
                imageHeight = 60.0;
            }
            if (StringUtils.isNotBlank(logoZoom)) {
                imageWidth *= Integer.parseInt(logoZoom) / 100.0;
                imageHeight *= Integer.parseInt(logoZoom) / 100.0;
            }
            // 计算图片位置
            double x = 0.0, y = 0.0;
            if (StringUtils.isNotEmpty(logoPosition)) {
                switch (logoPosition) {
                    case Constants.LOGO_TOP_CENTER:
                        x = (ppt.getPageSize().getWidth() - imageWidth) / 2;
                        y = 0;
                        break;
                    case Constants.LOGO_TOP_LEFT:
                        x = 0;
                        y = 0;
                        break;
                    case Constants.LOGO_TOP_RIGHT:
                        x = ppt.getPageSize().getWidth() - imageWidth;
                        y = 0;
                        break;
                    case Constants.LOGO_BOTTOM_CENTER:
                        x = (ppt.getPageSize().getWidth() - imageWidth) / 2;
                        y = ppt.getPageSize().getHeight() - imageHeight;
                        break;
                    case Constants.LOGO_BOTTOM_LEFT:
                        x = 0;
                        y = ppt.getPageSize().getHeight() - imageHeight;
                        break;
                    case Constants.LOGO_BOTTOM_RIGHT:
                        x = ppt.getPageSize().getWidth() - imageWidth;
                        y = ppt.getPageSize().getHeight() - imageHeight;
                        break;
                    default:
                        // 默认位置 右上角
                        x = ppt.getPageSize().getWidth() - imageWidth;
                        y = 0;
                        break;
                }
            } else {
                // 默认位置 右上角
                x = ppt.getPageSize().getWidth() - imageWidth;
                y = 0;
            }


            // 在右上上角添加Logo
            // 设置唯一标识符
            String uniqueId = "logo-" + System.currentTimeMillis();
            List<XSLFSlide> slides = ppt.getSlides();
            for (XSLFSlide slide : slides) {
                XSLFPictureShape picture = slide.getSlideLayout().createPicture(pictureData);
                picture.setName(uniqueId);
                picture.setAnchor(new Rectangle2D.Double(x, y, imageWidth, imageHeight));
            }

            // 将修改后的PPT保存回原文件
            try (OutputStream outputStream = Files.newOutputStream(Paths.get(pptxFilePath))) {
                ppt.write(outputStream);
                log.info("{}，添加logo，{}成功", pptxFilePath, dictValue);
            } catch (IOException e) {
                log.error("Error saving the PPT file: {}", e.getMessage());
            }

        } catch (
                IOException e) {
            log.error("Error processing the PPT file: {}", e.getMessage());
        }
    }

    /**
     * 添加logo
     *
     * @param pptxFilePath pptx文件路径
     * @param imageWidth   logo宽度
     * @param imageHeight  logo高度
     * @param logoZoom     logo缩放
     */
    public void addLogoInPPT(String pptxFilePath, double imageWidth, double imageHeight, String logoPosition, String logoPath, String logoZoom) throws Exception {
        // 传递的logoPath为空
        if (StringUtils.isBlank(logoPath)) {
            // 通过redis缓存获取到log路径
            SysDictData sysDictData = dealPptParamArgs(Constants.Logo_Path, Constants.Sys_Dict_Theme_File_Path);
            logoPath = sysDictData.getDictValue();
        }

        pptLogoAdder.addLogo(pptxFilePath, logoPath, imageWidth, imageHeight, logoPosition, logoZoom);
    }
//    public void addLogoInPPT(String pptxFilePath, double imageWidth, double imageHeight, String logoPosition, String logoZoom) throws Exception {
////        String pptxFilePath = "D:\\tmp\\t\\simpleStyleEducation_page_4_0_1_1_1.pptx"; // 输入PPT文件路径
////        String logoFilePath = "D:\\tmp\\t\\logo2.png"; // 输入Logo图片路径
//        long l1 = System.currentTimeMillis();
//        // 通过redis缓存获取到log路径
//        SysDictData sysDictData = dealPptParamArgs(Constants.Logo_Path);
//        String dictValue = sysDictData.getDictValue();
//        // 使用 try-with-resources 语句来自动关闭资源
//
//        try (XMLSlideShow ppt = new XMLSlideShow(new FileInputStream(pptxFilePath));
//             FileInputStream logoInputStream = new FileInputStream(dictValue)) {
//
//            // 读取 logo 图片
//            byte[] logoBytes = IOUtils.toByteArray(logoInputStream); // 图片的输入流
//            XSLFPictureData pictureData = ppt.addPicture(logoBytes, PictureData.PictureType.PNG);
//
//            // 获取图片原始大小
////            BufferedImage logoImage = ImageIO.read(new ByteArrayInputStream(logoBytes));
//            if (imageWidth == 0 || imageHeight == 0) {
//                imageWidth = 200.0;
//                imageHeight = 60.0;
//            }
//            if (StringUtils.isNotBlank(logoZoom)) {
//                imageWidth *= Integer.parseInt(logoZoom) / 100.0;
//                imageHeight *= Integer.parseInt(logoZoom) / 100.0;
//            }
//            // 计算图片位置
//            double x = 0.0, y = 0.0;
//            if (StringUtils.isNotEmpty(logoPosition)) {
//                switch (logoPosition) {
//                    case Constants.LOGO_TOP_CENTER:
//                        x = (ppt.getPageSize().getWidth() - imageWidth) / 2.txt;
//                        y = 0;
//                        break;
//                    case Constants.LOGO_TOP_LEFT:
//                        x = 0;
//                        y = 0;
//                        break;
//                    case Constants.LOGO_TOP_RIGHT:
//                        x = ppt.getPageSize().getWidth() - imageWidth;
//                        y = 0;
//                        break;
//                    case Constants.LOGO_BOTTOM_CENTER:
//                        x = (ppt.getPageSize().getWidth() - imageWidth) / 2.txt;
//                        y = ppt.getPageSize().getHeight() - imageHeight;
//                        break;
//                    case Constants.LOGO_BOTTOM_LEFT:
//                        x = 0;
//                        y = ppt.getPageSize().getHeight() - imageHeight;
//                        break;
//                    case Constants.LOGO_BOTTOM_RIGHT:
//                        x = ppt.getPageSize().getWidth() - imageWidth;
//                        y = ppt.getPageSize().getHeight() - imageHeight;
//                        break;
//                    default:
//                        // 默认位置 右上角
//                        x = ppt.getPageSize().getWidth() - imageWidth;
//                        y = 0;
//                        break;
//                }
//            } else {
//                // 默认位置 右上角
//                x = ppt.getPageSize().getWidth() - imageWidth;
//                y = 0;
//            }
//
//
//            // 遍历所有幻灯片，并在左上角添加Logo
//            List<XSLFSlide> slides = ppt.getSlides();
//
//            for (int i = 0; i < slides.size(); i++) {
////                if (i == 0 || i == slides.size() - 1) {
////                    continue;
////                }
//                XSLFSlide slide = slides.get(i);
//                // 创建图片并设置位置和大小
//                XSLFPictureShape picture = slide.createPicture(pictureData);
//                // 设置唯一标识符
//                String uniqueId = "logo-" + System.currentTimeMillis();
//                picture.setName(uniqueId);
//                picture.setAnchor(new Rectangle2D.Double(x, y, imageWidth, imageHeight));
//
//                // 获取当前幻灯片的内容列表
//                List<XSLFShape> shapes = slide.getShapes();
//
//                // 将图片形状移到形状列表的最前面，实现图片置于最上层的效果
//                shapes.remove(picture);
//                shapes.add(0, picture);
//            }
//
//            // 将修改后的PPT保存回原文件
//            try (OutputStream outputStream = Files.newOutputStream(Paths.get(pptxFilePath))) {
//                ppt.write(outputStream);
//                log.info("{}，添加logo，{}成功", pptxFilePath, dictValue);
//            } catch (IOException e) {
//                log.error("Error saving the PPT file: {}", e.getMessage());
//            }
//            long l2 = System.currentTimeMillis();
//            log.info("添加logo耗时：{}", l2 - l1);
//        } catch (
//                IOException e) {
//            log.error("Error processing the PPT file: {}", e.getMessage());
//        }
//    }

    /**
     * 获取主题对应的模板文件夹路径
     *
     * @param theme 主题 如dy (淡雅)
     * @return 路径
     * @themeFolderBasePath 模板文件夹路径
     */
    private String getPptModFolderFByTheme(String theme, String themeFolderBase) {
        // 基础路径 默认放置模板的目录
//        StringBuilder themeFolderBasePath = new StringBuilder("D:\\tmp\\ppt-model\\");
        // 找到以 主题-xxx 命名的文件
        // 创建一个 File 对象，表示 basePath 目录
        StringBuilder themeFolderBasePath = new StringBuilder(themeFolderBase);
        File baseDir = new File(themeFolderBase);
        if (!baseDir.exists()) {
            throw new RuntimeException("模板目录不存在,请重新尝试");
        }
        // 获取该目录下的所有子目录
        File[] subdirectories = baseDir.listFiles(File::isDirectory);
        if (subdirectories == null) {
            throw new RuntimeException("模板目录不存在,请重新尝试");
        }

        // 输出子目录的名字 找到 主题 对应的模板文件夹
        for (File subdirectory : subdirectories) {
            String subdirectoryName = subdirectory.getName();
            // 判断子目录名中是否包含 "-"
            if (subdirectoryName.contains("-")) {
                // 获取 "-" 之前的内容作为 theme
                String substring = subdirectoryName.substring(0, subdirectoryName.indexOf("-"));
                // 如果 theme 和指定的 theme 匹配，则输出子目录名
                if (substring.equals(theme)) {
                    themeFolderBasePath.append(subdirectoryName).append(File.separator);
                    System.out.println("子目录名: " + subdirectoryName);
                    return themeFolderBasePath.toString();
                }
            }
        }
        throw new RuntimeException("当前主题" + theme + "没有对应的模板,请联系管理人员添加模板");
    }

    /**
     * 下载文件。
     * 此方法处理文件的下载逻辑。它通过读取指定文件路径的PowerPoint文件，
     * 并将其内容以响应的形式发送给客户端，从而实现文件的下载。
     *
     * @param filePath 文件路径，指定要下载的PowerPoint文件的位置。
     * @param request  HttpServletRequest对象，代表客户端的请求。
     * @param response HttpServletResponse对象，用于向客户端发送响应。
     */
    @Override
    public void downLoadPPt(String filePath, HttpServletRequest request, HttpServletResponse response) {
        File file = new File(filePath);

        if (!file.exists()) {
            throw new RuntimeException("文件不存在");
        }

        // 设置响应头信息
        try {
            response.reset();
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(file.getName(), "utf8"));
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Cache-Control", "no-cache");
            response.setHeader("filename", URLEncoder.encode("xxxPPt", "utf8"));
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition, filename");
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Content-Length", String.valueOf(file.length()));
            response.setHeader("Connection", "keep-alive");
            //设置浏览器接受类型为流
            response.setContentType("application/octet-stream;charset=UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        // 使用 try-with-resources 确保资源自动关闭
        try (InputStream inputStream = new BufferedInputStream(Files.newInputStream(file.toPath()), 8192);
             OutputStream outputStream = new BufferedOutputStream(response.getOutputStream(), 8192)) {
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            log.info("{}，文件流写入成功", file.getPath());
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

//    @Override
//    public void downLoadPPt(String filePath, HttpServletRequest request, HttpServletResponse response) {
//        File file = new File(filePath);
//
//        if (!file.exists()) {
//            throw new RuntimeException("文件不存在");
//        }
//
//        String name = file.getName();
//        name = name.substring(name.indexOf("[") + 1, name.lastIndexOf("]")) + ".pptx";
//        // 设置响应头信息
//        try {
//            response.reset();
//            response.setCharacterEncoding("UTF-8");
//            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(file.getName(), "utf8"));
//            response.setHeader("Pragma", "no-cache");
//            response.setHeader("Cache-Control", "no-cache");
//            response.setHeader("filename", URLEncoder.encode(name, "utf8"));
//            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition, filename");
//            response.setHeader("Access-Control-Allow-Origin", "*");
//            //设置浏览器接受类型为流
//            response.setContentType("application/octet-stream;charset=UTF-8");
//        } catch (UnsupportedEncodingException e) {
//            e.printStackTrace();
//        }
//
//        // 使用 try-with-resources 确保资源自动关闭
//        try (InputStream inputStream = Files.newInputStream(file.toPath());
//             OutputStream outputStream = response.getOutputStream()) {
//            // 增加缓冲区大小，一次传输更大的数据块
//            byte[] buffer = new byte[2048]; // 2KB 缓冲区
//            int bytesRead;
//            while ((bytesRead = inputStream.read(buffer)) != -1) {
//                outputStream.write(buffer, 0, bytesRead);
//            }
//            log.info("{}，文件流写入成功", file.getPath());
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//    }

    /**
     * 任意文件地址下载 返回文件流
     *
     * @param filePath 文件地址
     * @param request  请求
     * @param response 响应
     **/
    @Override
    public void downLoad(String filePath, HttpServletRequest request, HttpServletResponse response) {
        File file = new File(filePath);

        if (!file.exists()) {
            throw new RuntimeException("文件不存在");
        }

        try {
            response.reset();
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=\"" + URLEncoder.encode(file.getName(), "utf8") + "\"");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Cache-Control", "no-cache");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-Length", String.valueOf(file.length()));
            response.setContentType("application/octet-stream;charset=UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("编码格式不支持", e);
        }

        try (InputStream inputStream = new BufferedInputStream(Files.newInputStream(file.toPath()), 8192);
             OutputStream outputStream = new BufferedOutputStream(response.getOutputStream(), 8192)) {
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            log.info("文件 {} 写入成功，大小为 {} 字节", file.getName(), file.length());
        } catch (IOException e) {
            throw new RuntimeException("文件下载失败", e);
        }

    }

    @Override
    public ResponseEntity<byte[]> createPptOutputAndReturn(String filePath, HttpServletRequest request) throws IOException {
        File file = new File(filePath);

        if (!file.exists()) {
            throw new RuntimeException("文件不存在");
        }

        try (InputStream inputStream = Files.newInputStream(file.toPath())) {
            byte[] bytes = Files.readAllBytes(file.toPath());
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", URLEncoder.encode(file.getName(), "utf-8"));
            headers.setContentLength(bytes.length);
            headers.set("filename", URLEncoder.encode(file.getName(), "utf8"));
            headers.set("Access-Control-Expose-Headers", "attachment, filename");

            return new ResponseEntity<>(bytes, headers, HttpStatus.OK);
        } catch (IOException e) {
            e.printStackTrace();
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 处理上传的文件,格式化成 指定text文本格式
     *
     * @param pPtParam      参数
     * @param deleteFileSet 需要删除的中间格式化产生的文档地址集合
     * @return 返回生成的格式化txt地址
     */
    private String dealFileToFormatText(PPtParam pPtParam, Set<String> deleteFileSet) {
        // txt文件地址
        String filePath = pPtParam.getFilePath();
        // 主题
        String theme = pPtParam.getTemplateManagement().gettValue();
        // 作者
        String author = pPtParam.getAuthor();
        // 输出
        StringBuilder outFile = new StringBuilder();
        // ppt文本格式化参数对象
        FormatePptTextDto formatePptTextDto = new FormatePptTextDto();
        // filePath存在时，说明是传递的是 根据输入文本生成的txt文件
        if (StringUtils.isNotEmpty(filePath)) {
            // 直接处理文本变成可用的文本模板
            try {
                // 查找 .txt 的位置
                int dotIndex = filePath.lastIndexOf('.');
                // 如果 .txt 存在
                if (dotIndex != -1) {
                    // 在 .txt 之前插入 -format
                    outFile.append(filePath, 0, dotIndex)
                            .append("-formatText")
                            .append(filePath.substring(dotIndex));
                } else {
                    throw new RuntimeException("文件扩展名不正确，请重新上传");
                }
                // 开始格式化txt文档
                formatePptTextDto.setFilePath(filePath);
                formatePptTextDto.setTheme(theme);
                formatePptTextDto.setAuther(author);
                formatePptTextDto.setOutFilePath(outFile.toString());
                textUtils.formatePptText(formatePptTextDto);
                deleteFileSet.add(outFile.toString());
                return outFile.toString();
            } catch (Exception e) {
                throw new RuntimeException("文件处理异常", e);
            }
        } else {
            // 是上传的txt/doc/docx文件
            // 获取文件唯一标识 id
            Long fileObjectName = pPtParam.getFileId();
            //  通过feign 远程调用接口  根据文件id获取文件的存储地址
            AjaxResult fileInfoResult = remoteFileService.getFileInfo(fileObjectName);
            if (Constants.CODE_200 != 200) {
                throw new RuntimeException("远程调用文件服务失败" + fileInfoResult.get("msg"));
            }

            Object o = fileInfoResult.get("data");
            SysFileInfo sysFileInfo = JSON.parseObject(JSON.toJSONString(o), SysFileInfo.class);

            if (sysFileInfo == null) {
                throw new RuntimeException("文件状态异常，请尝试重新上传");
            }
            // 校验文档字数
            File file = new File(sysFileInfo.getFilePath());
            int fontNum = countWordsInFile(file);
            log.info("文档字数：{},校验通过", fontNum);
//			// 反馈ppt生成进度
//			sseMessageService.sendMessage(SecurityUtils.getUserId().toString(),
//					"文档字数:" + fontNum + "校验通过",
//					Constants.PPT_PROGRESS_TEXT_SUCCESS);

            redisPublisher.publish2(
                    "文档字数:" + fontNum + "校验通过"
                            + "%end%"
                            + Constants.PPT_PROGRESS_TEXT_SUCCESS);


            // 构建输出
            String outFileFolder = pPtParam.getOutFileFolder();
            StringBuilder wordToTxtPath = new StringBuilder(outFileFolder);
            // 判断文件类型进行转换
            if ("txt".equals(sysFileInfo.getFileSuffix())) {
                // 本身是txt，直接取地址
                filePath = sysFileInfo.getFilePath();
            } else if ("doc".equals(sysFileInfo.getFileSuffix()) || "docx".equals(sysFileInfo.getFileSuffix())) {
                // 是doc或docx的文档, 转成无格式的txt
                String fileNameWithoutExtension = getFileNameWithoutExtension(sysFileInfo.getFileOriginName());
                wordToTxtPath.append(fileNameWithoutExtension)
                        .append("_wordToTxt_")
                        .append(UUID.randomUUID().toString().replace("-", ""))
                        .append(".txt");
                try {
                    // 调用方法处理doc，docx转成txt
                    convertWordToTxt(sysFileInfo.getFilePath(), wordToTxtPath.toString());
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                filePath = wordToTxtPath.toString();
                deleteFileSet.add(filePath);
            }

            // 获取文件名 去除后缀
            String fileNameWithoutExtension = getFileNameWithoutExtension(sysFileInfo.getFileOriginName());

            outFile.append(outFileFolder).append(fileNameWithoutExtension)
                    .append("_formatText_")
                    .append(UUID.randomUUID().toString().replace("-", ""))
                    .append(".txt");
        }
        // 处理文本变成可用的文本模板
        try {
            formatePptTextDto.setFilePath(filePath);
            formatePptTextDto.setTheme(theme);
            formatePptTextDto.setAuther(author);
            formatePptTextDto.setOutFilePath(outFile.toString());
            textUtils.formatePptText(formatePptTextDto);

            deleteFileSet.add(outFile.toString());
            return outFile.toString();
        } catch (Exception e) {
            throw new RuntimeException("文件处理异常", e);
        }
    }

    /**
     * 获取文件名，不包含后缀
     */
    public static String getFileNameWithoutExtension(String fileName) {
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex == -1) { // No extension found
            return fileName;
        }
        return fileName.substring(0, dotIndex);
    }

    /**
     * 处理后的文本生成ppt
     *
     * @param pptCreateParamsDto 参数对象
     * @return ppt的路径地址
     */
    private ResultDto dealFormatTextToCreatePpt(PptCreateParamsDto pptCreateParamsDto) {

        try {
            // 生成的ppt地址
            ResultDto pptResult = pptUtilsService.createPpt(pptCreateParamsDto);

            if ("false".equals(String.valueOf(pptResult.getResult()))) {
                throw new RuntimeException(String.valueOf(pptResult.getRemark()));
            }
            return pptResult;

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 处理word转txt
     * 兼容 doc和 docx
     *
     * @param inputFilePath  word的地址 如D:\\tmp\\讲义.doc
     * @param outputFilePath 转化后的txt地址 如D:\\tmp\\讲义-wordFormatTxt.txt
     */
    public static void convertWordToTxt(String inputFilePath, String outputFilePath) {
        try (FileInputStream fis = new FileInputStream(inputFilePath)) {
            if (inputFilePath.endsWith(".docx")) {
                // 处理 .docx 文件
                try (XWPFDocument document = new XWPFDocument(fis);
                     FileWriter fw = new FileWriter(outputFilePath)) {

                    XWPFWordExtractor extractor = new XWPFWordExtractor(document);
                    String text = extractor.getText();

                    fw.write(text);
                }
            } else if (inputFilePath.endsWith(".doc")) {
                // 处理 .doc 文件
                try (HWPFDocument document = new HWPFDocument(fis);
                     FileWriter fw = new FileWriter(outputFilePath)) {

                    WordExtractor extractor = new WordExtractor(document);
                    String[] paragraphs = extractor.getParagraphText();

                    for (String paragraph : paragraphs) {
                        fw.write(paragraph + "\n");
                    }
                }
            } else {
                System.err.println("不支持的文档格式化类型: " + inputFilePath);
            }
        } catch (Exception e) {
            throw new RuntimeException("word转txt异常" + e);
        }
    }


    /**
     * 获取系统可用字体map集合列表
     *
     * @return
     */
    @Override
    public List<Map<String, String>> getFontList() {
        List<Object> cacheObject = redisService.getCacheObject(Constants.SYS_FONT_MAP_LIST);

        if (cacheObject == null || cacheObject.isEmpty()) {
            List<Map<String, String>> fontList = SetPptFontStyle.getFontList();
            Collections.reverse(fontList);
            // 添加进入缓存
            redisService.setCacheObject(Constants.SYS_FONT_MAP_LIST, fontList);
            return fontList;
        } else {
            // 将缓存中的 List<Object> 转换为 List<Map<String, String>>
            List<Map<String, String>> fontList = new ArrayList<>();
            for (Object obj : cacheObject) {
                if (obj instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, String> map = (Map<String, String>) obj;
                    fontList.add(map);
                } else {
                    log.error("缓存中的对象不是Map类型，请检查缓存数据");
                }
            }
            return fontList;
        }
    }

    // 判断字符串是否包含中文字符
    private static boolean containsChinese(String str) {
        // 使用正则表达式判断是否包含中文字符
        return Pattern.compile("[\u4e00-\u9fa5]").matcher(str).find();
    }


    /**
     * 综合处理txt,doc,docx文件字数
     */
    public int countWordsInFile(File file) {
        String fileName = file.getName().toLowerCase();
        Future<Integer> futureResult = threadPoolDealTask.submitTask(() -> {
            if (fileName.endsWith(".txt")) {
                return countCharactersInTxtFile(file);
            } else if (fileName.endsWith(".doc")) {
                return countCharactersInDocFile(file);
            } else if (fileName.endsWith(".docx")) {
                return countCharactersInDocxFile(file);
            } else {
                throw new IllegalArgumentException("Unsupported file type: " + fileName);
            }
        });
        try {
            return futureResult.get();
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return 0;
    }

    /**
     * 处理 doc 文档字符数
     */
    public int countCharactersInDocFile(File file) throws IOException {
        int maxCharCount = Integer.parseInt(configPrinterConfig.getMaxCharCount()); // 设置字数上限
        int charCount = 0;

        try (FileInputStream fis = new FileInputStream(file);
             HWPFDocument doc = new HWPFDocument(fis);
             WordExtractor extractor = new WordExtractor(doc)) {

            String[] paragraphs = extractor.getParagraphText();
            for (String paragraph : paragraphs) {
                for (char ch : paragraph.toCharArray()) {
                    if (!Character.isWhitespace(ch)) { // 排除空白字符
                        charCount++;
                        if (charCount > maxCharCount) { // 如果字符数超过最大限制，抛出异常
                            throw new IllegalArgumentException("文本字符数超出预定值 " + maxCharCount);
                        }
                    }
                }
            }
        }

        return charCount;
    }

    /**
     * 处理 docx 文档字符数
     */
    public int countCharactersInDocxFile(File file) throws IOException {
        int maxCharCount = Integer.parseInt(configPrinterConfig.getMaxCharCount()); // 设置字数上限
        int charCount = 0;

        try (FileInputStream fis = new FileInputStream(file);
             XWPFDocument docx = new XWPFDocument(fis);
             XWPFWordExtractor extractor = new XWPFWordExtractor(docx)) {

            String text = extractor.getText();
            for (char ch : text.toCharArray()) {
                if (!Character.isWhitespace(ch)) { // 排除空白字符
                    charCount++;
                    if (charCount > maxCharCount) { // 如果字符数超过最大限制，抛出异常
                        throw new IllegalArgumentException("文本字符数超出预定值 " + maxCharCount);
                    }
                }
            }
        }

        return charCount;
    }

    /**
     * 处理 txt 文档字符数
     */
    public int countCharactersInTxtFile(File file) throws IOException {
        int maxCharCount = Integer.parseInt(configPrinterConfig.getMaxCharCount()); // 设置字数上限
        int charCount = 0;

        try (BufferedReader reader = Files.newBufferedReader(file.toPath(), StandardCharsets.UTF_8)) {
            int ch;
            while ((ch = reader.read()) != -1) {
                if (!Character.isWhitespace(ch)) { // 排除空白字符
                    charCount++;
                    if (charCount > maxCharCount) { // 如果字符数超过最大限制，抛出异常
                        throw new IllegalArgumentException("文本字符数超出预定值 " + maxCharCount);
                    }
                }
            }
        }

        return charCount;
    }

    /**
     * 统计字符串内容的字数
     */
    public int countCharactersInString(String content) throws IOException {
        int maxCharCount = Integer.parseInt(configPrinterConfig.getMaxCharCount()); // 设置字数上限
        int charCount = 0;

        try (BufferedReader reader = new BufferedReader(new StringReader(content))) {
            int ch;
            while ((ch = reader.read()) != -1) {
                if (!Character.isWhitespace(ch)) { // 排除空白字符
                    charCount++;
                    if (charCount > maxCharCount) { // 如果字符数超过最大限制，抛出异常
                        throw new IllegalArgumentException("文本字符数超出预定值 " + maxCharCount);
                    }
                }
            }
        }

        return charCount;
    }


    @Override
    public Map<String, String> enterCreatePPTPageDto(EnterCreatePPTPageDto dto, HttpServletRequest request) {
        HashMap<String, String> resMap = new HashMap<>();
        HashMap<String, String> errorMap = new HashMap<>();
        String uid = dto.getUid();

        if (StringUtils.isBlank(uid)) {
            errorMap.put("error_msg", "参数缺失");
            return errorMap;
        }
        if (!redisService.hasKey(Constants.CONTENT_UID_KEY + uid)) {
            errorMap.put("error_msg", "参数失效");
            return errorMap;
        }

        // 构造重定向 URL
        StringBuilder targetUrl = new StringBuilder();
        targetUrl.append(configPrinterConfig.getRedirectUrl())
                .append("?uid=").append(uid);
        log.info("重定向到: {}", targetUrl);
        resMap.put("target_url", targetUrl.toString());

        return resMap;
    }

    @Override
    public Map<String, String> getContentId(EnterCreatePPTPageDto enterCreatePPTPageDto, HttpServletRequest request) {
        UUID uid = UUID.randomUUID();
        String content = enterCreatePPTPageDto.getContent();
        String type = enterCreatePPTPageDto.getType();
        String apiKey = request.getHeader("api-key");
        HashMap<String, String> errorMap = new HashMap<>();
        HashMap<String, String> resMap = new HashMap<>();
        if (StringUtils.isBlank(apiKey) || !redisService.hasKey(TokenConstants.SYSTEM_ACCESS_API_KEY_SECRET + apiKey)) {
            errorMap.put("error", "未授权");
            return errorMap;
        }

        if (StringUtils.isBlank(type)) {
            type = "topic";
        }

        if ("topic".equals(type) && StringUtils.isNotBlank(content) && content.length() > 50) {
            // 判断字数在50字内
            errorMap.put("error", "字数超出设定 50字以内 ");
            return errorMap;
        }

        if ("text".equals(type) && StringUtils.isNotBlank(content) && content.length() > 8000) {
            // 判断字数在8000字内 超出截断
            content = content.substring(0, 8000);
        }
        Integer duration = enterCreatePPTPageDto.getDuration();
        if (duration < 0 || duration > 48 * 60) {
            duration = 60;
        }
        enterCreatePPTPageDto.setUid(Optional.ofNullable(enterCreatePPTPageDto.getUserId()).map(Object::toString).orElse(""));
        enterCreatePPTPageDto.setApiKey(apiKey);

        // 构造重定向 URL
        StringBuilder targetUrl = new StringBuilder();
        targetUrl.append(configPrinterConfig.getRedirectUrl())
                .append("?uid=").append(uid);
        log.info("PPT页面URL: {}", targetUrl);


        redisService.setCacheObject(CONTENT_UID_KEY + uid, enterCreatePPTPageDto, Long.valueOf(duration), TimeUnit.MINUTES);
        resMap.put("uid", uid.toString());
        resMap.put("duration", String.valueOf(duration));
        resMap.put("userId", enterCreatePPTPageDto.getUserId());
        resMap.put("target_url", targetUrl.toString());
        resMap.put("targetUrl", targetUrl.toString());

        return resMap;
    }

    @Override
    public Map<String, String> getUidDetail(EnterCreatePPTPageDto enterCreatePPTPageDto) {
        String uid = enterCreatePPTPageDto.getUid();
        HashMap<String, String> errorMap = new HashMap<>();
        try {
            if (StringUtils.isBlank(uid) || !redisService.hasKey(CONTENT_UID_KEY + uid)) {
                errorMap.put("error", "校验失败");
                return errorMap;
            }

            Object cacheObject = redisService.getCacheObject(CONTENT_UID_KEY + uid);
            if (cacheObject == null) {
                errorMap.put("error", "数据不存在");
                return errorMap;
            }
            EnterCreatePPTPageDto dto = JSON.parseObject(JSON.toJSONString(cacheObject), EnterCreatePPTPageDto.class);
            // 如果你需要 Map 形式，可以手动转一下
            Map<String, String> map = new HashMap<>();
            map.put("apiKey", dto.getApiKey());
            map.put("content", dto.getContent());
            map.put("type", dto.getType());
            map.put("uid", dto.getUid());
            map.put("userId", dto.getUserId());

            return map;
        } catch (Exception e) {
            errorMap.put("error", e.getMessage());
            return errorMap;
        }
    }


    @Override
    public Flux<String> chatModelFlux(ChatModelDto chatModelDto) {
        // 确定语言类型提示
        String languageTypeTip = "中文";

        // 构建新聊天对话集合
        List<MessagesDto> messagesList = new ArrayList<>();
        messagesList.add(MessagesDto.builder()
                .role("user")
                .content("【前置的严格条件:当前对话语言：" + languageTypeTip + "】")
                .build());
        messagesList.add(MessagesDto.builder()
                .role("user")
                .content("【前置的严格条件: 禁止使用表情等特殊字符,只需要按照一下要求生成文本,输出任何备注，提示。描述等信息提示，只需要按照格式生成结构化内容")
                .build());

        String requirements = "【任务描述】\n" +
                "请基于以下输入内容，生成结构化的PPT大纲。输入内容：【{}】\n" +
                "按照输入内容生成符合下面要求的文本内容\n" +
                "【输出规范】\n" +
                "1. 整体结构要求\n" +
                "   * 首行为主题（单独成行）\n" +
                "   * 确保层级分明，逻辑连贯\n" +
                "   * 各级标题必须换行\n" +
                "   * 所有内容需紧扣主题\n" +
                "\n" +
                "2.标题格式与数量规范\n" +
                "   [主标题]\n" +
                "   * 单独一行\n" +
                "   * 字数：10-15字\n" +
                "   * 要求：准确概括核心主题\n" +
                "   \n" +
                "   [一级标题]\n" +
                "   * 格式：使用 # 开头\n" +
                "   * 数量：4-5个\n" +
                "   * 字数：10-15字\n" +
                "   \n" +
                "   [二级标题]\n" +
                "   * 格式：使用 ## 开头\n" +
                "   * 数量：每个一级标题下固定4个\n" +
                "   * 字数：10-15字\n" +
                "   \n" +
                "   [三级标题]\n" +
                "   * 格式：使用 ### 开头，必须以冒号结尾\n" +
                "   * 字数：10-15字\n" +
                "   * 数量分布规则：\n" +
                "     - 每个二级标题下的三级标题数量必须是2、3、4或5个之一\n" +
                "     - 相邻的二级标题下的三级标题数量必须不同\n" +
                "       例如：\n" +
                "       ## 二级标题1（3个三级标题）\n" +
                "       ## 二级标题2（5个三级标题）\n" +
                "       ## 二级标题3（2个三级标题）\n" +
                "       ## 二级标题4（4个三级标题）\n" +
                "     - 在整个文档中，必须确保：\n" +
                "       > 至少出现4次包含2个三级标题的二级标题\n" +
                "       > 至少出现4次包含3个三级标题的二级标题\n" +
                "       > 至少出现4次包含4个三级标题的二级标题\n" +
                "       > 至少出现4次包含5个三级标题的二级标题\n" +
                "\n" +
                "3. 正文要求\n" +
                "   * 位置：紧跟三级标题冒号后\n" +
                "   * 字数：严格控制在150-200字之间\n" +
                "   * 内容要求：\n" +
                "     - 必须以句号结尾\n" +
                "     - 必须包含至少2个具体数据或实例\n" +
                "     - 语言专业、准确、简洁\n" +
                "     - 避免重复冗余\n" +
                "\n" +
                "【结构示例】\n" +
                "城市摊贩管理创新\n" +
                "\n" +
                "# 摊贩管理基础\n" +
                "## 政策框架\n" +
                "### 管理体系构建：[150-200字正文，包含数据]\n" +
                "### 法规制度完善：[150-200字正文，包含数据]\n" +
                "### 监管机制创新：[150-200字正文，包含数据]\n" +
                "\n" +
                "## 执行机制\n" +
                "### 日常巡查制度：[150-200字正文，包含数据]\n" +
                "### 投诉处理流程：[150-200字正文，包含数据]\n" +
                "### 应急响应机制：[150-200字正文，包含数据]\n" +
                "### 考核评估体系：[150-200字正文，包含数据]\n" +
                "### 奖惩制度建设：[150-200字正文，包含数据]\n" +
                "\n" +
                "## 技术支撑\n" +
                "### 信息系统建设：[150-200字正文，包含数据]\n" +
                "### 智能监管平台：[150-200字正文，包含数据]\n" +
                "\n" +
                "## 社会参与\n" +
                "### 公众监督机制：[150-200字正文，包含数据]\n" +
                "### 行业自律体系：[150-200字正文，包含数据]\n" +
                "### 社区共治模式：[150-200字正文，包含数据]\n" +
                "### 媒体舆论监督：[150-200字正文，包含数据]\n" +
                "\n" +
                "【强制检查项】\n" +
                "1. 三级标题数量分布检查：\n" +
                "   * 检查每个二级标题下的三级标题数量是否为2/3/4/5之一\n" +
                "   * 确认相邻二级标题的三级标题数量是否不同\n" +
                "   * 验证四种数量(2./3/4/5)是否各自至少出现4次\n" +
                "\n" +
                "2.格式检查：\n" +
                "   * 标题层级符号(#/##/###)使用是否正确\n" +
                "   * 三级标题是否以冒号结尾\n" +
                "   * 各级标题间是否正确换行\n" +
                "\n" +
                "3. 内容检查：\n" +
                "   * 标题字数是否在10-15字之间\n" +
                "   * 正文字数是否在150-200字之间\n" +
                "   * 正文是否包含至少2个数据或例证\n" +
                "   * 是否存在重复冗余内容\n" +
                "\n" +
                "【注意事项】\n" +
                "* 严格执行三级标题数量分布规则\n" +
                "* 确保相邻二级标题下的三级标题数量不同\n" +
                "* 保证每种数量（2/3/4/5）的三级标题组合至少出现4次\n" +
                "* 提示词中的 [] ,和 ()等符号 都是为提示词补充说明，模型回复的时候不需要任何提示说明\n" +
                "* 所有正文必须有具体数据支撑";

        if ("topic".equals(chatModelDto.getType())) {
            // 一句话主题生成
            requirements = requirements.replace("{}", "根据以下用户提供的主题信息" + chatModelDto.getContent());
        }
        if ("content".equals(chatModelDto.getType())) {
            // 调整文本格式内容
            requirements = requirements.replace("{}", "根据以下用户提供的文本内容" + chatModelDto.getContent());
        }
        messagesList.add(MessagesDto.builder()
                .role("user")
                .content(requirements)
                .build());


        String model = "deepseek-v3"; // 百度千帆提供的
        return sendContentDeepSeekFlux(messagesList, model);
    }

    private Flux<String> sendContentDeepSeekFlux(List<MessagesDto> messagesList, String model) {
        // 参数校验
        if (messagesList == null || messagesList.isEmpty()) {
            return Flux.error(new IllegalArgumentException("messagesList 不能为空"));
        }

        // 构建 JSON 请求体
        JsonObject requestBody = new JsonObject();
        requestBody.addProperty("model", model);
        requestBody.add("messages", gson.toJsonTree(messagesList));
        requestBody.addProperty("stream", true);
        okhttp3.MediaType mediaType = okhttp3.MediaType.parse("application/json");

        // 构建 HTTP 请求
        RequestBody body = RequestBody.create(gson.toJson(requestBody), mediaType);
        Request request = new Request.Builder()
                .url("https://qianfan.baidubce.com/v2/chat/completions")
                .post(body)
                .addHeader("Content-Type", "application/json")
                .addHeader("appid", "")
                .addHeader("Authorization", "Bearer " + BAIDU_API_BEARER_TOKEN)
                .build();

        String sessionId = snowflake.nextIdStr();
        String chat_session_key = "chat_session:" + sessionId;
        return Flux.create(sink -> {

            Call call = HTTP_CLIENT.newCall(request);


            // 流式中断时的清理逻辑
            sink.onDispose(() -> {
                redisService.deleteObject(chat_session_key);
                if (!call.isCanceled()) {
                    call.cancel();
                }
            });

            call.enqueue(new Callback() {
                @Override
                public void onFailure(@NotNull Call call, @NotNull IOException e) {
                    sink.error(e); // 请求失败时发送错误
                }

                @Override
                public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {
                    if (!response.isSuccessful()) {
                        sink.error(new IOException("请求失败: code=" + response.code()));
                        return;
                    }
                    redisService.setCacheObject(chat_session_key, true, 5L, TimeUnit.MINUTES);


                    try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().byteStream(),
                            StandardCharsets.UTF_8))) {
                        String line;
                        StringBuilder content = new StringBuilder();
                        StringBuilder reasoningContent = new StringBuilder();

                        while ((line = reader.readLine()) != null && redisService.hasKey(chat_session_key)) {
                            if (line.isEmpty()) {
                                continue;
                            }
                            // 解析流式数据
                            if (line.startsWith("data: ")) {
                                String jsonString = line.replaceFirst("data: ", "");
                                if (jsonString.equals("[DONE]")) break; // 结束标记

                                // 解析 JSON
                                JsonObject jsonObject = gson.fromJson(jsonString, JsonObject.class);
                                JsonArray choices = jsonObject.getAsJsonArray("choices");
                                if (choices == null || choices.isEmpty()) continue;

                                // 获取choices数组中的第一个元素
                                JsonObject firstChoice = choices.get(0).getAsJsonObject();

                                // 获取delta对象
                                JsonObject delta = firstChoice.getAsJsonObject("delta");

                                // 处理 content
                                if (delta.has("content") && !delta.get("content").isJsonNull()) {
                                    String contentValue = delta.get("content").getAsString();
                                    MessagesDto messageData = MessagesDto.builder()
                                            .role("assistant")
                                            .resType(ResTypeDto.ANSWER.getValue())
                                            .content(contentValue)
                                            .sessionId(sessionId)
                                            .messageId(snowflake.nextIdStr()).build();
                                    sink.next(gson.toJson(messageData));
                                    content.append(contentValue);
                                }

                                // 处理 reasoningContent
                                if (delta.has("reasoning_content") && !delta.get("reasoning_content").isJsonNull()) {
                                    String reasoningContentValue = delta.get("reasoning_content").getAsString();
                                    MessagesDto messageData = MessagesDto.builder()
                                            .role("assistant")
                                            .resType(ResTypeDto.THINKING.getValue())
                                            .content(reasoningContentValue)
                                            .sessionId(sessionId)
                                            .messageId(snowflake.nextIdStr()).build();
                                    sink.next(gson.toJson(messageData));
                                    reasoningContent.append(reasoningContentValue);
                                }
                            }
                        }


                        // 完成流式响应
                        MessagesDto messageData = MessagesDto.builder()
                                .role("assistant")
                                .resType(ResTypeDto.FINISH.getValue())
                                .content("[DONE]")
                                .sessionId(sessionId)
                                .messageId(snowflake.nextIdStr()).build();
                        sink.next(gson.toJson(messageData));
                        sink.complete();
                        redisService.deleteObject(chat_session_key);

                        System.out.println("content");
                        System.out.println(content);
                    } catch (Exception e) {
                        log.error("流式处理异常: ", e);
                        sink.error(e);
                    } finally {
                        response.close(); // 确保资源释放
                    }
                }
            });
        });
    }


    /// ////////////////////////////////////////////////////////////////////////////////////////


    @Override
    public String getDefaultPptJson(String tp) {
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(tp)) {
            if (inputStream == null) {
                log.error("未找到资源文件: ppt_default.json");
                return "";
            }

            // 1. 读取原始JSON数据
            String jsonStr = IOUtils.toString(inputStream, StandardCharsets.UTF_8);

            // 2. GZIP压缩
            byte[] compressedData = Utils.compressWithGzip(jsonStr);

            // 3. Base64编码

            // 4. 返回结果
            return Base64.getEncoder().encodeToString(compressedData);
        } catch (IOException e) {
            log.error("读取或压缩JSON失败", e);
            return "";
        }
    }


    @Override
    public Flux<String> getPptOutline(OutlineDto outline) {
        String model = outline.getModel().substring("qianfan-".length());

        // 确定语言类型提示
        String languageTypeTip = outline.getLanguage();

        // 构建新聊天对话集合
        List<MessagesDto> messagesList = new ArrayList<>();

        messagesList.add(MessagesDto.builder()
                .role("user")
                .content("【系统指令: 你是专业PPT大纲生成助手，使用" + languageTypeTip + "语言回复】")
                .build());

        String requirements = "请严格按照以下格式创建一个关于\"" + outline.getContent() + "\"的PPT大纲：\n\n" +
                "格式规则：\n" +
                "1. 只输出大纲内容，不要有任何解释和注释\n" +
                "2. 严格按照以下层级结构：\n" +
                "   - 一级标题：使用\"#\"开头，整个大纲只有1个一级标题\n" +
                "   - 二级标题：使用\"##\"开头，必须有5-6个二级标题\n" +
                "   - 三级标题：使用\"###\"开头，每个二级标题下必须有3-4个三级标题\n" +
                "   - 内容项：使用\"-\"开头，每个三级标题下必须有3-4个内容项\n" +
                "3. 严格按照以下换行规则：\n" +
                "   - 一级标题后不空行\n" +
                "   - 二级标题与前面内容之间空一行\n" +
                "   - 三级标题与前面内容不空行\n" +
                "   - 内容项与前面内容不空行\n" +
                "   - 最后一个内容项与下一个二级标题之间空一行\n" +
                "4. 标题内容不要重复上级标题的内容\n" +
                "5. 不要使用表情、特殊字符或标记\n\n" +
                "示例格式：\n" +
                "# 城市管理学\n" +
                "## 城市管理概述\n" +
                "### 城市管理的定义与内涵\n" +
                "- 城市管理的概念界定\n" +
                "- 城市管理的基本特征\n" +
                "- 城市管理的目标与任务\n" +
                "### 城市管理的发展历程\n" +
                "- 传统城市管理模式\n" +
                "- 现代城市管理转型\n" +
                "- 智慧城市管理趋势\n" +
                "### 城市管理的理论基础\n" +
                "- 公共管理理论\n" +
                "- 城市规划理论\n" +
                "- 可持续发展理论\n" +
                "\n" +
                "## 城市规划与设计\n" +
                "### 城市空间布局\n" +
                "- 城市功能分区\n" +
                "- 土地利用规划\n" +
                "- 城市空间结构优化\n" +
                "### 城市基础设施规划\n" +
                "- 交通系统规划\n" +
                "- 市政公用设施规划\n" +
                "- 智能基础设施建设\n";

        messagesList.add(MessagesDto.builder()
                .role("user")
                .content(requirements)
                .build());

        return sendContentFlux(messagesList, model);
    }

    private Flux<String> sendContentFlux(List<MessagesDto> messagesList, String model) {
        // 参数校验
        if (messagesList == null || messagesList.isEmpty()) {
            return Flux.error(new IllegalArgumentException("messagesList 不能为空"));
        }

        // 构建 JSON 请求体
        JsonObject requestBody = new JsonObject();
        requestBody.addProperty("model", model);
        requestBody.add("messages", gson.toJsonTree(messagesList));
        requestBody.addProperty("stream", true);
        requestBody.addProperty("disable_search", false);
        requestBody.addProperty("enable_citation", false);
        okhttp3.MediaType mediaType = okhttp3.MediaType.parse("application/json");

        // 构建 HTTP 请求
        RequestBody body = RequestBody.create(gson.toJson(requestBody), mediaType);
        Request request = new Request.Builder()
                .url("https://qianfan.baidubce.com/v2/chat/completions")
                .post(body)
                .addHeader("Content-Type", "application/json")
                .addHeader("appid", "")
                .addHeader("Authorization", "Bearer " + BAIDU_API_BEARER_TOKEN)
                .build();

        String sessionId = snowflake.nextIdStr();
        String chat_session_key = "chat_session:" + sessionId;

        return Flux.create(sink -> {
            Call call = HTTP_CLIENT.newCall(request);

            // 流式中断时的清理逻辑
            sink.onDispose(() -> {
                redisService.deleteObject(chat_session_key);
                if (!call.isCanceled()) {
                    call.cancel();
                }
            });

            call.enqueue(new Callback() {
                @Override
                public void onFailure(@NotNull Call call, @NotNull IOException e) {
                    sink.error(e); // 请求失败时发送错误
                }

                @Override
                public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {
                    if (!response.isSuccessful()) {
                        sink.error(new IOException("请求失败: code=" + response.code()));
                        return;
                    }
                    redisService.setCacheObject(chat_session_key, true, 5L, TimeUnit.MINUTES);

                    try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().byteStream(),
                            StandardCharsets.UTF_8))) {
                        String line;

                        while ((line = reader.readLine()) != null && redisService.hasKey(chat_session_key)) {
                            if (line.isEmpty()) {
                                continue;
                            }

                            // 解析流式数据
                            if (line.startsWith("data: ")) {
                                String jsonString = line.replaceFirst("data: ", "");
                                if (jsonString.equals("[DONE]")) break; // 结束标记

                                // 解析 JSON
                                JsonObject jsonObject = gson.fromJson(jsonString, JsonObject.class);
                                JsonArray choices = jsonObject.getAsJsonArray("choices");
                                if (choices == null || choices.isEmpty()) continue;

                                // 获取choices数组中的第一个元素
                                JsonObject firstChoice = choices.get(0).getAsJsonObject();

                                // 获取delta对象
                                JsonObject delta = firstChoice.getAsJsonObject("delta");

                                // 处理 content
                                if (delta.has("content") && !delta.get("content").isJsonNull()) {
                                    String contentValue = delta.get("content").getAsString();
                                    sink.next(contentValue);
                                }
                            }
                        }

                        sink.complete();
                        redisService.deleteObject(chat_session_key);
                    } catch (Exception e) {
                        log.error("流式处理异常: ", e);
                        sink.error(e);
                    } finally {
                        response.close(); // 确保资源释放
                    }
                }
            });
        });
    }

    @Override
    public Flux<String> generatePptContent(PptContentDto pptContent) {
        String markdownContent = pptContent.getContent();
        AtomicInteger countModelAPi = new AtomicInteger(0);
        // 1. 解析Markdown为有序列表
        log.info("开始解析Markdown文档");
        List<LinkedHashMap<String, Object>> results = parseMarkdown(markdownContent);

        if (results.isEmpty()) {
            String errorMsg = "Markdown解析结果为空";
            log.error(errorMsg);
            return Flux.error(new RuntimeException(errorMsg));
        }
        log.info("成功解析Markdown文档，共解析出 {} 个页面", results.size());

        String model = "deepseek-v3";

        // 分离不同类型的内容
        List<LinkedHashMap<String, Object>> coverItems = new ArrayList<>();
        List<LinkedHashMap<String, Object>> endItems = new ArrayList<>();
        List<LinkedHashMap<String, Object>> otherItems = new ArrayList<>();

        // 对结果进行分类
        for (LinkedHashMap<String, Object> item : results) {
            String type = (String) item.get("type");
            if ("cover".equals(type)) {
                coverItems.add(item);
            } else if ("end".equals(type)) {
                endItems.add(item);
            } else {
                otherItems.add(item);
            }
        }

        log.info("内容分类完成: cover={}, other={}, end={}",
                coverItems.size(), otherItems.size(), endItems.size());

        // 1. 创建cover内容的Flux - 现在添加模型处理
        Flux<String> coverFlux = Flux.fromIterable(coverItems)
                .flatMap(dataMap -> {
                    LinkedHashMap<String, Object> data = (LinkedHashMap<String, Object>) dataMap.get("data");
                    String title = (String) data.get("title");
                    List<MessagesDto> messagesList = new ArrayList<>();

                    String requirements = "你是一位PPTX封面内容的专业编写人员，需要为PPT封面编写吸引人的描述。\n" +
                            "你的任务:\n" +
                            "1. 为PPT的封面标题创建一个简短但有吸引力的描述。\n" +
                            "2. 描述严格在10-20个汉字，突出主题的重要性和价值。\n" +
                            "3. 使用专业、正式的语言风格。\n" +
                            "4. 只输出描述文本，不要添加任何额外的说明或注释。\n" +
                            "以下是PPT的封面标题:\n\n" + title;

                    messagesList.add(MessagesDto.builder()
                            .role("user")
                            .content(requirements)
                            .build());

                    return Mono.fromCallable(() -> {
                                String newText = chatModel(messagesList, model);
                                countModelAPi.incrementAndGet();
//                                String newText = "";
                                log.info("Cover生成描述: {}", newText);
                                if (StringUtils.isNotBlank(newText)) {
                                    newText = newText.replaceAll("^\"|\"$", "").replace("\\\"", "\"");
                                    data.put("text", newText);
                                } else {
                                    data.put("text", "探索" + title + "的奥秘");
                                }
                                return dataMap;
                            })
                            .subscribeOn(Schedulers.boundedElastic())
                            .flatMap(result -> {
                                try {
                                    String json = objectMapper.writeValueAsString(result);
                                    log.info("流式返回cover项: {}", title);
                                    return Mono.just(json);
                                } catch (JsonProcessingException e) {
                                    log.error("JSON序列化失败", e);
                                    return Mono.error(new RuntimeException("JSON序列化失败", e));
                                }
                            });
                });

        // 2. 创建处理其他内容的Flux
        Flux<String> otherFlux = Flux.fromIterable(otherItems)
                .concatMap(dataMap -> {  // 使用concatMap替代flatMap以保持顺序
                    String type = (String) dataMap.get("type");

                    if ("content".equals(type)) {
                        LinkedHashMap<String, Object> data = (LinkedHashMap<String, Object>) dataMap.get("data");
                        ArrayList<LinkedHashMap<String, Object>> items = (ArrayList<LinkedHashMap<String, Object>>) data.get("items");

                        // 对items进行顺序处理
                        return Flux.fromIterable(items)
                                .concatMap(itemMap -> {
                                    String title = (String) itemMap.get("title");
                                    List<MessagesDto> messagesList = new ArrayList<>();

                                    String requirements = "你是一位PPTX内容的编写人员, 需要根据以下要求对内容项进行解释和扩充。\n" +
                                            "你的任务:\n" +
                                            "1. 对内容项进行解释和扩充.\n" +
                                            "2. 只输出必要的数据，不需要输出跟大纲无关的内容,输出的结果以纯文本输出。\n" +
                                            "3. 不需要输出总结性的文本。\n" +
                                            "4. 对内容条目进行长文本解释和扩充严格在20至30个汉字或者英文之间不包含标点符号。\n" +
                                            "以下是需要处理的文本,集合章节的主题,扩充条目内容信息:\n" +
                                            "\n"
                                            + "章节:" + data.get("title") + "\n条目:" + title;
                                    messagesList.add(MessagesDto.builder()
                                            .role("user")
                                            .content(requirements)
                                            .build());

                                    return Mono.fromCallable(() -> {
                                                String newText = chatModel(messagesList, model);
                                                countModelAPi.incrementAndGet();
                                                if (StringUtils.isNotBlank(newText)) {
                                                    newText = newText.replaceAll("^\"|\"$", "").replace("\\\"", "\"");
                                                    log.info("newText==={}", newText);
                                                    itemMap.put("text", newText);
                                                } else {
                                                    itemMap.put("text", title);
                                                }
                                                return itemMap;
                                            })
                                            .subscribeOn(Schedulers.boundedElastic());
                                })
                                .collectList()
                                .flatMap(processedItems -> {
                                    try {
                                        data.put("items", processedItems);
                                        String json = objectMapper.writeValueAsString(dataMap);
                                        log.info("流式返回content项: {}", data.get("title"));
                                        return Mono.just(json);
                                    } catch (JsonProcessingException e) {
                                        log.error("JSON序列化失败", e);
                                        return Mono.error(new RuntimeException("JSON序列化失败", e));
                                    }
                                });
                    } else if ("transition".equals(type)) {
                        // 处理transition类型 - 章节过渡页
                        LinkedHashMap<String, Object> data = (LinkedHashMap<String, Object>) dataMap.get("data");
                        String title = (String) data.get("title");
                        List<MessagesDto> messagesList = new ArrayList<>();

                        String requirements = "你是一位PPTX章节过渡页内容的专业编写人员。\n" +
                                "你的任务:\n" +
                                "1. 为PPT章节标题创建一个简洁的描述，介绍这个章节的核心内容和目的。\n" +
                                "2. 描述严格10-20个汉字，突出章节的价值和与整体主题的关联。\n" +
                                "3. 使用专业、正式的语言风格。\n" +
                                "4. 只输出描述文本，不要添加任何额外的说明或注释。\n" +
                                "以下是PPT的章节标题:\n\n" + title;

                        messagesList.add(MessagesDto.builder()
                                .role("user")
                                .content(requirements)
                                .build());

                        return Mono.fromCallable(() -> {
                                    String newText = chatModel(messagesList, model);
                                    countModelAPi.incrementAndGet();
//                                    String newText = "";
                                    log.info("Transition生成描述: {}", newText);
                                    if (StringUtils.isNotBlank(newText)) {
                                        newText = newText.replaceAll("^\"|\"$", "").replace("\\\"", "\"");
                                        data.put("text", newText);
                                    } else {
                                        data.put("text", "关于" + title + "的探讨");
                                    }
                                    return dataMap;
                                })
                                .subscribeOn(Schedulers.boundedElastic())
                                .flatMap(result -> {
                                    try {
                                        String json = objectMapper.writeValueAsString(result);
                                        log.info("流式返回transition项: {}", title);
                                        return Mono.just(json);
                                    } catch (JsonProcessingException e) {
                                        log.error("JSON序列化失败", e);
                                        return Mono.error(new RuntimeException("JSON序列化失败", e));
                                    }
                                });
                    } else {
                        // 非特殊类型的数据直接序列化并返回
                        try {
                            String json = objectMapper.writeValueAsString(dataMap);
                            log.info("流式返回其他类型项: {}", type);
                            return Mono.just(json);
                        } catch (JsonProcessingException e) {
                            log.error("JSON序列化失败", e);
                            return Mono.error(new RuntimeException("JSON序列化失败", e));
                        }
                    }
                });

        // 3. 创建end内容的Flux
        Flux<String> endFlux = Flux.fromIterable(endItems)
                .flatMap(dataMap -> {
                    try {
                        String json = objectMapper.writeValueAsString(dataMap);
                        log.info("流式返回end项");
                        return Mono.just(json);
                    } catch (JsonProcessingException e) {
                        return Mono.error(new RuntimeException("JSON序列化失败", e));
                    }
                });

        // 按顺序连接这三个流: cover -> other -> end
        return Flux.concat(coverFlux, otherFlux, endFlux)
                .doOnComplete(() -> log.info("==总计调用了model次数== {}", countModelAPi.get()))
                .doOnCancel(() -> log.info("流被取消"))
                .map(json -> json + "\n\n")
                .delayElements(Duration.ofMillis(500))
                .doFinally(signal -> log.info("流处理结束，信号类型: {}", signal));
    }

    /**
     * 解析Markdown文本为结构化数据
     */
    private List<LinkedHashMap<String, Object>> parseMarkdown(String markdown) {
        List<LinkedHashMap<String, Object>> results = new ArrayList<>();
        if (markdown == null || markdown.trim().isEmpty()) {
            log.warn("输入的Markdown为空或无效");
            return results;
        }

        String[] lines = markdown.split("\\r?\\n");
        LinkedHashMap<String, Object> currentSection = new LinkedHashMap<>();
        List<String> sections = new ArrayList<>();
        LinkedHashMap<String, List<LinkedHashMap<String, Object>>> sectionContents = new LinkedHashMap<>();
        List<LinkedHashMap<String, Object>> currentSectionContents = null;

        // 1. 处理封面（# 开头）
        for (String line : lines) {
            String trimmedLine = line.trim();
            if (trimmedLine.isEmpty()) continue;

            if (trimmedLine.startsWith("# ")) {
                String title = trimmedLine.substring(2).trim();
                // 添加封面
                LinkedHashMap<String, Object> cover = new LinkedHashMap<>();
                cover.put("type", "cover");
                LinkedHashMap<String, Object> coverData = new LinkedHashMap<>();
                coverData.put("title", title);
                coverData.put("text", "");
                cover.put("data", coverData);
                results.add(cover);
                break;
            }
        }

        // 2. 收集所有章节标题（## 开头）用于目录
        for (String line : lines) {
            String trimmedLine = line.trim();
            if (trimmedLine.startsWith("## ")) {
                String sectionTitle = trimmedLine.substring(3).trim();
                sections.add(sectionTitle);
                sectionContents.put(sectionTitle, new ArrayList<>());
            }
        }

        // 3. 添加目录
        if (!sections.isEmpty()) {
            LinkedHashMap<String, Object> contents = new LinkedHashMap<>();
            contents.put("type", "contents");
            LinkedHashMap<String, Object> contentsData = new LinkedHashMap<>();
            contentsData.put("items", new ArrayList<>(sections));
            contents.put("data", contentsData);
            results.add(contents);
        }

        // 4. 处理章节内容
        String currentSectionTitle = null;
        List<LinkedHashMap<String, Object>> currentItems = new ArrayList<>();
        String currentSubSection = null;

        for (String line : lines) {
            String trimmedLine = line.trim();
            if (trimmedLine.isEmpty()) continue;

            if (trimmedLine.startsWith("## ")) {
                // 处理前一个子节的内容
                if (!currentItems.isEmpty() && currentSubSection != null) {
                    addContentSection(currentSectionContents, currentSubSection, currentItems);
                    currentItems.clear();
                }

                currentSectionTitle = trimmedLine.substring(3).trim();
                currentSectionContents = sectionContents.get(currentSectionTitle);
                currentSubSection = null;

                // 添加过渡页
                LinkedHashMap<String, Object> transition = new LinkedHashMap<>();
                transition.put("type", "transition");
                LinkedHashMap<String, Object> transitionData = new LinkedHashMap<>();
                transitionData.put("title", currentSectionTitle);
                transitionData.put("text", "");
                transition.put("data", transitionData);
                if (currentSectionContents != null) {
                    currentSectionContents.add(transition);
                }

            } else if (trimmedLine.startsWith("### ")) {
                // 处理前一个子节的内容
                if (!currentItems.isEmpty() && currentSubSection != null) {
                    addContentSection(currentSectionContents, currentSubSection, currentItems);
                    currentItems.clear();
                }

                currentSubSection = trimmedLine.substring(4).trim();

            } else if (trimmedLine.startsWith("- ")) {
                String itemTitle = trimmedLine.substring(2).trim();
                LinkedHashMap<String, Object> item = new LinkedHashMap<>();
                item.put("title", itemTitle);
                item.put("text", "");
                currentItems.add(item);
            }
        }

        // 处理最后一组内容
        if (!currentItems.isEmpty() && currentSubSection != null && currentSectionContents != null) {
            addContentSection(currentSectionContents, currentSubSection, currentItems);
        }

        // 5. 按照章节顺序添加内容
        for (String section : sections) {
            List<LinkedHashMap<String, Object>> sectionContent = sectionContents.get(section);
            if (sectionContent != null && !sectionContent.isEmpty()) {
                results.addAll(sectionContent);
            }
        }

        // 6. 添加结束页
        LinkedHashMap<String, Object> end = new LinkedHashMap<>();
        end.put("type", "end");
        results.add(end);

        return results;
    }

    /**
     * 添加内容部分
     */
    private void addContentSection(List<LinkedHashMap<String, Object>> sectionContents,
                                   String subSection,
                                   List<LinkedHashMap<String, Object>> items) {
        LinkedHashMap<String, Object> content = new LinkedHashMap<>();
        content.put("type", "content");
        LinkedHashMap<String, Object> contentData = new LinkedHashMap<>();
        contentData.put("title", subSection);
        contentData.put("items", new ArrayList<>(items));
        content.put("data", contentData);
        if (sectionContents != null) {
            sectionContents.add(content);
        }
    }

    /**
     * 调用外部聊天模型API 不流式
     */
    private String chatModel(List<MessagesDto> messagesList, String model) {
        if (StringUtils.isBlank(model)) {
            model = "ernie-4.0-turbo-8k";
        }
        okhttp3.MediaType mediaType = okhttp3.MediaType.parse("application/json; charset=utf-8");
        // 构建 JSON 请求体
        JsonObject requestBodyJson = new JsonObject();
        requestBodyJson.addProperty("model", model);
        requestBodyJson.add("messages", gson.toJsonTree(messagesList));
        requestBodyJson.addProperty("stream", false);

        String requestBodyString = gson.toJson(requestBodyJson);
        log.info("调用模型API, Request Body: {}", requestBodyString);
        RequestBody body = RequestBody.create(requestBodyString, mediaType);

        Request request = new Request.Builder()
                .url(BAIDU_CHAT_V2_API_ENDPOINT)
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .addHeader("appid", "")
                .addHeader("Authorization", "Bearer " + BAIDU_API_BEARER_TOKEN)
                .build();

        try (Response response = HTTP_CLIENT.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                String errorBody = response.body() != null ? response.body().string() : "No error body";
                log.error("模型API调用失败: Code={}, Message={}, Body={}", response.code(), response.message(), errorBody);
                return null; // 返回null表示失败
            }

            ResponseBody responseBody = response.body();
            if (responseBody == null) {
                log.error("模型API调用成功，但响应体为空");
                return null;
            }

            String responseString = responseBody.string();
            log.info("模型API响应: {}", responseString);

            // 解析JSON响应
            try {
                JsonObject jsonResponse = JsonParser.parseString(responseString).getAsJsonObject();

                if (jsonResponse.has("error_code") || jsonResponse.has("error_msg")) {
                    String errorCode = jsonResponse.has("error_code") ? jsonResponse.get("error_code").getAsString() : "N/A";
                    String errorMsg = jsonResponse.has("error_msg") ? jsonResponse.get("error_msg").getAsString() : "N/A";
                    log.error("模型API响应中包含业务错误: Code={}, Msg={}", errorCode, errorMsg);
                    return null;
                }

                // 优先检查 result 字段 (根据百度千帆文档，ernie-turbo等模型结果在 result)
                if (jsonResponse.has("result") && jsonResponse.get("result").isJsonPrimitive()) {
                    String content = jsonResponse.get("result").getAsString();
                    log.info("成功提取模型生成内容 (from result)");
                    return content;
                }
                // 备选检查 choices 字段 (兼容通用 OpenAI 格式)
                else if (jsonResponse.has("choices") && jsonResponse.get("choices").isJsonArray()) {
                    com.google.gson.JsonArray choices = jsonResponse.getAsJsonArray("choices");
                    if (!choices.isEmpty() && choices.get(0).isJsonObject()) {
                        JsonObject firstChoice = choices.get(0).getAsJsonObject();
                        if (firstChoice.has("message") && firstChoice.get("message").isJsonObject()) {
                            JsonObject message = firstChoice.getAsJsonObject("message");
                            if (message.has("content") && message.get("content").isJsonPrimitive()) {
                                String content = message.get("content").getAsString();
                                log.info("成功提取模型生成内容 (from choices)");
                                return content;
                            }
                        }
                    }
                }

                log.warn("响应JSON结构不符合预期或缺少有效内容字段 (result or choices[0].message.content)。Response: {}", responseString);
                return null; // 返回 null 表示未找到有效内容

            } catch (Exception e) {
                log.error("解析模型API响应JSON失败: {}", responseString, e);
                return null;
            }

        } catch (IOException e) {
            // 网络层面的IO异常，需要向上抛出，因为这通常是不可恢复的或需要特殊处理
            log.error("调用模型API时发生网络错误", e);
        }
        return null;
    }


    @Override
    public List<QianFanTextToImgVo> getBaiduText2image(QianFanTextToImgDto qianFanTextToImgDto) throws Exception {
        String model = qianFanTextToImgDto.getModel();
        if ("Stable-Diffusion-XL".equals(model)) {
            return getQianfanImgStableDiffusionXl(qianFanTextToImgDto);
        } else if ("irag-1.0".equals(model)) {
            return getQianfanImgIrag(qianFanTextToImgDto);
        } else
            return getQianfanImgIrag(qianFanTextToImgDto);
    }

    private List<QianFanTextToImgVo> getQianfanImgIrag(QianFanTextToImgDto qianFanTextToImgDto) {
        okhttp3.MediaType mediaType = okhttp3.MediaType.parse("application/json; charset=utf-8");
        // 构建 JSON 请求体
        JsonObject requestBodyJson = new JsonObject();
        requestBodyJson.addProperty("model", "irag-1.0");
        requestBodyJson.addProperty("prompt", qianFanTextToImgDto.getPrompt());
        requestBodyJson.addProperty("n", qianFanTextToImgDto.getNum());
        requestBodyJson.addProperty("size", qianFanTextToImgDto.getSize());

        String requestBodyString = gson.toJson(requestBodyJson);
        RequestBody body = RequestBody.create(requestBodyString, mediaType);

        Request request = new Request.Builder()
                .url("https://qianfan.baidubce.com/v2/images/generations")
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer " + BAIDU_API_BEARER_TOKEN)
                .build();

        try (Response response = HTTP_CLIENT.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                String errorBody = response.body() != null ? response.body().string() : "No error body";
                log.error("模型API调用失败: Code={}, Message={}, Body={}", response.code(), response.message(), errorBody);
                throw new RuntimeException("Unexpected code " + response + ", Body: " + errorBody);
            }

            ResponseBody responseBody = response.body();
            if (responseBody == null) {
                log.error("模型API调用成功，但响应体为空");
                throw new RuntimeException("Response body is null");
            }

            String responseString = responseBody.string();
            JsonObject jsonResponse = JsonParser.parseString(responseString).getAsJsonObject();

            // 获取data 列表
            if (jsonResponse.has("data")) {
                JsonArray jsonArray = jsonResponse.get("data").getAsJsonArray();
                List<QianFanTextToImgVo> list = gson.fromJson(jsonArray, new TypeToken<List<QianFanTextToImgVo>>() {
                }.getType());
                for (QianFanTextToImgVo vo : list) {
                    if (StringUtils.isNotBlank(vo.getUrl())) {
                        vo.setB64Image(dealUrToEncodeBase64(vo.getUrl()));
                    }
                }
                return list;
            } else {
                throw new RuntimeException("提示词错误");
            }

        } catch (IOException e) {
            log.error("调用模型API时发生网络错误", e);
            throw new RuntimeException(e);
        }
    }

    public String dealUrToEncodeBase64(String url) throws IOException {
        OkHttpClient client = new OkHttpClient();

        Request request = new Request.Builder()
                .url(url)
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful() || response.body() == null) {
                throw new IOException("请求失败: " + response);
            }
            byte[] bytes = response.body().bytes();
            return Base64.getEncoder().encodeToString(bytes);
        }
    }

    private List<QianFanTextToImgVo> getQianfanImgStableDiffusionXl(QianFanTextToImgDto qianFanTextToImgDto) throws Exception {
        okhttp3.MediaType mediaType = okhttp3.MediaType.parse("application/json; charset=utf-8");
        // 构建 JSON 请求体
        JsonObject requestBodyJson = new JsonObject();
        requestBodyJson.addProperty("model", "");
        requestBodyJson.addProperty("prompt", qianFanTextToImgDto.getPrompt());
        requestBodyJson.addProperty("negativePrompt", qianFanTextToImgDto.getNegativePrompt());
        requestBodyJson.addProperty("style", qianFanTextToImgDto.getStyle());
        requestBodyJson.addProperty("size", qianFanTextToImgDto.getSize());
        requestBodyJson.addProperty("n", qianFanTextToImgDto.getNum());
        requestBodyJson.addProperty("steps", qianFanTextToImgDto.getSteps());
        requestBodyJson.addProperty("samplerIndex", qianFanTextToImgDto.getSamplerIndex());
        requestBodyJson.addProperty("refiner", qianFanTextToImgDto.getRefiner());

        String requestBodyString = gson.toJson(requestBodyJson);
        RequestBody body = RequestBody.create(requestBodyString, mediaType);

        Request request = new Request.Builder()
                .url("https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/text2image/sd_xl")
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer " + BAIDU_API_BEARER_TOKEN)
                .build();

        try (Response response = HTTP_CLIENT.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                String errorBody = response.body() != null ? response.body().string() : "No error body";
                log.error("模型API调用失败: Code={}, Message={}, Body={}", response.code(), response.message(), errorBody);
                throw new RuntimeException("Unexpected code " + response + ", Body: " + errorBody);
            }

            ResponseBody responseBody = response.body();
            if (responseBody == null) {
                log.error("模型API调用成功，但响应体为空");
                throw new RuntimeException("Response body is null");
            }

            String responseString = responseBody.string();
            JsonObject jsonResponse = JsonParser.parseString(responseString).getAsJsonObject();

            if (jsonResponse.has("error_code") || jsonResponse.has("error_msg")) {
                String errorCode = jsonResponse.has("error_code") ? jsonResponse.get("error_code").getAsString() : "N/A";
                String errorMsg = jsonResponse.has("error_msg") ? jsonResponse.get("error_msg").getAsString() : "N/A";
                log.error("模型API响应中包含业务错误: Code={}, Msg={}", errorCode, errorMsg);
                if ("336302".equals(errorCode) || errorMsg.contains("illegal")) {
                    throw new RuntimeException("提示词违规,请重新生成");
                } else {
                    throw new RuntimeException(errorMsg);
                }
            }

            // 获取data 列表
            if (jsonResponse.has("data")) {
                JsonArray jsonArray = jsonResponse.get("data").getAsJsonArray();
                return gson.fromJson(jsonArray, new TypeToken<List<QianFanTextToImgVo>>() {
                }.getType());

            }

        } catch (IOException e) {
            log.error("调用模型API时发生网络错误", e);
            throw new RuntimeException(e);
        }
        return null;
    }


    @Override
    public Map<String, String> createAiPptApiToken(AiPptDto aiPptDto) {
        HashMap<String, String> map = new HashMap<>();
        String timestamp = aiPptDto.getTimestamp();
        if (Objects.isNull(timestamp) || !validateTimestamp(Long.parseLong(timestamp))) {
            map.put("error", "非法请求");
            log.error(map.get("error"));
            return map;
        }


        // 参数初始化
        String uid = aiPptDto.getUid();
        String apiToken = SecurityUtils.getApiToken();
        if (StringUtils.isNotBlank(apiToken)) {
            String cacheKey = TokenConstants.SYSTEM_ACCESS_KEY_SECRET_TOKEN + apiToken;
            Object cacheObject = redisService.getCacheObject(cacheKey);
            if (!Objects.isNull(cacheObject)){
                String jsonStr = JSON.toJSONString(cacheObject);
                if (StringUtils.isNotBlank(jsonStr)) {
                    JSONObject jsonObj = JSON.parseObject(jsonStr); // fastjson
                    uid = jsonObj.getString("clientId"); // 取clientId 作为 uid
                }
            }
        }
        if (StringUtils.isBlank(uid) || uid.length() > 32) {
            map.put("error", "缺失用户唯一标识数据或长度超限");
            log.error("参数错误：uid为空或超出长度限制（当前长度：{}）", uid == null ? "null" : uid.length());
            return map;
        }
        if (aiPptDto.getLimit() == null) {
            aiPptDto.setLimit(500);
        }
        if (aiPptDto.getTimeOfHours() == null) {
            aiPptDto.setTimeOfHours(2);
        }

        if (StringUtils.isBlank(apiToken)) {
            // 设置新的uid = 前缀 + uid
            uid = configPrinterConfig.getCustomUidPrefix() + uid;
        }
        aiPptDto.setUid(uid);
        String cacheKey = Constants.AI_PPT_TOKEN_DATA + aiPptDto.getUid();
        Map<String, String> cacheMap = redisService.getCacheObject(cacheKey);

        if (cacheMap != null) {
            // 获取剩余过期时间（单位：秒）
            long expireSeconds = redisService.getExpire(cacheKey);
            if (expireSeconds <= 600) { // 剩余时间小于等于10分钟，重新生成token
                log.info("缓存即将过期，剩余时间: {} 秒，准备刷新", expireSeconds);
                redisService.deleteObject(cacheKey); // 删除定义的两个缓存
                redisService.deleteObject(Constants.AI_PPT_TOKEN_DATA + cacheMap.get("pptToken"));
            } else {
                // 缓存有效，直接返回
                log.info("命中缓存 token: {}", cacheMap.get("pptToken"));
                cacheMap.remove("aiPptApiKey");
                return cacheMap;
            }
        }
        // 请求生成新 token
        String apiKey = configPrinterConfig.getAiPptApiKey();

        // 构建 JSON 请求体
        okhttp3.MediaType mediaType = okhttp3.MediaType.parse("application/json; charset=utf-8");
        JsonObject requestBodyJson = new JsonObject();
        requestBodyJson.addProperty("uid", uid);
        requestBodyJson.addProperty("limit", aiPptDto.getLimit());
        requestBodyJson.addProperty("timeOfHours", aiPptDto.getTimeOfHours());

        String requestBodyString = gson.toJson(requestBodyJson);
        RequestBody body = RequestBody.create(requestBodyString, mediaType);

        // 构建请求
        Request goRequest = new Request.Builder()
                .url("https://docmee.cn/api/user/createApiToken")
                .post(body)
                .addHeader("Api-Key", apiKey)
                .addHeader("Content-Type", "application/json")
                .build();

        // 执行请求并处理响应
        try (Response response = HTTP_CLIENT.newCall(goRequest).execute()) {
            if (!response.isSuccessful()) {
                map.put("error", "请求失败，状态码: " + response.code());
                log.error(map.get("error"));
                return map;
            }

            // 解析响应JSON获取token
            assert response.body() != null;
            String responseBody = response.body().string();
            JsonObject jsonResponse = JsonParser.parseString(responseBody).getAsJsonObject();

            // 检查响应结构并获取token
            if (jsonResponse.has("data") && jsonResponse.getAsJsonObject("data").has("token")) {
                String token = jsonResponse.getAsJsonObject("data").get("token").getAsString();
                String expireTime = jsonResponse.getAsJsonObject("data").get("expireTime").getAsString();
                map.put("pptToken", token);
                map.put("expireTime", expireTime);
                map.put("timeOfHours", String.valueOf(aiPptDto.getTimeOfHours()));
                map.put("uid", uid);
                HashMap<String, String> mp = new HashMap<>(map);
                mp.put("aiPptApiKey", apiKey);
                redisService.setCacheObject(Constants.AI_PPT_TOKEN_DATA + token, mp,
                        Long.valueOf(aiPptDto.getTimeOfHours()), TimeUnit.HOURS);

                redisService.setCacheObject(Constants.AI_PPT_TOKEN_DATA + aiPptDto.getUid(), mp,
                        Long.valueOf(aiPptDto.getTimeOfHours()), TimeUnit.HOURS);

                return map;
            } else {
                map.put("error", "数据缺失");
                log.error(map.get("error"));
                return map;
            }
        } catch (Exception e) {
            map.put("error", e.getMessage());
            log.error(map.get("error"));
            return map;
        }
    }

    public static boolean validateTimestamp(long clientTimestamp) {
        long serverTime = System.currentTimeMillis();
        return Math.abs(serverTime - clientTimestamp) <= 3 * 60 * 1000;
    }


    private boolean isValidFileExtension(String fileName) {
        if (fileName == null || fileName.lastIndexOf(".") == -1) {
            return false;
        }
        String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        return ALLOWED_FILE_EXTENSIONS.contains(extension);
    }

    public Map<String, String> aIPPTCreateTask(AiPptDto aiPptDto, MultipartFile file) {
        Map<String, String> resultMap = new HashMap<>();
        Map<String, String> errorMap = new HashMap<>();

        Integer type = aiPptDto.getType();
        String content = aiPptDto.getContent();

//        String apiKey = configPrinterConfig.getAiPptApiKey();
        String pptToken = aiPptDto.getPptToken();

        aiPptDto.setPptToken(pptToken);
        aiPptDto.setAiPptApiKey(configPrinterConfig.getAiPptApiKey());


        if (type == null || type < 1 || type > 7) {
            errorMap.put("error", "Invalid type parameter. Type must be between 1 and 7.");
            log.error(errorMap.get("error"));
            return errorMap;
        }

        // 验证文件
        if (file != null && !file.isEmpty()) {
            // 验证文件格式
            if (!isValidFileExtension(file.getOriginalFilename())) {
                errorMap.put("error", "Unsupported file format. Allowed formats: " + String.join(", ", ALLOWED_FILE_EXTENSIONS));
                log.error(errorMap.get("error"));
                return errorMap;
            }

            long MAX_TOTAL_FILE_SIZE = 50 * 1024 * 1024; // 50MB in bytes
            // 验证文件大小
            if (file.getSize() > MAX_TOTAL_FILE_SIZE) {
                errorMap.put("error", "Total file size exceeds 50MB limit.");
                log.error(errorMap.get("error"));
                return errorMap;
            }
        }

        // 根据type验证参数
        switch (type) {
            case 1: // 智能生成：需要用户提供主题或要求
                if (StringUtils.isEmpty(content) || content.length() > 1000) {
                    errorMap.put("error", "Content is required and must not exceed 1000 characters for type 1.");
                    log.error(errorMap.get("error"));
                    return errorMap;
                }
                break;

            case 2: // 上传文件生成
                if (file == null || file.isEmpty()) {
                    errorMap.put("error", "File is required for type 2.");
                    log.error(errorMap.get("error"));
                    return errorMap;
                }
                break;

            case 3: // 上传思维导图生成
                if (file == null || file.isEmpty()) {
                    errorMap.put("error", "Mind map file (xmind/mm/md) is required for type 3.");
                    log.error(errorMap.get("error"));
                    return errorMap;
                }
                String ext = Objects.requireNonNull(file.getOriginalFilename()).substring(file.getOriginalFilename().lastIndexOf(".") + 1).toLowerCase();
                if (!Arrays.asList("xmind", "mm", "md").contains(ext)) {
                    errorMap.put("error", "Invalid mind map format. Only xmind/mm/md formats are supported.");
                    log.error(errorMap.get("error"));
                    return errorMap;
                }
                break;

            case 4: // Word精准转PPT
                if (file == null || file.isEmpty()) {
                    errorMap.put("error", "Word file is required for type 4.");
                    log.error(errorMap.get("error"));
                    return errorMap;
                }
                String wordExt = Objects.requireNonNull(file.getOriginalFilename()).substring(file.getOriginalFilename().lastIndexOf(".") + 1).toLowerCase();
                if (!Arrays.asList("doc", "docx").contains(wordExt)) {
                    errorMap.put("error_msg", "Invalid file format. Only doc/docx formats are supported for type 4.");
                    log.error(errorMap.get("error"));
                    return errorMap;
                }
                break;

            case 5: // 通过网页链接生成
                if (StringUtils.isEmpty(content) || !content.startsWith("http")) {
                    errorMap.put("error", "Valid URL is required for type 5.");
                    log.error(errorMap.get("error"));
                    return errorMap;
                }
                break;

            case 6: // 粘贴文本内容生成
                if (StringUtils.isEmpty(content) || content.length() > 20000) {
                    errorMap.put("error", "Text content is required and must not exceed 20000 characters for type 6.");
                    log.error(errorMap.get("error"));
                    return errorMap;
                }
                break;

            case 7: // Markdown大纲生成
                if (StringUtils.isEmpty(content)) {
                    errorMap.put("error", "Markdown content is required for type 7.");
                    log.error(errorMap.get("error"));
                    return errorMap;
                }
                break;
        }

        // 构建multipart请求
        String url = "https://open.docmee.cn/api/ppt/v2/createTask";

        MultipartBody.Builder builder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("type", String.valueOf(type));

        if (content != null && !content.isEmpty()) {
            builder.addFormDataPart("content", content);
        }

        if (file != null && !file.isEmpty()) {
            try {
                RequestBody fileBody = RequestBody.create(
                        file.getBytes(),
                        okhttp3.MediaType.parse("application/octet-stream")
                );
                builder.addFormDataPart("file", file.getOriginalFilename(), fileBody);
            } catch (IOException e) {
                errorMap.put("error", "Failed to process file: " + e.getMessage());
                log.error(errorMap.get("error"));
                return errorMap;
            }
        }

        Request request = new Request.Builder()
                .url(url)
                .post(builder.build())
                .addHeader("token", pptToken)
                .build();

        try (Response response = HTTP_CLIENT.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                errorMap.put("error", "Request failed with status code: " + response.code());
                log.error(errorMap.get("error"));
                return errorMap;
            }

            assert response.body() != null;
            String responseBody = response.body().string();
            JsonObject jsonResponse = JsonParser.parseString(responseBody).getAsJsonObject();

            if (jsonResponse.has("data") && jsonResponse.getAsJsonObject("data").has("id")) {
                String taskId = jsonResponse.getAsJsonObject("data").get("id").getAsString();
                resultMap.put("taskId", taskId);
                aiPptDto.setTaskId(taskId);

                redisService.setCacheObject(Constants.AI_PPT_TASK_ID + taskId, aiPptDto, 30L, TimeUnit.MINUTES);
                return resultMap;
            } else {
                log.error(String.valueOf(jsonResponse));
                errorMap.put("error", "Invalid response structure. Missing 'data.id'.");

                return errorMap;
            }
        } catch (Exception e) {
            errorMap.put("error", "An error occurred while processing the request: " + e.getMessage());
            log.error(errorMap.get("error"));
            return errorMap;
        }
    }


    @Override
    public Boolean cancelGeneration(MessagesDto messageDto) {
        if (StringUtils.isNotBlank(messageDto.getRequestId())) {
            String requestCacheKey = "request:" + messageDto.getRequestId();
            return  redisService.deleteObject(requestCacheKey);
        }else{
            log.error("请求ID为空");
            return false;
        }
    }

    @Override
    public Flux<String> aiPptGenerateContent(AiPptGenerateContentDto aiPptGenerateContentDto) {
        String model = aiPptGenerateContentDto.getModel();
        Flux<String> flux = null;

        if ("默认".equals(model)) {
            flux = aiPptDefaultGenerateContent(aiPptGenerateContentDto);
        } else {
            flux = chatQianFanModelFlux(aiPptGenerateContentDto);
        }
        return flux;
    }


    public Flux<String> aiPptDefaultGenerateContent(AiPptGenerateContentDto aiPptGenerateContentDto) {
        // 参数校验
        if (aiPptGenerateContentDto.getId() == null) {
            return Flux.error(new IllegalArgumentException("任务ID不能为空"));
        }


        AiPptDto aiPptDto = redisService.getCacheObject(Constants.AI_PPT_TASK_ID + aiPptGenerateContentDto.getId());
        String pptToken = aiPptDto.getPptToken();

//        String pptToken = aiPptGenerateContentDto.getPptToken();
//        if (StringUtils.isBlank(apiKey)) {
//            apiKey = configPrinterConfig.getAiPptApiKey();
//        }

        // 构建请求体
        JsonObject requestBody = new JsonObject();
        requestBody.addProperty("id", aiPptGenerateContentDto.getId());
        requestBody.addProperty("stream", aiPptGenerateContentDto.getStream() != null ? aiPptGenerateContentDto.getStream() : true);

        if (aiPptGenerateContentDto.getLength() != null) {
            requestBody.addProperty("length", aiPptGenerateContentDto.getLength());
        }
        if (aiPptGenerateContentDto.getScene() != null) {
            requestBody.addProperty("scene", aiPptGenerateContentDto.getScene());
        }
        if (aiPptGenerateContentDto.getAudience() != null) {
            requestBody.addProperty("audience", aiPptGenerateContentDto.getAudience());
        }
        if (aiPptGenerateContentDto.getLang() != null) {
            requestBody.addProperty("lang", aiPptGenerateContentDto.getLang());
        }
        if (aiPptGenerateContentDto.getPrompt() != null) {
            aiPptGenerateContentDto.setPrompt(getSceneSpecificInstructions(aiPptGenerateContentDto.getPrompt(), "", "中文(zh)", ""));
            if (aiPptGenerateContentDto.getPrompt().length() > 50) {
                requestBody.addProperty("prompt", aiPptGenerateContentDto.getPrompt().substring(0, 50));
            } else {
                requestBody.addProperty("prompt", aiPptGenerateContentDto.getPrompt());
            }
        }

        String url = "https://open.docmee.cn/api/ppt/v2/generateContent";

        okhttp3.MediaType mediaType = okhttp3.MediaType.parse("application/json; charset=utf-8");

        // 构建HTTP请求
        RequestBody body = RequestBody.create(gson.toJson(requestBody), mediaType);
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("token", pptToken)
                .addHeader("Accept", "text/event-stream")
                .build();

        String sessionId = snowflake.nextIdStr();
        String requestId = UUID.randomUUID().toString();
        String requestCacheKey = "request:" + requestId;

        return Flux.create(sink -> {
            Call call = HTTP_CLIENT.newCall(request);

            // 流式中断时的清理逻辑
            sink.onDispose(() -> {
                redisService.deleteObject(requestCacheKey);
                if (!call.isCanceled()) {
                    call.cancel();
                }
            });

            call.enqueue(new Callback() {
                @Override
                public void onFailure(@NotNull Call call, @NotNull IOException e) {
                    sink.error(e);
                }

                @Override
                public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {
                    if (!response.isSuccessful()) {
                        sink.error(new IOException("请求失败: code=" + response.code()));
                        return;
                    }

                    redisService.setCacheObject(requestCacheKey, true, 5L, TimeUnit.MINUTES);

                    if (response.body() != null) {
                        try (BufferedReader reader = new BufferedReader(
                                new InputStreamReader(response.body().byteStream(), StandardCharsets.UTF_8))) {
                            String line;
                            while ((line = reader.readLine()) != null && redisService.hasKey(requestCacheKey)) {
                                if (line.isEmpty()) {
                                    continue;
                                }

                                try {
                                    // 解析流式数据
                                    if (line.startsWith("data: ")) {
                                        String jsonString = line.replaceFirst("data: ", "");

                                        // 解析JSON响应
                                        JsonObject jsonResponse = JsonParser.parseString(jsonString).getAsJsonObject();

                                        // 构建符合格式的响应
                                        JsonObject formattedResponse = new JsonObject();

                                        // 复制status
                                        int status = jsonResponse.has("status") ? jsonResponse.get("status").getAsInt() : 3;
                                        formattedResponse.addProperty("status", status);

                                        // 处理text字段
                                        String text = jsonResponse.has("text") ? jsonResponse.get("text").getAsString() : "";
                                        formattedResponse.addProperty("text", text);

                                        // 如果是最终结果（status=4），添加result结构
                                        if (status == 4 && jsonResponse.has("result")) {
                                            formattedResponse.add("result", jsonResponse.get("result"));
                                        }
                                        formattedResponse.addProperty("requestId", requestId);
                                        formattedResponse.addProperty("sessionId", sessionId);
                                        // 发送格式化的SSE响应
                                        sink.next(gson.toJson(formattedResponse));

                                        if (status == 4) {
                                            break;
                                        }
                                    }

                                } catch (Exception e) {
                                    log.warn("解析响应数据失败: {}", line, e);
                                    // 如果解析失败，保持原始格式发送
                                    sink.next("data: " + line);
                                }
                            }

                            sink.complete();
                            redisService.deleteObject(requestCacheKey);
                        } catch (Exception e) {
                            log.error("流式处理异常: ", e);
                            sink.error(e);
                            redisService.deleteObject(requestCacheKey);
                        } finally {
                            response.close();
                        }
                    }
                }
            });
        });
    }

    public Flux<String> chatQianFanModelFlux(AiPptGenerateContentDto aiPptGenerateContentDto) {
        // 场景类型
        String themedScenes = aiPptGenerateContentDto.getPrompt();
        // 确定语言类型提示
        String languageType = LanguageTypeEnum.getByCode(aiPptGenerateContentDto.getLang()).getDesc() + "[" + aiPptGenerateContentDto.getLang() + "]";

        // 获取用户输入的核心内容
        AiPptDto aiPptDto = redisService.getCacheObject(Constants.AI_PPT_TASK_ID + aiPptGenerateContentDto.getId());
        String userCoreContent = aiPptDto.getContent(); // 用户输入的核心主题，如“城市管理学”


        // 构建新聊天对话集合
        List<MessagesDto> messagesList = new ArrayList<>();

        // 1. 强化场景和核心内容的首要地位
        messagesList.add(MessagesDto.builder()
                .role("user")
                .content("【首要任务与上下文】: 你将要生成一个PPT大纲。这个大纲的【场景主题】是：【" + themedScenes + "】，【核心内容】是关于：【" + userCoreContent + "】。后续所有指令都必须严格围绕这两点展开。")
                .build());
        messagesList.add(MessagesDto.builder()
                .role("user")
                .content("【严格条件:使用指定的语言进行处理：" + languageType + "】") // 语言提示可以保留
                .build());
        messagesList.add(MessagesDto.builder()
                .role("user")
                .content("【核心指令】: 你的输出必须是纯粹的、结构化的Markdown大纲。严格遵循指定的Markdown层级和语法。禁止使用任何表情符号或非文本字符以及相关的符号标识。除了要求的大纲内容本身，绝对不允许包含任何额外的备注、提示、解释、介绍、总结或任何其他元信息。")
                .build());

        // 进行网络数据搜素
        StringBuilder webSearchContentBuilder = new StringBuilder();
        if (aiPptGenerateContentDto.getWebSearch()) {
            List<MessagesDto> webSearchMessageList = new ArrayList<>();

            webSearchMessageList.add(
                    MessagesDto.builder()
                            .role("user")
                            .content(userCoreContent).build()
            );
            List<ReferencesDto> referencesDtos = chatQianfanModelWebSearch(webSearchMessageList, null);
            // 进行网络数据搜素
            if (referencesDtos != null && !referencesDtos.isEmpty()) {
                webSearchContentBuilder.append("【网络搜索数据内容】：\n");
                for (ReferencesDto ref : referencesDtos) {
                    webSearchContentBuilder.append("{标题:").append(ref.getTitle()).append("\n");
                    webSearchContentBuilder.append("内容:").append(ref.getContent()).append("\n");
                    webSearchContentBuilder.append("时间:").append(ref.getDate()).append("}").append("\n");
                }
            }
            log.info("【进行网络数据搜素】: {}", referencesDtos.toString());
            webSearchContentBuilder.append("你的回答必须引用网络搜索数据,需要有具体人物,具体时间,具体地点,具体事件,最后结果等结构化回答");
            webSearchMessageList.add(
                    MessagesDto.builder()
                            .role("user")
                            .content(String.valueOf(webSearchContentBuilder)).build()
            );
        }

        String sceneSpecificGuidance = getSceneSpecificInstructions(themedScenes, userCoreContent, languageType, String.valueOf(webSearchContentBuilder));

        // 生成场景化指导语 修改 PPT大纲结构提示，融入场景化指导
        String requirements = getPPTOutlineStructureTip(userCoreContent, themedScenes, sceneSpecificGuidance, webSearchContentBuilder.toString());
        String model = aiPptGenerateContentDto.getModel();
        messagesList.add(MessagesDto.builder()
                .role("user")
                .content(requirements)
                .build());
        return chatQianfanModleToOutlineFlux(messagesList, model);
    }

    @NotNull
    private static String getPPTOutlineStructureTip(String userCoreContent, String themedScenes, String sceneSpecificGuidance, String webSearchContent) {
        // 在这里插入场景化指导
        // 新增：明确要求AI使用这些深度解析
        // 稍微调整了这里的描述
        // 强调场景贴合
        // 改为“输入内容回顾”
        // 再次强调场景
        // 对主题名称也做场景化提示
        // 对章节也做场景化提示
        // 对段落内容也做场景化提示
        // 再次强调
        // 再次强调优先场景指导
        return "【总任务】\n" +
                "你是一个专业的PPT大纲生成助手。你的任务是根据用户提供的主题内容【" + userCoreContent + "】以及明确指定的场景主题【" + themedScenes + "】，严格按照下面定义的Markdown格式和规范，生成一份结构清晰、内容充实、逻辑严密、层级分明，并且【高度符合" + themedScenes + "场景要求】的PPT大纲。\n" +
                "\n" + sceneSpecificGuidance + "\n" + // 在这里插入场景化指导
                "\n 并结合以下【网络搜索参考内容】:" + webSearchContent +
                "【请严格运用上述【" + themedScenes + "场景深度解析与指导】中的结构建议、核心要素、内容侧重点和语言风格来构建大纲的每一部分，特别是目录章节的划分和具体内容的撰写。】\n" + // 新增：明确要求AI使用这些深度解析
                "【输出核心原则】\n" +
                "1. 纯净输出：最终结果只能是Markdown格式的纯文本。禁止包含任何```markdown 以及 ```等符号说明等说明、注释、解释、表情符号、特殊字符，或任何非大纲内容的文本。\n" +
                "2. 严格格式：必须严格遵循下述的Markdown层级结构、符号（包括符号后的空格）和内容要求。不得有任何变通、省略或自创格式。\n" +
                "3. 结构完整：大纲必须包含不少于6个章节（除非场景指导中有特定结构建议，则优先遵循场景指导调整章节划分），每个章节下应有多个页面标题，每个页面标题下应有多个段落标题，每个段落标题下应有详细的段落内容。\n" + // 稍微调整了这里的描述
                "4. 内容丰富与场景贴合：每个章节、页面、段落都要有具体、详实、逻辑清晰的内容，避免空泛和重复。最重要的是，所有内容都必须【明确体现出" + themedScenes + "场景的特点和要求】，并与核心主题【" + userCoreContent + "】紧密关联。\n" + // 强调场景贴合
                "5. 存在网络搜索内容时,【内容明确体现出" + webSearchContent + "网络搜索的结果】使用 人物,时间,地点,结果结构化语言整理，并与核心主题【" + userCoreContent + "】紧密关联。\n" + // 强调场景贴合
                "\n" +
                "【输入内容回顾】\n" + // 改为“输入内容回顾”
                "核心主题内容：【" + userCoreContent + "】\n" +
                "相关网络搜索内容：【" + webSearchContent + "】\n" +
                "指定场景主题：【" + themedScenes + "】\n" + // 再次强调场景
                "\n" +
                "【强制引用要求】\n" +
                "如果【网络搜索参考内容汇总】中存在数据,则必须引用网络搜索的结果提取**真实发生的信息点、数据、事件标题或热点词汇**，并在大纲中**显式使用这些内容**。每个章节下的至少一部分内容需体现与这些新闻内容的直接关联。不要虚构、不要泛泛而谈，务必使每一个段落的内容与现实新闻保持高关联性。\n" +
                "【输出规范】\n" +
                "1. 整体结构要求 (必须严格遵守，包括符号和空格):\n" +
                "   # 主题名称 (应能反映核心主题【" + userCoreContent + "】和场景【" + themedScenes + "】和网络搜索内容【" + webSearchContent + "】)\n" + // 对主题名称也做场景化提示
                "   ## 目录章节（不少于6个，章节主题自拟，但需符合【" + themedScenes + "】场景的逻辑结构和【" + userCoreContent + "】的内容需求）\n" + // 对章节也做场景化提示
                "   ### 页面标题 (例如：1.1 XXX, 1.2 YYY)\n" +
                "   #### 段落标题 (例如：1.1.1 XXX, 1.1.2 YYY)\n" +
                "   - 段落内容 (详细阐述，每个####下至少一条,最多2条内容。内容必须服务于【" + userCoreContent + "】并在【" + themedScenes + "】场景下有意义)\n" + // 对段落内容也做场景化提示
                "\n" +
                "2. 文本内容与长度要求:\n" +
                "   - `# 主题名称`: 概括核心内容与场景，20字以内。\n" +
                "   - `## 目录章节`: 章节标题，清晰指明章节主题，20字以内，总章节数不少于6个（除非场景指导另有建议）。章节划分应体现【" + themedScenes + "】的典型结构。\n" + // 再次强调
                "   - `### 页面标题`: 页面核心内容，具体化章节内容，20字以内。每章下建议2-3个页面标题。\n" +
                "   - `#### 段落标题`: 段落要点，概括段落大意，20字以内。每个页面下建议2-3个段落标题。\n" +
                "   - `- 段落内容`: 围绕段落标题进行充实、具体、且逻辑清晰的阐述。确保内容与主题【" + userCoreContent + "】及场景【" + themedScenes + "】紧密相关，避免空泛描述。每条内容200字以内，每个段落标题下最多2条内容。\n" + // 再次强调
                "\n" +
                "3. 符号说明：\n" +
                "   - 本提示中使用的 `【】` 和 `()` 等符号仅为解释说明，你的输出中不应包含它们，除非它们是主题内容本身的一部分。\n" +
                "\n" +
                "【结构详细示例】\n" +
                "请注意：以下是一个【通用】的PPT大纲结构示例。你在生成时，必须将此通用结构与前面提供的【" + themedScenes + "场景深度解析与指导】以及【" + userCoreContent + "核心主题】相结合。这意味着，虽然层级格式（#, ##, ###, ####, -）需要遵守，但【章节的划分逻辑、标题的措辞、具体内容的侧重点、甚至章节数量】都应【优先适配" + themedScenes + "场景深度解析与指导】中的要求。\n" + // 再次强调优先场景指导
                "【结构详细示例】\n" +
                "# 探索古代文明的奥秘\n" +
                "\n" +
                "## 1 引言：文明的回响\n" +
                "### 1.1 探索的意义\n" +
                "#### 1.1.1 了解过去，启迪未来\n" +
                "通过研究古代文明，我们可以更好地理解人类社会的发展历程，从中汲取智慧，为未来的发展提供借鉴。\n" +
                "#### 1.1.2 文化传承与认同\n" +
                "古代文明是人类共同的文化遗产，探索它们有助于增强民族自豪感，促进文化多样性的保护与传承。\n" +
                "#### 1.1.3 挑战与未知\n" +
                "古代文明中仍存在许多未解之谜，探索它们能激发人们的好奇心和求知欲，推动科学研究的进步。\n" +
                "\n" +
                "### 1.2 何谓古代文明\n" +
                "#### 1.2.1 文明的定义与特征\n" +
                "文明通常指具有复杂的社会组织、发达的科技和文化、城市化以及文字系统的社会形态。\n" +
                "#### 1.2.2 古代文明的时间跨度\n" +
                "一般认为，古代文明的时间跨度从公元前4000年左右到公元5世纪西罗马帝国灭亡，具体时间因地区而异。\n" +
                "#### 1.2.3 典型古代文明的代表\n" +
                "包括古埃及文明、古希腊文明、古罗马文明、两河流域文明、古印度文明和古代中国文明等。\n" +
                "\n" +
                "## 2 古埃及文明：尼罗河的馈赠\n" +
                "### 2.1 金字塔的辉煌\n" +
                "#### 2.1.1 金字塔的建造背景\n" +
                "金字塔是古埃及法老的陵墓，体现了法老的权力和信仰，需要耗费大量人力物力。\n" +
                "#### 2.1.2 金字塔的建造技术\n" +
                "古埃及人在没有现代机械的情况下，利用简单的工具和数学知识，完成了金字塔的建造，展现了高超的工程技术。\n" +
                "#### 2.1.3 金字塔的文化象征\n" +
                "金字塔不仅是陵墓，也是古埃及人对永生的向往和对太阳神的崇拜的象征。\n" +
                "\n" +
                "### 2.2 象形文字的破译\n" +
                "#### 2.2.1 象形文字的发现与解读\n" +
                "罗塞塔石碑的发现为破解象形文字提供了关键线索，让人们能够解读古埃及的文献和历史。\n" +
                "#### 2.2.2 象形文字的记录内容\n" +
                "象形文字记录了古埃及的政治、经济、文化和宗教等方面的信息，为我们了解古埃及文明提供了宝贵的资料。\n" +
                "#### 2.2.3 象形文字的影响\n" +
                "象形文字是人类文明的重要组成部分，对后来的文字发展产生了深远影响。\n" +
                "\n" +
                "### 2.3 古埃及的日常生活\n" +
                "#### 2.3.1 农业与灌溉\n" +
                "古埃及人依靠尼罗河的定期泛滥进行农业生产，发展了先进的灌溉技术，保障了粮食供应。\n" +
                "#### 2.3.2 社会阶层与分工\n" +
                "古埃及社会分为法老、贵族、僧侣、士兵、农民和奴隶等不同阶层，各阶层承担不同的社会职责。\n" +
                "#### 2.3.3 宗教信仰与神祇\n" +
                "古埃及人信仰多神，崇拜太阳神、尼罗河神等自然神祇，宗教信仰深刻影响着他们的生活和文化。\n" +
                "\n" +
                "## 3 古希腊文明：西方文明之源\n" +
                "### 3.1 民主制度的萌芽\n" +
                "#### 3.1.1 雅典民主制度的建立\n" +
                "雅典是古希腊民主制度的摇篮，公民参与政治决策，体现了人民主权的理念。\n" +
                "#### 3.1.2 民主制度的实践与局限\n" +
                "雅典民主制度虽然具有进步性，但也存在局限性，如奴隶和妇女没有政治权利。\n" +
                "#### 3.1.3 民主制度的影响\n" +
                "雅典民主制度对后来的政治思想和制度产生了深远影响，成为西方民主制度的重要源头。\n" +
                "\n" +
                "### 3.2 哲学思想的辉煌\n" +
                "#### 3.2.1 苏格拉底的诘问\n" +
                "苏格拉底通过不断提问和辩论，启发人们思考人生的意义和价值，其思想对西方哲学产生了重要影响。\n" +
                "#### 3.2.2 柏拉图的理想国\n" +
                "柏拉图提出了理想国的概念，探讨了政治、伦理和知识等问题，其思想具有深刻的哲学内涵。\n" +
                "#### 3.2.3 亚里士多德的逻辑学\n" +
                "亚里士多德建立了系统的逻辑学体系，对科学研究和思维方式产生了重要影响。\n" +
                "\n" +
                "### 3.3 奥林匹克运动会的起源\n" +
                "#### 3.3.1 运动会的历史与意义\n" +
                "奥林匹克运动会起源于古希腊，是为纪念宙斯神而举行的体育盛会，体现了古希腊人对体育的重视和对和平的向往。\n" +
                "#### 3.3.2 运动会的项目与规则\n" +
                "古代奥林匹克运动会包括跑步、摔跤、铁饼等项目，比赛规则简单而严格，体现了公平竞争的精神。\n" +
                "#### 3.3.3 运动会的影响\n" +
                "奥林匹克运动会是现代奥运会的源头，对促进国际交流和友谊发挥了重要作用。\n" +
                "\n" +
                "## 4 古罗马文明：帝国的扩张与 legado\n" +
                "### 4.1 罗马帝国的崛起\n" +
                "#### 4.1.1 从共和国到帝国\n" +
                "罗马从共和国发展成为强大的帝国，通过军事扩张和政治改革，建立了庞大的统治体系。\n" +
                "#### 4.1.2 罗马帝国的统治\n" +
                "罗马帝国通过完善的法律体系和行政管理，维持了帝国的稳定和繁荣。\n" +
                "#### 4.1.3 罗马帝国的文化\n" +
                "罗马帝国吸收了古希腊文化的精华，并加以发展，形成了独特的罗马文化。\n" +
                "\n" +
                "### 4.2 罗马法的影响\n" +
                "#### 4.2.1 罗马法的起源与发展\n" +
                "罗马法是古罗马的法律体系，经历了从习惯法到成文法的演变过程。\n" +
                "#### 4.2.2 罗马法的基本原则\n" +
                "罗马法强调公平、正义和程序正当，对后来的法律体系产生了深远影响。\n" +
                "#### 4.2.3 罗马法的现代意义\n" +
                "罗马法的一些基本原则仍然适用于现代社会，为法律的制定和实施提供了借鉴。\n" +
                "\n" +
                "### 4.3 罗马的建筑奇迹\n" +
                "#### 4.3.1 斗兽场的宏伟\n" +
                "罗马斗兽场是古罗马的标志性建筑，是举行角斗和公共娱乐的场所，体现了古罗马的工程技术和文化特色。\n" +
                "#### 4.3.2 万神殿的精妙\n" +
                "万神殿是古罗马的宗教建筑，体现了古罗马人在建筑设计和材料运用方面的精湛技艺。\n" +
                "#### 4.3.3 道路系统的完善\n" +
                "古罗马修建了发达的道路系统，方便了军队的调动和物资的运输，促进了经济的发展和文化的交流。\n" +
                "\n" +
                "## 5 其他古代文明的探索\n" +
                "### 5.1 两河流域文明：人类文明的摇篮\n" +
                "#### 5.1.1 苏美尔文明的成就\n" +
                "苏美尔文明是两河流域最古老的文明，创造了楔形文字、城市和法律等文明成果，对后来的文明产生了重要影响。\n" +
                "#### 5.1.2 巴比伦文明的汉谟拉比法典\n" +
                "汉谟拉比法典是巴比伦文明的代表，体现了古巴比伦的法律思想和文化精神。\n" +
                "#### 5.1.3 亚述帝国的军事扩张\n" +
                "亚述帝国通过军事扩张建立了庞大的帝国，但其残暴的统治最终导致帝国的衰落。\n" +
                "\n" +
                "### 5.2 古印度文明：神秘的宗教与文化\n" +
                "#### 5.2.1 印度河文明的城市规划\n" +
                "印度河文明以其精巧的城市规划和先进的排水系统而闻名，体现了古印度人的智慧和创造力。\n" +
                "#### 5.2.2 佛教的起源与传播\n" +
                "佛教起源于古印度，对亚洲乃至世界产生了深远影响，成为世界三大宗教之一。\n" +
                "#### 5.2.3 种姓制度的影响\n" +
                "种姓制度是古印度社会的重要特征，对社会结构和文化发展产生了复杂的影响。\n" +
                "\n" +
                "### 5.3 古代中国文明：绵延不绝的传承\n" +
                "#### 5.3.1 青铜时代的辉煌\n" +
                "中国青铜时代创造了精美的青铜器，体现了古代中国的科技水平和艺术成就。\n" +
                "#### 5.3.2 儒家思想的影响\n" +
                "儒家思想是中国传统文化的核心，对中国社会和文化产生了深远影响。\n" +
                "#### 5.3.3 长城与丝绸之路\n" +
                "长城是古代中国的防御工事，丝绸之路是连接东西方的贸易通道，体现了古代中国的对外交流和文化传播。\n" +
                "\n" +
                "## 6 结论：文明的启示\n" +
                "### 6.1 文明的多样性与共性\n" +
                "#### 6.1.1 多样性的体现\n" +
                "不同的古代文明在地理环境、文化传统和社会制度等方面存在差异，呈现出多样性的特点。\n" +
                "#### 6.1.2 共性的特征\n" +
                "古代文明都具有城市、文字、宗教和法律等基本特征，反映了人类社会发展的共同规律。\n" +
                "#### 6.1.3 文明交流与融合\n" +
                "古代文明之间存在交流与融合，相互影响和促进，共同推动了人类文明的进步。\n" +
                "\n" +
                "### 6.2 对现代社会的启示\n" +
                "#### 6.2.1 尊重文化多样性\n" +
                "我们应该尊重不同的文化，促进文化交流和互鉴，共同构建和谐世界。\n" +
                "#### 6.2.2 汲取历史经验\n" +
                "古代文明的兴衰成败为我们提供了宝贵的经验教训，有助于我们更好地应对未来的挑战。\n" +
                "#### 6.2.3 传承人类文明遗产\n" +
                "我们应该保护古代文明的遗址和文物，传承人类文明的精华，让后代也能从中受益。\n" +
                "\n" +
                "### 6.3 未来探索的方向\n" +
                "#### 6.3.1 考古技术的进步\n" +
                "随着考古技术的不断进步，我们有望发现更多的古代文明遗址，揭示更多的历史真相。\n" +
                "#### 6.3.2 跨学科的研究\n" +
                "通过历史学、考古学、人类学等多学科的合作研究，我们可以更全面地了解古代文明。\n" +
                "#### 6.3.3 公众的参与与支持\n" +
                "公众的参与和支持是古代文明研究的重要力量，有助于提高人们对文化遗产的保护意识。\n" +
                "\n" +
                "【最终强调】\n" +
                "请严格按照以上所有规范执行，特别是关于【" + themedScenes + "场景深度解析与指导】【结合相关网络搜素内容(需要明确搜索中出现的人物,时间,地点,结果):" + webSearchContent + "】+ 以及 【" + userCoreContent + "核心内容】的各项要求。最终输出必须是纯粹的、结构化的Markdown文本，并且能够【精确地】体现出所选场景的特色和结构。任何偏差都将导致输出无效。";
    }

    private String getSceneSpecificInstructions(String themedScenes, String userContent, String languageType, String webSearchContentBuilder) {
        String instruction = "";
        String escapedUserContent = userContent.replace("【", "").replace("】", ""); // Basic sanitization

        // Normalize scene names for broader matching (e.g., handle spaces, case)
        String normalizedScene = themedScenes.toLowerCase().replace(" ", "");

        switch (normalizedScene) {
            case "邮件":
            case "email":
                instruction = "当前PPT大纲的目的是为了准备一封关于【" + escapedUserContent + "】的邮件。\n" +
                        "【邮件场景核心要素与结构建议】:\n" +
                        "1.  **邮件主题 (Subject Line):** 必须简洁、明确、信息丰富，准确概括邮件核心内容（关于【" + escapedUserContent + "】）。**考虑加入[行动请求]/[信息]/[重要]等标签，以及日期或项目编号以增强可追溯性和优先级。** PPT大纲的 `# 主题名称` 应直接反映此邮件主题。\n" +
                        "2.  **收件人 (To), 抄送 (Cc), 密送 (Bcc):** 明确主要收件人。**思考抄送和密送的必要性及对象。**\n" +
                        "3.  **称呼 (Salutation):** 根据与收件人的关系和场合选择合适的称呼（例如：尊敬的XX总/教授，亲爱的XX同事/同学，Hi XX团队）。**注意正式程度。**\n" +
                        "4.  **开场白/目的陈述 (Opening/Purpose Statement):** 清晰简述邮件目的，或对先前沟通的延续。**如果是首次联系，进行简短的自我介绍和背景说明。** 大纲的第一个章节可以围绕此展开。\n" +
                        "5.  **背景信息/情境铺垫 (Background Information/Context Setting):** (如果必要) 提供足够的背景信息，帮助收件人快速理解【" + escapedUserContent + "】的来龙去脉和重要性。\n" +
                        "6.  **正文 - 核心信息传递 (Body - Core Message Delivery):** 这是邮件的核心。应清晰、有条理地分点阐述与【" + escapedUserContent + "】相关的具体信息、发现、问题、请求或建议。**使用项目符号、编号列表或加粗突出关键信息，使其更易读。** PPT大纲的 `## 目录章节` 应主要体现这些核心信息点。\n" +
                        "7.  **正文 - 数据/证据/示例支撑 (Body - Data/Evidence/Examples):** (如果适用) 引用关键数据、事实、图表（邮件中可描述或链接）、具体示例或链接来支持你的观点或请求，增强说服力。\n" +
                        "8.  **行动呼吁 (Call to Action - CTA):** (如果适用) **明确、具体、单一地**希望收件人做什么（例如：请于X月X日前回复此邮件、请批准附件中的方案、请提供XX反馈信息、请参加XX会议）。**CTA应突出且易于执行，告知期望的完成时间。**\n" +
                        "9.  **后续步骤/期望 (Next Steps/Expectations):** (如果适用) 说明下一步的计划或你将采取的行动，以及对收件人回应的期望。\n" +
                        "10. **附件说明与检查 (Attachments Description & Check):** 清晰列出所有附件的名称和简要内容说明。**务必在发送前确认附件已正确添加且可打开。**\n" +
                        "11. **结束语/礼貌致谢 (Closing Remarks/Courteous Thanks):** 礼貌结尾，可表达感谢或期待（例如：感谢您的时间与协助，期待您的回复，顺祝商祺，此致敬礼）。\n" +
                        "12. **署名 (Signature):** 发件人全名、职位/身份、所属部门/公司/学校、联系方式（电话、备用邮箱、LinkedIn等）。**确保签名档信息完整且专业。**\n" +
                        "【内容侧重点】: 围绕【" + escapedUserContent + "】展开，信息传递需**准确、高效、完整、有条理。** 避免冗长无关的描述和不必要的术语。每个段落内容需直接服务于邮件的沟通目的和收件人的需求。\n" +
                        "【语言风格】: **根据收件人和邮件目的调整正式度。** 总体应专业、简洁、清晰、礼貌、直接。**使用积极的语气，避免指责、命令或含糊不清的表达。注意拼写和语法。**\n" +
                        "【章节数量】: 邮件场景下，章节数量通常较少，**以不超过3-5个核心信息块为宜，** 灵活调整。PPT大纲的章节是帮助组织邮件思路，不一定完全对应邮件的视觉段落。";
                break;

            case "讲话发言稿":
            case "公众演讲":
            case "public speaking": // Corrected alias
                instruction = "当前PPT大纲是为了一篇关于【" + escapedUserContent + "】的讲话发言稿或公众演讲。目标是清晰、有说服力且引人入胜地传递信息。\n" +
                        "【发言稿/演讲场景核心要素与结构建议】:\n" +
                        "1.  **演讲标题与副标题 (Speech Title & Subtitle):** \n" +
                        "    a.  **主标题:** 应简洁、有力、点明【" + escapedUserContent + "】核心，并能激发听众好奇心。\n" +
                        "    b.  **副标题 (可选):** 可用于进一步阐释主题范围、核心观点或目标受众。\n" +
                        "    c.  PPT大纲的 `# 主题名称` 应直接反映此演讲标题。\n" +
                        "2.  **开场致辞与建立连接 (Opening Remarks & Connection Building):**\n" +
                        "    a.  **问候与致谢:** 向听众问好，感谢主办方邀请和听众的到来。\n" +
                        "    b.  **自我介绍 (适度):** 简要介绍自己及与主题【" + escapedUserContent + "】的专业关联或热情所在，建立可信度。\n" +
                        "    c.  **与听众建立初步连接:** 可以是一个简短的共鸣点、对听众的认可或提及共同的背景。\n" +
                        "3.  **“钩子”/引人入胜的开场白 (The \"Hook\" / Compelling Opening - < 90 seconds):** (此部分可作为第一个 `## 目录章节` 的核心内容)\n" +
                        "    a.  **目的:** 迅速抓住听众的全部注意力，让他们对接下来的内容产生强烈兴趣。\n" +
                        "    b.  **常见有效技巧 (选择1-2种):**\n" +
                        "        i.  **提出一个与【" + escapedUserContent + "】相关的、引人深思或挑战性的问题。**\n" +
                        "        ii. **分享一个惊人且相关的统计数据、事实或发现。**\n" +
                        "        iii. **讲述一个非常简短（30-60秒）、生动、与主题【" + escapedUserContent + "】高度相关的个人故事、轶事或隐喻。**\n" +
                        "        iv. **引用一句有力的、与主题贴切的名人名言或行业洞察。**\n" +
                        "        v.  **制造一个出人意料的声明、对比或展示一个令人惊讶的视觉元素。**\n" +
                        "        vi. **直接点明听众的核心痛点或期望。**\n" +
                        "4.  **明确核心信息/主旨句/承诺 (Clear Core Message/Thesis Statement/Promise to the Audience):**\n" +
                        "    a.  **清晰、简洁地阐述本次演讲的核心观点、希望传递的关键信息，或你对听众的承诺（听完后他们将获得什么）。**\n" +
                        "    b.  **与【" + escapedUserContent + "】紧密相关，应能用一两句话概括，并易于记忆。**\n" +
                        "    c.  **这应是贯穿整个演讲的主线和反复强调的重点。**\n" +
                        "5.  **演讲路线图/议程预览 (\"Roadmap\" / Agenda Preview - \"Tell them what you're going to tell them\"):**\n" +
                        "    a.  **简要告知听众接下来将讨论的2-4个主要部分/论点及其逻辑顺序。**\n" +
                        "    b.  **帮助听众建立心理预期，更容易跟上演讲思路。**\n" +
                        "    c.  此部分可作为开场后的第一个独立小节或融入引言。\n" +
                        "6.  **主要论点/内容模块一 (Main Point/Content Module 1 - e.g., Understanding 【" + escapedUserContent + "】: Background & Significance):** (每个主要论点构成一个 `## 目录章节`)\n" +
                        "    a.  **清晰陈述第一个核心论点/方面，并解释其与主旨句的联系。**\n" +
                        "    b.  **支撑材料 (Supporting Evidence & Elaboration):** (每个 `### 页面标题` 和 `#### 段落标题` 应细化这些)\n" +
                        "        i.  **事实与数据:** 提供可信的数据来源和清晰的图表化展示（大纲中描述图表内容）。\n" +
                        "        ii. **案例分析/具体示例:** 使用真实或有代表性的案例来说明观点。\n" +
                        "        iii. **故事/轶事:** 用相关的短故事增强代入感和记忆点。\n" +
                        "        iv. **专家引言/研究发现:** 引用权威观点或研究成果增加说服力。\n" +
                        "        v.  **类比/隐喻:** 用听众熟悉的事物解释复杂概念。\n" +
                        "    c.  **视觉辅助提示:** (大纲中描述) 配合此论点的PPT应呈现哪些关键视觉元素（图片、关键词、流程图等）以强化信息传递。\n" +
                        "7.  **主要论点/内容模块二 (Main Point/Content Module 2 - e.g., Analyzing 【" + escapedUserContent + "】: Challenges & Opportunities):** (结构类似模块一，确保内容逻辑递进)\n" +
                        "8.  **主要论点/内容模块三 (Main Point/Content Module 3 - e.g., Solutions/Strategies for 【" + escapedUserContent + "】):** (结构类似模块一，可根据内容复杂度设置更多模块，但建议不超过4-5个核心论点以保持听众注意力)\n" +
                        "9.  **逻辑过渡与内部小结 (Effective Transitions & Internal Summaries):**\n" +
                        "    a.  **在各主要论点之间使用清晰、自然的过渡词、短语或设问句，** 引导听众思路转换（例如：“了解了背景之后，我们再来看看具体的挑战…”）。\n" +
                        "    b.  **在每个重要论点阐述完毕后，进行简短的小结，** 强化听众对该部分核心内容的记忆，并自然引出下一部分。\n" +
                        "10. **互动与参与技巧 (Audience Engagement Techniques - Strategically Placed):**\n" +
                        "    a.  **提问:** 使用开放式问题、封闭式问题、反问、设问等与听众互动。\n" +
                        "    b.  **邀请听众参与:** 如快速举手表决、简短的“邻座讨论”、分享一句话感想等。\n" +
                        "    c.  **使用幽默 (适度且得体):** 如果适合演讲场合和个人风格，可以缓解气氛，增强亲和力。\n" +
                        "    d.  **调用感官:** 描述生动的场景，使用有力的词汇，甚至配合道具或演示（如果适用）。\n" +
                        "    e.  **眼神交流、肢体语言与声音变化:** (大纲中可备注关键时刻的表达提示，如“此处停顿”、“加强语气”、“环视听众”等)。\n" +
                        "11. **情感共鸣与故事化叙事 (Emotional Connection & Narrative Storytelling):**\n" +
                        "    a.  **在关键部分，通过讲述与【" + escapedUserContent + "】相关的真实、感人的故事或个人经历，** 触动听众的情感，建立更深层次的连接。\n" +
                        "    b.  **描绘愿景或共同的未来，** 激发听众的认同感和向往。\n" +
                        "12. **强有力的总结与回顾 (Powerful Conclusion & Recap - \"Tell them what you told them\"):** (此部分可作为最后一个 `## 目录章节` 的核心内容)\n" +
                        "    a.  **清晰、简要地回顾演讲的2-3个主要论点，** 再次强调核心信息/主旨句。\n" +
                        "    b.  **避免引入任何新的信息或论点。**\n" +
                        "    c.  **可以用与开场“钩子”相呼应的方式来收尾，形成完整结构。**\n" +
                        "13. **升华、行动呼吁或最终启示 (Elevation, Call to Action, or Final Memorable Thought):**\n" +
                        "    a.  **将主题【" + escapedUserContent + "】的意义进行升华，** 赋予其更深远的价值或影响。\n" +
                        "    b.  **如果适用，发出明确、具体、可行的行动呼吁 (Call to Action)，** 鼓励听众在演讲结束后采取某些行动。\n" +
                        "    c.  **或者，留下一个发人深省的问题、一句充满力量的总结语或一个难忘的意象，** 让听众在离开后仍能回味和思考。\n" +
                        "14. **感谢致辞与后续互动指引 (Acknowledgements & Guidance for Further Interaction):**\n" +
                        "    a.  **再次真诚感谢听众的聆听、主办方的邀请和所有支持者。**\n" +
                        "    b.  **提供清晰的联系方式、社交媒体账号、或获取相关资料（如PPT、补充阅读）的途径。**\n" +
                        "    c.  **预告Q&A环节（如果安排在演讲主体之后）。**\n" +
                        "【内容侧重点】: 围绕【" + escapedUserContent + "】展开，**始终以听众为中心，** 考虑他们的背景、需求和期望。内容需具有清晰的逻辑结构（如问题-分析-解决，是什么-为什么-怎么办），强有力的论证，以及情感上的连接。目标是有效地告知、说服、激励或娱乐（视演讲目的而定）。\n" +
                        "【语言风格】: **口语化，易于理解，生动有趣，富有节奏感和感染力。** 根据演讲的具体场合（如学术会议、商业路演、内部培训、庆典活动）、听众的专业背景和人数，灵活调整语言的正式程度、幽默感、情感色彩和专业深度。**多用短句、排比、对比、反问等修辞手法增强表达效果。注意语速的控制和关键时刻的停顿（大纲中可标注关键提示）。**\n" +
                        "【专业术语】: 针对【" + escapedUserContent + "】中可能出现的专业术语，必须进行**通俗易懂的解释，或使用生动的类比、实例进行说明，** 确保非专业背景的听众也能准确理解核心信息。**避免滥用行话。**\n" +
                        "【视觉配合（PPT）】: 大纲中的每个要点都应思考PPT画面的呈现方式，**PPT应作为演讲的视觉辅助，突出关键词、核心数据、流程图、高质量图片，避免大段文字。** 大纲内容应指导PPT内容的提炼。";
                break;

            case "营销策划":
            case "marketing": // Corrected alias
                instruction = "当前PPT大纲用于制定一份关于【" + escapedUserContent + "】的营销策划案。\n" +
                        "【营销策划场景核心要素与结构建议】:\n" +
                        "1.  **封面页 (Cover Page):** 策划案名称（例如：【" + escapedUserContent + "】营销策划方案 V1.0），客户/品牌Logo，策划方信息，提交日期。\n" +
                        "2.  **目录 (Table of Contents):** 清晰列出主要章节及页码。\n" +
                        "3.  **执行摘要 (Executive Summary):** (1-2页，最后撰写但放在最前) 高度概括整个策划案：\n" +
                        "    a.  **核心挑战/机遇 (Core Challenge/Opportunity related to 【" + escapedUserContent + "】).**\n" +
                        "    b.  **营销总目标 (Overall Marketing Objective).**\n" +
                        "    c.  **核心策略概述 (Key Strategy Overview).**\n" +
                        "    d.  **关键行动亮点 (Highlights of Key Actions).**\n" +
                        "    e.  **预期核心成果/ROI概览 (Expected Core Outcomes/ROI Overview).**\n" +
                        "    f.  **总预算需求 (Total Budget Requirement).**\n" +
                        "4.  **引言与背景分析 (Introduction & Situational Analysis):**\n" +
                        "    a.  **项目背景与营销需求 (Project Background & Marketing Need for 【" + escapedUserContent + "】).**\n" +
                        "    b.  **宏观环境分析 (PESTEL Analysis - Political, Economic, Social, Technological, Environmental, Legal).**\n" +
                        "    c.  **行业分析 (Industry Analysis - Size, Growth, Trends, Key Success Factors).**\n" +
                        "    d.  **市场分析 (Market Analysis - Market Segments, Consumer Behavior, Unmet Needs).**\n" +
                        "    e.  **竞争对手分析 (Competitor Analysis - Key Competitors, Strengths, Weaknesses, Strategies, Market Share).**\n" +
                        "    f.  **自身产品/品牌分析 (SWOT Analysis for 【" + escapedUserContent + "】 - Strengths, Weaknesses, Opportunities, Threats).**\n" +
                        "5.  **营销目标设定 (Marketing Objectives - SMART Principle):**\n" +
                        "    a.  **业务目标回顾 (Recap of Business Goals that marketing will support).**\n" +
                        "    b.  **具体营销目标 (Specific Marketing Objectives):** 例如：品牌认知度提升X%，网站流量增加Y%，潜在客户获取数量Z，销售转化率提升W%，市场份额达到V%等，均需与【" + escapedUserContent + "】直接关联并可衡量。\n" +
                        "6.  **目标受众画像 (Target Audience Persona):**\n" +
                        "    a.  **人口统计学特征 (Demographics).**\n" +
                        "    b.  **心理特征与生活方式 (Psychographics & Lifestyle).**\n" +
                        "    c.  **行为特征 (Behavioral - e.g., media consumption, purchase habits).**\n" +
                        "    d.  **痛点与需求 (Pain Points & Needs related to 【" + escapedUserContent + "】).**\n" +
                        "7.  **市场定位与核心价值主张 (Market Positioning & Core Value Proposition):**\n" +
                        "    a.  **选择目标细分市场 (Target Segment Selection).**\n" +
                        "    b.  **差异化定位策略 (Differentiation Strategy for 【" + escapedUserContent + "】).**\n" +
                        "    c.  **清晰、有吸引力的价值主张 (Clear and Compelling Value Proposition).**\n" +
                        "8.  **核心营销策略组合 (Marketing Strategy Mix - e.g., 4Ps, 7Ps, SAVE Model):**\n" +
                        "    a.  **产品策略 (Product Strategy for 【" + escapedUserContent + "】):** 核心产品/服务，特性，质量，设计，品牌，包装，服务，新品开发等。\n" +
                        "    b.  **价格策略 (Price Strategy):** 定价目标，定价方法，价格水平，折扣与促销，支付条款。\n" +
                        "    c.  **渠道策略 (Place/Distribution Strategy):** 分销渠道选择，渠道覆盖，物流与库存管理，线上线下整合。\n" +
                        "    d.  **推广/整合营销传播策略 (Promotion/Integrated Marketing Communications - IMC Strategy):**\n" +
                        "        i.  **广告 (Advertising - Digital, Traditional).**\n" +
                        "        ii. **公共关系 (Public Relations - Media Relations, Events, Influencer Marketing).**\n" +
                        "        iii. **促销活动 (Sales Promotion - Discounts, Contests, Loyalty Programs).**\n" +
                        "        iv. **内容营销 (Content Marketing - Blog, Video, Social Media Content).**\n" +
                        "        v.  **社交媒体营销 (Social Media Marketing).**\n" +
                        "        vi. **数字营销 (Digital Marketing - SEO, SEM, Email Marketing).**\n" +
                        "    e.  **(If 7Ps) 人员策略 (People Strategy):** 客户服务，销售团队。\n" +
                        "    f.  **(If 7Ps) 过程策略 (Process Strategy):** 客户旅程，服务交付。\n" +
                        "    g.  **(If 7Ps) 物证策略 (Physical Evidence Strategy):** 品牌体验，环境。\n" +
                        "9.  **具体行动计划与时间表 (Action Plan & Timeline/Gantt Chart):**\n" +
                        "    a.  **营销活动日历 (Marketing Campaign Calendar).**\n" +
                        "    b.  **各活动/任务的详细方案:** 目标，内容，执行步骤，负责人，起止时间，所需资源。\n" +
                        "10. **营销预算与资源分配 (Marketing Budget & Resource Allocation):**\n" +
                        "    a.  **总预算及构成明细 (Overall Budget & Detailed Breakdown).**\n" +
                        "    b.  **各营销活动/渠道的预算分配。**\n" +
                        "    c.  **预期投资回报率 (Projected ROI) 及计算依据。**\n" +
                        "11. **效果评估、KPI监测与优化机制 (Measurement, KPIs, Monitoring & Optimization):**\n" +
                        "    a.  **关键绩效指标 (Key Performance Indicators - KPIs) 及目标值。**\n" +
                        "    b.  **数据追踪与分析工具/方法。**\n" +
                        "    c.  **汇报周期与形式。**\n" +
                        "    d.  **持续优化调整机制。**\n" +
                        "12. **风险评估与应对预案 (Risk Assessment & Contingency Plan):** 识别市场、竞争、执行等方面的潜在风险，并制定相应的应对措施。\n" +
                        "13. **团队与职责 (Team & Responsibilities - Optional, if not covered elsewhere):** 营销团队结构及关键成员职责。\n" +
                        "14. **总结与下一步建议 (Conclusion & Next Steps):** 再次强调策划案的核心价值、成功关键，并提出下一步的批准或执行建议。\n" +
                        "15. **附录 (Appendix - Optional):** 详细市场调研数据，竞品广告案例，详细财务模型等。\n" +
                        "【内容侧重点】: **以实现业务目标为导向，数据驱动，逻辑严密，策略清晰，方案具体可行且富有创意。** 所有分析和策略均需紧密围绕【" + escapedUserContent + "】展开。**强调可衡量性和投资回报。**\n" +
                        "【语言风格】: 专业、客观、具有高度说服力。**多用图表、数据和行业术语（但需确保目标受众能理解）。** 结构清晰，表达精炼。";
                break;

            case "危机公关说明":
            case "危机沟通声明": // 更常用的中文表述
            case "crisiscommunicationstatement":
            case "crisis response":
                instruction = "当前PPT大纲用于准备一份关于【" + escapedUserContent + "】（指具体危机事件，例如：产品质量问题、数据泄露、安全事故、高管不当言论等）的官方危机公关说明或新闻发布会演示材料。**核心目标是：控制损害、传递准确信息、展现负责任态度、争取公众/利益相关者理解与信任，并引导舆论。**\n" +
                        "【危机公关说明场景核心要素与结构建议】:\n" +
                        "1.  **标题页 (Title Page):** \n" +
                        "    a.  **清晰的标题:** 例如“关于【" + escapedUserContent + "】事件的官方声明”或“【公司名称】就【" + escapedUserContent + "】事件的情况说明”。\n" +
                        "    b.  **公司Logo。**\n" +
                        "    c.  **发布日期和具体时间（精确到分钟，如果可能）。**\n" +
                        "    d.  **(可选) 发言人姓名与职务。**\n" +
                        "2.  **开场声明/立场陈述 (Opening Statement/Position Statement - 由最高级别合适发言人发布):**\n" +
                        "    a.  **发言人身份介绍与开场致意:** 简要介绍发言人，感谢媒体/公众的关注。\n" +
                        "    b.  **对事件的首次官方确认与简要定性:** 清晰、直接地承认【" + escapedUserContent + "】事件的发生，并对其进行初步、客观的描述（避免主观臆断或淡化）。\n" +
                        "    c.  **表达公司的基本立场和核心态度:** 例如，对事件表示“高度重视”、“深感遗憾/痛心/关切”、“正严肃对待”、“将彻查到底”等。**态度必须诚恳、负责。**\n" +
                        "    d.  **(如果适用) 对受直接影响者的初步致歉或慰问:** 根据事件性质，向受害者、客户、员工或公众表达真诚的歉意或深切的慰问。\n" +
                        "3.  **事实陈述与信息更新 (Statement of Facts & Information Update - Adhere to \"SPEED\" principle: Swift, Precise, Empathetic, Engaging, Dependable):**\n" +
                        "    a.  **目前已核实的关键事实 (Verified Key Facts about 【" + escapedUserContent + "】):** 按照时间顺序或逻辑顺序，清晰、准确地陈述：\n" +
                        "        i.  **事件发生的时间、地点、基本过程。**\n" +
                        "        ii. **初步确认的涉及范围和影响程度。**\n" +
                        "        iii. **事件是如何被发现或报告的。**\n" +
                        "    b.  **强调“目前已知/已核实”:** 表明信息可能随调查深入而更新，**只发布已确认无误的信息，坚决避免猜测、推断或未经证实的消息。**\n" +
                        "    c.  **事件的最新进展情况 (Latest Progress of the Situation - if ongoing):** 例如，救援情况、问题控制情况等。\n" +
                        "    d.  **(如果必要且有充分证据) 主动澄清不实信息或恶意谣言 (Proactive Clarification of Misinformation or Rumors):** 提供事实依据进行反驳。\n" +
                        "4.  **已迅速采取的应对措施 (Immediate Actions Taken & Ongoing Efforts):**\n" +
                        "    a.  **紧急响应与控制措施:** 为阻止事态扩大或减少损失已采取的即时行动（例如：停止相关操作、启动应急预案、隔离风险源）。\n" +
                        "    b.  **针对受直接影响方的安抚/援助/支持措施:** 例如，为受害者提供医疗救助、为客户提供退换货服务、为员工提供心理疏导等。**具体说明做了什么。**\n" +
                        "    c.  **内部调查/评估启动情况:** 宣布已成立调查组，明确调查范围和目标，承诺将进行独立、公正、彻底的调查。\n" +
                        "    d.  **与相关监管机构/部门的沟通与配合:** (如果适用) 表明已向有关部门报告并积极配合调查。\n" +
                        "5.  **展现同理心与承担责任的承诺 (Demonstrating Empathy & Commitment to Responsibility):**\n" +
                        "    a.  **再次向所有受【" + escapedUserContent + "】事件影响的各方表达深切的同情、歉意或慰问，** 并理解他们的担忧和感受。**言辞要真诚，避免空洞。**\n" +
                        "    b.  **明确承诺将如何处理后续事宜，并承担应有的责任 (法律、道义、经济等，根据实际情况)。** 不回避，不推诿。\n" +
                        "    c.  **强调公司将从事件中吸取教训。**\n" +
                        "6.  **下一步行动计划与时间表 (Next Steps & Timeline - Be Specific and Actionable):**\n" +
                        "    a.  **后续调查计划与预期完成时间节点 (Further Investigation Plan & Expected Timeline).**\n" +
                        "    b.  **问题根本原因分析计划 (Root Cause Analysis Plan).**\n" +
                        "    c.  **问题解决/补偿/补救方案的制定与公布计划 (Problem Resolution/Compensation/Remediation Plan Development & Announcement Plan).**\n" +
                        "    d.  **长期预防措施/系统性改进计划的制定与实施 (Preventive Measures/Systemic Improvement Plan Development & Implementation to prevent recurrence of 【" + escapedUserContent + "】).**\n" +
                        "    e.  **承诺的定期信息更新频率与官方发布渠道 (Commitment to Regular Updates: Frequency and Official Channels).**\n" +
                        "7.  **核心信息重申/关键信息点总结 (Reiteration of Core Message/Key Talking Points):**\n" +
                        "    a.  **简明扼要地再次强调公司的核心立场、对受影响者的承诺、以及解决问题的决心和关键行动。**\n" +
                        "    b.  **确保信息点与开场声明一致且有力。**\n" +
                        "8.  **问答环节指引与准备 (Guidance & Preparation for Q&A Session):** (此部分更多为内部准备，但在发布会中可由主持人提及规则)\n" +
                        "    a.  **指定官方发言人团队及各自分工 (Answering specific types of questions).**\n" +
                        "    b.  **准备充分的Q&A清单 (Anticipated tough questions and well-prepared, consistent answers).**\n" +
                        "    c.  **强调回答问题的核心原则:** 保持冷静、坦诚、准确；不猜测未知信息；不指责他人；不透露未授权信息；对敏感问题有标准应对口径。\n" +
                        "    d.  **设定问答时间限制或提问规则。**\n" +
                        "9.  **官方联系方式与权威信息获取渠道 (Official Contact & Authoritative Information Channels):**\n" +
                        "    a.  **提供专门的媒体联络人及联系方式。**\n" +
                        "    b.  **公布官方网站、社交媒体账号等获取事件更新和官方信息的唯一指定渠道。**\n" +
                        "    c.  **(如果适用) 设立公众咨询热线。**\n" +
                        "10. **结束声明与展望 (Closing Statement & Forward Look):**\n" +
                        "    a.  **再次感谢媒体和公众的关注与理解。**\n" +
                        "    b.  **重申公司将全力以赴处理好【" + escapedUserContent + "】事件，并从中学习，持续改进。**\n" +
                        "    c.  **(可选) 表达对恢复信任和未来发展的积极展望。**\n" +
                        "【内容侧重点】: 围绕【" + escapedUserContent + "】危机事件，**以5W1H（What, Who, When, Where, Why, How）为基础，遵循“3T原则”（Tell the truth, Tell it fast, Tell it all - 在法律和调查允许范围内）和“黄金24小时”原则。** 核心是：**迅速响应 (Speed)，承担责任 (Responsibility)，保持透明 (Transparency)，展现同理心 (Empathy)，以及提供解决方案 (Solution)。** 事实必须准确无误，态度必须诚恳真挚，行动必须明确果断。\n" +
                        "【语言风格】: **极其谨慎、官方、正式、冷静、客观、富有同情心和责任感。** 措辞必须经过仔细推敲，**力求精确，避免任何可能被误解、曲解或引发新一轮负面舆情的表述。** 所有对外发布的言辞必须保持高度统一，与公司所有官方口径（包括书面声明、社交媒体、高管访谈等）完全一致。\n" +
                        "【章节数量】: 结构清晰，信息传递层层递进，确保所有关键信息点都得到覆盖。**宁可内容简洁扼要，也不可含糊不清或遗漏关键环节。** 章节划分应服务于信息发布的逻辑性和清晰度。";
                break;

            case "年度报告": // 通常指公司对外发布的正式年度报告的演示版本
            case "representativeness's":
                instruction = "当前PPT大纲用于准备一份关于【" + escapedUserContent + "】（通常指公司上一个财年）的年度报告演示。\n" +
                        "【年度报告演示场景核心要素与结构建议】:\n" +
                        "1.  **封面页 (Cover Page):** 公司名称/Logo，【" + escapedUserContent + "】年度报告，报告期（如：2023财年），发布日期。\n" +
                        "2.  **致股东/利益相关者信函摘要 (Letter to Shareholders/Stakeholders - Key Highlights):** (由董事长/CEO)\n" +
                        "    a.  **对过去一年的总体回顾与评价。**\n" +
                        "    b.  **核心成就与挑战。**\n" +
                        "    c.  **对未来的展望与承诺。**\n" +
                        "    d.  **感谢致辞。**\n" +
                        "3.  **公司使命、愿景与核心价值观 (Mission, Vision, Core Values - Reiteration):** 提醒或重申公司的根本宗旨。\n" +
                        "4.  **年度经营业绩概览 (Annual Business Performance Overview):**\n" +
                        "    a.  **关键财务亮点 (Key Financial Highlights - Revenue, Profit, Growth, Margins vs. Previous Year & Targets).**\n" +
                        "    b.  **关键运营亮点 (Key Operational Highlights - Market Share, Customer Growth, Product Milestones related to 【" + escapedUserContent + "】).**\n" +
                        "    c.  **与战略目标的对比 (Performance Against Strategic Goals).**\n" +
                        "5.  **各业务板块/产品线年度回顾 (Business Segment/Product Line Annual Review):**\n" +
                        "    a.  **板块A业绩与贡献 (Segment A Performance & Contribution).**\n" +
                        "    b.  **板块A主要进展与创新 (Segment A Key Developments & Innovations).**\n" +
                        "    c.  **(其他板块类似结构)**\n" +
                        "6.  **市场环境与行业地位 (Market Environment & Industry Position):**\n" +
                        "    a.  **年度市场趋势回顾与分析。**\n" +
                        "    b.  **公司在行业中的竞争地位与变化。**\n" +
                        "7.  **战略回顾与执行成果 (Strategy Review & Execution Results):** 过去一年关键战略的实施情况及对【" + escapedUserContent + "】的影响。\n" +
                        "8.  **创新与研发投入 (Innovation & R&D Investment):** (如果重要)\n" +
                        "    a.  **年度研发投入情况。**\n" +
                        "    b.  **主要研发成果与技术突破。**\n" +
                        "9.  **企业社会责任/ESG表现 (Corporate Social Responsibility/ESG Performance):** (越来越重要)\n" +
                        "    a.  **环境责任 (Environmental Initiatives & Impact).**\n" +
                        "    b.  **社会责任 (Social Initiatives - Employees, Community, Supply Chain).**\n" +
                        "    c.  **公司治理 (Governance Practices).**\n" +
                        "10. **财务报告摘要 (Financial Report Summary - Key Statements):**\n" +
                        "    a.  **简化的利润表、资产负债表、现金流量表（图表化）。**\n" +
                        "    b.  **关键财务比率分析。**\n" +
                        "11. **风险管理 (Risk Management Overview):** 本年度主要风险识别与管理情况。\n" +
                        "12. **未来展望与战略规划 (Outlook & Strategic Plan for the Coming Year(s)):**\n" +
                        "    a.  **对未来市场环境的判断。**\n" +
                        "    b.  **下一年度的战略重点与目标（与【" + escapedUserContent + "】相关）。**\n" +
                        "    c.  **增长驱动因素与投资计划。**\n" +
                        "13. **管理层讨论与分析 (MD&A - Key Excerpts):** (如果适用，提取核心观点)\n" +
                        "14. **Q&A 环节 (Q&A Session):**\n" +
                        "15. **附注与联系方式 (Notes & Contact Information):**\n" +
                        "【内容侧重点】: 围绕【" + escapedUserContent + "】（公司上一财年表现），**全面、客观、透明地总结成绩，分析挑战，展望未来。** 强调对股东和利益相关者的价值回报和责任担当。\n" +
                        "【语言风格】: **专业、正式、严谨、数据驱动、具有公信力。** 遵循上市公司信息披露准则。图表化表达复杂数据。\n" +
                        "【章节数量】: 结构较为固定，确保全面覆盖年度报告的核心内容。";
                break;

            case "通知":
            case "announcement":
            case "notice":
                instruction = "当前PPT大纲是为了帮助起草一份关于【" + escapedUserContent + "】的正式、官方通知。**核心目标是：确保目标接收者准确、及时地理解通知的核心内容，并按要求行动（如果需要）。**\n" +
                        "【通知场景核心要素与结构建议】:\n" +
                        "1.  **通知标题 (Notice Title):** \n" +
                        "    a.  **必须清晰、醒目、直接点明通知的核心事项。** 例如：“关于【" + escapedUserContent + "】的重要通知”、“【" + escapedUserContent + "】调整的紧急通知”、“【部门名称】关于举办【" + escapedUserContent + "】活动的通知”。\n" +
                        "    b.  **包含关键词，** 使接收者能快速判断通知的相关性和重要性。\n" +
                        "    c.  PPT大纲的 `# 主题名称` 应直接反映此通知标题。\n" +
                        "2.  **文号 (Reference Number - 可选，用于正式机构或大型组织):** (如果适用) 例如：[年份]X号文，便于归档和查阅。\n" +
                        "3.  **主送机关/发布对象/范围 (To: Target Audience/Recipients):** \n" +
                        "    a.  **明确、具体地指出通知的接收方。** 例如：“各部门负责人”、“全体员工”、“2023级全体本科生”、“XX项目相关单位”等。\n" +
                        "    b.  **避免使用过于宽泛或不明确的称呼。**\n" +
                        "4.  **发文机关/发布单位/部门 (From: Issuing Authority/Department):** \n" +
                        "    a.  **明确通知的官方发布者全称。**\n" +
                        "    b.  **确保权威性和可信度。**\n" +
                        "5.  **发文日期/发布日期 (Date of Issue):** \n" +
                        "    a.  **必须准确无误，** 通常使用标准日期格式（如：XXXX年XX月XX日）。\n" +
                        "    b.  **在正式通知中，日期通常位于标题之下、正文之上，或落款处。**\n" +
                        "6.  **事由/引言/目的 (Purpose/Preamble/Objective of the Notice):**\n" +
                        "    a.  **开门见山，用一两句话简明扼要地说明发布此通知的原因、背景和核心目的。**\n" +
                        "    b.  **直接与【" + escapedUserContent + "】相关联。** 例如：“为规范【" + escapedUserContent + "】管理，现将有关事项通知如下：”\n" +
                        "7.  **正文/具体内容/事项详述 (Body/Detailed Information/Specifics of the Notice):** 这是通知的主体，必须清晰、准确、条理分明。\n" +
                        "    a.  **关键信息点一 (Key Information Point 1 regarding 【" + escapedUserContent + "】):** 清晰、准确地陈述第一个重要信息、规定、安排或要求。\n" +
                        "    b.  **关键信息点二 (Key Information Point 2 regarding 【" + escapedUserContent + "】):** (如果有多点) **强烈建议使用编号（一、二、三...）或项目符号（1., 2., 3... 或 (1), (2), (3)...）进行分条列出，** 确保逻辑清晰，易于阅读、理解和执行。\n" +
                        "    c.  **涉及时间、地点、人物、事件、原因、方式 (5W1H - Who, What, When, Where, Why, How) 的核心要素必须齐全且准确无误。**\n" +
                        "    d.  **如果涉及定义或解释，应简洁明了。**\n" +
                        "8.  **执行要求/具体办法/注意事项 (Action Required/Specific Measures/Important Considerations):** (如果适用)\n" +
                        "    a.  **明确需要接收方执行的具体事项、操作步骤、或必须遵守的规定。**\n" +
                        "    b.  **告知需要特别注意的方面，以避免误解或错误操作。**\n" +
                        "    c.  **措辞应具有指令性，如“请各单位严格执行”、“务必于X日前完成”。**\n" +
                        "9.  **截止日期/时效性/有效期 (Deadlines/Effective Date/Validity Period):** (如果适用)\n" +
                        "    a.  **明确相关事项的开始日期、截止日期、有效期限或完成时限。**\n" +
                        "    b.  **日期表述应清晰，避免歧义。**\n" +
                        "10. **相关政策依据/参考文件/附件说明 (Supporting Policies/Reference Documents/Attachments):** (如果适用)\n" +
                        "    a.  **提及通知所依据的上级文件、政策法规或内部规定（可注明文号）。**\n" +
                        "    b.  **如果通知附带其他文件（如表格、名单、详细方案），应在此处说明附件的名称和数量，** 例如“附件：1.《XXX实施细则》；2. XXX申请表示例”。\n" +
                        "11. **联系方式/咨询途径/反馈机制 (Contact Information/Inquiry Channels/Feedback Mechanism):**\n" +
                        "    a.  **提供负责解释此通知或处理相关事宜的部门/联系人及其准确的联系方式（电话、邮箱、办公地点）。**\n" +
                        "    b.  **以便接收方在有疑问时进行咨询或反馈。**\n" +
                        "12. **结束语/惯用语 (Closing Remarks/Formal Closing - Optional but Common):**\n" +
                        "    a.  **根据通知的正式程度和内容，可选用合适的结束语。** 例如：“特此通知。”、“本通知自发布之日起施行。”、“请遵照执行。”、“望周知。”\n" +
                        "13. **（落款处的）发文机关印章位置提示 (Placeholder for Official Seal of Issuing Authority):** (大纲中提示，实际通知中为盖章处)\n" +
                        "【内容侧重点】: 围绕【" + escapedUserContent + "】展开，信息传递必须**绝对准确无误、简洁明了、条理清晰、完整全面，避免任何可能产生歧义、误解或遗漏的表述。** 重点突出必须知晓、理解和执行的关键信息点。\n" +
                        "【语言风格】: **极其正式、规范、客观、直接、具有权威性和约束力。** 避免使用任何口语化、情感化、主观评价或不确定的词语。**用词要精准，符合公文写作规范。**\n" +
                        "【章节数量】: 通知通常结构严谨且内容精炼，章节数量可能远少于6个，**以最高效、最清晰地传达核心信息为首要目标。** PPT大纲的章节划分应服务于信息块的逻辑分隔和层级关系，确保结构完整。";
                break;

            case "证明":
            case "证明材料": // 更完整的中文表述
            case "certificate":
            case "proof":
            case "certification document":
                instruction = "当前PPT大纲用于协助撰写一份关于【" + escapedUserContent + "】（例如：学历证明、工作经历证明、身份证明、财产证明、事件真实性证明等）的正式证明材料。**核心目标是：以权威、可信的方式，对特定事实或情况予以确认和证实，供特定用途使用。**\n" +
                        "【证明场景核心要素与结构建议】:\n" +
                        "1.  **证明标题 (Certificate Title):** \n" +
                        "    a.  **必须居中、醒目，清晰、准确地概括证明的核心内容。** 例如：“学历证明”、“关于【张三】同志在【XX单位】工作经历的证明”、“【" + escapedUserContent + "】情况证明”、“兹证明”。\n" +
                        "    b.  **标题需精确反映证明的对象和所要证明的事项。**\n" +
                        "    c.  PPT大纲的 `# 主题名称` 应直接反映此证明标题。\n" +
                        "2.  **证明编号 (Certificate Number - 可选，用于规范管理):** (如果适用) 例如：证字[XXXX]第XXX号，便于唯一标识和追溯。\n" +
                        "3.  **致送单位/接收方 (To: Recipient Entity/Intended User - 如果明确):** (如果证明是开给特定机构的) 例如：“XX市公证处：”、“XX大学招生办公室：”。如果用途广泛，此项可省略或在正文中说明。\n" +
                        "4.  **被证明对象详细信息 (Detailed Information of the Certified Party/Subject):** 必须准确无误。\n" +
                        "    a.  **证明个人时:** \n" +
                        "        i.  **姓名 (Full Name as per official documents).**\n" +
                        "        ii. **性别 (Gender).**\n" +
                        "        iii. **出生日期 (Date of Birth).**\n" +
                        "        iv. **身份证号码/护照号码/其他有效证件号码 (ID Card No./Passport No./Other Valid Identifier).**\n" +
                        "        v.  **(根据证明内容可选) 籍贯、住址、学号/工号、职务/职称、入学/入职时间等。**\n" +
                        "    b.  **证明单位/组织时:**\n" +
                        "        i.  **单位/组织全称 (Full Legal Name of the Entity).**\n" +
                        "        ii. **统一社会信用代码/注册号 (Unified Social Credit Identifier/Registration No.).**\n" +
                        "        iii. **法定代表人/负责人姓名 (Name of Legal Representative/Responsible Person).**\n" +
                        "        iv. **单位地址 (Registered Address).**\n" +
                        "    c.  **证明特定事项/物品时:**\n" +
                        "        i.  **清晰、 unambiguous 地描述该事项的名称、性质、发生时间、地点等关键要素。**\n" +
                        "        ii. **清晰描述物品的名称、型号、序列号、特征、权属等。**\n" +
                        "5.  **证明事由/目的/用途 (Purpose/Reason for Certification/Intended Use):**\n" +
                        "    a.  **简明扼要地说明为何需要出具此份证明材料。**\n" +
                        "    b.  **以及此证明材料预计将用于何种具体目的或场合。** 例如：“为办理【XX签证】手续，特出具此证明。”，“该同志申请【XX职称评定】，需证明其【" + escapedUserContent + "】情况。”\n" +
                        "6.  **正文 - 核心证明内容/事实陈述 (Body - Core Statement of Fact/Factual Declaration):** 这是证明的核心，必须绝对清晰、准确、不容置疑。\n" +
                        "    a.  **以肯定、直接、明确的语气，陈述需要被证明的关于【" + escapedUserContent + "】的核心事实。**\n" +
                        "    b.  **措辞必须严谨、规范、无任何歧义或含糊之处。** 避免使用推测、估计、可能等不确定词语。\n" +
                        "    c.  **内容应与被证明对象信息和证明事由紧密关联。** 例如：“兹证明【张三】（身份证号：XXX）自XXXX年XX月至XXXX年XX月于我单位【XX部门】担任【XX职务】，期间表现良好，无不良记录。”\n" +
                        "    d.  **如果涉及多个需证明的事项，应分条列出，确保清晰。**\n" +
                        "7.  **正文 - 事实依据与详细说明 (Body - Factual Basis & Detailed Explanation - Use with Caution, Prioritize Brevity if Possible):** (仅在绝对必要且不影响证明权威性和简洁性的情况下适度使用)\n" +
                        "    a.  **支撑核心事实的关键时间节点、地点、具体过程或数据。**\n" +
                        "    b.  **所依据的相关文件、档案记录或内部编号 (例如：学籍档案号、劳动合同编号)。**\n" +
                        "    c.  **见证人信息及其证明效力 (极少情况下适用，且需注明见证人身份的权威性)。**\n" +
                        "    d.  **此部分应服务于增强核心证明内容的可信度，而非引入复杂细节。**\n" +
                        "8.  **证明结论 (Conclusion of Certification):**\n" +
                        "    a.  **再次以肯定语气对核心证明内容进行确认和总结。**\n" +
                        "    b.  **常用表述：“以上情况属实，特此证明。”、“情况属实，据此证明。”**\n" +
                        "9.  **证明的有效期限 (Validity Period of Certificate - If Applicable):**\n" +
                        "    a.  **如果证明具有时效性，必须明确注明。** 例如：“本证明自出具之日起【三个月/一年】内有效。”或“本证明有效期至XXXX年XX月XX日。”\n" +
                        "    b.  **如果无特定有效期，可不写或注明“长期有效”（需谨慎使用）。**\n" +
                        "10. **免责声明/限制条款/补充说明 (Disclaimer/Limitation Clause/Supplementary Notes - If Applicable & Legally Sound):** (特定情况下使用，需确保合规)\n" +
                        "    a.  **例如：“本证明仅用于XXX目的，不得用作其他用途。”**\n" +
                        "    b.  **“本证明复印件无效，需核验原件。”**\n" +
                        "    c.  **“本证明内容如有涂改，则属无效。”**\n" +
                        "11. **出具证明的机构/单位全称 (Full Legal Name of Issuing Authority/Entity):** 必须与公章名称完全一致。\n" +
                        "12. **（落款处的）出具证明机构/单位公章位置 (Placeholder for Official Seal of Issuing Authority):** (大纲中提示，实际证明中为盖章处) **公章是证明效力的核心。**\n" +
                        "13. **出具证明的日期 (Date of Issue):** \n" +
                        "    a.  **必须准确无误，** 位于公章下方或旁边。\n" +
                        "    b.  **强烈建议使用中文大写数字日期，** 以防篡改（例如：贰零贰肆年叁月拾伍日）。\n" +
                        "14. **（可选）经办人及联系方式 (Contact Person & Information of Issuer - Optional, for Verification):**\n" +
                        "    a.  **为便于接收方核实证明的真实性，可提供出具单位的经办人姓名、职务及官方联系电话或地址。**\n" +
                        "    b.  **此信息通常放在证明的页脚或不显眼处。**\n" +
                        "【内容侧重点】: 围绕【" + escapedUserContent + "】的核心事实进行陈述。内容必须**绝对真实、准确、客观、权威，逻辑严谨，不包含任何主观臆断、推测、评价性或不确定的语言。** 每一个字词都可能影响证明的效力。\n" +
                        "【语言风格】: **极其正式、规范、严谨、客观、简洁明了，具有法律文书或官方公文的特点。** 避免任何不必要的修饰性词语、口语化表达或情感色彩。**用词必须精准，符合特定证明类型的惯用表述。**\n" +
                        "【章节数量】: 证明材料的结构高度固定化和格式化，章节划分应严格遵循证明文书的通用格式和逻辑顺序，通常内容集中，章节不多。**排版应整洁、规范。**";
                break;

            case "申请":
            case "application":
            case "request":
                instruction = "当前PPT大纲是为了一份关于【" + escapedUserContent + "】的申请。\n" +
                        "【申请场景核心要素与结构建议】:\n" +
                        "1.  **申请标题 (Application Title):** 例如“关于申请【" + escapedUserContent + "】的报告/申请书”或“【个人/单位名称】的【" + escapedUserContent + "】申请”。**标题应清晰、直接。**\n" +
                        "2.  **申请接收方/审批单位 (To: Recipient Authority/Approving Body):** 明确申请提交的对象，使用尊称（如：尊敬的XX领导，XX部门审阅）。\n" +
                        "3.  **申请人基本信息 (Applicant's Basic Information):**\n" +
                        "    a.  **个人申请:** 姓名、部门、职位、联系方式。\n" +
                        "    b.  **单位申请:** 单位全称、负责人、联系人及联系方式。\n" +
                        "4.  **申请日期 (Date of Application):**\n" +
                        "5.  **申请事项概述 (Overview of Application Subject):** 开门见山，清晰、具体地说明希望申请获得的具体内容、批准的事项或解决的问题，即【" + escapedUserContent + "】。\n" +
                        "6.  **申请背景与现状分析 (Background & Current Situation Analysis):** 阐述提出此申请的背景情况，当前面临的问题或需求，为申请的合理性铺垫。\n" +
                        "7.  **申请理由与必要性阐述 (Reasons & Justification of Necessity):** 这是申请的核心。**分点、详细、有理有据地**阐述：\n" +
                        "    a.  为何必须申请【" + escapedUserContent + "】？（紧迫性、重要性）\n" +
                        "    b.  【" + escapedUserContent + "】能解决什么具体问题或带来什么关键益处？\n" +
                        "    c.  如果不批准，可能会有什么负面影响或错失什么机会？\n" +
                        "    d.  **是否有政策依据或先例支持？**\n" +
                        "8.  **具体方案/计划/措施 (Detailed Plan/Proposed Measures):** (如果申请涉及具体行动)\n" +
                        "    a.  **目标:** 通过【" + escapedUserContent + "】希望达成的具体、可衡量的目标。\n" +
                        "    b.  **实施步骤:** 清晰的行动步骤和时间规划。\n" +
                        "    c.  **资源需求:** 所需的人力、物力、财力等。\n" +
                        "    d.  **可行性分析:** 证明方案的可行性和预期效果。\n" +
                        "9.  **期望获得的批准/支持与具体诉求 (Desired Outcome/Support & Specific Requests):** 明确希望申请得到何种具体的批复、支持、资源或权限。\n" +
                        "10. **风险评估与应对 (Risk Assessment & Mitigation - if applicable):** (对于复杂申请) 简述可能风险及应对。\n" +
                        "11. **附件说明 (Supporting Documents/Appendices):** 列出随申请提交的证明材料、详细方案、预算表等补充信息，并简述其内容。\n" +
                        "12. **结束语与恳请 (Closing Remarks & Earnest Request):** 再次表达诚意，恳请批准。例如“以上申请，恳请领导/贵单位予以审议批准为盼！”\n" +
                        "13. **申请人签名/单位盖章 (Applicant's Signature / Official Seal):** (大纲中提示位置)\n" +
                        "【内容侧重点】: 重点阐述申请【" + escapedUserContent + "】的**充分理由、高度必要性和潜在价值**。提供充足的论据和（可能的）证据支持。目标是**说服审批人，使其相信批准此申请是合理且有益的。**\n" +
                        "【语言风格】: **正式、诚恳、礼貌、条理清晰、逻辑严密、具有说服力。** 语气要谦逊但理由要坚定。避免命令式或过于主观臆断的语气。";
                break;

            case "计划": // General plan, can be a parent to work plan, project plan if not specified:
            case "plan":
                instruction = "当前PPT大纲是为了一份关于【" + escapedUserContent + "】的计划。\n" +
                        "【计划场景核心要素与结构建议】:\n" +
                        "1.  **计划标题 (Plan Title):** 清晰概括计划的核心内容，例如“关于【" + escapedUserContent + "】的【年度/专项/阶段性】计划”。\n" +
                        "2.  **计划制定者与日期 (Planner & Date):** 明确计划的制定单位/个人和制定日期。\n" +
                        "3.  **引言/计划背景与依据 (Introduction/Background & Rationale):**\n" +
                        "    a.  **现状分析:** 当前在【" + escapedUserContent + "】方面的情况、存在的问题或面临的挑战。\n" +
                        "    b.  **机遇识别:** 制定此计划所基于的机遇或有利条件。\n" +
                        "    c.  **必要性与重要性:** 为何需要制定并实施此计划。\n" +
                        "4.  **指导思想与基本原则 (Guiding Ideology & Basic Principles):** (如果适用，尤其对于战略性计划) 阐述计划遵循的总体方向和核心原则。\n" +
                        "5.  **计划目标 (Objectives/Goals - SMART Principle):**\n" +
                        "    a.  **总体目标:** 对【" + escapedUserContent + "】在计划期内希望达成的宏观成果的描述。\n" +
                        "    b.  **具体目标/分项目标:** 将总体目标分解为若干具体的、可衡量的、可实现的、相关的、有时限的子目标。\n" +
                        "6.  **主要任务与策略举措 (Key Tasks & Strategic Initiatives):**\n" +
                        "    a.  **任务分解:** 为实现每个具体目标，需要完成哪些关键任务和活动。\n" +
                        "    b.  **策略与方法:** 针对每项任务，将采取何种策略、方法或技术路径。\n" +
                        "7.  **实施步骤与时间规划 (Implementation Steps & Timeline/Gantt Chart):**\n" +
                        "    a.  **阶段划分:** (如果适用) 将计划分为几个主要阶段。\n" +
                        "    b.  **详细步骤:** 每个任务或阶段的具体执行步骤。\n" +
                        "    c.  **时间节点与里程碑:** 明确各项工作的开始时间、完成时间及关键的检查点。\n" +
                        "8.  **资源需求与保障 (Resource Requirements & Guarantees):**\n" +
                        "    a.  **人力资源:** 所需人员数量、技能要求、团队组建。\n" +
                        "    b.  **物资与设备:** 所需的物资、设备、场地等。\n" +
                        "    c.  **财务预算:** 各项活动所需的资金估算及资金来源。\n" +
                        "    d.  **技术支持:** 所需的技术平台或外部支持。\n" +
                        "    e.  **政策保障:** (如果适用) 需要的政策支持。\n" +
                        "9.  **组织领导与责任分工 (Organization & Responsibilities):**\n" +
                        "    a.  **领导小组/项目团队:** (如果适用) 明确计划的领导和管理架构。\n" +
                        "    b.  **责任单位/负责人:** 清晰界定各项任务的责任部门和具体负责人。\n" +
                        "10. **风险评估与应对预案 (Risk Assessment & Contingency Plan):** 分析计划实施过程中可能遇到的内外部风险（如市场变化、技术难题、资源短缺等），并提出相应的预防和应对策略。\n" +
                        "11. **监督检查与效果评估 (Monitoring, Inspection & Evaluation):**\n" +
                        "    a.  **进度跟踪机制:** 如何监控计划的执行进度。\n" +
                        "    b.  **评估标准与方法:** 用什么指标和方法来衡量计划的实施效果和目标达成情况。\n" +
                        "    c.  **奖惩机制:** (如果适用)\n" +
                        "12. **预期成果与效益分析 (Expected Outcomes & Benefit Analysis):** 详细描述计划成功实施后，在【" + escapedUserContent + "】方面预期的具体成果、社会效益、经济效益等。\n" +
                        "【内容侧重点】: 围绕【" + escapedUserContent + "】的实现，强调计划的**前瞻性、系统性、可行性、操作性和可衡量性。** 目标要明确，任务要具体，步骤要清晰，责任要落实，资源要保障。\n" +
                        "【语言风格】: 清晰、具体、务实、有条理、**具有指导性和权威性。** 多用肯定和祈使句式。";
                break;

            case "活动策划":
            case "event plan":
                instruction = "当前PPT大纲用于策划一个主题为【" + escapedUserContent + "】的活动。\n" +
                        "【活动策划场景核心要素与结构建议】:\n" +
                        "1.  **活动标题/主题 (Event Title/Theme):** 鲜明突出活动核心【" + escapedUserContent + "】，**应具有吸引力、创意性，易于传播。** PPT大纲的 `# 主题名称` 应反映此。\n" +
                        "2.  **执行摘要/活动概述 (Executive Summary/Event Overview):** (1页) 简述活动的核心创意、目标、目标人群、时间地点、主要亮点、预期效果和总预算。\n" +
                        "3.  **活动背景与目的/意义 (Event Background & Objectives/Significance):**\n" +
                        "    a.  **市场/社会背景:** 为何举办此活动，当前有何契机或需求。\n" +
                        "    b.  **活动目标 (SMART):** 期望通过活动达成什么具体目标（如品牌曝光、用户增长、销售转化、公益宣传、团队建设等，均与【" + escapedUserContent + "】相关）。\n" +
                        "    c.  **活动意义:** 此活动对参与者、组织方或社会的价值所在。\n" +
                        "4.  **目标参与人群分析 (Target Audience Analysis):**\n" +
                        "    a.  **人群画像:** 年龄、性别、职业、兴趣、消费习惯等。\n" +
                        "    b.  **人群需求与痛点:** 他们为何会对此活动感兴趣。\n" +
                        "5.  **活动核心创意与亮点 (Core Creative Concept & Highlights):** 活动的独特卖点是什么？最吸引人的环节或特色是什么？\n" +
                        "6.  **活动基本信息 (Basic Event Information):**\n" +
                        "    a.  **活动时间:** 年月日，具体开始和结束时间。\n" +
                        "    b.  **活动地点:** 详细地址，场地描述与选择理由。\n" +
                        "    c.  **活动规模:** 预计参与人数。\n" +
                        "    d.  **主办/承办/协办单位:** (如果适用)\n" +
                        "7.  **活动流程与详细内容安排 (Event Agenda/Program Details):** 这是核心。\n" +
                        "    a.  **时间轴/环节表:** 按时间顺序列出活动的每一个环节。\n" +
                        "    b.  **各环节内容描述:** 每个环节的具体内容、形式、时长、负责人、所需物料、互动方式等。确保与【" + escapedUserContent + "】主题紧密结合，富有吸引力和参与感。\n" +
                        "    c.  **嘉宾/表演者安排:** (如果适用)\n" +
                        "8.  **宣传推广方案 (Promotion & Marketing Plan):**\n" +
                        "    a.  **宣传口号/主视觉:** (大纲中描述)\n" +
                        "    b.  **宣传渠道:** 线上（社交媒体、官网、邮件）、线下（海报、传单、合作推广）。\n" +
                        "    c.  **宣传时间节点与内容规划:** 不同阶段的宣传重点。\n" +
                        "    d.  **媒体合作计划:** (如果适用)\n" +
                        "9.  **物料与资源准备 (Materials & Resource Preparation):**\n" +
                        "    a.  **场地布置与搭建方案:** (大纲中描述设计理念和主要元素)\n" +
                        "    b.  **设备清单:** 音响、灯光、投影、签到系统等。\n" +
                        "    c.  **宣传物料设计与制作:** 海报、手册、礼品、邀请函等。\n" +
                        "    d.  **餐饮/住宿安排:** (如果适用)\n" +
                        "10. **人员组织与分工 (Staffing & Responsibilities):**\n" +
                        "    a.  **项目团队架构:** 主要负责人和核心成员。\n" +
                        "    b.  **各岗位职责:** 策划、执行、宣传、接待、安保、后勤、志愿者等。\n" +
                        "    c.  **人员培训计划:** (如果适用)\n" +
                        "11. **预算方案与资金来源 (Budget Plan & Funding Sources):**\n" +
                        "    a.  **详细费用 breakdown:** 各项支出的预估（场地、物料、人员、宣传等）。\n" +
                        "    b.  **总预算与成本控制:**\n" +
                        "    c.  **资金来源:** 自筹、赞助、门票等。\n" +
                        "12. **风险评估与应急预案 (Risk Assessment & Contingency Plan):** 针对天气、安全、设备故障、嘉宾缺席、参与人数不足等可能发生的意外情况的应对措施。\n" +
                        "13. **活动效果评估标准与方法 (Event Evaluation Metrics & Methods):** 如何衡量活动是否成功，例如参与人数、满意度调查、媒体曝光量、社交媒体讨论度、后续转化率等。\n" +
                        "14. **赞助商回报方案 (Sponsor ROI Plan - if applicable):**\n" +
                        "【内容侧重点】: 围绕【" + escapedUserContent + "】主题，强调活动的**创意性、互动性、体验感、可行性和细节的周全性。** 流程设计、用户体验和风险控制是关键。\n" +
                        "【语言风格】: 生动、富有吸引力、条理清晰、注重细节。**在策划阶段应具有说服力，在执行手册中应具有指导性。**";
                break;

            case "教学教案":
            case "lesson plan":
            case "teaching":
                instruction = "当前PPT大纲用于协助设计一份关于【" + escapedUserContent + "】（例如：一节课、一个单元、一个主题活动）的详细教学教案。**核心目标是：指导教师有效组织和实施教学活动，帮助学生达成预设的学习目标，并促进学生的全面发展。**\n" +
                        "【教学教案场景核心要素与结构建议】:\n" +
                        "1.  **教案头信息 (Lesson Plan Header Information):**\n" +
                        "    a.  **课题/单元/主题名称 (Lesson/Unit/Theme Title):** 清晰、准确地反映【" + escapedUserContent + "】的教学内容。\n" +
                        "    b.  **授课教师 (Teacher's Name):**\n" +
                        "    c.  **授课班级/年级/学段 (Grade Level/Class):** 明确教学对象。\n" +
                        "    d.  **学科领域 (Subject Area):** 例如：语文、数学、物理、历史、美术等。\n" +
                        "    e.  **课时安排 (Number of Periods/Duration):** 例如：1课时（45分钟），2课时等。\n" +
                        "    f.  **授课日期 (Date of Lesson):** (可选，或在实际使用时填写)\n" +
                        "    g.  **教材版本及章节 (Textbook Version & Chapter/Section):** (如果基于特定教材)\n" +
                        "    h.  **课型 (Type of Lesson):** 例如：新授课、复习课、实验课、活动课、研讨课等。\n" +
                        "2.  **学情分析 (Student Analysis/Learner Profile):**\n" +
                        "    a.  **学生已有知识与技能基础 (Students' Prior Knowledge & Skills related to 【" + escapedUserContent + "】).**\n" +
                        "    b.  **学生的学习特点、兴趣、需求及可能遇到的困难 (Learning Styles, Interests, Needs, and Potential Difficulties).**\n" +
                        "    c.  **本课教学内容与学生认知发展水平的匹配度。**\n" +
                        "3.  **教学目标 (Learning Objectives/Goals - SMART & Bloom's Taxonomy informed):** 必须清晰、具体、可观察、可测量。\n" +
                        "    a.  **知识与技能目标 (Knowledge & Skills Objectives):** 学生通过本课学习，应理解、掌握、运用的关于【" + escapedUserContent + "】的核心知识点和具体技能。**使用行为动词描述，** 例如：能够识别...、能够解释...、能够运用...解决问题、能够独立完成...实验操作。\n" +
                        "    b.  **过程与方法目标 (Process & Methods Objectives):** 学生在本课学习过程中，应经历的学习过程、体验的探究方法、培养的思维能力。例如：通过小组合作探究...、学会运用...研究方法、培养...的观察和分析能力。\n" +
                        "    c.  **情感态度与价值观目标 (Affective/Attitudinal & Values Objectives):** 学生在本课学习中，应形成或发展的积极情感、正确态度和核心价值观。例如：激发对【" + escapedUserContent + "】的学习兴趣、培养...的科学探究精神、增强...的社会责任感、认同...的文化价值。\n" +
                        "4.  **教学重点 (Key Teaching Points/Focus Areas):**\n" +
                        "    a.  **明确本课时【" + escapedUserContent + "】相关的、必须让学生深刻理解和熟练掌握的核心概念、原理、技能或方法。**\n" +
                        "    b.  **是教学目标的核心体现，是教师在教学过程中需要着重强调和讲解的内容。**\n" +
                        "5.  **教学难点 (Difficult Points/Potential Challenges):**\n" +
                        "    a.  **明确本课时【" + escapedUserContent + "】相关的、学生在理解和掌握过程中最容易产生困惑、发生错误或感到困难的内容。**\n" +
                        "    b.  **需要教师运用恰当的教学策略和方法来帮助学生突破。**\n" +
                        "6.  **教学资源与准备 (Teaching Resources & Preparation):**\n" +
                        "    a.  **教师准备 (Teacher's Preparation):**\n" +
                        "        i.  **教具:** PPT课件、挂图、模型、实物、实验仪器药品、多媒体资源（音频、视频片段）等。\n" +
                        "        ii. **教学参考资料:** 教材、教参、相关文献、网络资源等。\n" +
                        "    b.  **学生准备 (Students' Preparation):**\n" +
                        "        i.  **学具:** 课本、练习册、文具、实验报告单、特定材料（如手工课材料）等。\n" +
                        "        ii. **课前预习要求:** (如果适用) 阅读特定章节、查找相关资料、完成预习单等。\n" +
                        "7.  **教学方法与策略 (Teaching Methods & Strategies):**\n" +
                        "    a.  **本课时主要采用的教学方法:** 例如：讲授法、讨论法、启发式教学、案例教学法、项目式学习(PBL)、探究式学习、合作学习、游戏化教学等。\n" +
                        "    b.  **针对教学重难点所设计的具体教学策略。**\n" +
                        "8.  **教学过程/环节设计 (Teaching Procedures/Lesson Steps - Detailed & Sequential):** 这是教案的核心，应详细描述每个环节的活动、时间分配、师生互动和预期效果。\n" +
                        "    a.  **（一）课堂导入/创设情境 (Introduction/Engagement - Approx. X minutes):**\n" +
                        "        i.  **活动设计:** 如何激发学生兴趣、集中注意力、回顾旧知、自然引入【" + escapedUserContent + "】新课主题（例如：提问、故事、视频、游戏、实物演示）。\n" +
                        "        ii. **设计意图:** 说明此导入环节的目的。\n" +
                        "    b.  **（二）新知探究/讲授与互动 (New Knowledge Exploration/Presentation & Interaction - Approx. Y minutes):** (可根据内容多少分为多个子环节)\n" +
                        "        i.  **活动设计:** 围绕【" + escapedUserContent + "】的核心知识点和技能点，教师如何引导学生进行观察、思考、讨论、实验、阅读、分析、归纳、总结；教师如何进行清晰的讲解、演示、示范。\n" +
                        "        ii. **学生活动:** 学生在此环节需要做什么（听、看、说、做、思）。\n" +
                        "        iii. **关键提问与引导语设计:**\n" +
                        "        iv. **设计意图:** 如何帮助学生理解重点、突破难点。\n" +
                        "    c.  **（三）巩固练习/技能应用 (Practice & Application - Approx. Z minutes):**\n" +
                        "        i.  **活动设计:** 设计与【" + escapedUserContent + "】相关的、形式多样的练习题（口头、书面）、小组合作任务、角色扮演、情境模拟、实际操作、作品创作等。\n" +
                        "        ii. **练习的层次性与针对性：** 是否兼顾不同水平的学生。\n" +
                        "        iii. **设计意图:** 检验学习效果，强化知识技能，培养应用能力。\n" +
                        "    d.  **（四）课堂小结/归纳提升 (Summary & Conclusion - Approx. W minutes):**\n" +
                        "        i.  **活动设计:** 引导学生自主回顾本节课【" + escapedUserContent + "】的主要内容、核心概念、关键方法和学习收获；或由教师进行系统梳理和总结提升。\n" +
                        "        ii. **知识结构化呈现:** (如思维导图、表格)\n" +
                        "        iii. **设计意图:** 帮助学生形成完整的知识体系，强化记忆。\n" +
                        "    e.  **（五）作业布置/拓展延伸 (Homework Assignment & Extension Activities - Approx. V minutes):**\n" +
                        "        i.  **作业内容与形式:** 布置与【" + escapedUserContent + "】相关的、适量的、分层的巩固性作业或富有挑战性的拓展性、探究性作业（如小调查、小制作、主题阅读、撰写报告）。\n" +
                        "        ii. **作业要求与评价提示:**\n" +
                        "        iii. **设计意图:** 巩固所学，延伸学习，培养自主学习能力。\n" +
                        "9.  **板书设计 (Blackboard/Whiteboard Design - If Applicable):**\n" +
                        "    a.  **规划黑板/白板上呈现【" + escapedUserContent + "】关键知识点、核心概念、逻辑结构图、重要公式、典型例题的布局。**\n" +
                        "    b.  **力求简洁、美观、条理清晰、重点突出，辅助教学。**\n" +
                        "10. **教学评价设计 (Assessment Design - Formative & Summative - Optional but Recommended):**\n" +
                        "    a.  **形成性评价:** 在教学过程中如何观察、记录、评估学生的学习表现（如课堂提问回答、小组讨论参与、练习完成情况）。\n" +
                        "    b.  **总结性评价:** (如果适用) 课后小测验、作品评价标准等。\n" +
                        "11. **教学反思 (Teaching Reflection - Post-Lesson Activity, but can be pre-planned for focus):**\n" +
                        "    a.  **预设反思点:** 本课教学目标达成度如何？教学重难点是否有效突破？教学环节设计是否合理？时间分配是否恰当？学生参与度如何？哪些地方成功，哪些地方需要改进？有何意外收获或突发状况及应对？\n" +
                        "    b.  **对未来教学的启示。**\n" +
                        "【内容侧重点】: **以学生为中心 (student-centered)，以学习目标为导向 (objective-driven)。** 确保所有教学活动、内容选择、方法运用都紧密服务于【" + escapedUserContent + "】的教学目标。**注重启发性、互动性、实践性和过程性。** 充分考虑学生的认知规律和个体差异。\n" +
                        "【语言风格】: **教师指导语和学生活动指令必须清晰、准确、简洁、生动、易懂、具有启发性和引导性。** 教案本身的撰写语言应专业、规范、条理清晰。**避免含糊不清或过于理论化的表述。**\n" +
                        "【章节数量】: 教学教案的结构相对固定和完整，章节划分应严格遵循教学设计的逻辑流程，确保每个环节都得到充分考虑和设计。**细节决定成败。**";
                break;


            case "项目汇报":
            case "project report":
                instruction = "当前PPT大纲用于进行一次关于【" + escapedUserContent + "】项目的汇报。\n" +
                        "【项目汇报场景核心要素与结构建议】:\n" +
                        "1.  **汇报标题 (Report Title):** 项目名称（【" + escapedUserContent + "】）及汇报类型（如：项目启动汇报、中期进展汇报、阶段性成果汇报、项目总结/验收汇报）。\n" +
                        "2.  **汇报人与日期 (Presenter & Date):**\n" +
                        "3.  **议程/汇报大纲 (Agenda/Outline):** 简要列出本次汇报的主要内容模块。\n" +
                        "4.  **项目背景与目标回顾 (Project Background & Objectives Recap):**\n" +
                        "    a.  **项目立项背景/驱动因素:** 为何启动此项目。\n" +
                        "    b.  **项目核心目标与预期价值 (SMART goals):** 项目旨在解决什么问题，达成什么具体、可衡量的目标，创造什么价值。\n" +
                        "    c.  **项目范围与关键交付物:** 清晰界定项目的工作范围和最终需要交付的成果。\n" +
                        "5.  **项目团队与组织架构 (Project Team & Organization - for kick-off or if changed):** 介绍核心团队成员及其职责。\n" +
                        "6.  **项目计划与时间轴回顾 (Project Plan & Timeline Recap - for progress reports):** 简要回顾总体计划和关键里程碑。\n" +
                        "7.  **当前进展/本阶段完成情况 (Current Progress / Accomplishments This Period):** (核心部分)\n" +
                        "    a.  **已完成的主要任务/里程碑:** 对照计划，详细说明已完成的工作内容和达成的关键节点。\n" +
                        "    b.  **关键数据与成果展示:** 用量化的数据、图表、实例或演示来展示【" + escapedUserContent + "】相关的实际成果。\n" +
                        "    c.  **与计划的对比分析 (Actual vs. Plan):** (如果适用) 分析实际进度与计划进度的差异，解释原因。\n" +
                        "8.  **预算执行与资源使用情况 (Budget Execution & Resource Utilization):**\n" +
                        "    a.  **已花费预算 vs. 计划预算:** 清晰展示成本控制情况。\n" +
                        "    b.  **资源（人力、物力）使用效率分析:**\n" +
                        "9.  **质量保证情况 (Quality Assurance Status):** (如果适用) 项目质量控制措施及当前质量状况。\n" +
                        "10. **遇到的主要问题、挑战与障碍 (Key Issues, Challenges & Obstacles Encountered):** 坦诚说明项目执行中遇到的技术、资源、沟通、外部环境等方面的困难。\n" +
                        "11. **已采取/建议的解决方案与应对措施 (Solutions Implemented/Proposed & Mitigation Actions):** 针对上述问题，已经采取了哪些措施，效果如何？或者，需要哪些支持和决策来解决现有问题？\n" +
                        "12. **风险更新与管理 (Risk Update & Management):**\n" +
                        "    a.  **已识别风险的当前状态:** (已发生、已缓解、未变化、新出现)\n" +
                        "    b.  **新出现的风险及其影响评估:**\n" +
                        "    c.  **风险应对策略的有效性:**\n" +
                        "13. **下一阶段工作计划与重点 (Next Steps / Upcoming Plans & Priorities):**\n" +
                        "    a.  **未来X周/月的主要任务和目标:**\n" +
                        "    b.  **关键里程碑节点:**\n" +
                        "    c.  **资源需求与协调事项:**\n" +
                        "14. **项目总结与经验教训 (Project Summary & Lessons Learned - for final report):**\n" +
                        "    a.  **目标达成情况评估:** 全面评估项目目标是否达成。\n" +
                        "    b.  **主要成果与价值回顾:**\n" +
                        "    c.  **成功经验总结:** 哪些方面做得好，值得推广。\n" +
                        "    d.  **不足与教训反思:** 哪些方面存在问题，未来如何改进。\n" +
                        "15. **请求支持/决策事项 (Request for Support / Items for Decision):** (如果适用) 明确需要上级或相关方提供的帮助或需要做出的决策。\n" +
                        "16. **Q&A / 讨论 (Q&A / Discussion):** 预留问答和讨论时间。\n" +
                        "【内容侧重点】: 围绕【" + escapedUserContent + "】项目的实际情况，**以数据和事实为依据，客观、清晰、透明地展示进展、成果、问题和计划。** 目标导向，问题导向，解决方案导向。\n" +
                        "【语言风格】: 专业、客观、精炼、条理清晰、**实事求是**。根据汇报对象调整详细程度和技术深度。";
                break;

            case "解决方案":
            case "solution proposal":
                instruction = "当前PPT大纲用于提出一个针对【" + escapedUserContent + "】相关问题的解决方案。\n" +
                        "【解决方案场景核心要素与结构建议】:\n" +
                        "1.  **方案标题 (Solution Title):** 清晰点明是针对【" + escapedUserContent + "】的解决方案，可加入关键词体现方案特点（如：创新的、高效的、低成本的）。\n" +
                        "2.  **执行摘要/方案概览 (Executive Summary/Solution Overview):** (1-2页) 简述待解决的核心问题、所提方案的核心思想、主要构成、关键优势、预期效益和大致投入。\n" +
                        "3.  **问题陈述与深度分析 (Problem Statement & In-depth Analysis):**\n" +
                        "    a.  **当前面临的核心问题/痛点:** 清晰定义需要解决的与【" + escapedUserContent + "】相关的具体问题。\n" +
                        "    b.  **问题产生的根本原因分析 (Root Cause Analysis):**\n" +
                        "    c.  **问题的影响范围与严重性/紧迫性:** 该问题带来了哪些负面影响，解决的紧迫程度如何。\n" +
                        "    d.  **现有解决方案的不足 (Limitations of Current Approaches - if any):**\n" +
                        "4.  **解决方案的总体思路与框架 (Overall Approach & Framework of the Solution):** 宏观层面介绍解决方案的指导思想、核心逻辑和整体架构。\n" +
                        "5.  **详细方案阐述 (Detailed Solution Explanation):** 这是核心。\n" +
                        "    a.  **方案模块一：[模块名称与功能] (Solution Module 1: [Name & Functionality]):** 详细描述该模块如何运作，解决哪部分问题。\n" +
                        "    b.  **方案模块二：[模块名称与功能] (Solution Module 2: [Name & Functionality]):** (可有多个模块) **使用图示、流程图辅助说明。**\n" +
                        "    c.  **技术原理/方法论支撑 (Underlying Technology/Methodology):** (如果适用) 解释方案所依赖的关键技术或方法。\n" +
                        "    d.  **创新点与独特性 (Innovation & Uniqueness):** 方案的创新之处在哪里。\n" +
                        "6.  **方案的优势与核心价值 (Benefits & Core Value Proposition):**\n" +
                        "    a.  **解决问题的有效性:** 如何高效解决【" + escapedUserContent + "】相关问题。\n" +
                        "    b.  **带来的具体效益:** 例如提高效率X%、降低成本Y%、提升用户满意度Z点等（尽可能量化）。\n" +
                        "    c.  **相比其他方案的比较优势:** (如果适用)\n" +
                        "7.  **实施计划与路线图 (Implementation Plan & Roadmap):**\n" +
                        "    a.  **阶段划分与目标:** 将方案实施分为几个阶段，各阶段的目标是什么。\n" +
                        "    b.  **关键任务与时间表:** 各阶段的主要任务和预计完成时间。\n" +
                        "    c.  **所需资源与团队配置:** 实施方案所需的人力、技术、资金等。\n" +
                        "8.  **预期效果与衡量指标 (Expected Outcomes & Success Metrics/KPIs):** 描述方案成功实施后预期的具体成果，并设定可衡量的成功标准。\n" +
                        "9.  **成本效益分析/投资回报分析 (Cost-Benefit Analysis / ROI Analysis):** (如果适用，尤其对商业方案) 详细分析投入与产出。\n" +
                        "10. **风险评估与应对策略 (Risk Assessment & Mitigation Strategies):** 分析方案实施可能面临的技术、市场、执行等风险及应对措施。\n" +
                        "11. **成功案例/概念验证 (Success Stories / Proof of Concept - if available):** (如果已有类似成功经验或PoC结果)\n" +
                        "12. **总结与行动建议/下一步 (Conclusion & Recommendations/Next Steps):** 总结方案核心价值，并提出下一步行动建议（如请求批准、启动试点、寻求合作等）。\n" +
                        "【内容侧重点】: 围绕【" + escapedUserContent + "】的问题，重点突出方案的**创新性、可行性、有效性、可衡量性和价值回报。** 逻辑严密，论证充分，数据支撑。\n" +
                        "【语言风格】: 专业、清晰、**有说服力、逻辑性强、自信。** 根据受众调整技术细节的深度。";
                break;

            case "教学课件":
            case "courseware":
            case "teaching slides": // More specific alias
            case "representational":
                instruction = "当前PPT大纲是为【" + escapedUserContent + "】（例如：一节课的知识点、一个讲座主题）的教学演示课件（通常为PPT/Slides）设计的。**核心目标是：作为教师教学的视觉辅助工具，清晰、生动、有条理地向学生呈现核心教学内容，激发学习兴趣，促进理解和记忆。**\n" +
                        "【教学课件场景核心要素与结构建议】:\n" +
                        "1.  **课件封面页 (Cover/Title Slide):**\n" +
                        "    a.  **醒目的课程/讲座标题:** 清晰概括【" + escapedUserContent + "】的核心内容。\n" +
                        "    b.  **讲师姓名/机构Logo (可选)。**\n" +
                        "    c.  **日期/版本号 (可选)。**\n" +
                        "    d.  **与主题相关的、吸引人的背景图片/设计元素。**\n" +
                        "2.  **开场白/议程/学习目标预览 (Opening/Agenda/Learning Objectives Preview Slide):**\n" +
                        "    a.  **简短的欢迎语或开场白。**\n" +
                        "    b.  **清晰列出本次课件将涵盖的主要内容模块/议程 (Roadmap for students)。**\n" +
                        "    c.  **简明扼要地呈现核心学习目标 (What students will know or be able to do after this session).**\n" +
                        "3.  **引入/回顾/情境创设 (Introduction/Review/Context Setting Slide(s)):**\n" +
                        "    a.  **目的:** 激发学生学习兴趣，集中注意力，回顾与【" + escapedUserContent + "】相关的先前知识，或创设与主题相关的问题情境。\n" +
                        "    b.  **呈现方式:** 可以是引人入胜的问题、相关的短故事/案例、有趣的图片/视频片段、或对前一课知识的简要回顾。\n" +
                        "4.  **主要内容模块/知识点讲解 (Main Content Modules/Key Concept Explanation Slides):** (每个核心知识点或技能点可构成一个或多个 `## 目录章节`，每个章节下有多个 `### 页面标题` 对应具体的幻灯片内容点)\n" +
                        "    a.  **模块标题页 (Module Title Slide - Optional):** 清晰标示新模块的开始。\n" +
                        "    b.  **核心概念/定义呈现 (Key Concept/Definition Presentation):** 使用简洁的文字、清晰的图示或定义框突出显示。\n" +
                        "    c.  **原理/机制/过程图解 (Principle/Mechanism/Process Illustration):** **强烈建议使用流程图、示意图、动画（大纲中描述动画效果）或分步图解** 来解释复杂内容。\n" +
                        "    d.  **实例/案例分析与展示 (Examples/Case Study Presentation):** 使用具体、相关的例子帮助学生理解抽象概念或理论在【" + escapedUserContent + "】中的应用。\n" +
                        "    e.  **关键数据/图表可视化 (Key Data/Chart Visualization):** 将重要数据通过柱状图、折线图、饼图等形式直观呈现，并配以简要解读。\n" +
                        "    f.  **重要公式/定理/法则展示 (Important Formulas/Theorems/Rules Presentation):** 清晰列出，并可配以简单推导或应用示例。\n" +
                        "    g.  **操作演示/步骤分解 (Demonstration/Step-by-Step Breakdown - if applicable):** (如果涉及技能操作) 使用截图、视频片段或清晰的步骤图示。\n" +
                        "    h.  **互动性提问/思考点 (Interactive Questions/Points for Reflection):** 在关键内容讲解后，设计简短的思考题或讨论点，引导学生主动参与（问题可以直接显示在幻灯片上）。\n" +
                        "5.  **模块小结/过渡 (Module Summary/Transition Slides - Optional):** 在较长的内容模块结束后，进行简要小结，并自然过渡到下一模块。\n" +
                        "6.  **知识点/技能点总结回顾 (Key Takeaways/Overall Summary Slide(s)):**\n" +
                        "    a.  **以列表、思维导图或关键词云等形式，** 系统概括本次课件【" + escapedUserContent + "】的核心内容、重要结论或关键技能。\n" +
                        "    b.  **帮助学生巩固记忆，形成整体认知。**\n" +
                        "7.  **练习/应用/思考题 (Practice/Application/Further Thinking Exercises - Optional, can be brief):**\n" +
                        "    a.  **提供1-2个与【" + escapedUserContent + "】核心内容相关的简短练习题、应用场景思考或开放性问题，** 供学生课上或课后巩固。\n" +
                        "8.  **问答与讨论环节提示 (Q&A / Discussion Prompt Slide):** (如果计划在课件演示过程中或结束后进行)\n" +
                        "    a.  **明确提示进入问答或讨论环节。**\n" +
                        "    b.  **可预留空白区域或引导性问题。**\n" +
                        "9.  **结束页/感谢与展望 (Closing/Thank You & Outlook Slide):**\n" +
                        "    a.  **感谢聆听/参与。**\n" +
                        "    b.  **(可选) 简要展望后续学习内容或相关领域的发展。**\n" +
                        "    c.  **(可选) 提供联系方式或进一步学习的资源链接。**\n" +
                        "【内容侧重点】: 围绕【" + escapedUserContent + "】的核心知识与技能传递，**高度注重内容的逻辑性、条理性和视觉化呈现。** 课件应**简洁明了，突出重点，避免信息过载。** 鼓励通过设计引发学生思考和参与。\n" +
                        "【语言风格】: **面向学生的语言：清晰、简洁、准确、易于理解、生动有趣（如果适合主题）。** 根据授课对象的年龄和专业背景调整语言的正式程度和专业术语的使用（对术语进行解释）。**PPT上的文字应为精炼的要点，而非大段讲稿。**\n" +
                        "【视觉设计提示（大纲中应有所体现）】: **强调图文并茂，色彩搭配协调，字体清晰易读，动画和过渡效果适度且服务于内容。** 思考每张幻灯片的核心视觉元素和信息层级。\n" +
                        "【章节数量】: 课件的章节（对应PPT的Section或主要内容块）应根据知识点的逻辑结构划分，幻灯片数量（对应`###`和`####`）则需考虑每张幻灯片的信息承载量和讲解时间。";
                break;

            case "工作总结":
            case "work summary":
            case "workreport": // Keep this as it's a common term
            case "performance summary": // Another relevant alias
                instruction = "当前PPT大纲用于撰写一份关于【" + escapedUserContent + "】（例如：个人年度/季度工作、某项目完成情况、某专项任务总结）的工作总结。**核心目标是：全面回顾和评估指定时期/任务的工作表现，提炼成功经验与不足教训，为未来工作提供借鉴和改进方向。**\n" +
                        "【工作总结场景核心要素与结构建议】:\n" +
                        "1.  **总结标题 (Summary Title):** \n" +
                        "    a.  **清晰、准确地概括总结的范围和核心内容。** 例如：“【张三】2023年度【" + escapedUserContent + "】工作总结”、“关于【XX项目】第一阶段（" + escapedUserContent + "）的工作总结报告”、“【XX部门】关于提升【" + escapedUserContent + "】效率的专项工作总结”。\n" +
                        "    b.  PPT大纲的 `# 主题名称` 应直接反映此标题。\n" +
                        "2.  **引言/基本情况概述 (Introduction/Overview of Basic Situation):**\n" +
                        "    a.  **总结的时间范围、所负责的工作/项目/任务的核心内容（即【" + escapedUserContent + "】）。**\n" +
                        "    b.  **简要回顾期初设定的主要工作目标或预期成果。**\n" +
                        "    c.  **(可选) 概述本期工作的整体背景或面临的主要环境。**\n" +
                        "3.  **主要工作完成情况与业绩成果 (Key Work Accomplishments & Performance Results):** (这是核心和重点)\n" +
                        "    a.  **围绕【" + escapedUserContent + "】及核心职责，分点、条理清晰地列出本总结期内完成的主要工作任务和项目。**\n" +
                        "    b.  **对每项重要工作/任务，运用STAR原则 (Situation, Task, Action, Result) 或类似逻辑进行具体阐述：**\n" +
                        "        i.  **背景/任务 (Situation/Task):** 当时面临什么情况或接受了什么任务？\n" +
                        "        ii. **行动/措施 (Action):** 针对情况/任务，你（或团队）采取了哪些关键行动和具体措施？\n" +
                        "        iii. **结果/成效 (Result):** 这些行动带来了哪些具体、可衡量的成果和积极影响？**尽可能使用数据、指标、实例或客户/领导的正面评价来证明。** (例如：完成了X%，提升了Y%，节省了Z成本，获得了N个客户好评)。\n" +
                        "    c.  **关键绩效指标 (KPIs) 达成情况分析:** (如果适用) 对照期初目标，详细说明各项KPI的实际完成情况，并对超额完成或未达标的情况进行简要原因分析。\n" +
                        "4.  **工作亮点、创新与突破 (Highlights, Innovations & Breakthroughs):** (如果适用)\n" +
                        "    a.  **突出在【" + escapedUserContent + "】工作中具有创新性、示范性、或特别值得称道的方面。**\n" +
                        "    b.  **例如：引入新方法/技术、优化关键流程、解决疑难问题、开拓新领域、获得重要奖项/认可等。**\n" +
                        "    c.  **简述这些亮点带来的具体价值或影响。**\n" +
                        "5.  **存在的主要问题、挑战与不足反思 (Key Problems, Challenges Encountered & Self-Reflection on Shortcomings):**\n" +
                        "    a.  **客观、坦诚地分析在【" + escapedUserContent + "】工作中遇到的主要困难、障碍和未达预期的方面。** (例如：资源限制、技能瓶颈、外部环境变化、协作问题等)。\n" +
                        "    b.  **深入反思个人或团队在工作方法、知识技能、沟通协调、决策判断等方面存在的不足和需要改进之处。**\n" +
                        "    c.  **避免推诿责任，重点在于分析原因和学习。**\n" +
                        "6.  **经验总结与教训提炼 (Summary of Experiences & Lessons Learned):**\n" +
                        "    a.  **从成功的工作实践中提炼出可复制、可推广的宝贵经验和有效方法。**\n" +
                        "    b.  **从遇到的问题和失败中深刻吸取教训，避免未来重蹈覆辙。**\n" +
                        "    c.  **这些经验教训应具有指导性和启发性。**\n" +
                        "7.  **未来工作展望与改进计划 (Future Work Outlook & Improvement Plan):**\n" +
                        "    a.  **基于本次总结的分析，提出下一阶段关于【" + escapedUserContent + "】的工作思路、重点方向或初步目标。**\n" +
                        "    b.  **针对发现的问题和不足，制定具体、可行的改进措施和个人/团队能力提升计划。**\n" +
                        "    c.  **(可选) 对未来工作中可能遇到的新挑战或机遇进行预判。**\n" +
                        "8.  **意见与建议 (Opinions & Suggestions - Optional):** (如果总结是向上级汇报)\n" +
                        "    a.  **基于工作实践，对改进团队工作、优化流程、提升效率等方面提出建设性的意见或建议。**\n" +
                        "9.  **致谢 (Acknowledgements - Optional but Recommended):**\n" +
                        "    a.  **感谢在本期工作中给予支持和帮助的领导、同事、合作伙伴或客户。**\n" +
                        "【内容侧重点】: 围绕【" + escapedUserContent + "】的实际工作，**以事实为依据，以数据为支撑，全面回顾，客观评估。** 重点突出**主要业绩成果、关键经验教训，并展现深刻的自我反思和积极的改进意愿。** 确保总结的真实性、针对性和指导性。\n" +
                        "【语言风格】: **客观、真实、简洁、精炼、条理清晰、逻辑性强。** 成绩要肯定，问题要坦诚，分析要深入，计划要务实。**避免空话、套话、自我吹嘘或敷衍了事。** 根据汇报对象调整正式程度。\n" +
                        "【章节数量】: 结构应完整，确保以上核心要素均有体现。章节划分应服务于内容的逻辑呈现和阅读的便利性。";
                break;

            case "工作计划":
            case "workplace": // Corrected alias, "workplace" is a location
            case "action plan": // Another relevant alias
                instruction = "当前PPT大纲用于制定一份关于【" + escapedUserContent + "】（例如：个人/团队下一阶段工作、新项目启动、某项任务执行）的详细工作计划。**核心目标是：明确未来一段时间内的工作方向、具体目标、行动步骤、资源需求和预期成果，确保工作有序、高效地推进并达成预期。**\n" +
                        "【工作计划场景核心要素与结构建议】:\n" +
                        "1.  **计划标题 (Plan Title):** \n" +
                        "    a.  **清晰、准确地概括计划的核心内容和时间范围。** 例如：“【张三】2024年上半年【" + escapedUserContent + "】工作计划”、“【XX项目】（" + escapedUserContent + "）实施计划书V1.0”、“关于提升【" + escapedUserContent + "】客户满意度的行动计划”。\n" +
                        "    b.  PPT大纲的 `# 主题名称` 应直接反映此标题。\n" +
                        "2.  **引言/计划背景与依据 (Introduction/Background & Rationale):**\n" +
                        "    a.  **简述制定此工作计划的背景、原因和重要性。** (例如：基于上级指示、年度战略目标、市场变化、问题诊断结果等)。\n" +
                        "    b.  **阐明本计划与【" + escapedUserContent + "】的直接关联以及期望解决的核心问题或抓住的关键机遇。**\n" +
                        "    c.  **(可选) 引用相关数据或分析来支持计划的必要性。**\n" +
                        "3.  **指导思想与基本原则 (Guiding Ideology & Basic Principles - Optional, for strategic plans):** (如果适用，尤其对于较宏观或长期的计划) 阐述计划遵循的总体方向、核心理念和关键行为准则。\n" +
                        "4.  **计划的总体目标与具体目标 (Overall & Specific Objectives - SMART Principle):**\n" +
                        "    a.  **总体目标 (Overall Goal):** 对本计划期内，在【" + escapedUserContent + "】方面希望达成的最核心、最宏观的成果进行清晰描述。\n" +
                        "    b.  **具体目标 (Specific Objectives):** 将总体目标分解为若干个**具体的 (Specific)、可衡量的 (Measurable)、可实现的 (Achievable)、相关的 (Relevant)、有时限的 (Time-bound)** 子目标。**每个具体目标都应清晰指向【" + escapedUserContent + "】的某个方面。**\n" +
                        "5.  **主要任务分解与行动策略 (Key Task Breakdown & Action Strategies):** (这是计划的核心执行部分)\n" +
                        "    a.  **针对每一个具体目标，将其分解为若干个可执行的主要任务或工作包 (Work Packages)。**\n" +
                        "    b.  **对每个主要任务，进一步细化为具体的行动步骤、方法或策略。** 清晰描述“做什么”以及“大致如何做”。\n" +
                        "    c.  **明确各项任务之间的逻辑关系和依赖性 (Dependencies)。**\n" +
                        "6.  **时间进度安排与里程碑 (Timeline & Key Milestones):**\n" +
                        "    a.  **为每项主要任务和关键行动步骤设定明确的开始日期、结束日期和预计工时。**\n" +
                        "    b.  **设定清晰、可衡量的关键里程碑 (Milestones) 及其达成标志，** 用于监控项目进度和阶段性成果。\n" +
                        "    c.  **(推荐) 使用甘特图 (Gantt Chart) 或类似可视化工具来呈现时间计划（大纲中描述其构成）。**\n" +
                        "7.  **责任分工与团队协作 (Responsibilities, Roles & Team Collaboration):**\n" +
                        "    a.  **明确各项主要任务和关键行动步骤的唯一负责人 (Owner/Accountable Person)。**\n" +
                        "    b.  **明确参与协作的团队成员或部门及其具体职责 (RACI chart elements can be useful here: Responsible, Accountable, Consulted, Informed)。**\n" +
                        "    c.  **建立清晰的沟通和协作机制。**\n" +
                        "8.  **所需资源与预算预估 (Resource Requirements & Budget Estimation):**\n" +
                        "    a.  **人力资源:** 所需人员的数量、技能要求、角色分配。\n" +
                        "    b.  **物资与设备:** 所需的物资、工具、软件、硬件、场地等。\n" +
                        "    c.  **财务预算:** 各项任务和资源所需的资金估算，以及资金来源和审批流程（如果适用）。\n" +
                        "    d.  **信息与知识资源:** 需要获取的特定信息、数据、培训或外部专家支持。\n" +
                        "9.  **预期成果、交付物与考核指标 (Expected Outcomes, Deliverables & Key Performance Indicators - KPIs):**\n" +
                        "    a.  **清晰描述计划完成后预期的具体成果和有形的交付物 (Deliverables)。**\n" +
                        "    b.  **设定与具体目标相对应的、可衡量的关键绩效考核指标 (KPIs)，** 用于评估计划的执行效果和目标达成情况。\n" +
                        "10. **潜在风险评估与应对预案 (Potential Risk Assessment & Mitigation/Contingency Plan):**\n" +
                        "    a.  **预估在计划实施过程中可能遇到的主要风险因素** (例如：市场变化、技术难题、资源短缺、人员变动、依赖项延迟等)。\n" +
                        "    b.  **对每个风险进行可能性和影响程度的评估。**\n" +
                        "    c.  **针对高优先级风险，提出具体的预防措施和应对预案。**\n" +
                        "11. **沟通与汇报机制 (Communication & Reporting Mechanism):**\n" +
                        "    a.  **明确计划执行过程中的沟通频率、方式和对象。**\n" +
                        "    b.  **设定进度汇报和问题上报的流程。**\n" +
                        "12. **计划的审批与修订流程 (Approval & Revision Process - If applicable):** 说明计划如何获得批准以及在何种情况下需要修订。\n" +
                        "【内容侧重点】: 围绕【" + escapedUserContent + "】的目标实现，**强调计划的明确性、系统性、可操作性、时效性和可衡量性。** 任务分解的合理性、时间节点的清晰性、责任分工的明确性以及资源保障的可行性是核心。**计划应具有前瞻性和一定的灵活性。**\n" +
                        "【语言风格】: **清晰、具体、务实、严谨、有条理、具有指导性和前瞻性。** 多用肯定句和祈使句式表达行动和要求。**避免含糊不清或过于理想化的描述。** 根据计划的正式程度调整语言。\n" +
                        "【章节数量】: 结构应完整，确保以上核心要素均得到充分考虑和阐述。章节划分应服务于计划的逻辑性和易理解性。";
                break;

            case "研究报告":
            case "research report": // Corrected alias
                instruction = "当前PPT大纲用于呈现一份关于【" + escapedUserContent + "】（例如：学术研究、市场调研、行业分析、技术评估等）的正式研究报告的演示版本。**核心目标是：清晰、准确、有逻辑地向特定受众（如学术同行、决策者、客户）展示研究过程、核心发现、分析讨论和主要结论，并体现研究的严谨性和价值。**\n" +
                        "【研究报告演示场景核心要素与结构建议】:\n" +
                        "1.  **标题页 (Title Page):** \n" +
                        "    a.  **完整且精确的研究报告标题:** 清晰反映研究主题【" + escapedUserContent + "】及其核心范畴。\n" +
                        "    b.  **作者/研究团队信息 (Author(s)/Research Team):** 姓名、所属机构/部门。\n" +
                        "    c.  **报告日期/版本号 (Date/Version of Report).**\n" +
                        "    d.  **(可选) 资助机构/项目编号 (Funding Source/Project ID).**\n" +
                        "    e.  PPT大纲的 `# 主题名称` 应直接反映此报告标题。\n" +
                        "2.  **摘要/执行摘要 (Abstract/Executive Summary - Concise & Comprehensive):** (1-2页，根据报告复杂度和受众调整)\n" +
                        "    a.  **研究背景与问题陈述 (Brief Background & Problem Statement).**\n" +
                        "    b.  **核心研究目的与目标 (Core Research Objectives).**\n" +
                        "    c.  **关键研究方法概述 (Key Methodology Overview).**\n" +
                        "    d.  **最重要的研究发现/核心结果 (Most Significant Findings/Key Results).**\n" +
                        "    e.  **主要结论与核心启示/建议 (Main Conclusions & Key Implications/Recommendations).**\n" +
                        "3.  **引言/研究背景与问题提出 (Introduction/Background & Problem Formulation):**\n" +
                        "    a.  **详细阐述研究【" + escapedUserContent + "】的背景、现实需求或理论驱动因素。**\n" +
                        "    b.  **明确界定研究问题 (Research Questions) 和/或研究假设 (Hypotheses)。**\n" +
                        "    c.  **阐述本研究的理论意义、实践价值或预期贡献。**\n" +
                        "    d.  **(可选) 研究范围与限制的初步说明 (Scope and Limitations - brief mention).**\n" +
                        "4.  **文献综述/理论基础 (Literature Review/Theoretical Framework - If applicable and significant):**\n" +
                        "    a.  **系统回顾与【" + escapedUserContent + "】相关的国内外已有关键研究、核心理论和主要观点。**\n" +
                        "    b.  **评述现有研究的进展与不足，** 引出本研究的切入点、创新点或对话点。\n" +
                        "    c.  **构建本研究的理论分析框架（如果适用）。**\n" +
                        "5.  **研究方法/研究设计 (Methodology/Research Design - Detailed & Justified):**\n" +
                        "    a.  **总体研究范式/路径选择 (Overall Research Paradigm/Approach - e.g., Quantitative, Qualitative, Mixed-methods).**\n" +
                        "    b.  **具体研究方法的详细描述:** 例如，调查研究（问卷设计、抽样方法、样本量与特征）、实验研究（实验设计、变量控制、被试）、案例研究（案例选择标准、数据收集途径）、内容分析、模型构建、算法设计等。\n" +
                        "    c.  **数据收集过程与工具 (Data Collection Procedures & Instruments):** 清晰说明数据来源、收集步骤、使用的工具或设备。\n" +
                        "    d.  **数据分析方法与技术 (Data Analysis Methods & Techniques):** 说明采用的统计分析方法、质性分析编码过程、模型验证方法等。\n" +
                        "    e.  **研究的信度与效度保障措施 (Measures for Reliability & Validity - if applicable).**\n" +
                        "6.  **研究发现/结果呈现与初步解读 (Findings/Results Presentation & Initial Interpretation):** (这是核心部分，通常包含多个子章节/幻灯片组)\n" +
                        "    a.  **围绕研究问题/假设，客观、清晰、有条理地呈现通过研究获得的主要数据和核心发现。**\n" +
                        "    b.  **强烈建议使用图表 (Charts, Graphs, Tables)、图片、模型示意图等视觉化工具** 来辅助说明和突出关键结果，并配以简洁的文字解读。\n" +
                        "    c.  **只呈现与研究问题直接相关的、经过验证的结果，避免无关信息或主观臆断。**\n" +
                        "    d.  **对统计显著性、效应量等进行说明（如果适用）。**\n" +
                        "7.  **讨论与分析 (Discussion & In-depth Analysis):**\n" +
                        "    a.  **对研究结果进行深入的分析和富有洞察力的解读，** 解释其内在含义和逻辑关系。\n" +
                        "    b.  **将本研究的发现与文献综述中的理论、模型或前人研究结果进行比较、印证、对话或修正。**\n" +
                        "    c.  **探讨研究结果的理论贡献和实践启示/应用价值。**\n" +
                        "    d.  **分析研究中可能存在的偏差、未预料到的发现或需要进一步探讨的问题。**\n" +
                        "8.  **结论 (Conclusions):**\n" +
                        "    a.  **简明扼要地总结本研究的主要结论，** 直接回应引言中提出的研究问题或假设。\n" +
                        "    b.  **强调本研究的核心贡献和创新点。**\n" +
                        "    c.  **避免重复讨论部分的详细分析，结论应高度凝练。**\n" +
                        "9.  **研究局限性 (Limitations of the Study - Be Honest & Constructive):** (如果适用，体现研究的严谨性)\n" +
                        "    a.  **客观指出本研究在理论、方法、样本、数据、范围等方面存在的不足之处或未能解决的问题。**\n" +
                        "    b.  **这些局限性如何可能影响研究结果的解释或推广。**\n" +
                        "10. **建议与未来研究展望 (Recommendations & Future Research Directions):**\n" +
                        "    a.  **基于研究结论，针对实践层面（如政策制定、产品开发、管理改进）提出具体、可操作的建议。**\n" +
                        "    b.  **基于研究局限性和新的发现，对未来相关研究方向、可深入探讨的问题或方法改进提出展望。**\n" +
                        "11. **致谢 (Acknowledgements - Optional, but good practice):** 感谢对研究提供帮助的个人、团队或机构（如导师、合作者、资助方、数据提供方）。\n" +
                        "12. **参考文献 (References/Bibliography - Key citations, full list in written report):** (在大纲中可提示有此部分，PPT中通常只列关键或少量参考文献，或直接引导至完整报告)\n" +
                        "13. **附录 (Appendix - Key supplementary materials, full in written report):** (在大纲中可提示，PPT中可展示关键附录内容截图或列表)\n" +
                        "【内容侧重点】: 围绕【" + escapedUserContent + "】的研究问题，**高度强调研究过程的科学性、方法的严谨性、数据的真实性、结果的客观性和结论的可靠性与价值。** 逻辑结构清晰，论证过程充分，分析讨论深入。\n" +
                        "【语言风格】: **学术化、正式、客观、精确、逻辑严密、简洁明了。** 避免使用情感化、口语化或模糊不清的表达。**准确使用专业术语，并对关键概念进行清晰界定。**\n" +
                        "【章节数量】: 结构相对固定，需完整覆盖研究报告的核心组成部分。演示时可根据时间和受众调整各部分的详略程度，但逻辑链条不能断裂。";
                break;

            case "会议材料":
            case "meeting materials": // Corrected alias
                instruction = "当前PPT大纲是为一次关于【" + escapedUserContent + "】（例如：项目评审会、部门例会、决策讨论会、信息同步会、培训研讨会）的会议准备的演示材料。**核心目标是：根据会议的具体目的（如信息传递、问题研讨、方案评审、决策制定、培训学习），高效、清晰地呈现相关信息，引导讨论，促进共识，并记录关键成果。**\n" +
                        "【会议材料场景核心要素与结构建议】:\n" +
                        "1.  **会议标题页 (Meeting Title Slide):**\n" +
                        "    a.  **清晰的会议标题:** 点明会议主题（围绕【" + escapedUserContent + "】）和会议类型。\n" +
                        "    b.  **会议日期、时间、地点 (或线上会议链接信息)。**\n" +
                        "    c.  **主办/召集部门/团队 (可选)。**\n" +
                        "    d.  **参会人员范围提示 (可选)。**\n" +
                        "    e.  PPT大纲的 `# 主题名称` 应直接反映此会议标题。\n" +
                        "2.  **开场与会议目的重申 (Opening & Reiteration of Meeting Purpose):**\n" +
                        "    a.  **主持人/主讲人简短开场白，欢迎参会者。**\n" +
                        "    b.  **清晰重申本次会议的核心目的、期望达成的具体成果或需要解决的关键问题。**\n" +
                        "    c.  **(可选) 简要回顾上次会议相关结论或行动项进展 (如果是系列会议)。**\n" +
                        "3.  **会议议程与时间安排 (Meeting Agenda & Time Allocation):**\n" +
                        "    a.  **清晰列出本次会议的主要议题/环节及其逻辑顺序。**\n" +
                        "    b.  **为每个议题/环节预估合理的时间分配。**\n" +
                        "    c.  **(可选) 明确各议题的负责人或引导者。**\n" +
                        "4.  **议题一：[具体议题名称，与【" + escapedUserContent + "】相关] (Agenda Item 1: [Specific Topic related to 【" + escapedUserContent + "】]):** (每个议题构成一个 `## 目录章节`)\n" +
                        "    a.  **背景介绍/问题陈述 (Background Introduction/Problem Statement):** 简明扼要地介绍此议题的由来、重要性或当前面临的问题。\n" +
                        "    b.  **相关数据/信息展示 (Presentation of Relevant Data/Information):** 提供支持讨论或决策的客观数据、图表、事实、案例等。\n" +
                        "    c.  **现状分析/观点阐述 (Current Situation Analysis/Viewpoint Elaboration):** (如果适用) 对现状进行分析，或阐述提出方的观点和理由。\n" +
                        "    d.  **待讨论的关键问题/核心议题 (Key Questions/Core Issues for Discussion):** 清晰列出需要与会者共同思考和讨论的具体问题。\n" +
                        "    e.  **(如果适用) 备选方案/建议方案介绍 (Presentation of Alternative/Proposed Solutions):** 简要介绍不同方案的优缺点、成本效益等，供讨论和选择。\n" +
                        "    f.  **引导讨论的开放性问题 (Open-ended Questions to Facilitate Discussion).**\n" +
                        "5.  **议题二：[具体议题名称] (Agenda Item 2):** (结构类似议题一，根据实际议题内容调整子项)\n" +
                        "6.  **(更多议题按需添加)...**\n" +
                        "7.  **讨论汇总与初步共识 (Discussion Summary & Preliminary Consensus - for each major item or at intervals):** (可选，但推荐)\n" +
                        "    a.  **对已讨论议题的关键观点和主要分歧进行简要总结。**\n" +
                        "    b.  **记录已达成的初步共识或待进一步明确的问题。**\n" +
                        "8.  **决策环节 (Decision-Making Session - if applicable):**\n" +
                        "    a.  **清晰呈现待决策事项。**\n" +
                        "    b.  **明确决策标准和流程。**\n" +
                        "    c.  **记录最终决策结果。**\n" +
                        "9.  **会议总结与行动计划 (Meeting Summary & Action Plan/Next Steps):**\n" +
                        "    a.  **对整个会议的主要讨论内容、关键结论和形成的决议进行全面总结。**\n" +
                        "    b.  **明确下一步的行动计划 (Action Items):**\n" +
                        "        i.  **具体任务内容 (Specific Task).**\n" +
                        "        ii. **负责人 (Responsible Person/Team).**\n" +
                        "        iii. **完成截止日期 (Due Date).**\n" +
                        "        iv. **所需资源/支持 (Resources/Support Needed - if any).**\n" +
                        "10. **Q&A/自由讨论/反馈收集 (Q&A/Open Discussion/Feedback Collection):**\n" +
                        "    a.  **为参会者提供提问、发表补充意见或反馈的机会。**\n" +
                        "11. **结束语与感谢 (Closing Remarks & Thank You):**\n" +
                        "    a.  **感谢所有参会者的积极参与和贡献。**\n" +
                        "    b.  **(可选) 强调会议成果的重要性或对未来工作的指引。**\n" +
                        "    c.  **(可选) 预告下次会议安排或后续沟通事宜。**\n" +
                        "【内容侧重点】: 围绕【" + escapedUserContent + "】和明确的会议议程，内容应**聚焦、简洁、有条理、目标导向。** 旨在**促进有效的信息共享、引导深入讨论、推动问题解决、辅助科学决策或达成培训目标。** 每个议题的材料都必须具有高度的针对性和实用性。\n" +
                        "【语言风格】: **专业、清晰、简洁、客观、建设性。** 根据会议的具体类型（如：高层决策会、部门工作例会、跨部门协调会、技术研讨会、客户沟通会、团队头脑风暴会等）和参会人员的背景，灵活调整语言的正式程度、技术深度和互动方式。**避免冗长和跑题。**\n" +
                        "【章节数量】: 结构应以会议议程为核心框架，确保每个议题都有清晰的呈现和引导。章节数量根据议题多少而定，但整体流程应完整。";
                break;

            case "产品介绍":
            case "product introduction":
            case "productivity": // Keep this as it's very relevant
            case "product demo": // If the focus is more on demonstration
                instruction = "当前PPT大纲用于进行一次关于产品【" + escapedUserContent + "】（可以是新产品发布、现有产品功能更新、特定解决方案推广等）的介绍、演示或推广。**核心目标是：激发目标受众（如潜在客户、合作伙伴、投资者、媒体）的兴趣，清晰传递产品的核心价值和独特优势，建立积极的产品认知，并最终促使其采取期望的行动（如购买、试用、合作、报道等）。**\n" +
                        "【产品介绍场景核心要素与结构建议】:\n" +
                        "1.  **引人入胜的开场/“钩子” (Compelling Opening/\"Hook\" - Grab Attention Immediately):**\n" +
                        "    a.  **标题页:** 产品名称【" + escapedUserContent + "】 + 一句引人注目的Slogan/价值宣言。\n" +
                        "    b.  **开场方式 (选择1-2种):**\n" +
                        "        i.  **点出目标受众面临的痛点/未被满足的需求 (Relatable Pain Point/Unmet Need):** 让他们感同身受。\n" +
                        "        ii. **提出一个与【" + escapedUserContent + "】能解决的问题相关的、发人深省的问题。**\n" +
                        "        iii. **分享一个惊人的行业数据或趋势，** 引出产品的市场机会。\n" +
                        "        iv. **讲述一个简短、生动的用户故事开场。**\n" +
                        "2.  **问题/机会的深入阐述 (Elaboration on the Problem/Opportunity):**\n" +
                        "    a.  **详细描述目标用户在没有【" + escapedUserContent + "】之前所面临的具体困境、挑战或错失的机会。**\n" +
                        "    b.  **量化问题的影响 (Quantify the pain if possible - e.g., time wasted, money lost, opportunities missed).**\n" +
                        "3.  **我们的解决方案：产品【" + escapedUserContent + "】概述 (Our Solution: Product 【" + escapedUserContent + "】 Overview):**\n" +
                        "    a.  **清晰、简洁地介绍产品【" + escapedUserContent + "】是什么。**\n" +
                        "    b.  **阐述【" + escapedUserContent + "】是如何直接解决前面提出的痛点或满足需求的。**\n" +
                        "    c.  **核心价值主张 (Core Value Proposition):** 用一句话概括产品带给用户的最核心价值。\n" +
                        "4.  **核心功能与独特特性展示 (Key Features & Unique Selling Propositions - USPs):**\n" +
                        "    a.  **聚焦1-3个最核心、最具吸引力、最能体现差异化的功能点或特性进行详细介绍。** 避免功能堆砌。\n" +
                        "    b.  **解释这些功能如何运作 (How it works - simplified for the audience).**\n" +
                        "    c.  **强调其独特性或创新之处。**\n" +
                        "5.  **用户利益与价值实现 (Customer Benefits & Value Realization - Focus on \"WIIFM\"):**\n" +
                        "    a.  **将每个核心功能/特性转化为用户能够直接感知和体验到的具体利益和价值。** (例如：节省XX%的时间，提高XX%的效率，降低XX%的成本，带来XX的愉悦体验，解决XX的烦恼)。\n" +
                        "    b.  **使用具体场景或用户视角来描述这些利益。**\n" +
                        "6.  **产品演示 (Product Demonstration - Live or Video):** (如果适用，这是非常有力的环节)\n" +
                        "    a.  **大纲中描述演示的关键路径、核心场景和期望突出的功能点。**\n" +
                        "    b.  **演示应流畅、聚焦核心价值，避免过于复杂或冗长。**\n" +
                        "7.  **目标用户画像与典型应用场景 (Target User Persona & Typical Use Cases):**\n" +
                        "    a.  **清晰描绘产品【" + escapedUserContent + "】主要面向的1-2类典型用户画像。**\n" +
                        "    b.  **展示产品在这些用户典型工作/生活场景中的具体应用和价值。**\n" +
                        "8.  **竞争优势与市场差异化 (Competitive Advantage & Market Differentiation):** (如果适用，尤其对标竞品时)\n" +
                        "    a.  **简要提及主要竞争对手或替代方案。**\n" +
                        "    b.  **清晰、有依据地突出产品【" + escapedUserContent + "】的独特之处和核心竞争优势 (e.g., 技术领先、价格优势、用户体验更佳、服务更完善)。**\n" +
                        "    c.  **(可选) 使用对比图表。**\n" +
                        "9.  **成功案例/客户评价/权威认可 (Success Stories/Testimonials/Endorsements):** (如果已有)\n" +
                        "    a.  **分享1-2个有代表性的客户成功案例，** 说明他们如何通过使用【" + escapedUserContent + "】解决了问题、获得了价值。\n" +
                        "    b.  **引用积极的客户评价或媒体/行业专家的认可。**\n" +
                        "10. **商业模式与定价策略 (Business Model & Pricing Strategy - If applicable, esp. for B2B or paid products):**\n" +
                        "    a.  **简要说明产品的商业模式（如订阅、一次性购买、免费增值等）。**\n" +
                        "    b.  **清晰展示不同版本/套餐的价格信息和主要区别。**\n" +
                        "    c.  **强调性价比或价值回报。**\n" +
                        "11. **团队介绍 (Our Team - Optional, esp. for startups or if team expertise is a key selling point):** 简要介绍核心团队的背景和专业实力。\n" +
                        "12. **总结与核心价值重申 (Summary & Reiteration of Core Value):**\n" +
                        "    a.  **再次简明扼要地强调产品【" + escapedUserContent + "】的核心价值主张和为用户带来的关键利益。**\n" +
                        "13. **明确的行动呼吁 (Clear Call to Action - CTA):**\n" +
                        "    a.  **引导听众采取具体的下一步行动。** 例如：立即注册试用、扫描二维码下载、访问官网了解更多、联系销售代表获取报价、参与早期用户计划、预约体验等。\n" +
                        "    b.  **CTA应简单、直接、易于执行，并提供明确的指引。**\n" +
                        "14. **Q&A 环节与联系方式 (Q&A Session & Contact Information):**\n" +
                        "    a.  **预留问答时间。**\n" +
                        "    b.  **提供清晰的联系方式（官网、邮箱、电话、社交媒体）。**\n" +
                        "【内容侧重点】: **始终以目标用户为中心，围绕产品【" + escapedUserContent + "】的核心价值主张和能为用户解决的实际问题/带来的核心利益展开。** 强调“为什么这款产品对你有价值？”、“它如何让你的生活/工作更美好？”，而不是简单罗列功能。**故事性、视觉化呈现和情感连接非常重要。**\n" +
                        "【语言风格】: **生动、简洁、清晰、富有吸引力和说服力，以用户能够理解和共鸣的语言进行表达。** 根据产品特性和目标受众调整语言的专业程度和情感色彩（如科技感、亲和力、高端感等）。**避免过多晦涩的技术术语，除非面向高度专业的技术受众。**\n" +
                        "【视觉配合（PPT）】: **PPT设计应高度视觉化，** 使用高质量的产品图片、演示截图/视频、清晰的图表、简洁的动画效果来增强演示效果。**每张幻灯片聚焦一个核心信息点，文字精炼。**";
                break;

            case "公司介绍":
            case "company introduction":
            case "company profile":
            case "about": // Common website section name
                instruction = "当前PPT大纲用于进行一次关于【" + escapedUserContent + "】（可以指公司整体，也可以是公司的某个特定方面、业务或倡议）的公司介绍。**核心目标是：向目标受众（如潜在客户、合作伙伴、求职者、媒体、公众等）清晰、全面、正面地展示公司的核心信息、价值主张、实力和潜力，以建立信任、塑造良好品牌形象，并根据介绍目的促成期望的互动。**\n" +
                        "【公司介绍场景核心要素与结构建议】:\n" +
                        "1.  **封面/开场页 (Cover/Opening Slide):**\n" +
                        "    a.  **醒目的公司名称和Logo。**\n" +
                        "    b.  **一句简洁有力的Slogan、价值宣言或点明【" + escapedUserContent + "】核心的标语。**\n" +
                        "    c.  **(可选) 与公司形象或【" + escapedUserContent + "】主题相关的视觉元素。**\n" +
                        "    d.  PPT大纲的 `# 主题名称` 应直接反映公司名称或本次介绍的核心【" + escapedUserContent + "】。\n" +
                        "2.  **“我们是谁？” - 公司概况与定位 (Who We Are? - Company Overview & Positioning):**\n" +
                        "    a.  **简明扼要地介绍公司的核心业务领域、市场定位（在哪个行业，服务于谁）。**\n" +
                        "    b.  **公司的成立时间、发展阶段、规模（如员工人数、分支机构）。**\n" +
                        "    c.  **点出公司在行业中的独特性或核心价值主张。**\n" +
                        "3.  **我们的使命、愿景与核心价值观 (Our Mission, Vision & Core Values):**\n" +
                        "    a.  **使命 (Mission):** 公司存在的根本目的和为社会/客户创造的核心价值。\n" +
                        "    b.  **愿景 (Vision):** 公司对未来的期望和长远发展目标。\n" +
                        "    c.  **核心价值观 (Core Values):** 指导公司行为和决策的基本准则和信念。\n" +
                        "    d.  **这些应真实反映公司文化，并与【" + escapedUserContent + "】相呼应（如果适用）。**\n" +
                        "4.  **我们的故事/发展历程与重要里程碑 (Our Story/Journey & Key Milestones):** (如果适用，特别是对有一定历史的公司或需要展示成长性的场合)\n" +
                        "    a.  **以时间轴或故事化的方式，展示公司的重要发展节点、关键转折和取得的重大成就。**\n" +
                        "    b.  **突出那些能够体现公司成长性、创新能力或行业影响力的里程碑事件。**\n" +
                        "5.  **我们的核心产品/服务体系 (Our Core Products/Services Portfolio):**\n" +
                        "    a.  **清晰介绍公司的主要产品线或服务类别，特别是与本次介绍核心【" + escapedUserContent + "】紧密相关的部分。**\n" +
                        "    b.  **突出每项核心产品/服务的独特卖点 (USPs)、为客户带来的核心价值和解决的关键问题。**\n" +
                        "    c.  **(可选) 简要展示产品/服务的实际应用场景或效果。**\n" +
                        "6.  **我们的市场与客户 (Our Market & Customers/Clients):**\n" +
                        "    a.  **描述公司的目标市场、主要客户群体或行业领域。**\n" +
                        "    b.  **展示已取得的市场地位、客户认可或合作伙伴关系 (例如：重要客户Logo墙、典型合作案例简介、客户评价节选)。**\n" +
                        "    c.  **(可选) 市场规模与增长潜力概述。**\n" +
                        "7.  **我们的团队与企业文化 (Our Team & Company Culture):** (尤其对吸引人才、合作伙伴或强调软实力时重要)\n" +
                        "    a.  **简要介绍核心管理团队的背景和专业实力 (如果适用且公开)。**\n" +
                        "    b.  **突出公司的人才理念、团队构成特点（如多元化、专业化）。**\n" +
                        "    c.  **展示积极、独特、有吸引力的企业文化和工作氛围 (例如：创新、协作、客户至上、员工关怀等)。**\n" +
                        "8.  **我们的竞争优势/核心竞争力 (Our Competitive Advantage/Core Competencies):**\n" +
                        "    a.  **清晰阐述公司在行业中所具备的独特优势和难以被复制的核心竞争力。** (例如：技术领先、品牌影响力、成本控制、渠道优势、客户服务、创新能力、高效运营等)。\n" +
                        "    b.  **这些优势如何帮助公司更好地服务客户和实现【" + escapedUserContent + "】的目标。**\n" +
                        "9.  **我们的社会责任/ESG实践 (Our Social Responsibility/ESG Initiatives - Increasingly Important):** (如果适用)\n" +
                        "    a.  **展示公司在环境保护、社会贡献、员工福祉、公益事业、公司治理等方面的努力和成果。**\n" +
                        "10. **未来展望与战略规划 (Future Outlook & Strategic Plan):**\n" +
                        "    a.  **分享公司对未来行业趋势的判断和自身的发展方向。**\n" +
                        "    b.  **阐述公司未来1-3年的主要战略规划和重点举措，特别是与【" + escapedUserContent + "】相关的计划。**\n" +
                        "    c.  **表达对未来的信心和期望。**\n" +
                        "11. **总结与行动呼吁/互动邀请 (Summary & Call to Action/Invitation for Interaction):**\n" +
                        "    a.  **简要回顾公司介绍的核心亮点。**\n" +
                        "    b.  **根据介绍的具体目的，提出明确的行动呼吁或互动邀请。** (例如：欢迎合作洽谈、邀请加入我们、了解更多产品信息、关注我们的社交媒体等)。\n" +
                        "12. **联系方式与Q&A提示 (Contact Information & Q&A Prompt):**\n" +
                        "    a.  **提供清晰的公司官网、主要联系邮箱、电话、地址等。**\n" +
                        "    b.  **提示进入问答环节。**\n" +
                        "【内容侧重点】: 围绕【" + escapedUserContent + "】（作为公司介绍的某个切入点或整体视角），**全面、真实、正面地展示公司的综合实力、独特价值、发展潜力和社会责任感。** 核心在于**建立信任、塑造积极的品牌形象，并与目标受众建立有效连接。**\n" +
                        "【语言风格】: **专业、自信、积极、富有吸引力、真诚可信。** 根据目标受众（如投资者、潜在客户、求职者、合作伙伴、媒体等）和介绍场合，灵活调整语言的正式程度、侧重点和情感色彩。**避免空洞的口号，多用事实和实例支撑。**\n" +
                        "【视觉配合（PPT）】: **PPT设计应体现公司品牌形象，** 使用统一的视觉识别系统（颜色、字体、版式）。图文并茂，使用高质量图片、简洁图表和适度动画，提升演示效果。**每张幻灯片信息量适中，突出核心。**";
                break;

            case "商业计划书":
            case "businessman": // Corrected alias
            case "pitch deck": // Very common term for this
            case "investment proposal":
                instruction = "当前PPT大纲用于呈现一份关于【" + escapedUserContent + "】（指创业项目、新业务拓展、或特定商业倡议）的商业计划书演示文稿（Pitch Deck），通常用于融资、寻求合作或内部战略审批。**核心目标是：在有限的时间内，清晰、有说服力地阐述项目的商业价值、市场机会、竞争优势、盈利模式、团队能力和财务预期，以打动目标受众（如投资者、合作伙伴、决策层）并促使其采取支持行动。**\n" +
                        "【商业计划书演示场景核心要素与结构建议】: (通常10-15张核心幻灯片)\n" +
                        "1.  **封面页 (Cover Slide):**\n" +
                        "    a.  **公司/项目Logo与名称【" + escapedUserContent + "】。**\n" +
                        "    b.  **一句精炼、引人注目的价值主张或项目Slogan (One-liner pitch)。**\n" +
                        "    c.  **演示者姓名/团队名称，日期。**\n" +
                        "    d.  PPT大纲的 `# 主题名称` 应直接反映项目名称和核心价值。\n" +
                        "2.  **问题/痛点 (The Problem/Pain Point):** (1-2张幻灯片)\n" +
                        "    a.  **清晰、生动地描述目标客户当前面临的、尚未被有效解决的重大问题或迫切需求。**\n" +
                        "    b.  **使用数据、真实故事或用户场景来强调问题的普遍性、严重性和紧迫性。**\n" +
                        "    c.  **让听众感同身受，认同这个问题的存在价值。**\n" +
                        "3.  **我们的解决方案/产品与服务 (Our Solution/Product & Service):** (1-2张幻灯片)\n" +
                        "    a.  **清晰、简洁地介绍你的核心产品/服务【" + escapedUserContent + "】是如何有效解决上述问题的。**\n" +
                        "    b.  **突出解决方案的创新性、独特性和核心优势。**\n" +
                        "    c.  **(可选) 使用产品原型截图、简短演示视频或流程图来直观展示。**\n" +
                        "4.  **市场机会与规模 (Market Opportunity & Size - TAM, SAM, SOM):** (1张幻灯片)\n" +
                        "    a.  **清晰定义目标市场，并提供可信的数据来证明其规模和增长潜力** (例如：总可用市场 TAM, 可服务市场 SAM, 目标市场 SOM)。\n" +
                        "    b.  **分析市场趋势和驱动因素，** 说明为何现在是进入该市场的最佳时机。\n" +
                        "5.  **商业模式 (Business Model - How We Make Money):** (1张幻灯片)\n" +
                        "    a.  **清晰、简明地解释公司/项目如何创造、传递和获取价值，即如何盈利。**\n" +
                        "    b.  **说明主要的收入来源 (Revenue Streams)、定价策略、客户获取成本 (CAC)、客户生命周期价值 (LTV) 等关键要素。**\n" +
                        "6.  **产品/技术优势与壁垒 (Product/Technology Advantage & Defensibility/Moat):** (1张幻灯片)\n" +
                        "    a.  **详细阐述产品或技术的核心优势、创新点、以及难以被竞争对手复制的壁垒** (如：专利、独特算法、网络效应、品牌、独家资源等)。\n" +
                        "    b.  **(可选) 产品发展路线图 (Product Roadmap) 概览。**\n" +
                        "7.  **市场推广与销售策略 (Go-to-Market Strategy/Marketing & Sales Plan):** (1张幻灯片)\n" +
                        "    a.  **清晰阐述如何有效地接触、获取、转化和服务目标客户。**\n" +
                        "    b.  **说明主要的营销渠道、销售策略、合作伙伴计划等。**\n" +
                        "8.  **竞争格局与竞争优势 (Competitive Landscape & Competitive Advantage):** (1张幻灯片)\n" +
                        "    a.  **识别主要的直接和间接竞争对手。**\n" +
                        "    b.  **通过对比分析（如定位图、功能对比表），清晰、客观地展示本项目的差异化竞争优势。** **强调“我们为什么更好/不同”。**\n" +
                        "9.  **管理团队 (Management Team - \"Why Us?\"):** (1张幻灯片)\n" +
                        "    a.  **介绍核心创始团队成员及其与项目【" + escapedUserContent + "】相关的关键经验、专业技能和成功案例。**\n" +
                        "    b.  **突出团队的互补性、执行力和行业资源。**\n" +
                        "    c.  **(可选) 顾问团队或重要早期支持者。**\n" +
                        "10. **运营现状与里程碑/牵引力 (Traction/Milestones Achieved - \"Show, Don't Just Tell\"):** (1张幻灯片)\n" +
                        "    a.  **展示项目迄今为止取得的关键进展和成果，** 例如：用户数据（注册用户、活跃用户、付费用户）、收入、重要合作、产品迭代、媒体报道、奖项荣誉等。\n" +
                        "    b.  **用数据和事实证明项目的可行性和市场潜力。**\n" +
                        "11. **财务预测与关键指标 (Financial Projections & Key Metrics):** (1-2张幻灯片)\n" +
                        "    a.  **展示未来3-5年的核心财务预测，** 包括收入、成本、利润、现金流等（通常以图表形式）。\n" +
                        "    b.  **清晰说明预测的关键假设和驱动因素。**\n" +
                        "    c.  **突出关键财务指标 (KPIs) 和盈利能力。**\n" +
                        "12. **融资需求与资金用途 (The Ask/Funding Request & Use of Funds - If for fundraising):** (1张幻灯片)\n" +
                        "    a.  **明确本轮融资的金额需求。**\n" +
                        "    b.  **详细说明融资资金的主要用途分配** (例如：产品研发、市场推广、团队扩张、运营资金等)。\n" +
                        "    c.  **(可选) 期望的估值或融资条款概要。**\n" +
                        "13. **风险分析与应对策略 (Risk Analysis & Mitigation Strategies - Briefly):** (可选，或融入Q&A准备)\n" +
                        "    a.  **简要识别项目面临的主要风险。**\n" +
                        "    b.  **概述应对这些风险的策略。**\n" +
                        "14. **总结与行动呼吁/投资亮点 (Conclusion & Call to Action/Investment Highlights):** (1张幻灯片)\n" +
                        "    a.  **再次强调项目的核心投资价值、市场机会和团队优势。**\n" +
                        "    b.  **发出明确的行动呼吁，** 例如：邀请进行下一轮深入沟通、请求投资决策等。\n" +
                        "15. **联系方式 (Contact Information):** 清晰列出主要联系人、邮箱、电话、公司网址。\n" +
                        "【内容侧重点】: 围绕【" + escapedUserContent + "】的商业可行性和投资价值，**全面、系统地展示市场机会、产品/服务创新、强大的团队执行力、清晰的盈利模式和可观的财务回报预期。** 逻辑必须严密，数据必须支撑，故事必须引人入胜，演示必须充满激情和自信。\n" +
                        "【语言风格】: **专业、精炼、自信、数据驱动、富有说服力和前瞻性。** 针对潜在投资者、合作伙伴或决策层，语言应直接、有力，避免冗余和含糊。**用讲故事的方式串联各个模块，使其更具吸引力。**\n" +
                        "【视觉配合（PPT）】: **PPT设计应简洁、专业、高度视觉化。** 大量使用图表、数据可视化、关键数字高亮、高质量图片和少量精炼文字。**每张幻灯片传递一个核心信息，避免信息过载。** 整体风格应与品牌形象一致。";
                break;

            case "科普宣传":
            case "popularsciencecommunication": // More descriptive alias
                instruction = "当前PPT大纲用于进行一次关于【" + escapedUserContent + "】（例如：某个科学现象、一项新技术、一个健康知识点、一种环保理念等）的科普宣传或公众意识提升活动。**核心目标是：以通俗易懂、生动有趣、科学准确的方式，向非专业公众普及【" + escapedUserContent + "】的相关知识，消除误解，提升科学素养，激发探索兴趣，或倡导积极行为。**\n" +
                        "【科普宣传场景核心要素与结构建议】:\n" +
                        "1.  **引人入胜的标题/开场问题 (Catchy Title/Intriguing Opening Question):**\n" +
                        "    a.  **标题应能立即抓住公众对【" + escapedUserContent + "】的好奇心，** 可以是设问、比喻、或与生活相关的表述。\n" +
                        "    b.  PPT大纲的 `# 主题名称` 应直接反映此科普主题，并力求生动。\n" +
                        "    c.  **开场可以是一个与【" + escapedUserContent + "】相关的、令人惊讶的事实、一个常见的误解、或一个贴近生活的小故事/场景。**\n" +
                        "2.  **“这是什么？” - 核心概念通俗化普及 (What is 【" + escapedUserContent + "】? - Concept Introduction in Plain Language):**\n" +
                        "    a.  **用最简单、最直观、最贴近生活的语言解释【" + escapedUserContent + "】的基本概念、定义或现象。**\n" +
                        "    b.  **避免使用复杂的专业术语，** 如果必须使用，务必给出清晰、形象的解释或类比。\n" +
                        "    c.  **可以配合生动的图片、简笔画或短动画（大纲中描述）来辅助理解。**\n" +
                        "3.  **“它与我/我们有什么关系？” - 重要性与生活关联 (Why is 【" + escapedUserContent + "】 Important/Relevant to Us?):**\n" +
                        "    a.  **清晰阐述【" + escapedUserContent + "】与普通人的日常生活、健康、环境、社会发展等方面的具体联系和重要性。**\n" +
                        "    b.  **让公众感受到这个知识点与自身息息相关，从而提高学习动机。**\n" +
                        "    c.  **使用具体案例或数据来说明其影响。**\n" +
                        "4.  **“它是如何运作的/发生的/为什么会这样？” - 原理/过程趣味揭秘 (How Does 【" + escapedUserContent + "】 Work/Happen? Why? - Unveiling the Mechanism with Fun):** (如果适用)\n" +
                        "    a.  **用生动的比喻、形象的图示、简化的模型、或分步动画（大纲中描述）来解释【" + escapedUserContent + "】背后的科学原理、运作机制或发生过程。**\n" +
                        "    b.  **将复杂的过程分解为易于理解的小步骤。**\n" +
                        "    c.  **可以设计一些互动小实验的思路或提问。**\n" +
                        "5.  **常见误区澄清/“原来如此！”时刻 (Debunking Common Myths/Creating \"Aha!\" Moments):** (如果适用)\n" +
                        "    a.  **针对公众对【" + escapedUserContent + "】可能存在的常见错误认知、迷思或谣言，进行科学、有力的澄清和纠正。**\n" +
                        "    b.  **通过对比、举证或简单实验演示，让公众恍然大悟。**\n" +
                        "6.  **生活中的实例与奇妙应用 (Real-world Examples & Amazing Applications of 【" + escapedUserContent + "】):**\n" +
                        "    a.  **展示【" + escapedUserContent + "】在日常生活、自然界、科技前沿等方面的具体应用、有趣现象或令人惊叹的案例。**\n" +
                        "    b.  **增强知识的趣味性和感知度，让科学“活”起来。**\n" +
                        "    c.  **多用高质量的图片和短视频（大纲中描述）。**\n" +
                        "7.  **“我们能做什么？”/知识的实际应用/行动倡议 (What Can We Do?/Practical Application/Call to Action):**\n" +
                        "    a.  **引导公众思考如何将所学的关于【" + escapedUserContent + "】的知识应用到实际生活中。**\n" +
                        "    b.  **如果适用，提出具体、简单、可行的行动倡议** (例如：节约用水、健康饮食、参与环保活动、支持科学研究等)。\n" +
                        "    c.  **强调个人行动的意义和力量。**\n" +
                        "8.  **趣味互动环节/有奖问答/小游戏 (Fun Interactive Session/Quiz with Prizes/Simple Games):** (可选，但能极大提升效果)\n" +
                        "    a.  **设计与【" + escapedUserContent + "】相关的、简单有趣的互动问答、谜语、小实验或在线小游戏。**\n" +
                        "    b.  **增加活动的参与度和娱乐性，巩固知识记忆。**\n" +
                        "9.  **总结回顾与核心信息强化 (Summary & Reinforcement of Key Messages):**\n" +
                        "    a.  **用简洁、生动的语言再次总结本次科普宣传的核心要点和最重要的信息。**\n" +
                        "    b.  **可以使用“一句话记住【" + escapedUserContent + "】”或“三个关键点”等形式。**\n" +
                        "10. **延伸阅读/探索资源/求助途径 (Further Reading/Exploration Resources/Where to Get Help):**\n" +
                        "    a.  **提供一些可靠的、可供公众进一步深入了解【" + escapedUserContent + "】的书籍、网站、纪录片、科普机构或专家信息。**\n" +
                        "    b.  **(如果适用) 提供遇到相关问题时的求助或咨询途径。**\n" +
                        "11. **结束语与感谢 (Closing Remarks & Thank You):** 感谢参与，鼓励持续探索。\n" +
                        "【内容侧重点】: 围绕【" + escapedUserContent + "】，核心在于**将复杂、专业的科学知识转化为公众易于理解、乐于接受、并能产生共鸣的信息。** 注重**科学性、准确性、趣味性、启发性和互动性的完美结合。** 多运用视觉化手段和生活化案例。\n" +
                        "【语言风格】: **通俗易懂、生动有趣、形象具体、富有启发性和亲和力。** 坚决避免使用过多或不加解释的专业术语。**多用比喻、拟人、提问、感叹等修辞手法。** 根据目标受众的年龄层次（如儿童、青少年、成人）调整语言的深度和表达方式。\n" +
                        "【视觉配合（PPT）】: **PPT设计应色彩鲜明、图文并茂、动画活泼、互动性强。** 大量使用高质量的图片、有趣的插画、简明扼要的图表、短视频片段和互动元素。**文字内容务必精炼，以视觉信息为主导。**";
                break;

            case "团队会议":
            case "meeting":
                instruction = "当前PPT大纲用于组织一次关于【" + escapedUserContent + "】的团队内部会议。\n" +
                        "【团队会议场景核心要素与结构建议】:\n" +
                        "1.  **会议标题与日期 (Meeting Title & Date):** 清晰点明会议主题【" + escapedUserContent + "】和召开日期。\n" +
                        "2.  **参会人员 (Attendees):** (可选，或在会议邀请中明确) 列出主要参会团队成员。\n" +
                        "3.  **会议目的与议程 (Meeting Objectives & Agenda):**\n" +
                        "    a.  **核心目的:** 本次会议希望达成什么共识、解决什么问题、或同步什么信息。\n" +
                        "    b.  **主要议题列表:** 清晰列出将要讨论的各个议题及预计时间分配。\n" +
                        "4.  **议题一：[议题名称 - 与【" + escapedUserContent + "】相关] (Topic 1: [Topic Name]):**\n" +
                        "    a.  **背景回顾/现状同步 (Background/Current Status Update):** 简要介绍此议题的来龙去脉或最新进展。\n" +
                        "    b.  **关键数据/信息展示 (Key Data/Information Presentation):** (如果适用) 提供支持讨论的数据或信息。\n" +
                        "    c.  **待讨论的问题/挑战 (Questions/Challenges for Discussion):** 清晰列出需要团队共同探讨的核心问题。\n" +
                        "    d.  **备选方案/思路启发 (Alternative Solutions/Brainstorming Prompts):** (如果适用) 提供一些初步的思考方向。\n" +
                        "5.  **议题二：[议题名称] (Topic 2):** (结构类似议题一，按需设置)\n" +
                        "6.  **开放讨论环节 (Open Discussion Session):** (如果适用) 针对特定议题或整体【" + escapedUserContent + "】进行自由讨论。\n" +
                        "7.  **决策与行动计划 (Decisions & Action Plan):**\n" +
                        "    a.  **形成的共识/决策:** 清晰记录会议中达成的关键决策。\n" +
                        "    b.  **后续行动项 (Action Items):** 明确每项行动的内容、负责人 (Owner)、截止日期 (Due Date)。\n" +
                        "8.  **下次会议安排/重要提醒 (Next Meeting/Important Reminders):** (如果适用)\n" +
                        "9.  **会议总结 (Meeting Wrap-up):** 简要回顾会议成果和关键行动项。\n" +
                        "【内容侧重点】: 围绕【" + escapedUserContent + "】和会议议程，**促进有效沟通、信息共享和团队协作。** 确保议题清晰，讨论聚焦，行动明确。\n" +
                        "【语言风格】: 内部沟通，可适当轻松但仍需专业。**鼓励参与，注重效率。** 清晰、简洁、直接。\n" +
                        "【章节数量】: 根据议题数量灵活调整，重点是议题的清晰呈现和行动项的明确。";
                break;

            case "培训材料":
            case "training materials": // 区别于“教学课件”可能更偏向技能或流程培训
                instruction = "当前PPT大纲用于制作关于【" + escapedUserContent + "】的培训材料。\n" +
                        "【培训材料场景核心要素与结构建议】:\n" +
                        "1.  **培训标题与目标学员 (Training Title & Target Audience):** 清晰说明培训主题【" + escapedUserContent + "】和主要面向的学员群体。\n" +
                        "2.  **培训目标 (Training Objectives - Action-Oriented):** 学员在培训结束后应该**能够做什么**（知识、技能、态度上的具体改变）。使用行为动词描述。\n" +
                        "3.  **培训议程/模块划分 (Training Agenda/Module Breakdown):** 清晰列出培训的主要内容模块和时间安排。\n" +
                        "4.  **开场与破冰 (Opening & Icebreaker):** (如果适用) 欢迎学员，介绍培训师，进行简单的互动环节以活跃气氛。\n" +
                        "5.  **模块一：[模块名称 - 理论基础/背景知识] (Module 1: [Topic - Foundational Theory/Background]):**\n" +
                        "    a.  **核心概念与定义 (Key Concepts & Definitions related to 【" + escapedUserContent + "】)**\n" +
                        "    b.  **重要性与应用场景 (Importance & Application Scenarios)**\n" +
                        "    c.  **相关原则/框架 (Relevant Principles/Frameworks)**\n" +
                        "6.  **模块二：[模块名称 - 技能讲解/流程演示] (Module 2: [Topic - Skill Explanation/Process Demonstration]):**\n" +
                        "    a.  **步骤详解 (Step-by-Step Breakdown of 【" + escapedUserContent + "】)**\n" +
                        "    b.  **操作演示/案例分析 (Live Demo/Case Study Analysis)**\n" +
                        "    c.  **常见错误与注意事项 (Common Mistakes & Key Considerations)**\n" +
                        "7.  **模块三：[模块名称 - 实践与练习] (Module 3: [Topic - Practice & Exercises]):**\n" +
                        "    a.  **个人/小组练习任务 (Individual/Group Exercise Task related to 【" + escapedUserContent + "】)**\n" +
                        "    b.  **练习指导与反馈 (Guidance & Feedback on Exercises)**\n" +
                        "    c.  **讨论与分享 (Discussion & Sharing of Learnings)**\n" +
                        "8.  **(更多模块按需添加)**\n" +
                        "9.  **知识点回顾与总结 (Key Learnings Recap & Summary):** 总结本次培训的核心要点和技能。\n" +
                        "10. **评估与反馈 (Assessment & Feedback):** (如果适用) 简短的测试、问卷调查或反馈环节。\n" +
                        "11. **后续学习资源/支持 (Further Learning Resources/Support):** 提供相关文档、工具、联系人等。\n" +
                        "12. **结束语与致谢 (Closing Remarks & Acknowledgements):**\n" +
                        "【内容侧重点】: 围绕【" + escapedUserContent + "】的知识和技能传递，**强调实用性、互动性和学员的参与感。** 理论与实践相结合。\n" +
                        "【语言风格】: 清晰、易懂、鼓励性、互动性强。**多用案例、图示和练习。** 根据学员背景调整专业术语的使用。\n" +
                        "【章节数量】: 根据培训内容的多少和复杂度灵活设置，模块化结构是关键。";
                break;


            case "述职报告":
            case "performancereviewpresentation":
            case "debunking report": // "述职报告" is more about reporting duties/performance
                instruction = "当前PPT大纲用于准备一份关于【" + escapedUserContent + "】（通常指个人在某岗位/某时期）的述职报告。\n" +
                        "【述职报告场景核心要素与结构建议】:\n" +
                        "1.  **报告标题 (Report Title):** 例如“【姓名】-【岗位】-【" + escapedUserContent + "】（时期，如2023年度）述职报告”。\n" +
                        "2.  **个人基本情况与岗位职责回顾 (Basic Information & Role Responsibilities Recap):**\n" +
                        "    a.  **姓名、部门、岗位、述职周期。**\n" +
                        "    b.  **核心岗位职责与关键绩效指标 (KPIs) 概述。**\n" +
                        "3.  **主要工作业绩与贡献 (Key Accomplishments & Contributions):** (核心部分)\n" +
                        "    a.  **围绕【" + escapedUserContent + "】及岗位职责，分点阐述本期完成的主要工作任务。**\n" +
                        "    b.  **使用STAR原则 (Situation, Task, Action, Result) 描述关键项目或任务，** 突出个人在其中的行动和取得的具体成果（量化数据、实例、正面反馈）。\n" +
                        "    c.  **KPI达成情况分析：** 对照目标，说明各项KPI的完成度，分析超额完成或未达标的原因。\n" +
                        "    d.  **创新与改进：** 在工作中进行的创新尝试、流程优化、效率提升等。\n" +
                        "4.  **能力展现与提升 (Competency Demonstration & Development):**\n" +
                        "    a.  **核心能力在工作中的体现：** 结合实例说明如何运用专业技能、领导力、沟通协调能力等解决问题、达成目标。\n" +
                        "    b.  **学习与成长：** 本期在知识、技能、经验方面的学习收获和个人成长。\n" +
                        "5.  **存在的问题与不足反思 (Self-Reflection on Challenges & Shortcomings):**\n" +
                        "    a.  **工作中遇到的主要困难与挑战。**\n" +
                        "    b.  **客观、诚恳地分析个人在工作中的不足之处、失误或待改进的方面。**\n" +
                        "    c.  **原因分析与经验教训。**\n" +
                        "6.  **未来工作规划与展望 (Future Work Plan & Outlook):**\n" +
                        "    a.  **下一阶段的工作目标与重点（与【" + escapedUserContent + "】和岗位发展相关）。**\n" +
                        "    b.  **针对不足之处的改进计划和学习发展计划。**\n" +
                        "    c.  **对团队/公司发展的建议（可选）。**\n" +
                        "7.  **总结与致谢 (Conclusion & Acknowledgements):** 简要总结，感谢领导、同事的支持。\n" +
                        "【内容侧重点】: 围绕【" + escapedUserContent + "】和个人岗位职责，**以事实和数据为依据，全面、客观地展示个人业绩、能力和不足，并体现反思与规划能力。** 突出亮点，坦诚不足。\n" +
                        "【语言风格】: **专业、客观、诚恳、条理清晰。** 既要肯定成绩，也要勇于反思。避免空泛和夸大。\n" +
                        "【章节数量】: 结构相对清晰，确保各方面内容都有所覆盖。";
                break;

            case "论文答辩":
            case "thesis defense":
            case "dissertation defense":
                instruction = "当前PPT大纲用于准备一次关于【" + escapedUserContent + "】（论文题目）的论文答辩。\n" +
                        "【论文答辩场景核心要素与结构建议】:\n" +
                        "1.  **标题页 (Title Slide):** 论文题目全称【" + escapedUserContent + "】，作者姓名，导师姓名，答辩日期，学校/院系。\n" +
                        "2.  **目录/答辩流程概述 (Outline/Defense Procedure Overview):** 简要介绍答辩的主要环节。\n" +
                        "3.  **研究背景与意义 (Research Background & Significance):**\n" +
                        "    a.  **引出研究问题:** 当前领域存在什么问题、空白或趋势，为何选择【" + escapedUserContent + "】作为研究主题。\n" +
                        "    b.  **理论意义与实践价值:** 本研究的学术贡献和实际应用前景。\n" +
                        "4.  **国内外研究现状/文献综述 (Literature Review/State-of-the-Art):**\n" +
                        "    a.  **关键文献回顾:** 简要介绍与【" + escapedUserContent + "】相关的核心前人研究。\n" +
                        "    b.  **点明研究缺口/本文切入点:** 基于文献综述，明确本研究的创新之处或解决的关键问题。\n" +
                        "5.  **研究思路/框架与研究方法 (Research Framework & Methodology):**\n" +
                        "    a.  **总体研究思路/逻辑框架图:** 清晰展示研究的整体设计。\n" +
                        "    b.  **研究对象与样本选择:** (如果适用)\n" +
                        "    c.  **采用的具体研究方法:** (如：实验法、调查法、案例分析法、模型构建与仿真等) 简述方法选择的理由。\n" +
                        "    d.  **数据收集与处理过程:**\n" +
                        "6.  **核心研究内容与主要发现/结果 (Core Research Content & Key Findings/Results):** (这是主体部分，可拆分为多个章节)\n" +
                        "    a.  **章节/模块一：[研究内容点1]** - 详细阐述【" + escapedUserContent + "】的第一个核心研究方面，展示关键数据、图表、模型、分析过程。\n" +
                        "    b.  **章节/模块二：[研究内容点2]** - (类似结构)\n" +
                        "    c.  **突出主要的研究结果和发现。**\n" +
                        "7.  **讨论与分析 (Discussion & Analysis):**\n" +
                        "    a.  **对研究结果的深入解读与解释。**\n" +
                        "    b.  **与前人研究的比较与联系。**\n" +
                        "    c.  **研究结果的启示与含义。**\n" +
                        "8.  **结论与展望 (Conclusions & Future Work):**\n" +
                        "    a.  **主要研究结论总结:** 清晰、凝练地概括本研究的核心结论，回应研究问题。\n" +
                        "    b.  **研究创新点与贡献总结。**\n" +
                        "    c.  **研究不足与局限性。**\n" +
                        "    d.  **未来研究方向展望/建议。**\n" +
                        "9.  **致谢 (Acknowledgements):** 感谢导师、参与研究的同仁、提供帮助的机构和个人、以及家人朋友。\n" +
                        "10. **(Q&A 环节通常在PPT演示后进行，但可在心中预演)**\n" +
                        "【内容侧重点】: 围绕【" + escapedUserContent + "】（论文核心），清晰、准确、有逻辑地阐述研究工作的全过程和主要成果。**突出研究的创新性和学术价值。**\n" +
                        "【语言风格】: **学术、严谨、精炼、客观、逻辑性强。** 表达清晰，避免口语化。对图表和数据的解读要到位。\n" +
                        "【章节数量】: 严格按照学术论文的逻辑结构展开，确保每个关键部分都有充分阐述。";
                break;

            default: // Fallback for any unhandled or "default" scene
                instruction = "当前PPT大纲用于一个关于【" + escapedUserContent + "】的通用演示或汇报。\n";
                break;
        }
        return "【使用" + languageType + "语言," + themedScenes + "场景深度解析与指导】结合相关的网络搜索内容: :\n" + webSearchContentBuilder + instruction + "\n";
    }

    private Flux<String> chatQianfanModleToOutlineFlux(List<MessagesDto> messagesList, String model) {
        // 参数校验
        if (messagesList == null || messagesList.isEmpty()) {
            return Flux.error(new IllegalArgumentException("messagesList 不能为空"));
        }

        // 构建 JSON 请求体
        JsonObject requestBody = new JsonObject();
        requestBody.addProperty("model", model);
        requestBody.add("messages", gson.toJsonTree(messagesList));
        requestBody.addProperty("stream", true);

        JsonObject extraBody = new JsonObject();
        extraBody.addProperty("max_completion_tokens", 8192);
        requestBody.add("extra_body", extraBody);

        okhttp3.MediaType mediaType = okhttp3.MediaType.parse("application/json");

        // 构建 HTTP 请求
        RequestBody body = RequestBody.create(gson.toJson(requestBody), mediaType);
        Request request = new Request.Builder()
                .url("https://qianfan.baidubce.com/v2/chat/completions")
                .post(body)
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer " + BAIDU_API_BEARER_TOKEN)
                .build();

        String sessionId = snowflake.nextIdStr();
        String requestId = UUID.randomUUID().toString();
        String requestCacheKey = "request:" + requestId;
        return Flux.create(sink -> {

            Call call = HTTP_CLIENT.newCall(request);


            // 流式中断时的清理逻辑
            sink.onDispose(() -> {
                redisService.deleteObject(requestCacheKey);
                if (!call.isCanceled()) {
                    call.cancel();
                }
            });

            call.enqueue(new Callback() {
                @Override
                public void onFailure(@NotNull Call call, @NotNull IOException e) {
                    sink.error(e); // 请求失败时发送错误
                }

                @Override
                public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {
                    if (!response.isSuccessful()) {
                        sink.error(new IOException("请求失败: code=" + response.code()));
                        return;
                    }
                    redisService.setCacheObject(requestCacheKey, true, 5L, TimeUnit.MINUTES);


                    try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().byteStream(),
                            StandardCharsets.UTF_8))) {
                        String line;
                        StringBuilder content = new StringBuilder();
                        StringBuilder reasoningContent = new StringBuilder();

                        while ((line = reader.readLine()) != null && redisService.hasKey(requestCacheKey)) {
                            if (line.isEmpty()) {
                                continue;
                            }
                            // 解析流式数据
                            if (line.startsWith("data: ")) {
                                String jsonString = line.replaceFirst("data: ", "");
                                if (jsonString.equals("[DONE]")) break; // 结束标记

                                // 解析 JSON
                                JsonObject jsonObject = gson.fromJson(jsonString, JsonObject.class);
                                JsonArray choices = jsonObject.getAsJsonArray("choices");
                                if (choices == null || choices.isEmpty()) continue;

                                // 获取choices数组中的第一个元素
                                JsonObject firstChoice = choices.get(0).getAsJsonObject();

                                // 获取delta对象
                                JsonObject delta = firstChoice.getAsJsonObject("delta");

                                // 处理 content
                                if (delta.has("content") && !delta.get("content").isJsonNull()) {
                                    String contentValue = delta.get("content").getAsString();
                                    MessagesDto messageData = MessagesDto.builder()
                                            .role("assistant")
                                            .resType(ResTypeDto.ANSWER.getValue())
                                            .content(contentValue)
                                            .text(contentValue)
                                            .status("3")// 保证和ai ppt 的流式返回大纲内容一致
                                            .sessionId(sessionId)
                                            .requestId(requestId)
                                            .messageId(snowflake.nextIdStr()).build();
                                    sink.next(gson.toJson(messageData));
                                    content.append(contentValue);
                                }
                            }
                        }

                        // 完成流式响应
                        MessagesDto messageData = MessagesDto.builder()
                                .role("assistant")
                                .resType(ResTypeDto.FINISH.getValue())
                                .content("[DONE]")
                                .status("4") // 保证和ai ppt 的流式返回大纲内容一致
                                .sessionId(sessionId)
                                .requestId(requestId)
                                .messageId(snowflake.nextIdStr()).build();
                        sink.next(gson.toJson(messageData));
                        sink.complete();
                        redisService.deleteObject(requestCacheKey);
                    } catch (Exception e) {
                        log.error("流式处理异常: ", e);
                        sink.error(e);
                    } finally {
                        response.close(); // 确保资源释放
                    }
                }
            });
        });
    }

    private List<ReferencesDto> chatQianfanModelWebSearch(List<MessagesDto> messagesList, String model) {
        // 参数校验
        if (messagesList == null || messagesList.isEmpty()) {
            log.error("messagesList 不能为空");
            return new ArrayList<>();
        }

        // 构建 JSON 请求体
        JsonObject requestBody = new JsonObject();
        requestBody.add("messages", gson.toJsonTree(messagesList));
        requestBody.addProperty("search_mode", "required");
        requestBody.addProperty("stream", true);
        requestBody.addProperty("instruction", "##");
        requestBody.addProperty("enable_corner_markers", false);
        requestBody.addProperty("enable_deep_search", true);
        requestBody.addProperty("max_completion_tokens", 12288);
        JsonArray resourceTypeFilterArray = new JsonArray();
        JsonObject resourceType = new JsonObject();
        resourceType.addProperty("type", "web");
        resourceType.addProperty("top_k", 20);
        resourceTypeFilterArray.add(resourceType);

        requestBody.add("resource_type_filter", resourceTypeFilterArray);


        okhttp3.MediaType mediaType = okhttp3.MediaType.parse("application/json");

        // 构建 HTTP 请求
        RequestBody body = RequestBody.create(gson.toJson(requestBody), mediaType);
        Request request = new Request.Builder()
                .url("https://qianfan.baidubce.com/v2/ai_search/chat/completions")
                .post(body)
                .addHeader("Content-Type", "application/json")
                .addHeader("Authorization", "Bearer " + BAIDU_API_BEARER_TOKEN)
                .build();

        String sessionId = snowflake.nextIdStr();
        String chatSessionKey = "chat_session:" + sessionId;

        try (Response response = HTTP_CLIENT.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("请求失败: code = {}, message = {}", response.code(), response.message());
                return Collections.emptyList();
            }
            String responseBody;
            try (ResponseBody respBody = response.body()) {
                if (respBody == null || StringUtils.isBlank(responseBody = respBody.string())) {
                    log.error("响应体为空");
                    return Collections.emptyList();
                }
            }

            JsonObject jsonObject = JsonParser.parseString(responseBody).getAsJsonObject();
            JsonArray referencesArray = Optional.ofNullable(jsonObject.getAsJsonArray("references"))
                    .orElseGet(JsonArray::new);
            if (referencesArray.isEmpty()) {
                log.warn("references 字段为空");
                return Collections.emptyList();
            }
            return gson.fromJson(referencesArray, new TypeToken<List<ReferencesDto>>() {
            }.getType());

        } catch (Exception e) {
            log.error("请求或解析失败", e);
            return Collections.emptyList();
        }

    }


    @Override
    public TableDataInfo aiPptGetAiPptTemplates(AiPptTemplatesDto aiPptTemplateDto) {
        TableDataInfo tableDataInfo = new TableDataInfo();
        tableDataInfo.setRows(new ArrayList<>());
        tableDataInfo.setTotal(0L);

        String url = "https://open.docmee.cn/api/ppt/templates";
        String pptToken = aiPptTemplateDto.getPptToken();
//        String apiKey = aiPptTemplateDto.getPptToken();
//        if (StringUtils.isBlank(apiKey)) {
//            apiKey = configPrinterConfig.getAiPptApiKey();
//        }


        // 构建 JSON 请求体
        okhttp3.MediaType mediaType = okhttp3.MediaType.parse("application/json; charset=utf-8");
        JsonObject requestBodyJson = new JsonObject();
        requestBodyJson.addProperty("page", aiPptTemplateDto.getPage());
        requestBodyJson.addProperty("size", aiPptTemplateDto.getSize());
        requestBodyJson.addProperty("desc", true);
        requestBodyJson.add("filters",
                gson.toJsonTree(aiPptTemplateDto.getFilters())
        );
        JsonObject orderItem = new JsonObject();
        orderItem.addProperty("field", "createTime");
        orderItem.addProperty("desc", true);
        JsonArray ordersArray = new JsonArray();
        ordersArray.add(orderItem);
        requestBodyJson.add("orders", ordersArray);

        String requestBodyString = gson.toJson(requestBodyJson);
        RequestBody body = RequestBody.create(requestBodyString, mediaType);

        // 构建请求
        Request goRequest = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("token", pptToken)
                .addHeader("Content-Type", "application/json")
                .build();

        // 执行请求并处理响应
        try (Response response = HTTP_CLIENT.newCall(goRequest).execute()) {
            if (!response.isSuccessful()) {
                tableDataInfo.setCode(500);
                tableDataInfo.setMsg("请求错误");
                log.error("请求错误: {}", response);
                return tableDataInfo;
            }

            // 解析响应JSON获取数据
            assert response.body() != null;
            String responseBody = response.body().string();
            JsonObject jsonResponse = JsonParser.parseString(responseBody).getAsJsonObject();

            // 检查响应结构并获取数据
            if (jsonResponse.get("code").getAsInt() == 0) {
                tableDataInfo.setCode(200);
                tableDataInfo.setMsg("");
                // 提取data数组并序列化为JSON字符串
                JsonArray data = jsonResponse.getAsJsonArray("data");
                int total = jsonResponse.get("total").getAsInt();
                List<AiPptTemplatesDto.Template> rows = gson.fromJson(data, new TypeToken<List<AiPptTemplatesDto.Template>>() {
                }.getType());
                tableDataInfo.setTotal(total);
                tableDataInfo.setRows(rows);
                return tableDataInfo;
            } else {
                // 使用响应中的message字段返回错误信息
                tableDataInfo.setCode(500);
                tableDataInfo.setMsg("请求错误");
                log.error("请求错误: {}", responseBody);
                return tableDataInfo;
            }
        } catch (Exception e) {
            tableDataInfo.setCode(500);
            tableDataInfo.setMsg("请求错误");
            log.error("请求错误: {}", e.getMessage());
            return tableDataInfo;
        }
    }

    @Override
    public TableDataInfo aiPptGetRandomTemplates(AiPptTemplatesDto aiPptTemplateDto) {
        TableDataInfo tableDataInfo = new TableDataInfo();
        tableDataInfo.setRows(new ArrayList<>());
        tableDataInfo.setTotal(0L);

        String url = "https://open.docmee.cn/api/ppt/randomTemplates";

        String pptToken = aiPptTemplateDto.getPptToken();
//        if (StringUtils.isBlank(apiKey)) {
//            apiKey = configPrinterConfig.getAiPptApiKey();
//        }


        // 构建 JSON 请求体
        okhttp3.MediaType mediaType = okhttp3.MediaType.parse("application/json; charset=utf-8");
        JsonObject requestBodyJson = new JsonObject();
        requestBodyJson.addProperty("size", aiPptTemplateDto.getSize());
        requestBodyJson.add("filters",
                gson.toJsonTree(aiPptTemplateDto.getFilters())
        );
        JsonObject orderItem = new JsonObject();
        orderItem.addProperty("field", "createTime");
        orderItem.addProperty("desc", true);
        JsonArray ordersArray = new JsonArray();
        ordersArray.add(orderItem);
        requestBodyJson.add("orders", ordersArray);

        String requestBodyString = gson.toJson(requestBodyJson);
        RequestBody body = RequestBody.create(requestBodyString, mediaType);

        // 构建请求
        Request goRequest = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("token", pptToken)
                .addHeader("Content-Type", "application/json")
                .build();

        // 执行请求并处理响应
        try (Response response = HTTP_CLIENT.newCall(goRequest).execute()) {
            if (!response.isSuccessful()) {
                tableDataInfo.setCode(500);
                tableDataInfo.setMsg("请求错误");
                log.error("请求错误: {}", response.body());
                return tableDataInfo;
            }

            // 解析响应JSON获取数据
            assert response.body() != null;
            String responseBody = response.body().string();
            JsonObject jsonResponse = JsonParser.parseString(responseBody).getAsJsonObject();

            // 检查响应结构并获取数据
            if (jsonResponse.get("code").getAsInt() == 0) {
                tableDataInfo.setCode(200);
                tableDataInfo.setMsg("");
                // 提取data数组并序列化为JSON字符串
                JsonArray data = jsonResponse.getAsJsonArray("data");
//                int total = jsonResponse.get("total").getAsInt();
                List<AiPptTemplatesDto.Template> rows = gson.fromJson(data, new TypeToken<List<AiPptTemplatesDto.Template>>() {
                }.getType());
                tableDataInfo.setTotal(aiPptTemplateDto.getSize());
                tableDataInfo.setRows(rows);
                return tableDataInfo;
            } else {
                // 使用响应中的message字段返回错误信息
                tableDataInfo.setCode(500);
                tableDataInfo.setMsg("请求错误");
                log.error("请求错误: {}", responseBody);
                return tableDataInfo;
            }
        } catch (Exception e) {
            tableDataInfo.setCode(500);
            tableDataInfo.setMsg("请求错误");
            log.error("请求错误: {}", e.getMessage());
            return tableDataInfo;
        }
    }

    @Override
    public TableDataInfo aiPptListPptx(AiPptListPptxDto aiPptListPptxDto) {
        TableDataInfo tableDataInfo = new TableDataInfo();
        tableDataInfo.setRows(new ArrayList<>());
        tableDataInfo.setTotal(0L);

        String url = "https://open.docmee.cn/api/ppt/listPptx";

        String pptToken = aiPptListPptxDto.getPptToken();
//        if (StringUtils.isBlank(apiKey)) {
//            apiKey = configPrinterConfig.getAiPptApiKey();
//        }


        // 构建 JSON 请求体
        okhttp3.MediaType mediaType = okhttp3.MediaType.parse("application/json; charset=utf-8");
        JsonObject requestBodyJson = new JsonObject();
        requestBodyJson.addProperty("size", aiPptListPptxDto.getSize());
        requestBodyJson.addProperty("page", aiPptListPptxDto.getPage());

        JsonObject orderItem = new JsonObject();
        orderItem.addProperty("field", "createTime");
        orderItem.addProperty("desc", true);
        JsonArray ordersArray = new JsonArray();
        ordersArray.add(orderItem);

        requestBodyJson.add("orders", ordersArray);

        String requestBodyString = gson.toJson(requestBodyJson);
        RequestBody body = RequestBody.create(requestBodyString, mediaType);

        // 构建请求
        Request goRequest = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("token", pptToken)
                .addHeader("Content-Type", "application/json")
                .build();

        // 执行请求并处理响应
        try (Response response = HTTP_CLIENT.newCall(goRequest).execute()) {
            if (!response.isSuccessful()) {
                tableDataInfo.setCode(500);
                tableDataInfo.setMsg("请求错误");
                log.error("请求错误: {}", response.body());
                return tableDataInfo;
            }

            // 解析响应JSON获取数据
            assert response.body() != null;
            String responseBody = response.body().string();
            JsonObject jsonResponse = JsonParser.parseString(responseBody).getAsJsonObject();

            // 检查响应结构并获取数据
            if (jsonResponse.get("code").getAsInt() == 0) {
                tableDataInfo.setCode(200);
                tableDataInfo.setMsg("");
                tableDataInfo.setTotal(jsonResponse.get("total").getAsInt());
                // 提取data数组并序列化为JSON字符串
                JsonArray data = jsonResponse.getAsJsonArray("data");
//                int total = jsonResponse.get("total").getAsInt();
                List<AiPptTemplatesDto.Template> rows = gson.fromJson(data, new TypeToken<List<AiPptTemplatesDto.Template>>() {
                }.getType());
                tableDataInfo.setRows(rows);
                return tableDataInfo;
            } else {
                // 使用响应中的message字段返回错误信息
                tableDataInfo.setCode(500);
                tableDataInfo.setMsg("请求错误");
                log.error("请求错误: {}", responseBody);
                return tableDataInfo;
            }
        } catch (Exception e) {
            tableDataInfo.setCode(500);
            tableDataInfo.setMsg("请求错误");
            log.error("请求错误: {}", e.getMessage());
            return tableDataInfo;
        }
    }

    @Override
    public Map<String, Object> aiPptGeneratePptx(AiPptGeneratePptxDto aiPptGeneratePptxDto) {
        HashMap<String, Object> resMap = new HashMap<>();
        HashMap<String, Object> errorMap = new HashMap<>();

        String taskId = aiPptGeneratePptxDto.getId(); // 任务id
        String templateId = aiPptGeneratePptxDto.getTemplateId(); // 模板id
        String markdown = aiPptGeneratePptxDto.getMarkdown();// 大纲内容markdown
        String reporter = aiPptGeneratePptxDto.getReporter(); // 汇报人
        //        String pptToken = aiPptGeneratePptxDto.getPptToken();

        AiPptDto aiPptDto = redisService.getCacheObject(Constants.AI_PPT_TASK_ID + taskId);
        String pptToken = aiPptDto.getPptToken();
        String aiPptApiKey = aiPptDto.getAiPptApiKey();

        if (StringUtils.isEmpty(taskId)) {
            errorMap.put("error", "数据缺失,请重新尝试");
            log.error(String.valueOf(errorMap.get("error")));
            return errorMap;
        }

        if (StringUtils.isEmpty(templateId)) {
            errorMap.put("error", "模板id不能为空");
            log.error(String.valueOf(errorMap.get("error")));
            return errorMap;
        }

        if (StringUtils.isEmpty(markdown)) {
            errorMap.put("error", "大纲内容markdown不能为空");
            log.error(String.valueOf(errorMap.get("error")));
            return errorMap;
        }

        Map<String, String> ppTokenCacheMap = redisService.getCacheObject(Constants.AI_PPT_TOKEN_DATA + pptToken);
        String uid = ppTokenCacheMap.get("uid");
        if (StringUtils.isBlank(uid)) {
            errorMap.put("error", "数据缺失,请刷新后重新尝试");
            log.error(String.valueOf(errorMap.get("error")));
            return errorMap;
        }


        // 获取PPT生成限制配置
        LimitConfigInfoDto limitConfigInfoDto = getMaxLimitConfigInfo(uid);
        int limitTimeHour = limitConfigInfoDto.getDefaultGenerateLimitTimeHour();
        int maxLimit = limitConfigInfoDto.getDefaultGenerateLimit();
        log.info("限制PPT生成次数: {}, 限制时间: {}小时", maxLimit, limitTimeHour);

        Boolean canProceed = redissonLimitService.canProceed(Constants.AI_PPT_GENERATE_LIMIT,
                uid, maxLimit);

        if (!canProceed) {
            String limitInfo = redissonLimitService.tryAcquireMsg(Constants.AI_PPT_GENERATE_LIMIT, uid, maxLimit, limitTimeHour, TimeUnit.HOURS);
            errorMap.put("error", limitInfo);
            log.error(String.valueOf(errorMap.get("error")));
            return errorMap;
        }


        String url = "https://open.docmee.cn/api/ppt/v2/generatePptx";

        // 构建 JSON 请求体
        okhttp3.MediaType mediaType = okhttp3.MediaType.parse("application/json; charset=utf-8");
        JsonObject requestBodyJson = new JsonObject();
        requestBodyJson.addProperty("id", taskId);
        requestBodyJson.addProperty("templateId", templateId);
        requestBodyJson.addProperty("markdown", markdown);
        requestBodyJson.addProperty("reporter", reporter);

        String requestBodyString = gson.toJson(requestBodyJson);
        RequestBody body = RequestBody.create(requestBodyString, mediaType);

        // 构建请求
        Request goRequest = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("token", pptToken)
                .addHeader("Content-Type", "application/json")
                .build();
        // 执行请求并处理响应
        try (Response response = HTTP_CLIENT.newCall(goRequest).execute()) {
            if (!response.isSuccessful()) {
                errorMap.put("error", response.body());
                log.error("请求错误: {}", response.body());
                return errorMap;
            }

            // 解析响应JSON获取数据
            assert response.body() != null;
            String responseBody = response.body().string();
            JsonObject jsonResponse = JsonParser.parseString(responseBody).getAsJsonObject();

            // 检查响应结构并获取数据
            if (jsonResponse.get("code").getAsInt() == 0) {
                // 提取 data 对象
                JsonObject asJsonObject = jsonResponse.getAsJsonObject("data");

                // 获取 pptInfo 的 JsonElement
                JsonObject pptInfoObject = asJsonObject.getAsJsonObject().getAsJsonObject("pptInfo");

                if (pptInfoObject != null) {
                    if (pptInfoObject.has("id") && !pptInfoObject.get("id").isJsonNull()) {
                        resMap.put("id", pptInfoObject.get("id").getAsString());
                    }
                    if (pptInfoObject.has("subject") && !pptInfoObject.get("subject").isJsonNull()) {
                        resMap.put("subject", pptInfoObject.get("subject").getAsString());
                    }
                    if (pptInfoObject.has("coverUrl") && !pptInfoObject.get("coverUrl").isJsonNull()) {
                        resMap.put("coverUrl", pptInfoObject.get("coverUrl").getAsString());
                    }
                    if (pptInfoObject.has("templateId") && !pptInfoObject.get("templateId").isJsonNull()) {
                        resMap.put("templateId", pptInfoObject.get("templateId").getAsString());
                    }
                    if (pptInfoObject.has("pptxProperty") && !pptInfoObject.get("pptxProperty").isJsonNull()) {
                        resMap.put("pptxProperty", pptInfoObject.get("pptxProperty").getAsString());
                    }
                    if (pptInfoObject.has("userId") && !pptInfoObject.get("userId").isJsonNull()) {
                        resMap.put("userId", pptInfoObject.get("userId").getAsString());
                    }
                    if (pptInfoObject.has("userName") && !pptInfoObject.get("userName").isJsonNull()) {
                        resMap.put("userName", pptInfoObject.get("userName").getAsString());
                    }
                    if (pptInfoObject.has("companyId") && !pptInfoObject.get("companyId").isJsonNull()) {
                        resMap.put("companyId", pptInfoObject.get("companyId").getAsString());
                    }
                }

                // 处理数据记录
                if (!resMap.isEmpty()) {
                    PptPersonHistory pptPersonHistory = new PptPersonHistory();
                    if (resMap.containsKey("id")) {
                        pptPersonHistory.setPptId(String.valueOf(resMap.get("id")));
                    }
                    if (resMap.containsKey("subject")) {
                        pptPersonHistory.setSubject(String.valueOf(resMap.get("subject")));
                    }
                    if (resMap.containsKey("coverUrl")) {
                        pptPersonHistory.setCoverUrl(String.valueOf(resMap.get("coverUrl")));
                    }
                    if (resMap.containsKey("templateId")) {
                        pptPersonHistory.setTemplateId((String.valueOf(resMap.get("templateId"))));
                    }
                    if (resMap.containsKey("userId")) {
                        pptPersonHistory.setUserId(String.valueOf(resMap.get("userId")));
                    }
                    if (resMap.containsKey("userName")) {
                        pptPersonHistory.setUserName(String.valueOf(resMap.get("userName")));
                    }
                    if (resMap.containsKey("companyId")) {
                        pptPersonHistory.setCompanyId(String.valueOf(resMap.get("companyId")));
                    }

                    pptPersonHistory.setTaskId(taskId);
                    pptPersonHistory.setCreateBy(pptPersonHistory.getUserName());
                    pptPersonHistory.setOutlineMarkdown(markdown);
                    pptPersonHistory.setApiKey(aiPptApiKey);
                    threadPoolDealTask.submitTask(() -> {
                        pptPersonHistoryService.insertPptPersonHistory(pptPersonHistory);
                    });
                }
                redissonLimitService.markSuccess(Constants.AI_PPT_GENERATE_LIMIT, uid, limitTimeHour, TimeUnit.HOURS);
                // 返回结果
                return resMap;

            } else {
                // 使用响应中的message字段返回错误信息
                errorMap.put("error", "请求错误");
                log.error("请求错误: {}", responseBody);
                return errorMap;

            }
        } catch (Exception e) {
            errorMap.put("error", "请求错误");
            log.error("请求错误: {}", e.getMessage());
            return errorMap;
        }
    }

    private LimitConfigInfoDto getMaxLimitConfigInfo(String uid) {
        LimitConfigInfoDto limitConfigInfoDto = new LimitConfigInfoDto();
        if (StringUtils.isBlank(uid)) {
            limitConfigInfoDto.setDefaultGenerateLimit(0);
            limitConfigInfoDto.setDefaultGenerateLimitTimeHour(0);
            limitConfigInfoDto.setTemplateDefaultUploadLimit(0);
            limitConfigInfoDto.setTemplateDefaultUploadLimitTimeHour(0);
            return limitConfigInfoDto;
        }

        uid = uid.replace(configPrinterConfig.getCustomUidPrefix(), "");
        R<LoginUser> loginUserRes = remoteUserService.getUserInfo(uid, SecurityConstants.INNER);

        if (loginUserRes.getCode() != 200 || loginUserRes.getData() == null) {
            log.error("获取用户信息失败: {}", loginUserRes);
            // 出错也要兜底返回默认值
            limitConfigInfoDto.setDefaultGenerateLimit(Integer.parseInt(configPrinterConfig.getDefaultGenerateLimit()));
            limitConfigInfoDto.setDefaultGenerateLimitTimeHour(Integer.parseInt(configPrinterConfig.getDefaultGenerateLimitTimeHour()));
            limitConfigInfoDto.setTemplateDefaultUploadLimit(Integer.parseInt(configPrinterConfig.getTemplateDefaultUploadLimit()));
            limitConfigInfoDto.setTemplateDefaultUploadLimitTimeHour(Integer.parseInt(configPrinterConfig.getTemplateDefaultUploadLimitTimeHour()));
            return limitConfigInfoDto;
        }

        LoginUser data = loginUserRes.getData();
        Set<String> roles = data.getRoles();

        if (roles == null || roles.isEmpty()) {
            // 无角色时使用默认
            limitConfigInfoDto.setDefaultGenerateLimit(Integer.parseInt(configPrinterConfig.getDefaultGenerateLimit()));
            limitConfigInfoDto.setDefaultGenerateLimitTimeHour(Integer.parseInt(configPrinterConfig.getDefaultGenerateLimitTimeHour()));
            limitConfigInfoDto.setTemplateDefaultUploadLimit(Integer.parseInt(configPrinterConfig.getTemplateDefaultUploadLimit()));
            limitConfigInfoDto.setTemplateDefaultUploadLimitTimeHour(Integer.parseInt(configPrinterConfig.getTemplateDefaultUploadLimitTimeHour()));
            return limitConfigInfoDto;
        }

        Integer maxGenerateLimit = null;
        Integer maxGenerateHour = null;
        Integer maxUploadLimit = null;
        Integer maxUploadHour = null;

		// 角色是是否是 老师 或者是 学生(本科生 研究生)
		boolean isTeacher = roles.contains("teacher");
		if (isTeacher) {
			// 使用老师的单独配置 不影响下面的角色控制生成和上传
			maxGenerateLimit = Integer.parseInt(configPrinterConfig.getTeacherDefaultGenerateLimit());
			maxGenerateHour = Integer.parseInt(configPrinterConfig.getTeacherDefaultGenerateLimitTimeHour());
			maxUploadLimit = Integer.parseInt(configPrinterConfig.getTeacherTemplateDefaultUploadLimit());
			maxUploadHour = Integer.parseInt(configPrinterConfig.getTeacherTemplateDefaultUploadLimitTimeHour());
		}
		boolean isStudent = roles.contains("student") || roles.contains("graduate");
		if (isStudent) {
			// 使用学生的单独配置 不影响下面的角色控制生成和上传
			maxGenerateLimit = Integer.parseInt(configPrinterConfig.getStudentDefaultGenerateLimit());
			maxGenerateHour = Integer.parseInt(configPrinterConfig.getStudentDefaultGenerateLimitTimeHour());
			maxUploadLimit = Integer.parseInt(configPrinterConfig.getStudentTemplateDefaultUploadLimit());
			maxUploadHour = Integer.parseInt(configPrinterConfig.getStudentTemplateDefaultUploadLimitTimeHour());
		}
		if (isTeacher && isStudent) {
			// 角色是老师也是学生学生时使用最大的配置
			maxGenerateLimit = Math.max(maxGenerateLimit, Integer.parseInt(configPrinterConfig.getTeacherDefaultGenerateLimit()));
			maxGenerateHour = Math.max(maxGenerateHour, Integer.parseInt(configPrinterConfig.getTeacherDefaultGenerateLimitTimeHour()));
			maxUploadLimit = Math.max(maxUploadLimit, Integer.parseInt(configPrinterConfig.getTeacherTemplateDefaultUploadLimit()));
			maxUploadHour = Math.max(maxUploadHour, Integer.parseInt(configPrinterConfig.getTeacherTemplateDefaultUploadLimitTimeHour()));
			maxGenerateLimit = Math.max(maxGenerateLimit, Integer.parseInt(configPrinterConfig.getStudentDefaultGenerateLimit()));
		}

		// 继续处理 特定字符的 角色控制生成和上传
        Pattern generatePattern = Pattern.compile("^generate-ppt-limit-(\\d+)(?:-(\\d+))?$");
        Pattern uploadPattern = Pattern.compile("^upload-template-limit-(\\d+)(?:-(\\d+))?$");

        for (String role : roles) {
            if (StringUtils.isBlank(role)) continue;

            role = role.trim();

            Matcher generateMatcher = generatePattern.matcher(role);
            if (generateMatcher.matches()) {
                try {
                    int limit = Integer.parseInt(generateMatcher.group(1));
                    maxGenerateLimit = (maxGenerateLimit == null) ? limit : Math.max(maxGenerateLimit, limit);

                    if (generateMatcher.group(2) != null) {
                        int hour = Integer.parseInt(generateMatcher.group(2));
                        maxGenerateHour = (maxGenerateHour == null) ? hour : Math.max(maxGenerateHour, hour);
                    }
                } catch (NumberFormatException ignored) {
                }
                continue;
            }

            Matcher uploadMatcher = uploadPattern.matcher(role);
            if (uploadMatcher.matches()) {
                try {
                    int limit = Integer.parseInt(uploadMatcher.group(1));
                    maxUploadLimit = (maxUploadLimit == null) ? limit : Math.max(maxUploadLimit, limit);

                    if (uploadMatcher.group(2) != null) {
                        int hour = Integer.parseInt(uploadMatcher.group(2));
                        maxUploadHour = (maxUploadHour == null) ? hour : Math.max(maxUploadHour, hour);
                    }
                } catch (NumberFormatException ignored) {
                }
            }
        }

        // 只在未匹配到任何配置时使用默认值
        limitConfigInfoDto.setDefaultGenerateLimit(
                maxGenerateLimit != null ? maxGenerateLimit : Integer.parseInt(configPrinterConfig.getDefaultGenerateLimit())
        );
        limitConfigInfoDto.setDefaultGenerateLimitTimeHour(
                maxGenerateHour != null ? maxGenerateHour : Integer.parseInt(configPrinterConfig.getDefaultGenerateLimitTimeHour())
        );
        limitConfigInfoDto.setTemplateDefaultUploadLimit(
                maxUploadLimit != null ? maxUploadLimit : Integer.parseInt(configPrinterConfig.getTemplateDefaultUploadLimit())
        );
        limitConfigInfoDto.setTemplateDefaultUploadLimitTimeHour(
                maxUploadHour != null ? maxUploadHour : Integer.parseInt(configPrinterConfig.getTemplateDefaultUploadLimitTimeHour())
        );

        return limitConfigInfoDto;
    }


    @Override
    public String aiPptGeneratePptxProgress(AiPptGeneratePptxDto aiPptGeneratePptxDto) {
        String taskId = aiPptGeneratePptxDto.getId();
        PptPersonHistory pptPersonHistory = pptPersonHistoryService.getOneByTaskId(taskId);
        if (pptPersonHistory == null) {
            return "false";
        } else {
            return "true";
        }
    }

    @Override
    public List<Map<String, String>> getSceneOptions() {
        List<Object> dictCacheObjectList = redisService.getCacheObject("sys_dict:ai_ppt_scene_options");
        // 非空判断
        if (CollectionUtils.isEmpty(dictCacheObjectList)) {
            log.warn("sys_dict:ai_ppt_scene_options 字典数据为空");
            return new ArrayList<>();
        }
        // 通过流处理objet类型数据 转成 相应实体数据 并过滤出 status=0的正常数据
        List<SysDictData> dictCacheList = dictCacheObjectList.stream()
                .map(obj -> JSON.parseObject(JSON.toJSONString(obj), SysDictData.class))
                .filter(obj -> StringUtils.equals("0", obj.getStatus()))
                .collect(Collectors.toList());

        return dictCacheList.stream()
                .map(dictData -> {
                    HashMap<String, String> map = new HashMap<>();
                    map.put("label", dictData.getDictLabel());
                    map.put("value", dictData.getDictValue());
                    return map;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<Map<String, String>> getOutlineLanguage() {
        List<Object> dictCacheObjectList = redisService.getCacheObject("sys_dict:out_line_language");
        // 非空判断
        if (CollectionUtils.isEmpty(dictCacheObjectList)) {
            log.warn("sys_dict:out_line_language字典数据为空");
            return new ArrayList<>();
        }
        // 通过流处理objet类型数据 转成 相应实体数据 并过滤出 status=0的正常数据
        List<SysDictData> dictCacheList = dictCacheObjectList.stream()
                .map(obj -> JSON.parseObject(JSON.toJSONString(obj), SysDictData.class))
                .filter(obj -> StringUtils.equals("0", obj.getStatus()))
                .collect(Collectors.toList());

        return dictCacheList.stream()
                .map(dictData -> {
                    HashMap<String, String> map = new HashMap<>();
                    map.put("label", dictData.getDictLabel());
                    map.put("value", dictData.getDictValue());
                    return map;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<Map<String, String>> getImgStyleOptions() {
        List<Object> dictCacheObjectList = redisService.getCacheObject("sys_dict:qianfan_img_style_options");
        // 非空判断
        if (CollectionUtils.isEmpty(dictCacheObjectList)) {
            log.warn("sys_dict:qianfan_img_style_options字典数据为空");
            return new ArrayList<>();
        }
        // 通过流处理objet类型数据 转成 相应实体数据 并过滤出 status=0的正常数据
        List<SysDictData> dictCacheList = dictCacheObjectList.stream()
                .map(obj -> JSON.parseObject(JSON.toJSONString(obj), SysDictData.class))
                .filter(obj -> StringUtils.equals("0", obj.getStatus()))
                .collect(Collectors.toList());

        return dictCacheList.stream()
                .map(dictData -> {
                    HashMap<String, String> map = new HashMap<>();
                    map.put("label", dictData.getDictLabel());
                    map.put("value", dictData.getDictValue());
                    return map;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<Map<String, String>> getPptEditData() {
        String pptEditUrl = configPrinterConfig.getPptEditUrl();
        return new ArrayList<Map<String, String>>() {{
            add(new HashMap<String, String>() {{
                put("label", "editUrl");
                put("value", pptEditUrl);
            }});
        }};
    }

    @Override
    public List<Map<String, String>> getQianfanChatModel() {
        List<Object> dictCacheObjectList = redisService.getCacheObject("sys_dict:qianfan_chat_model");
        // 非空判断
        if (CollectionUtils.isEmpty(dictCacheObjectList)) {
            log.warn("sys_dict:qianfan_chat_model字典数据为空");
            return new ArrayList<>();
        }
        // 通过流处理objet类型数据 转成 相应实体数据 并过滤出 status=0的正常数据
        List<SysDictData> dictCacheList = dictCacheObjectList.stream()
                .map(obj -> JSON.parseObject(JSON.toJSONString(obj), SysDictData.class))
                .filter(obj -> StringUtils.equals("0", obj.getStatus()))
                .collect(Collectors.toList());

        return dictCacheList.stream()
                .map(dictData -> {
                    HashMap<String, String> map = new HashMap<>();
                    map.put("label", dictData.getDictLabel());
                    map.put("value", dictData.getDictValue());
                    return map;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<Map<String, String>> getCasEnter() {
        List<Object> dictCacheObjectList = redisService.getCacheObject("sys_dict:sso_login_parameters");
        // 非空判断
        if (CollectionUtils.isEmpty(dictCacheObjectList)) {
            log.warn("sys_dict:sso_login_parameters");
            return new ArrayList<>();
        }
        // 通过流处理objet类型数据 转成 相应实体数据 并过滤出 status=0的正常数据
        List<SysDictData> dictCacheList = dictCacheObjectList.stream()
                .map(obj -> JSON.parseObject(JSON.toJSONString(obj), SysDictData.class))
                .filter(obj -> StringUtils.equals("0", obj.getStatus()))
                .collect(Collectors.toList());
        List<Map<String, String>> mapList = new ArrayList<>();
        for (SysDictData sysDictData : dictCacheList) {
            HashMap<String, String> map = new HashMap<>();
            if ("casEnter".equals(sysDictData.getDictLabel())) {
                map.put("label", sysDictData.getDictLabel());
                map.put("value", sysDictData.getDictValue());
                mapList.add(map);
            }
        }
        return mapList;
    }

    @Override
    public String aiPptMarkerAddr() throws NoSuchAlgorithmException, InvalidKeySpecException {
        String rsaPublicKey = "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAIhj5z02jAte6sdtOg8kar+9dHjKh7X8NpG/Fie5FtgtohmOHifK+7aFWNz/G8H4aotGJnLB9mjHCj/hlo1MiUECAwEAAQ==";
        String rsaPrivateKey = " MIIBUwIBADANBgkqhkiG9w0BAQEFAASCAT0wggE5AgEAAkEAiGPnPTaMC17qx206DyRqv710eMqHtfw2kb8WJ7kW2C2iGY4eJ8r7toVY3P8bwfhqi0YmcsH2aMcKP+GWjUyJQQIDAQABAkAsnXGBBpzdA1mNjt9VILaCrjixYp7uGujG2Fko+VTNtfVR3unbYoVs7VUDgtxqobATPhEsyWKrylJtR2RH4yqhAiEAxCoW5wEb34gYBFAdfNfQVCPkKW5RpSq3qfv8MkZK93UCIQCx/jj0xvUHgWMPtb8QLE+OsMXindr17KG0GURzWyVdHQIgYXCu06yfVdfaZjoNi7gACeuSKGpeL/caKFu9GJQGJQkCIGPZFOJbacEYGBYMAm50LP0CtT93BgnHAL8jHX8ItmERAiB5A+CypQfXKSX4SJQ7NiABAcAwxZCNG8zaW9WAdHGt/Q==";
        String apiKey = configPrinterConfig.getAiPptApiKey();

        // 解析公钥字符串为 RSAPublicKey 对象
        RSAPublicKey publicKey = RSAUtils.getPublicKey(rsaPublicKey);
        // 使用公钥进行加密
        return RSAUtils.publicEncrypt(apiKey, publicKey);
    }

    @Override
    public Boolean aiPptUpdatePptxAttr(AiPptDto aiPptDto) {
        String name = aiPptDto.getName();
        String id = aiPptDto.getId();
        String subject = aiPptDto.getSubject();
        String pptToken = aiPptDto.getPptToken();

        String url = "https://open.docmee.cn/api/ppt/updatePptxAttr";


        // 构建 JSON 请求体
        okhttp3.MediaType mediaType = okhttp3.MediaType.parse("application/json; charset=utf-8");
        JsonObject requestBodyJson = new JsonObject();
        requestBodyJson.addProperty("id", id);
        requestBodyJson.addProperty("name", name);
        requestBodyJson.addProperty("subject", subject);

        String requestBodyString = gson.toJson(requestBodyJson);
        RequestBody body = RequestBody.create(requestBodyString, mediaType);

        // 构建请求
        Request goRequest = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("token", pptToken)
                .addHeader("Content-Type", "application/json")
                .build();

        // 执行请求并处理响应
        try (Response response = HTTP_CLIENT.newCall(goRequest).execute()) {
            // 解析响应JSON获取数据
            assert response.body() != null;
            String responseBody = response.body().string();
            JsonObject jsonResponse = JsonParser.parseString(responseBody).getAsJsonObject();
            // 检查响应结构并获取数据
            return jsonResponse.get("code").getAsInt() == 0;
        } catch (Exception e) {
            log.error("请求错误: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public Boolean aiPptTemplatesNameUpdate(AiPptTemplatesDto templateDto) {
        String name = templateDto.getName();
        String id = templateDto.getId();
        String pptToken = templateDto.getPptToken();
        if (StringUtils.isBlank(pptToken)) {
            pptToken = configPrinterConfig.getAiPptApiKey();
        }
        String url = "https://open.docmee.cn/api/ppt/updateTemplate";


        // 构建 JSON 请求体
        okhttp3.MediaType mediaType = okhttp3.MediaType.parse("application/json; charset=utf-8");
        JsonObject requestBodyJson = new JsonObject();
        requestBodyJson.addProperty("id", id);
        requestBodyJson.addProperty("name", name);

        String requestBodyString = gson.toJson(requestBodyJson);
        RequestBody body = RequestBody.create(requestBodyString, mediaType);

        // 构建请求
        Request goRequest = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("Api-Key", pptToken)
                .addHeader("Content-Type", "application/json")
                .build();

        // 执行请求并处理响应
        try (Response response = HTTP_CLIENT.newCall(goRequest).execute()) {
            // 解析响应JSON获取数据
            assert response.body() != null;
            String responseBody = response.body().string();
            JsonObject jsonResponse = JsonParser.parseString(responseBody).getAsJsonObject();
            // 检查响应结构并获取数据
            return jsonResponse.get("code").getAsInt() == 0;
        } catch (Exception e) {
            log.error("请求错误: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public Map<String, String> aiPptDelete(AiPptListPptxDto aiPptListPptxDto) {
        HashMap<String, String> errorMap = new HashMap<>();
        HashMap<String, String> resMap = new HashMap<>();

        String id = aiPptListPptxDto.getId();
        String pptToken = aiPptListPptxDto.getPptToken();
//        String apiKey = configPrinterConfig.getAiPptApiKey();
        String url = "https://open.docmee.cn/api/ppt/delete";


        // 构建 JSON 请求体
        okhttp3.MediaType mediaType = okhttp3.MediaType.parse("application/json; charset=utf-8");
        JsonObject requestBodyJson = new JsonObject();
        requestBodyJson.addProperty("id", id);

        String requestBodyString = gson.toJson(requestBodyJson);
        RequestBody body = RequestBody.create(requestBodyString, mediaType);

        // 构建请求
        Request goRequest = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("token", pptToken)
                .addHeader("Content-Type", "application/json")
                .build();

        // 执行请求并处理响应
        try (Response response = HTTP_CLIENT.newCall(goRequest).execute()) {
            // 解析响应JSON获取数据
            assert response.body() != null;
            String responseBody = response.body().string();
            JsonObject jsonResponse = JsonParser.parseString(responseBody).getAsJsonObject();

            // 检查响应结构并获取数据
            if (jsonResponse.get("code").getAsInt() == 0) {

                return resMap;
            } else {
                // 使用响应中的message字段返回错误信息
                errorMap.put("error_msg", "请求错误");
                log.error("请求错误: {}", responseBody);
                return errorMap;
            }
        } catch (Exception e) {
            log.error("请求错误: {}", e.getMessage());
            errorMap.put("error_msg", "请求错误");
            return errorMap;
        }
    }

    @Override
    public Map<String, String> aiPptDelTemplateId(AiPptTemplateDto aiPptTemplateDto) {
        HashMap<String, String> errorMap = new HashMap<>();
        HashMap<String, String> resMap = new HashMap<>();

        String id = aiPptTemplateDto.getId();
        String pptToken = aiPptTemplateDto.getPptToken();
//        String apiKey = configPrinterConfig.getAiPptApiKey();
        String url = "https://open.docmee.cn/api/ppt/delTemplateId";


        // 构建 JSON 请求体
        okhttp3.MediaType mediaType = okhttp3.MediaType.parse("application/json; charset=utf-8");
        JsonObject requestBodyJson = new JsonObject();
        requestBodyJson.addProperty("id", id);

        String requestBodyString = gson.toJson(requestBodyJson);
        RequestBody body = RequestBody.create(requestBodyString, mediaType);

        // 构建请求
        Request goRequest = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("token", pptToken)
                .addHeader("Content-Type", "application/json")
                .build();

        // 执行请求并处理响应
        try (Response response = HTTP_CLIENT.newCall(goRequest).execute()) {
            // 解析响应JSON获取数据
            assert response.body() != null;
            String responseBody = response.body().string();
            JsonObject jsonResponse = JsonParser.parseString(responseBody).getAsJsonObject();

            // 检查响应结构并获取数据
            if (jsonResponse.get("code").getAsInt() == 0) {
                return resMap;
            } else {
                // 使用响应中的message字段返回错误信息
                errorMap.put("error_msg", "请求错误");
                log.error("请求错误: {}", responseBody);
                return errorMap;
            }
        } catch (Exception e) {
            log.error("请求错误: {}", e.getMessage());
            errorMap.put("error_msg", "请求错误");
            return errorMap;
        }
    }

    @Override
    public Map<String, String> aiPptDownloadTemplate(AiPptTemplateDto aiPptTemplateDto) {
        HashMap<String, String> errorMap = new HashMap<>();
        HashMap<String, String> resMap = new HashMap<>();

        String id = aiPptTemplateDto.getId();
        String pptToken = aiPptTemplateDto.getPptToken();
//        String apiKey = configPrinterConfig.getAiPptApiKey();
        String url = "https://open.docmee.cn/api/ppt/downloadTemplate";


        // 构建 JSON 请求体
        okhttp3.MediaType mediaType = okhttp3.MediaType.parse("application/json; charset=utf-8");
        JsonObject requestBodyJson = new JsonObject();
        requestBodyJson.addProperty("id", id);

        String requestBodyString = gson.toJson(requestBodyJson);
        RequestBody body = RequestBody.create(requestBodyString, mediaType);

        // 构建请求
        Request goRequest = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("token", pptToken)
                .addHeader("Content-Type", "application/json")
                .build();

        // 执行请求并处理响应
        try (Response response = HTTP_CLIENT.newCall(goRequest).execute()) {
            // 解析响应JSON获取数据
            assert response.body() != null;
            String responseBody = response.body().string();
            JsonObject jsonResponse = JsonParser.parseString(responseBody).getAsJsonObject();

            // 检查响应结构并获取数据
            if (jsonResponse.get("code").getAsInt() == 0) {
                JsonObject asJsonObject = jsonResponse.get("data").getAsJsonObject();
                resMap = gson.fromJson(asJsonObject, new TypeToken<HashMap<String, String>>() {
                }.getType());
                return resMap;
            } else {
                // 使用响应中的message字段返回错误信息
                errorMap.put("error_msg", "请求错误");
                log.error("请求错误: {}", responseBody);
                return errorMap;
            }
        } catch (Exception e) {
            log.error("请求错误: {}", e.getMessage());
            errorMap.put("error_msg", "请求错误");
            return errorMap;
        }
    }

    @Override
    public Map<String, String> aIPpTUploadTemplate(AiPptTemplateDto aiPptTemplateDto, MultipartFile file) {
        Map<String, String> resultMap = new HashMap<>();
        Map<String, String> errorMap = new HashMap<>();


        String pptToken = aiPptTemplateDto.getPptToken();

        Map<String, String> cacheMap = redisService.getCacheObject(Constants.AI_PPT_TOKEN_DATA + pptToken);
        if (cacheMap == null) {
            errorMap.put("error", "请检查pptToken是否存在且有效");
            log.error(String.valueOf(errorMap.get("error")));
            return errorMap;
        }

        String uid = cacheMap.get("uid");
        log.info("命中缓存 uid: {}", uid);


        // 限制自定义模板上传次数
        LimitConfigInfoDto limitConfigInfoDto = getMaxLimitConfigInfo(uid);
        int limitTimeHour = limitConfigInfoDto.getTemplateDefaultUploadLimitTimeHour();
        int maxLimit = limitConfigInfoDto.getTemplateDefaultUploadLimit();
        log.info("限制自定义模板上传次数次数: {}, 限制时间: {}小时", maxLimit, limitTimeHour);

        Boolean canProceed = redissonLimitService.canProceed(Constants.AI_PPT_UPLOAD_TEMPLATE_LIMIT,
                uid, maxLimit);

        if (!canProceed) {
            String limitInfo = redissonLimitService.tryAcquireMsg(Constants.AI_PPT_UPLOAD_TEMPLATE_LIMIT, uid, maxLimit, limitTimeHour, TimeUnit.HOURS);
            errorMap.put("error", limitInfo);
            log.error(String.valueOf(errorMap.get("error")));
            return errorMap;
        }

        // 验证文件
        if (file != null && !file.isEmpty()) {
            // 验证文件格式
            if (!isValidFileExtension(file.getOriginalFilename())) {
                errorMap.put("error", "Unsupported file format. Allowed formats: " + String.join(", ", ALLOWED_FILE_EXTENSIONS));
                log.error(String.valueOf(errorMap.get("error")));
                return errorMap;
            }

            long MAX_TOTAL_FILE_SIZE = 50 * 1024 * 1024; // 50MB in bytes
            // 验证文件大小
            if (file.getSize() > MAX_TOTAL_FILE_SIZE) {
                errorMap.put("error", "Total file size exceeds 50MB limit.");
                log.error(String.valueOf(errorMap.get("error")));
                return errorMap;
            }
        }

        int type = 4; // 4用户模板
        // 构建multipart请求
        String url = "https://open.docmee.cn/api/ppt/uploadTemplate";

        MultipartBody.Builder builder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("type", String.valueOf(type));

        if (StringUtils.isNotBlank(aiPptTemplateDto.getId())) {
            builder.addFormDataPart("templateId", aiPptTemplateDto.getId());
        }

        if (file != null && !file.isEmpty()) {
            try {
                RequestBody fileBody = RequestBody.create(
                        file.getBytes(),
                        okhttp3.MediaType.parse("application/octet-stream")
                );
                builder.addFormDataPart("file", file.getOriginalFilename(), fileBody);
            } catch (IOException e) {
                errorMap.put("error", "Failed to process file: " + e.getMessage());
                log.error(String.valueOf(errorMap.get("error")));
                return errorMap;
            }
        }

        Request request = new Request.Builder()
                .url(url)
                .post(builder.build())
                .addHeader("token", pptToken)
                .build();

        try (Response response = HTTP_CLIENT.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                errorMap.put("error", "Request failed with status code: " + response.code());
                log.error(String.valueOf(errorMap.get("error")));
                return errorMap;
            }

            assert response.body() != null;
            String responseBody = response.body().string();
            JsonObject jsonResponse = JsonParser.parseString(responseBody).getAsJsonObject();
            if (jsonResponse.get("code").getAsInt() == 0) {
                JsonObject asJsonObject = jsonResponse.get("data").getAsJsonObject();
                resultMap = gson.fromJson(asJsonObject, new TypeToken<Map<String, String>>() {
                }.getType());

                redissonLimitService.markSuccess(Constants.AI_PPT_UPLOAD_TEMPLATE_LIMIT, uid, limitTimeHour, TimeUnit.HOURS);

                // 存储数据
                PptPersonTemplate pptPersonTemplate = new PptPersonTemplate();
                pptPersonTemplate.setTemplateId(resultMap.get("id"));
                pptPersonTemplate.setType(String.valueOf(type));
                pptPersonTemplate.setCoverUrl(resultMap.get("coverUrl"));
                pptPersonTemplate.setFileUrl(resultMap.get("fileUrl"));
                pptPersonTemplate.setPptxProperty(resultMap.get("pptxProperty"));
                pptPersonTemplate.setCategory(resultMap.get("category"));
                pptPersonTemplate.setStyle(resultMap.get("style"));
                pptPersonTemplate.setThemeColor(resultMap.get("themeColor"));
                pptPersonTemplate.setSubject(resultMap.get("subject"));
                pptPersonTemplate.setNum(Long.parseLong(resultMap.get("num")));
                pptPersonTemplate.setUserId(resultMap.get("userId"));

                pptPersonTemplate.setPublicly("0");

                Map<String, String> tokenCacheMap = redisService.getCacheObject(Constants.AI_PPT_TOKEN_DATA + pptToken);
                pptPersonTemplate.setApiKey(tokenCacheMap.get("aiPptApiKey"));
                pptPersonTemplate.setUserName(tokenCacheMap.get("uid"));
                pptPersonTemplate.setCreateBy(tokenCacheMap.get("uid"));
                pptPersonTemplateService.insertPptPersonTemplate(pptPersonTemplate);
                return resultMap;
            } else {
                // 使用响应中的message字段返回错误信息
                errorMap.put("error", "请求错误");
                log.error("请求错误: {}", responseBody);
                return errorMap;
            }
        } catch (Exception e) {
            errorMap.put("error", "An error occurred while processing the request: " + e.getMessage());
            return errorMap;
        }
    }

    public String saveGeneratePptx(GeneratePptDto taskDto) {
        return "";
    }

    @Override
    public Map<String, String> aiPPtDownloadPptx(String id) {
        HashMap<String, String> errorMap = new HashMap<>();

        if (StringUtils.isEmpty(id)) {
            errorMap.put("error_msg", "参数缺失");
            log.error(String.valueOf(errorMap.get("error")));
            return errorMap;
        }

        String url = "https://open.docmee.cn/api/ppt/downloadPptx";
        String apiKey = configPrinterConfig.getAiPptApiKey();


        // 构建 JSON 请求体
        okhttp3.MediaType mediaType = okhttp3.MediaType.parse("application/json; charset=utf-8");
        JsonObject requestBodyJson = new JsonObject();
        requestBodyJson.addProperty("id", id);
        requestBodyJson.addProperty("refresh", false);

        String requestBodyString = gson.toJson(requestBodyJson);
        RequestBody body = RequestBody.create(requestBodyString, mediaType);

        // 构建请求
        Request goRequest = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("Api-Key", apiKey)
                .addHeader("Content-Type", "application/json")
                .build();

        // 执行请求并处理响应
        try (Response response = HTTP_CLIENT.newCall(goRequest).execute()) {
            if (!response.isSuccessful()) {
                log.error("请求错误: {}", response.body());
                errorMap.put("error_msg", "请求错误");
                return errorMap;
            }

            // 解析响应JSON获取数据
            assert response.body() != null;
            String responseBody = response.body().string();
            JsonObject jsonResponse = JsonParser.parseString(responseBody).getAsJsonObject();
            JsonObject asJsonObject = jsonResponse.getAsJsonObject("data");

            return gson.fromJson(asJsonObject, new TypeToken<Map<String, String>>() {
            }.getType());
        } catch (Exception e) {
            log.error("请求错误: {}", e.getMessage());
            errorMap.put("error_msg", "请求错误");
            return errorMap;
        }
    }

}
