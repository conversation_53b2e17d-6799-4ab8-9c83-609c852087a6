package com.ruoyi.ppt.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.ppt.domain.PptTemplateManagement;
import com.ruoyi.ppt.domain.PptTemplateManagementDetail;

/**
 * PPT模板（解压后的模板管理）详细Mapper接口
 *
 * <AUTHOR>
 * @date 2024-10-22
 */
public interface PptTemplateManagementDetailMapper extends BaseMapper<PptTemplateManagementDetail> {
    /**
     * 查询PPT模板（解压后的模板管理）详细
     *
     * @param id PPT模板（解压后的模板管理）详细主键
     * @return PPT模板（解压后的模板管理）详细
     */
    public PptTemplateManagementDetail selectPptTemplateManagementDetailById(Long id);

    /**
     * 查询PPT模板（解压后的模板管理）详细列表
     *
     * @param pptTemplateManagementDetail PPT模板（解压后的模板管理）详细
     * @return PPT模板（解压后的模板管理）详细集合
     */
    public List<PptTemplateManagementDetail> selectPptTemplateManagementDetailList(PptTemplateManagementDetail pptTemplateManagementDetail);

    /**
     * 新增PPT模板（解压后的模板管理）详细
     *
     * @param pptTemplateManagementDetail PPT模板（解压后的模板管理）详细
     * @return 结果
     */
    public int insertPptTemplateManagementDetail(PptTemplateManagementDetail pptTemplateManagementDetail);

    /**
     * 修改PPT模板（解压后的模板管理）详细
     *
     * @param pptTemplateManagementDetail PPT模板（解压后的模板管理）详细
     * @return 结果
     */
    public int updatePptTemplateManagementDetail(PptTemplateManagementDetail pptTemplateManagementDetail);

    /**
     * 删除PPT模板（解压后的模板管理）详细
     *
     * @param id PPT模板（解压后的模板管理）详细主键
     * @return 结果
     */
    public int deletePptTemplateManagementDetailById(Long id);

    /**
     * 批量删除PPT模板（解压后的模板管理）详细
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePptTemplateManagementDetailByIds(Long[] ids);
}
