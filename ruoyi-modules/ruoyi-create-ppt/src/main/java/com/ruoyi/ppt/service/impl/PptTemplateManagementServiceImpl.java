package com.ruoyi.ppt.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.ppt.config.ThreadPoolDealTask;
import com.ruoyi.ppt.config.ThreadPoolDealTask2;
import com.ruoyi.ppt.constant.Constants;
import com.ruoyi.ppt.domain.PptTemplateManagement;
import com.ruoyi.ppt.domain.PptTemplateManagementDetail;
import com.ruoyi.ppt.mapper.PptTemplateManagementMapper;
import com.ruoyi.ppt.service.IPptTemplateManagementService;
import com.ruoyi.ppt.utilsService.FileCustomUtils;
import com.ruoyi.ppt.utilsService.FilePathUrlBuilder;
import com.ruoyi.ppt.utilsService.Utils;
import com.ruoyi.ppt.utilsService.PPTToLongImage;
import com.ruoyi.system.api.RemoteFileService;
import com.ruoyi.system.api.domain.SysDictData;
import com.ruoyi.system.api.domain.UnZip;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.*;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * ppt模板（压缩包）管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-21
 */
@Slf4j
@Service
public class PptTemplateManagementServiceImpl extends ServiceImpl<PptTemplateManagementMapper, PptTemplateManagement> implements IPptTemplateManagementService {
    @Resource
    private PptTemplateManagementMapper pptTemplateManagementMapper;
    @Resource
    private RedisService redisService;
    @Resource
    private PptTemplateManagementDetailServiceImpl pptTemplateManagementDetailService;
    @Resource
    private FileCustomUtils fileCustomUtils;
    @Resource
    private RemoteFileService remoteFileService;
    @Resource
    private PPTToLongImage pptToLongImage;
    @Resource
    private ThreadPoolDealTask threadPoolDealTask;
    @Resource
    private ThreadPoolDealTask2 threadPoolDealTask2;
    @Resource
    private FilePathUrlBuilder filePathUrlBuilder;

    /**
     * 查询ppt模板（压缩包）管理
     *
     * @param id ppt模板管理主键
     * @return ppt模板管理
     */
    @Override
    public PptTemplateManagement selectPptTemplateManagementById(Long id) {
        return pptTemplateManagementMapper.selectPptTemplateManagementById(id);
    }

    /**
     * 查询ppt模板（压缩包）管理列表
     *
     * @param pptTemplateManagement ppt模板管理
     * @return ppt模板管理
     */
    @Override
    public List<PptTemplateManagement> selectPptTemplateManagementList(PptTemplateManagement pptTemplateManagement) {
        // 1. 查询所有符合条件的PptTemplateManagement记录
        List<PptTemplateManagement> templateList = pptTemplateManagementMapper.selectPptTemplateManagementList(pptTemplateManagement);

        // 判断非空
        if (templateList == null || templateList.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 2.txt. 查询所有相关的PptTemplateManagementDetail，使用条件拼接查询避免重复查询
        LambdaQueryWrapper<PptTemplateManagementDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .select(PptTemplateManagementDetail::getTemplateManagementId,
                        PptTemplateManagementDetail::getContentType,
                        PptTemplateManagementDetail::getPreviews)
                .in(PptTemplateManagementDetail::getTemplateManagementId,
                        templateList.stream().map(PptTemplateManagement::getId).collect(Collectors.toList()));
        List<PptTemplateManagementDetail> detailList = pptTemplateManagementDetailService.list(queryWrapper);


        
        // 3. 创建一个Map，方便根据pptTemplateId快速找到对应的details
        Map<Long, Set<String>> previewMap = dealPreviews(detailList);


        // 4. 为每个PptTemplateManagement添加预览图片列表
        templateList.forEach(item -> {
            if (previewMap.get(item.getId()) != null) {
                List<String> previews = new ArrayList<>(previewMap.get(item.getId()));
                item.setPreviewList(previews);  // 设置预览图列表
            }
        });

        return templateList;
    }

    public Map<Long, Set<String>> dealPreviews(List<PptTemplateManagementDetail> detailList) {
        // 使用 LinkedHashMap 保证插入顺序
        Map<Long, Set<String>> previewMap = new LinkedHashMap<>();

        Map<Long, Set<String>> previewBeginMap = new HashMap<>();
        Map<Long, Set<String>> previewEndMap = new HashMap<>();
        Map<Long, Set<String>> previewPageMap = new HashMap<>();
        Map<Long, Set<String>> previewBigTitleMap = new HashMap<>();
        Map<Long, Set<String>> previewContentMap = new HashMap<>();


        for (PptTemplateManagementDetail detail : detailList) {
            String contentType = detail.getContentType();
            if ("background".equalsIgnoreCase(contentType)) {
                continue;  // Skip background type details
            }
            if (StringUtils.isBlank(detail.getPreviews())) {
                continue;
            }

            List<String> previews = Arrays.asList(detail.getPreviews().split(","));
            HashSet<String> setPreviews = new HashSet<>(previews);
            if ("begin".equalsIgnoreCase(contentType)) {
                addPreviewsToMap(previewBeginMap, detail.getTemplateManagementId(), setPreviews);
            }
            if ("bitTitle".equalsIgnoreCase(contentType)) {
                addPreviewsToMap(previewBigTitleMap, detail.getTemplateManagementId(), setPreviews);
            }
            if ("end".equalsIgnoreCase(contentType)) {
                addPreviewsToMap(previewEndMap, detail.getTemplateManagementId(), setPreviews);
            }

            if ("content".equalsIgnoreCase(contentType)) {
                addPreviewsToMap(previewContentMap, detail.getTemplateManagementId(), setPreviews);
            }
            if ("page".equalsIgnoreCase(contentType)) {
                ;
                addPreviewsToMap(previewPageMap, detail.getTemplateManagementId(), setPreviews);
            }
        }

        // 合并 previewBeginMap 到 previewMap
        mergePreviewMap(previewBeginMap, previewMap, 3);

        // 合并 previewContentMap 到 previewMap
        mergePreviewMap(previewContentMap, previewMap, 3);

        // 合并 previewBigTitleMap 到 previewMap
        mergePreviewMap(previewBigTitleMap, previewMap, 3);

        // 合并 previewPageMap 到 previewMap
        mergePreviewMap(previewPageMap, previewMap, 10);

        // 合并 previewEndMap 到 previewMap
        mergePreviewMap(previewEndMap, previewMap, 3);

        return previewMap;
    }

    // 随机获取指定数量的预览图
    private static List<String> getRandomPreviews(List<String> previews, int count) {
        // 防止数量大于预览图数量
        count = Math.min(count, previews.size());

        List<String> randomPreviews = new ArrayList<>(previews);
        Collections.shuffle(randomPreviews);  // 打乱列表顺序
        return randomPreviews.subList(0, count);  // 返回前count个元素
    }
    private static Set<String> getRandomPreviews(Set<String> previews, int count) {
        // 防止数量大于预览图数量
        count = Math.min(count, previews.size());

        // 将 Set 转换为 List 以便可以打乱
        List<String> previewList = new ArrayList<>(previews);

        // 打乱列表顺序
        Collections.shuffle(previewList);

        // 获取前 count 个元素，并转换回 Set
        return new HashSet<>(previewList.subList(0, count));
    }
    private void mergePreviewMap(Map<Long, Set<String>> sourceMap, Map<Long, Set<String>> targetMap,Integer count) {
        for (Map.Entry<Long, Set<String>> entry : sourceMap.entrySet()) {
            Long templateManagementId = entry.getKey();
            Set<String> previewSet = getRandomPreviews(entry.getValue(), count);

            // 如果 targetMap 中已经包含该 templateManagementId，则合并 Set，否则初始化
            targetMap.putIfAbsent(templateManagementId, new LinkedHashSet<>()); // 确保存在 key
            targetMap.get(templateManagementId).addAll(previewSet); // 合并数据
        }
    }

    private void addPreviewsToMap(Map<Long, Set<String>> previewMap, Long templateManagementId, Set<String> newPreviews) {
        previewMap.merge(templateManagementId, newPreviews, (existingSet, newSet) -> {
            existingSet.addAll(newSet);
            return existingSet;
        });
    }



    /**
     * 新增ppt模板（压缩包）管理
     *
     * @param pptTemplateManagement ppt模板管理
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class) // 确保事务管理
    public int insertPptTemplateManagement(PptTemplateManagement pptTemplateManagement) {
        // 判断这个 tLabel 和 tValue zipFullName 是否重复
        String value = pptTemplateManagement.gettValue();
        String label = pptTemplateManagement.gettLabel();
        String zipFullName = pptTemplateManagement.getZipFullName();

        long count = this.count(new LambdaQueryWrapper<PptTemplateManagement>()
                .eq(PptTemplateManagement::gettLabel, label)
                .or().eq(PptTemplateManagement::gettValue, value)
                .or().eq(PptTemplateManagement::getZipFullName, zipFullName));

        if (count > 0) {
            throw new RuntimeException("value或label或名称已存在，请使用不同的命名");
        }

        if (StringUtils.isBlank(pptTemplateManagement.getPath())) {
            throw new RuntimeException("上传压缩包有误，请重新尝试");
        }
        pptTemplateManagement.setCreateTime(DateUtils.getNowDate());
        int i = pptTemplateManagementMapper.insertPptTemplateManagement(pptTemplateManagement);

        if (i > 0) {
            // 新增ppt模板压缩包 同时进行解压 同时数据保存到 模板详细表中
            String decompressionAddressFolder = unzip(pptTemplateManagement);
            ArrayList<PptTemplateManagementDetail> detailArrayList = storeExtractedFilesToBuildData(decompressionAddressFolder, pptTemplateManagement.getId(), value);

            pptTemplateManagementDetailService.saveBatch(detailArrayList);

            // 更新模板管理表中的解压路径
            pptTemplateManagement.setUnzipFolder(decompressionAddressFolder);
            pptTemplateManagementMapper.updatePptTemplateManagement(pptTemplateManagement);

            if (pptTemplateManagement.getGeneratePreview()) {
                threadPoolDealTask2.submitTask(() -> {
                    // 为模板详细生成预览图
                    pptTemplateManagementDetailService.generatePreviewsAndUpdate(detailArrayList,"模板管理新增");
                });
            }

        }

        return i;
    }

//    public void generatePreviewsAndUpdate(List<PptTemplateManagementDetail> detailArrayList,String msg) {
//        long l = System.currentTimeMillis();
//        Map<Long, List<String>> listMap = new HashMap<>();
//
//        try {
//            log.info("{}--------------------开始为模板生成预览图----------------------", msg);
//            // 创建一个List来保存所有的CompletableFuture
//            List<CompletableFuture<Void>> futures = new ArrayList<>();
//
//            for (PptTemplateManagementDetail item : detailArrayList) {
//                if (!"pptx".equals(item.getSuffix())) {
//                    continue;
//                }
//                String absolutePath = item.getAbsolutePath();
//                // 使用runAsync提交异步任务
//                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
//                    try {
//                        pptToLongImage.splitPptAntoImg(listMap, item.getId(), absolutePath);
//                    } catch (Exception e) {
//                        log.error("生成预览图失败，ID：{}", item.getId(), e);
//                    }
//                }, threadPoolDealTask2.getThreadPool());  // 传入线程池
//
//                futures.add(future);  // 添加每个Future到列表
//            }
//
//            // 等待所有任务完成
//            CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
//            // 阻塞直到所有任务完成
//            allOf.join();  // 或者使用 get()，根据需要决定
//
//            log.info("{}--------------------模板生成预览图结束----------------------", msg);
//            log.info("{}--------------------开始更新数据----------------------", msg);
//
//            // 任务完成后进行批量更新
//            List<PptTemplateManagementDetail> updateList = new ArrayList<>();
//            listMap.forEach((key, list) -> {
//                List<String> urls = filePathUrlBuilder.buildFilePathUrls(list);
//                PptTemplateManagementDetail detail = new PptTemplateManagementDetail();
//                detail.setId(key);
//                detail.setPreviews(String.join(",", urls));
//                detail.setAbsolutePreviews(String.join(",", list));
//                updateList.add(detail);
//            });
//
//            // 批量更新
//            if (!updateList.isEmpty()) {
//                log.info("批量更新模板预览图");
//                pptTemplateManagementDetailService.updateBatchById(updateList);
//            }
//            long l2 = System.currentTimeMillis();
//            log.info("{}--------------------更新数据完成----------------------", msg);
//            log.info("{}--------------------总计耗时:{}----------------------", msg, (l2 - l) / 1000 + "s");
//
//        } catch (Exception e) {
//            log.error(e.getMessage());
//        }
//    }

    /**
     * 修改ppt模板（压缩包）管理
     *
     * @param pptTemplateManagement ppt模板管理
     * @return 结果
     */
    @Override
    @Transactional // 确保事务管理
    public int updatePptTemplateManagement(PptTemplateManagement pptTemplateManagement) {
        pptTemplateManagement.setUpdateTime(DateUtils.getNowDate());
        return pptTemplateManagementMapper.updatePptTemplateManagement(pptTemplateManagement);
    }

    /**
     * 批量删除ppt模板（压缩包）管理
     *
     * @param ids 需要删除的ppt模板管理主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deletePptTemplateManagementByIds(Long[] ids) {
        // ppt模板压缩包文件路径集合
        List<PptTemplateManagement> pptTemplateManagements = pptTemplateManagementMapper.selectBatchIds(Arrays.asList(ids));
        List<String> collect1 = pptTemplateManagements.stream()
                .map(PptTemplateManagement::getPath) // 提取每个对象的路径
                .collect(Collectors.toList()); // 收集到List


        List<PptTemplateManagementDetail> detailList = pptTemplateManagementDetailService.list(
                new LambdaQueryWrapper<PptTemplateManagementDetail>()
                        .in(PptTemplateManagementDetail::getTemplateManagementId, Arrays.asList(ids))
        );
        List<Long> idCollect = detailList.stream().map(PptTemplateManagementDetail::getId)
                .collect(Collectors.toList());

        // 删除数据库中的 ppt模板压缩包管理
        int i = pptTemplateManagementMapper.deletePptTemplateManagementByIds(ids);
        // 删除数据库中的 ppt模板详细信息
        if (i > 0) {
            pptTemplateManagementDetailService.removeBatchByIds(idCollect);
        }

        // ppt模板详细对应的文件信息对应的集合
        List<String> collect = Stream.concat(
                detailList.stream()
                        .flatMap(detail -> Arrays.stream(
                                Optional.ofNullable(detail.getAbsolutePath()).orElse("").split(","))),
                detailList.stream()
                        .flatMap(detail -> Arrays.stream(
                                Optional.ofNullable(detail.getAbsolutePreviews()).orElse("").split(",")))
        ).collect(Collectors.toList());

        // 异步删除文件
        CompletableFuture.runAsync(() -> {

            fileCustomUtils.deleteFiles(collect1);
            fileCustomUtils.deleteFiles(collect);
//            remoteFileService.deleteFiles(collect1);
        });

        return i;
    }

    /**
     * 删除ppt模板（压缩包）管理信息
     *
     * @param id ppt模板管理主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deletePptTemplateManagementById(Long id) {

        List<PptTemplateManagementDetail> detailList = pptTemplateManagementDetailService.list(
                new LambdaQueryWrapper<PptTemplateManagementDetail>()
                        .eq(PptTemplateManagementDetail::getTemplateManagementId, id)
        );
        List<Long> idCollect = detailList.stream().map(PptTemplateManagementDetail::getId).collect(Collectors.toList());

        int i = pptTemplateManagementMapper.deletePptTemplateManagementById(id);
        if (i > 0) {
            pptTemplateManagementDetailService.removeBatchByIds(idCollect);
        }


        List<String> collect = Stream.concat(
                detailList.stream()
                        .flatMap(detail -> Arrays.stream(
                                Optional.ofNullable(detail.getAbsolutePath()).orElse("").split(","))),
                detailList.stream()
                        .flatMap(detail -> Arrays.stream(
                                Optional.ofNullable(detail.getAbsolutePreviews()).orElse("").split(",")))
        ).collect(Collectors.toList());

        // 异步删除文件
        CompletableFuture.runAsync(() -> {
            fileCustomUtils.deleteFiles(collect);
//            remoteFileService.deleteFiles(collect2);
        });

        return i;
    }


    /**
     * 解压ppt模板压缩包
     *
     * @param pptTemplateManagement
     * @return 解压路径
     */
    @Override
    public String unzip(PptTemplateManagement pptTemplateManagement) {
        // 1.获取上传存放的ppt模板压缩包路径
        String zipPath = pptTemplateManagement.getPath();

        // 2.txt.获取ppt模板压缩包文件名
        String zipFullName = pptTemplateManagement.getZipFullName();

        // 3.解压ppt模板压缩包到指定目录 从字典配置中得到模板解压路径
        List<SysDictData> cacheObject = redisService.getCacheObject(Constants.Sys_Dict_Theme_File_Path);

        if (cacheObject.isEmpty()) {
            throw new RuntimeException("字典数据+ 【" + Constants.Sys_Dict_Theme_File_Path + "】为空,请先配置字典");
        }
        String themeFolderBase = "";
        // 获取系统标识
        String osName = Utils.getOsName();
        if ("win".equals(osName)) {
            List<SysDictData> collect = cacheObject.stream().filter(dicData -> Constants.WIN_Theme_Folder_Base.equals(dicData.getDictLabel()))
                    .collect(Collectors.toList());
            if (collect.isEmpty()) {
                throw new RuntimeException("字典数据+ 【" + Constants.WIN_Theme_Folder_Base + "】无对应的字典数据,请先配置字典");
            }
            themeFolderBase = collect.get(0).getDictValue();
        }else if ("linux".equals(osName)) {
            List<SysDictData> collect = cacheObject.stream().filter(dicData -> Constants.LINUX_Theme_Folder_Base.equals(dicData.getDictLabel()))
                    .collect(Collectors.toList());
            if (collect.isEmpty()) {
                throw new RuntimeException("字典数据+ 【" + Constants.LINUX_Theme_Folder_Base + "】无对应的字典数据,请先配置字典");
            }
            themeFolderBase = collect.get(0).getDictValue();
        }else {
            throw new RuntimeException(osName + "系统不支持");
        }


        // 4.构造输出解压目录
        String decompressionAddressFolder = "";
        if (themeFolderBase.endsWith(File.separator)) {
            decompressionAddressFolder = themeFolderBase + zipFullName + File.separator;
        } else {
            decompressionAddressFolder = themeFolderBase + File.separator + zipFullName + File.separator;
        }
        try {
            // 替换路径中的所有不一致的分隔符为 "/"
            decompressionAddressFolder = decompressionAddressFolder.replace("\\", "/").replace("//", "/");

            // 清除原有的数据
            File folder = new File(decompressionAddressFolder);
            if (folder.exists() || folder.isDirectory()) {
                fileCustomUtils.deleteRecursively(folder);
            }

            // 5.开始解压 调用远程服务 remoteFileService.pptTemplateUnzip()
            UnZip unZip = new UnZip();
            unZip.setZipPath(zipPath);
            unZip.setTargetPath(decompressionAddressFolder);
              extractZipFile(zipPath, decompressionAddressFolder);
            Boolean success = remoteFileService.pptTemplateUnzip(unZip);
            if (success) {
                log.info("解压ppt模板压缩包成功,解压目录:{}", decompressionAddressFolder);
                return decompressionAddressFolder;
            } else {
                throw new RuntimeException("解压ppt模板压缩包失败");
            }
        } catch (IOException e) {
            throw new RuntimeException("文件异常,解压缩文件失败: " + e.getMessage(), e);
        }

    }

    /**
     * 解压，并而完成数据构建
     *
     * @param decompressionAddressFolder
     * @param templateManagementId
     */
    public ArrayList<PptTemplateManagementDetail> storeExtractedFilesToBuildData(String decompressionAddressFolder, Long templateManagementId,String themeLabel) {
        File folder = new File(decompressionAddressFolder);
        if (!folder.exists() || !folder.isDirectory()) {
            System.out.println("指定的路径不存在或不是一个目录: " + decompressionAddressFolder);
            return new ArrayList<>();
        }

        ArrayList<PptTemplateManagementDetail> detailArrayList = new ArrayList<>();
        // 开始遍历目录获取构建的数据
        traverseDirectory(folder, templateManagementId, detailArrayList,themeLabel);
        return detailArrayList;
    }

    /**
     * 遍历目录文件构建数据
     *
     * @param folder               文件目录
     * @param templateManagementId 模板管理id
     * @param detailArrayList      构建数据集合
     */
    private void traverseDirectory(File folder, Long templateManagementId, ArrayList<PptTemplateManagementDetail> detailArrayList,String themeLabel) {
        // 获取目录下的所有文件和子目录
        File[] files = folder.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    // 如果是目录，递归调用
                    traverseDirectory(file, templateManagementId, detailArrayList,themeLabel);
                } else {
                    PptTemplateManagementDetail pptTemplateManagementDetail = new PptTemplateManagementDetail();
                    pptTemplateManagementDetail.setCreateTime(DateUtils.getNowDate());
                    // 如果是文件，存储文件信息到数据库
                    String name = file.getName();
                    // 检查文件名中是否包含 "."
                    int dotIndex = name.lastIndexOf(".");
                    String extension = (dotIndex != -1 && dotIndex < name.length() - 1)
                            ? name.substring(dotIndex + 1) // 获取后缀名
                            : ""; // 如果没有后缀，则返回空字符串
                    pptTemplateManagementDetail.setTemplateManagementId(templateManagementId); // 设置模板（压缩包）管理id
                    pptTemplateManagementDetail.setFileName(file.getName().substring(0, name.lastIndexOf("."))); // 设置 文件名
                    pptTemplateManagementDetail.setSuffix(extension); // 设置文件后缀

                    pptTemplateManagementDetail.setAbsolutePath(fileCustomUtils.normalizePath(file.getAbsolutePath())); // 设置绝对地址
                    pptTemplateManagementDetail.setUnzipFolder(fileCustomUtils.normalizePath(folder.getPath())); // 设置解压的基础目录路径
                    if (name.contains(".pptx")) {
                        // 是模板pptx文件 示例格式  minimalRed_page_0_1_0_0_0.pptx
                        // 去掉后缀，获取文件名部分
                        String baseName = name.substring(0, name.lastIndexOf(".")); // 去掉 .pptx 后缀

                        // 以 "_" 作为分隔符进行分割
                        String[] parts = baseName.split("_");

//                        // 检查分割后的数组是否至少有一个元素，并取出第一个元素 minimalRed
//                        String themeLabel = parts.length > 0 ? parts[0] : "";
                        pptTemplateManagementDetail.setThemeLabel(themeLabel); // 设置主题标志

                        // 检查分割后的数组是否至少有两个元素，并取出第二个元素 page
                        String contentType = parts.length > 1 ? parts[1] : ""; // parts[1] 为第二个元素
                        pptTemplateManagementDetail.setContentType(contentType); // 设置内容类型

                        // 检查分割后的数组是否至少有两个元素，并取出第二个元素之后的内容 0_1_0_0_0
                        String versionStructure = parts.length > 2 ? String.join("_", Arrays.copyOfRange(parts, 2, parts.length)) : ""; // 从第三个元素开始拼接为结构编号
                        pptTemplateManagementDetail.setVersionStructure(versionStructure); // 设置结构编号

                    } else if (name.contains(Constants.BGC_BACKGROUND)) {
                        // 是背景图片
                        pptTemplateManagementDetail.setThemeLabel(themeLabel); // 设置主题标志
                        pptTemplateManagementDetail.setContentType(Constants.BGC_BACKGROUND); // 设置内容类型
                        pptTemplateManagementDetail.setVersionStructure("0_0_0_0_0"); // 设置结构编号

                    }else if (name.contains(Constants.BGC_BEGIN)) {
                        // 是首页背景图
                        pptTemplateManagementDetail.setThemeLabel(themeLabel); // 设置主题标志
                        pptTemplateManagementDetail.setContentType(Constants.BGC_BEGIN); // 设置内容类型
                        pptTemplateManagementDetail.setVersionStructure("0_0_0_0_0"); // 设置结构编号

                    }else if (name.contains(Constants.BGC_BIGTITLE)) {
                        // 是章节背景图
                        pptTemplateManagementDetail.setThemeLabel(themeLabel); // 设置主题标志
                        pptTemplateManagementDetail.setContentType(Constants.BGC_BIGTITLE); // 设置内容类型
                        pptTemplateManagementDetail.setVersionStructure("0_0_0_0_0"); // 设置结构编号

                    }
                    else if (name.contains(Constants.BGC_CONTENT)) {
                        // 是目录背景图
                        pptTemplateManagementDetail.setThemeLabel(themeLabel); // 设置主题标志
                        pptTemplateManagementDetail.setContentType(Constants.BGC_CONTENT); // 设置内容类型
                        pptTemplateManagementDetail.setVersionStructure("0_0_0_0_0"); // 设置结构编号

                    }else if (name.contains(Constants.BGC_PAGE)) {
                        // 是内容背景图
                        pptTemplateManagementDetail.setThemeLabel(themeLabel); // 设置主题标志
                        pptTemplateManagementDetail.setContentType(Constants.BGC_PAGE); // 设置内容类型
                        pptTemplateManagementDetail.setVersionStructure("0_0_0_0_0"); // 设置结构编号

                    }else if (name.contains(Constants.BGC_END)) {
                        // 是结尾背景图
                        pptTemplateManagementDetail.setThemeLabel(themeLabel); // 设置主题标志
                        pptTemplateManagementDetail.setContentType(Constants.BGC_END); // 设置内容类型
                        pptTemplateManagementDetail.setVersionStructure("0_0_0_0_0"); // 设置结构编号

                    }

                    // 根据类型进行 定义顺序
                    setOrderByContentType(pptTemplateManagementDetail);
                    detailArrayList.add(pptTemplateManagementDetail);

                }
            }
        }
    }

    private static void setOrderByContentType(PptTemplateManagementDetail pptTemplateManagementDetail) {
        switch (pptTemplateManagementDetail.getContentType()) {
            case Constants.BGC_BACKGROUND:
                pptTemplateManagementDetail.setSort(1.0);
                break;
            case Constants.BGC_BEGIN:
                pptTemplateManagementDetail.setSort(2.0);
                break;
            case Constants.BGC_BIGTITLE:
                pptTemplateManagementDetail.setSort(3.0);
                break;
            case Constants.BGC_CONTENT:
                pptTemplateManagementDetail.setSort(4.0);
                break;
            case Constants.BGC_PAGE:
                pptTemplateManagementDetail.setSort(5.0);
                break;
            case Constants.BGC_END:
                pptTemplateManagementDetail.setSort(6.0);
                break;
            case Constants.BEGIN:
                pptTemplateManagementDetail.setSort(7.0);
                break;    
            case Constants.BIGTITLE:
                pptTemplateManagementDetail.setSort(8.0);
                break;
            case Constants.CONTENT:
                pptTemplateManagementDetail.setSort(9.0);
                break;
            case Constants.PAGE:
                pptTemplateManagementDetail.setSort(10.0);
                break;
            case Constants.END:
                pptTemplateManagementDetail.setSort(11.0);
                break;
            default:
                pptTemplateManagementDetail.setSort(12.0);
                break;
        }
    }


    /**
     * 解压zip文件到指定目录
     *
     * @param zipPath zip文件地址
     * @param outPath 解压目录
     * @throws IOException 抛出异常
     */
    private void extractZipFile(String zipPath, String outPath) throws IOException {
        File destDir = new File(outPath);
        if (!destDir.exists()) {
            boolean created = destDir.mkdirs(); // 创建目标目录
            if (created) {
                log.info("目录创建成功: {}", outPath);
            } else {
                log.warn("目录已存在或创建失败: {}", outPath);
            }
        }

        List<String> charsets = Arrays.asList("GBK", "UTF-8", "ISO-8859-1");

        // 尝试使用每种字符集
        for (String charset : charsets) {
            try (ZipFile zip = new ZipFile(zipPath, charset)) {
                Enumeration<? extends ZipArchiveEntry> entries = zip.getEntries();
                boolean allFilesValid = true; // 标记是否所有文件名都有效
                while (entries.hasMoreElements()) {
                    ZipArchiveEntry entry = entries.nextElement();
                    String name = entry.getName();
                    name = name.replaceAll("[/\\\\]+$", "");
                    // 校验文件名 是否解析乱码
                    if (!fileCustomUtils.isValidFileName(name)) {
                        // 此字符解码失败，尝试其他字符集
                        allFilesValid = false; // 标记为无效
                        break;  // 跳出循环，尝试下一个字符集
                    }
                    // 处理文件
                    try {
                        File outFile = new File(destDir, entry.getName());
                        // 如果是目录，则创建目录
                        if (entry.isDirectory()) {
                            if (!outFile.mkdirs()) {
                                log.warn("目录已存在或创建失败: {}", outFile.getPath());
                            }
                        } else {
                            // 如果是文件，则创建父目录并写入文件
                            Files.createDirectories(outFile.getParentFile().toPath());

                            try (InputStream inputStream = zip.getInputStream(entry);
                                 BufferedOutputStream bos = new BufferedOutputStream(Files.newOutputStream(outFile.toPath()))) {
                                byte[] data = readInputStream(inputStream); // 使用自定义的读取方法
                                bos.write(data); // 写入文件
                            } catch (IOException e) {
                                log.error("写入文件失败: {}, 错误信息: {}", outFile.getPath(), e.getMessage());
                            }
                        }
                    } catch (Exception e) {
                        log.error("获取文件路径失败: {}, 错误信息: {}", entry.getName(), e.getMessage());
                    }
                }
                // 如果所有文件都有效，则返回
                if (allFilesValid) {
                    log.info("成功使用字符集 {} 解压所有文件", charset);
                    return; // 退出方法
                }
            } catch (IOException e) {
                log.error("使用字符集 {} 解压失败,请重新设置文件名: {}", charset, e.getMessage());
                // 可以选择继续尝试其他字符集
            }
        }

        // 如果所有字符集都失败，抛出异常
        throw new IOException("所有字符集解压失败");
    }

    /**
     * 自定义方法：读取 InputStream 中的所有字节
     *
     * @param inputStream
     * @return
     * @throws IOException
     */
    private byte[] readInputStream(InputStream inputStream) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[4096];
        int bytesRead;

        while ((bytesRead = inputStream.read(buffer)) != -1) {
            byteArrayOutputStream.write(buffer, 0, bytesRead);
        }

        return byteArrayOutputStream.toByteArray();
    }


    /**
     * 构建Map数据列表
     */
    @Override
    public List<Map<String, Object>> buildMapDataList() {
        List<PptTemplateManagement> templateList = selectPptTemplateManagementList(new PptTemplateManagement());
        return templateList.stream()
                .map(pptTemplateManagement -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", String.valueOf(pptTemplateManagement.getId()));
                    map.put("tLabel", pptTemplateManagement.gettLabel());
                    map.put("tValue", pptTemplateManagement.gettValue());
                    map.put("zipFullName", pptTemplateManagement.getZipFullName());
                    map.put("unzipFolder", pptTemplateManagement.getUnzipFolder());
                    map.put("previewList", pptTemplateManagement.getPreviewList());
                    return map;
                }).collect(Collectors.toList());
    }
}
