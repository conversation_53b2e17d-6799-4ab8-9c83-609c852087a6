package com.ruoyi.ppt.controller;

import com.google.gson.JsonObject;
import okhttp3.*;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import java.io.*;


/**
 * 需要添加依赖
 * <!-- https://mvnrepository.com/artifact/com.squareup.okhttp3/okhttp -->
 * <dependency>
 *     <groupId>com.squareup.okhttp3</groupId>
 *     <artifactId>okhttp</artifactId>
 *     <version>4.12.0</version>
 * </dependency>
 */

class Sample {

    public static final OkHttpClient HTTP_CLIENT = new OkHttpClient().newBuilder().readTimeout(300, TimeUnit.SECONDS).build();
    private static final com.google.gson.Gson gson = new com.google.gson.Gson();

    public static void main(String []args) throws IOException{
        MediaType mediaType = MediaType.parse("application/json");

        ArrayList<Map<String, String>> messagesList = new ArrayList<>();
        HashMap<String, String> map = new HashMap<>();
        map.put("role", "user");
        map.put("content", "您好");
        messagesList.add(map);
        // 构建 JSON 请求体
        JsonObject requestBody = new JsonObject();
        requestBody.addProperty("model", "ernie-4.0-turbo-8k");
        requestBody.add("messages", gson.toJsonTree(messagesList));
        requestBody.addProperty("stream", false);
        requestBody.addProperty("disable_search", false);
        requestBody.addProperty("enable_citation", false);

        // 构建 HTTP 请求
        RequestBody body = RequestBody.create(gson.toJson(requestBody), mediaType);

        Request request = new Request.Builder()
            .url("https://qianfan.baidubce.com/v2/chat/completions")
            .method("POST", body)
            .addHeader("Content-Type", "application/json")
            .addHeader("appid", "")
            .addHeader("Authorization", "Bearer bce-v3/ALTAK-dyQhjZkw5RjqzaBOcJ5lc/04a5392ac58e7e75a61e7ce5a4a92a62613b075c ")
            .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        System.out.println(response.body().string());

    }
    
    
}